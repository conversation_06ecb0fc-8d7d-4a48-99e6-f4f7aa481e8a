package com.alibaba.koastline.multiclusters.config

import com.alibaba.koastline.multiclusters.inceptor.TraceIdInterceptor
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer


@Configuration
class WebConfig(
    private val traceIdInterceptor: TraceIdInterceptor,
) : WebMvcConfigurer {
    override fun addInterceptors(registry: InterceptorRegistry) {
        super.addInterceptors(registry)
        registry.addInterceptor(traceIdInterceptor).order(Ordered.HIGHEST_PRECEDENCE)
            .addPathPatterns("/**")

        // sentinel 限流接入 https://aliyuque.antfin.com/twr7ai/internalsentinel/sv7k3a

    }
}
