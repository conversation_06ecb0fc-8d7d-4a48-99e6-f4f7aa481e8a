package com.alibaba.koastline.multiclusters.api.v4.schedule

import com.alibaba.koastline.multiclusters.api.v4.schedule.dto.NonDeclarativeOrientedScheduleRequest
import com.alibaba.koastline.multiclusters.api.v4.schedule.dto.NonDeclarativeScaleOutScheduleRequest
import com.alibaba.koastline.multiclusters.api.v4.schedule.dto.PodListScheduleRequest
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.runtime.RuntimeService
import com.alibaba.koastline.multiclusters.schedule.model.*
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.extra.ServerlessRunningScheduleService
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/schedule/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ScheduleApiV4 {
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService
    @Autowired
    lateinit var serverlessRunningScheduleService: ServerlessRunningScheduleService
    @Autowired
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService

    @PostMapping("/doScaleOutScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]扩容调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doScaleOutScheduleByNonDeclarative(@RequestBody nonDeclarativeScaleOutScheduleRequest: NonDeclarativeScaleOutScheduleRequest): AquamanResult<ScheduleResult> {
        val formattedServerless = computeIsServerless(nonDeclarativeScaleOutScheduleRequest.serverless, nonDeclarativeScaleOutScheduleRequest.scheduleEnvType)
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(nonDeclarativeScaleOutScheduleRequest.appName, nonDeclarativeScaleOutScheduleRequest.envStackId, nonDeclarativeScaleOutScheduleRequest.resourceGroup, emptyList()),
            DeclarationData(null, reviseOrientedDeclaration(
                nonDeclarativeScaleOutScheduleRequest.resourceGroup, nonDeclarativeScaleOutScheduleRequest.declaration
            )),
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.SCALE_OUT),
            ScheduleRequestParam(
                replicas = nonDeclarativeScaleOutScheduleRequest.replicas,
                serverless = formattedServerless,
                scheduleEnvType = computeScheduleEnvType(nonDeclarativeScaleOutScheduleRequest.serverless, nonDeclarativeScaleOutScheduleRequest.scheduleEnvType),
                serverlessRuntimeTemplate = getServerlessRuntimeTemplateByEnv(
                    serverless = formattedServerless,
                    appName = nonDeclarativeScaleOutScheduleRequest.appName,
                    resourceGroup = nonDeclarativeScaleOutScheduleRequest.resourceGroup,
                    envStackId = nonDeclarativeScaleOutScheduleRequest.envStackId
                )
            ),
            includeClusterInfoToResult = true
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doScaleOutScheduleByDeclarative")
    @Operation(summary = "[声明式]扩容调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doScaleOutScheduleByDeclarative(@ApiParam("应用名")@RequestParam appName: String,
                                        @ApiParam("环境StackId")@RequestParam(required = false) envStackId: String?,
                                        @ApiParam("分组名")@RequestParam resourceGroup: String,
                                        @ApiParam("变更副本数")@RequestParam replicas: Int,
                                        @ApiParam("是否为Serverless")@RequestParam(required = false) serverless : Boolean? = false,
                                        @ApiParam("调度环境类型")@RequestParam(required = false) scheduleEnvType : ScheduleEnvType?): AquamanResult<ScheduleResult> {
        val formattedServerless = computeIsServerless(serverless, scheduleEnvType)
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(appName, envStackId, resourceGroup, emptyList()),
            ScheduleType(SchedulePatternEnum.DECLARATIVE, SceneEnum.SCALE_OUT),
            ScheduleRequestParam(
                replicas = replicas,
                serverless = formattedServerless,
                scheduleEnvType = computeScheduleEnvType(serverless, scheduleEnvType),
                serverlessRuntimeTemplate = getServerlessRuntimeTemplateByEnv(formattedServerless, appName, resourceGroup, envStackId)
            )
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @PostMapping("/doOrientedScaleInScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]定向缩容调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doOrientedScaleInScheduleByNonDeclarative(@RequestBody nonDeclarativeOrientedScheduleRequest: NonDeclarativeOrientedScheduleRequest): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(nonDeclarativeOrientedScheduleRequest.appName, null, null, nonDeclarativeOrientedScheduleRequest.resourceSNList),
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.ORIENTED_SCALE_IN)
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doNonOrientedScaleInScheduleByDeclarative")
    @Operation(summary = "[声明式]非定向缩容调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doNonOrientedScaleInScheduleByDeclarative(@ApiParam("应用名")@RequestParam appName: String, @ApiParam("分组名")@RequestParam resourceGroup: String,
                                        @ApiParam("是否为Serverless")@RequestParam(required = false) serverless : Boolean? = false,
                                        @ApiParam("Serverless基座应用名[Serverless模式必填]")@RequestParam(required = false) serverlessBaseAppName : String?,
                                        @ApiParam("变更副本数[负数]")@RequestParam replicas: Int): AquamanResult<ScheduleResult> {
        if (serverless == true){
            require(!serverlessBaseAppName.isNullOrBlank()){ "serverlessBaseAppName cannot be null" }
        }
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(appName, null, resourceGroup, emptyList()),
            ScheduleType(SchedulePatternEnum.DECLARATIVE, SceneEnum.NON_ORIENTED_SCALE_IN),
            ScheduleRequestParam(
                replicas = replicas,
                serverless = serverless ?: false,
                serverlessRuntimeTemplate = getServerlessRuntimeTemplateByBaseApp(serverless, appName, resourceGroup, serverlessBaseAppName),
                serverlessBaseAppName = serverlessBaseAppName,
                scheduleEnvType = computeScheduleEnvType(serverless = serverless)
            )
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doNonOrientedScaleInScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]非定向缩容调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doNonOrientedScaleInScheduleByNonDeclarative(@ApiParam("应用名")@RequestParam appName: String,
                                                     @ApiParam("分组名")@RequestParam resourceGroup: String,
                                                     @ApiParam("站点")@RequestParam site: String,
                                                     @ApiParam("用途")@RequestParam stage: String,
                                                     @ApiParam("单元")@RequestParam unit: String,
                                                     @ApiParam("是否为Serverless")@RequestParam(required = false) serverless : Boolean? = false,
                                                     @ApiParam("Serverless基座应用名[Serverless模式必填]")@RequestParam(required = false) serverlessBaseAppName : String?,
                                                     @ApiParam("变更副本数[负数]")@RequestParam replicas: Int): AquamanResult<ScheduleResult>  {
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(appName, null, resourceGroup, emptyList()),
            declarationData = DeclarationData(declaration = OrientedDeclaration(site = site, stage = stage, unit = unit)),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.NON_ORIENTED_SCALE_IN),
            scheduleRequestParam = ScheduleRequestParam(
                replicas = replicas,
                serverless = serverless ?: false,
                serverlessRuntimeTemplate = getServerlessRuntimeTemplateByBaseApp(serverless, appName, resourceGroup, serverlessBaseAppName),
                serverlessBaseAppName = serverlessBaseAppName,
                scheduleEnvType = computeScheduleEnvType(serverless = serverless)
            )
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }


    @PostMapping("/doRestartScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]重启调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doRestartScheduleByNonDeclarative(@RequestBody nonDeclarativeOrientedScheduleRequest: NonDeclarativeOrientedScheduleRequest): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(nonDeclarativeOrientedScheduleRequest.appName, null, null, nonDeclarativeOrientedScheduleRequest.resourceSNList),
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.RESTART)
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doReplaceScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]置换调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doReplaceScheduleByNonDeclarative(@RequestBody nonDeclarativeReplaceScheduleRequest: NonDeclarativeOrientedScheduleRequest): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            ResourceScope(nonDeclarativeReplaceScheduleRequest.appName, null, null, nonDeclarativeReplaceScheduleRequest.resourceSNList),
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.REPLACE)
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doDeployScheduleByNonDeclarative")
    @Operation(summary = "[非声明式]发布调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doDeployScheduleByNonDeclarative(@ApiParam("应用名")@RequestParam appName: String,
                                         @ApiParam("环境StackId")@RequestParam(required = false) envStackId: String?,
                                         @ApiParam("分组名")@RequestParam(required = false) resourceGroup: String?,
                                         @ApiParam("是否为Serverless,默认为false")@RequestParam(required = false) serverless : Boolean? = false,
                                         @ApiParam("调度环境类型")@RequestParam(required = false) scheduleEnvType : ScheduleEnvType?,
                                         @ApiParam("环境版本StackPkId")@RequestParam(required = false) stackPkId: String?,
        @ApiParam("DryRun")@RequestParam(required = false) dryRun: Boolean? = false,
    ): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(appName, envStackId, resourceGroup, emptyList()),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.DEPLOY),
            scheduleRequestParam = ScheduleRequestParam(
                scheduleEnvType = computeScheduleEnvType(serverless, scheduleEnvType),
                envStackPkId = stackPkId
            ),
            includeClusterInfoToResult = true,
            dryRun = dryRun
        )
        return AquamanResult.ok(
                    scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @GetMapping("/doPodListSchedule")
    @Operation(summary = "PodList调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doPodListSchedule(@ApiParam("应用名")@RequestParam appName: String,
                          @ApiParam("环境StackId")@RequestParam(required = false) envStackId: String?,
                          @ApiParam("分组名")@RequestParam(required = false) resourceGroup: String?,
                          @ApiParam("sn列表")@RequestParam(required = false) snList: String?,
                          @ApiParam("调度环境类型")@RequestParam(required = false) scheduleEnvType : ScheduleEnvType?
    ): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(
                appName = appName,
                envStackId = envStackId,
                resourceGroup = resourceGroup,
                resourceSNList = snList ?.run { snList.split(",") } ?: emptyList(),
            ),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD),
            includeClusterInfoToResult = true,
            scheduleRequestParam = scheduleEnvType ?.run { ScheduleRequestParam(scheduleEnvType = this) }
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @PostMapping("/doPodListSchedule")
    @Operation(summary = "PodList调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doPodListSchedule(@RequestBody podListScheduleRequest: PodListScheduleRequest): AquamanResult<ScheduleResult> {
        podListScheduleRequest.validate()
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(
                appName = podListScheduleRequest.appName,
                envStackId = podListScheduleRequest.envStackId,
                resourceGroup = podListScheduleRequest.resourceGroup,
                resourceSNList = podListScheduleRequest.snList,
            ),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD),
            includeClusterInfoToResult = true,
            scheduleRequestParam = podListScheduleRequest.scheduleEnvType ?.run { ScheduleRequestParam(scheduleEnvType = this) }
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }

    @PostMapping("/seekServerlessAppMigrationBaseApp")
    @Operation(summary = "基座轮转runtimeId重调度")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun seekServerlessAppMigrationBaseApp(
        @RequestBody originalWorkloadMetadataConstraint: WorkloadMetadataConstraint,
        @RequestParam("migrationToBaseApp") migrationToBaseApp: String,
        @RequestParam("migrationToEnvStackId") migrationToEnvStackId: String
    ): AquamanResult<ScheduleResult> {
        require(migrationToBaseApp.isNotBlank()){ "migrationToBaseApp cannot be blank" }
        require(migrationToEnvStackId.isNotBlank()){ "migrationToEnvStackId cannot be blank" }

        val workloadMetadataConstraint = serverlessRunningScheduleService.seekServerlessAppMigrationBaseApp(
            originalWorkloadMetadataConstraint, migrationToBaseApp, migrationToEnvStackId
        )
        val workloadExpect = WorkloadExpectedState(
            workloadMetadataConstraint = workloadMetadataConstraint, params = emptyMap()
        )
        return AquamanResult.ok(
            ScheduleResult(workloadExpectedStates = listOf(workloadExpect))
        )
    }

    private fun getServerlessRuntimeTemplateByEnv(serverless: Boolean?, appName: String, resourceGroup: String, envStackId: String?): String? {
        serverless ?:let { return null }
        if (!serverless) { return null }
        return scheduleStandardService.getServerlessRuntimeTemplateByEnv(appName, resourceGroup, checkNotNull(envStackId))
    }

    private fun getServerlessRuntimeTemplateByBaseApp(serverless: Boolean?, appName: String, resourceGroup: String, serverlessBaseAppName: String?): String? {
        serverless ?:let { return null }
        if (!serverless) { return null }
        return scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(appName, resourceGroup, checkNotNull(serverlessBaseAppName))
    }

    private fun reviseOrientedDeclaration(resourceGroup: String, orientedDeclaration: OrientedDeclaration): OrientedDeclaration {
        return orientedDeclaration.copy(
            site = MetadataUtils.formatSite(orientedDeclaration.site),
            unit = MetadataUtils.formatUnit(orientedDeclaration.unit)!!,
            matchApRELabels = orientedDeclaration.matchApRELabels.ifEmpty {
                // 默认添加用户强声明的 label; 如果没有，则添加 CPU 资源声明
                val deed = apREDeedResourceGroupBindingService.getApREDeedByResourceGroup(resourceGroup)
                val deedLabels = deed?.matchApRELabels?.filter { !it.name.isNullOrEmpty() && !it.value.isNullOrEmpty() }
                    ?.map {
                        OrientedMatchApRELabel(
                            name = it.name!!, value = it.value!!,
                            matchApREFeatureSpecs = it.matchApREFeatureSpecs
                                ?.filter { spec -> !spec.specCode.isNullOrEmpty() }
                                ?.map {spec ->
                                    OrientedMatchApREFeatureSpec(
                                        specCode = spec.specCode!!, specType = spec.specType,
                                        matchFeatureSpecLabels = spec.matchFeatureSpecLabels
                                    )
                                }
                        )
                    }
                if (deedLabels.isNullOrEmpty()) {
                    listOf(
                        OrientedMatchApRELabel(
                            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                            value = ApRELabelExt.APRE_LABEL_FEATURE_VALUE_COMPUTE
                        )
                    )
                } else {
                    deedLabels
                }
            }
        )
    }

    /**
     * 计算调度环境类型，考虑过程兼容
     */
    private fun computeScheduleEnvType(serverless: Boolean?, scheduleEnvType: ScheduleEnvType? = null): ScheduleEnvType {
        if (scheduleEnvType != null) {
            return scheduleEnvType
        }
        if (serverless != null && serverless) {
            return SERVERLESS_APP
        }
        return ASI
    }

    /**
     * 计算是否为Serverless，考虑过程兼容
     */
    private fun computeIsServerless(serverless: Boolean?, scheduleEnvType: ScheduleEnvType?): Boolean {
        if (scheduleEnvType != null) {
            return scheduleEnvType == SERVERLESS_APP
        }
        return serverless ?: false
    }
}