package com.alibaba.koastline.multiclusters.api.v1.virtualenv

import com.alibaba.koastline.multiclusters.api.v1.virtualenv.dto.ClusterEnvironmentDTO
import com.alibaba.koastline.multiclusters.api.v1.virtualenv.params.ConfigurationContext
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService.Companion.AONE_PRODUCTLINE
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService.Companion.CLUSTER_ENVIRONMENT_KEY
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService.Companion.GLOBAL_EXTERNAL_ID
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService.Companion.MANAGED_CLUSTER_KEY
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService.Companion.SITE
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.model.ClusterProfile
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentDetails
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentSpec
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentTemplateSpec
import com.alibaba.koastline.multiclusters.common.exceptions.FunctionDeprecatedException
import com.alibaba.koastline.multiclusters.common.exceptions.UserHasNoRightException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.AuthTool
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.config.RegionPropertiesConfig
import com.alibaba.koastline.multiclusters.data.dao.env.AvailableZoneSiteMappingRepo
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/multiclusters/cluster-environments", produces = [MediaType.APPLICATION_JSON_VALUE])
class ClusterEnvironmentApi {
    val log by logger()

    @Autowired
    lateinit var defaultClusterEnvironmentService: DefaultClusterEnvironmentService

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var regionPropertiesConfig: RegionPropertiesConfig

    @Autowired
    lateinit var availableZoneSiteMappingRepo: AvailableZoneSiteMappingRepo

    @PostMapping
    @Operation(summary = "create a cluster environment for a business application")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createClusterEnvironment(@RequestBody clusterEnvironmentSpec: ClusterEnvironmentSpec): AquamanResult<ClusterEnvironment> {
        throw FunctionDeprecatedException()
    }

    @PutMapping("/{clusterEnvironmentKey}")
    @Operation(summary = "update a cluster environment's tags for a business application")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT')")
    fun replaceClusterEnvironmentByTags(@RequestBody envTags: String,
                                 @PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String): AquamanResult<Boolean> {
        log.info("${TraceUtils.getTraceId()},update cluster environment with env key: $clusterEnvironmentKey, and envtags $envTags")
        return try {
            defaultClusterEnvironmentService.replaceClusterEnvironmentTags(envTags, clusterEnvironmentKey).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @PostMapping("/{envLevel}/{region}/{az}")
    @Operation(summary = "validate a cluster environment for a business application with env annotations support")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    @Deprecated("this api will be offline in near future")
    fun validateClusterEnvironment(@PathVariable("envLevel") envLevel: String,
                                   @PathVariable("region") region: String,
                                   @PathVariable("az") az: String,
                                   @RequestBody envMeta: MutableMap<String, String>): AquamanResult<MutableMap<String, String>> {
        log.info("${TraceUtils.getTraceId()},validate cluster environment")
        if (!envMeta.containsKey("externalType") || !envMeta.containsKey("externalId")) {
            return AquamanResult.fail("missing params externalType and externalId in request body")
        }

        val externalId = envMeta["externalId"]!!
        val externalType = envMeta["externalType"]!!
        envMeta["envLevel"] = envLevel
        envMeta.remove("externalId")
        envMeta.remove("externalType")
        if (envMeta.isEmpty()) return AquamanResult.fail("request body should contains params more than externalId and externalType")

        return try {
            defaultClusterEnvironmentService.validateClusterEnvironment(externalId, externalType, envLevel, region, az, envMeta).let {
                // to keep return value consistent
                if (it.isNotEmpty() && it.containsKey(MANAGED_CLUSTER_KEY)) {
                    it.remove(MANAGED_CLUSTER_KEY)
                }
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }

    }

    @PostMapping("/v2/{envLevel}/{region}/{az}")
    @Operation(summary = "validate a cluster environment for a business application with env annotations support")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun validateClusterEnvironmentV2(@PathVariable("envLevel") envLevel: String,
                                   @PathVariable("region") region: String,
                                   @PathVariable("az") az: String,
                                   @RequestBody envMeta: MutableMap<String, String>): AquamanResult<ClusterEnvironmentDTO> {
        log.info("${TraceUtils.getTraceId()},validate cluster environment")
        if (!envMeta.containsKey("externalType") || !envMeta.containsKey("externalId")) {
            return AquamanResult.fail("missing params externalType and externalId in request body")
        }

        val externalId = envMeta["externalId"]!!
        val externalType = envMeta["externalType"]!!
        envMeta["envLevel"] = envLevel
        envMeta.remove("externalId")
        envMeta.remove("externalType")
        if (envMeta.isEmpty()) return AquamanResult.fail("request body should contains params more than externalId and externalType")

        return try {
            defaultClusterEnvironmentService.validateClusterEnvironment(externalId, externalType, envLevel, region, az, envMeta).let {
                // to keep return value consistent
                var clusterId: String? = null
                if (it.isNotEmpty() && it.containsKey(MANAGED_CLUSTER_KEY)) {
                    clusterId = defaultClusterService.queryManagedClusterByManagedClusterKey(it[MANAGED_CLUSTER_KEY]!!)?.clusterId
                    it.remove(MANAGED_CLUSTER_KEY)
                }
                val data = ClusterEnvironmentDTO(clusterId = clusterId, envTags = it)
                AquamanResult.ok(data)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @PostMapping("/{region}/{az}")
    @Operation(summary = "validate a cluster environment for a business application based on environment selectors")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun fetchClusterEnvironmentBySelectors(@PathVariable("region") region: String,
                                              @PathVariable("az") az: String,
                                              @RequestBody envMeta: MutableMap<String, String>): AquamanResult<ClusterProfile> {
        log.info("${TraceUtils.getTraceId()},validate cluster environments based on selectors")
        // todo: change this part: hard coded as real binding data will be inserted
        val externalId = GLOBAL_EXTERNAL_ID // global-scope
        val externalType = AONE_PRODUCTLINE

        try {
            defaultClusterEnvironmentService.validateClusterEnvironment(externalId, externalType, null, region, az, envMeta).let { clusterEnvironment ->
                val managedClusterKey = clusterEnvironment[MANAGED_CLUSTER_KEY]
                managedClusterKey?.let {
                    defaultClusterService.queryManagedClusterByManagedClusterKey(it)?.run {
                        log.info("${TraceUtils.getTraceId()}, find the cluster profile $this")
                        if (clusterEnvironment.containsKey(SITE)) {
                            log.info("${TraceUtils.getTraceId()}, az $az has a mapping to site $clusterEnvironment[SITE]")
                            this.clusterMetaData.clusterAnnotations[SITE] = clusterEnvironment[SITE]!!
                        }
                        return AquamanResult.ok(this)
                    }
                }
            }
            log.info("${TraceUtils.getTraceId()},cannot find cluster environments based on selectors: $region, $az, $envMeta")
            return AquamanResult.fail("cannot find the cluster environment")
        } catch (e: Exception) {
            return AquamanResult.fail(e.message)
        }
    }

    @PostMapping("/configuration/{region}/{az}")
    @Operation(summary = "query a cluster environment's configuration for a business application based on environment selectors")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClusterEnvironmentConfigBySelectors(@PathVariable("region") region: String,
                                                 @PathVariable("az") az: String,
                                                 @RequestBody envMeta: MutableMap<String, String>): AquamanResult<MutableList<ClusterEnvironmentDetails?>> {
        return try {
            val envLevel = envMeta["envLevel"]
            defaultClusterEnvironmentService.validateClusterEnvironment(envLevel, region, az, envMeta).let { listOfClusterProfile ->
                if (listOfClusterProfile.isEmpty()) {
                    AquamanResult.fail("cannot find the specified cluster")
                } else {
                    val result = mutableListOf<ClusterEnvironmentDetails?>()
                    listOfClusterProfile.forEach { clusterProfile ->
                        val clusterEnvKey = clusterProfile[CLUSTER_ENVIRONMENT_KEY]!!
                        defaultClusterEnvironmentService.getEnvironmentDetails(clusterEnvKey).let {
                            result.add(it)
                        }
                    }
                    AquamanResult.ok(result)
                }
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @PutMapping("/configuration/{clusterEnvironmentKey}")
    @Operation(summary = "replace a cluster environment's configuration(with key/value pairs) based on a cluster environment key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT')")
    fun modifyClusterEnvironmentConfigurationByClusterEnvironmentKey(@PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String,
                                                                     @RequestBody annotation: MutableMap<String, Any>): AquamanResult<MutableMap<String, Any>> {
        return try {
            defaultClusterEnvironmentService.modifyClusterEnvironmentConfiguration(clusterEnvironmentKey, annotation).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @PutMapping("/configuration/{region}/{az}")
    @Operation(summary = "replace a cluster environment's configuration(with key/value pairs) based on a cluster environment selectors")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT')")
    fun modifyClusterEnvironmentConfigurationByEnvtags(@PathVariable("region") region: String,
                                                       @PathVariable("az") az: String,
                                                       @RequestBody context: ConfigurationContext): AquamanResult<MutableList<MutableMap<String, Any>>>{
        val envLevel = context.envSelectors["envLevel"]
        val data = mutableListOf<MutableMap<String, Any>>()
        return try {
            log.info("validate cluster environment for envLevel: $envLevel, region: $region, az: $az")
            defaultClusterEnvironmentService.validateClusterEnvironment(envLevel, region, az, context.envSelectors).let { configList ->
                configList.forEach { configMap ->
                    defaultClusterEnvironmentService.modifyClusterEnvironmentConfiguration(configMap[CLUSTER_ENVIRONMENT_KEY]!!, context.annotation).let {
                        data.add(it)
                    }
                }
                log.info("find config list for region $region, az: $az, context: $context: $configList")
            }
            AquamanResult.ok(data)
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("/{clusterEnvironmentKey}")
    @Operation(summary = "query a cluster environment based on a cluster environment key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT')")
    fun queryClusterEnvironment(@PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String): AquamanResult<ClusterEnvironment?> {
        return try {
            defaultClusterEnvironmentService.getEnvironment(clusterEnvironmentKey).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("")
    @Operation(summary = "query all cluster environments")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun fetchClusterEnvironments(): AquamanResult<MutableList<ClusterEnvironmentDetails>> {
        val userName = AuthTool.getAuth().userName
        if(!(userName.equals(ADMIN, true) || userName.equals(CSE_API_SERVER, true)))  {
            return AquamanResult.fail("has no rights to call this api")
        }
        val regionAzSiteMapping = mutableMapOf<String, String>()
        availableZoneSiteMappingRepo.listAvailableZoneSiteMapping().forEach {
            regionAzSiteMapping["${it.region}#${it.availableZone}"] = it.site
        }

        return try {
            defaultClusterEnvironmentService.listClusterEnvironments().let { clusterEnvList ->
                clusterEnvList.forEach {
                    regionAzSiteMapping.get("${it.region}#${it.az}")?.apply {
                        it.annotation?.set("site", this)
                    }
                }
                AquamanResult.ok(clusterEnvList)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("/external-owner")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getClusterEnvironmentByExternalOwner(@RequestParam("externalType") externalType: String, @RequestParam("externalId") externalId: String?, @RequestParam("envLevel") envLevel: String? = null): AquamanResult<List<ClusterEnvironment?>?> {
        // externalType should belong to the user
        // externalId: format example - buid#124_343_3435, buid is the id to organize productline in aone; we follow the same rule, though it is not satisfying
        return try {
            AuthTool.getAuth().userName.apply {
                if (this != ADMIN && !externalType.contains(Regex(this))) throw UserHasNoRightException()
            }
            // build transformedIds
            val transformedIds = defaultClusterEnvironmentService.buildProductlineTransformedIds(externalType, externalId)

            val result = arrayListOf<ClusterEnvironment?>()
            transformedIds.forEach {
                defaultClusterEnvironmentService.getEnvironmentByExternalOwner(externalType, it, envLevel)?.apply {
                    // 暂时在旧接口入口层兼容排除Serverless集群环境
                    this.forEach { clusterEnvironment ->
                        if (clusterEnvironment?.envTags?.containsKey(DefaultClusterEnvironmentService.ENV_LABEL_SERVERLESS_RUNTIME_KEY) == false) {
                            result.add(clusterEnvironment)
                        }
                    }
                }
            }

            log.info("region properties config ${regionPropertiesConfig.regionProperties}")
            for (item in result) {
                item?.apply {
                    if (!this.envTags.containsKey("regionName")) {
                        val regionName = regionPropertiesConfig.regionProperties.regions[this.region] ?: ""
                        this.envTags["regionName"] = regionName
                    }
                    if (!this.envTags.containsKey("azName")) {
                        val azName = regionPropertiesConfig.regionProperties.az[this.az] ?: "可用区${this.az}"
                        this.envTags["azName"] = azName
                    }
                }
            }

            log.info("${TraceUtils.getTraceId()}, cluster environment list $result")
            AquamanResult.ok(result)
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @DeleteMapping("/{clusterEnvironmentKey}")
    @Operation(summary = "delete a cluster environment by cluster environment key")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteClusterEnvironment(@PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String): AquamanResult<Boolean> {
        return try {
            defaultClusterEnvironmentService.deleteEnvironment(clusterEnvironmentKey).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            log.warn("${TraceUtils.getTraceId()}, cannot delete env due to ${e.message}")
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("/details/{clusterEnvironmentKey}")
    @Operation(summary = "query details of a cluster environment based on an cluster environment key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClusterEnvironmentDetails(@PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String): AquamanResult<ClusterEnvironmentDetails?> {
        return try {
            defaultClusterEnvironmentService.getEnvironmentDetails(clusterEnvironmentKey).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            log.warn("${TraceUtils.getTraceId()}, cannot query env due to ${e.message}")
            AquamanResult.fail(e.message)
        }
    }

    /**
     * lists all cluster environments existing in a specified cluster and namespace
     */
    @GetMapping("/managedCluster/cluster-environments")
    @Operation(summary = "list all cluster environments on a specified managed cluster")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    @Deprecated("this api will be offline in near future")
    fun listClusterEnvironmentsByClusterIdAndNamespace(
            @RequestParam("clusterId") clusterId: String,
            @RequestParam("namespace") namespace: String
    ): AquamanResult<MutableList<ClusterEnvironment>> {
        val envList =  defaultClusterEnvironmentService.listEnvironments(clusterId, namespace)
        envList.takeIf { it.isEmpty() }?.let { return AquamanResult.ok(mutableListOf()) }
        return AquamanResult.ok(envList)
    }

    @PostMapping("/default-environments/{clusterId}")
    @Operation(summary = "generate a set of cluster environments for a managed cluster")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun initClusterEnvironmentsFromTemplate(@PathVariable("clusterId") clusterId: String,
                                            @RequestBody clusterEnvironmentsTemplate: ClusterEnvironmentTemplateSpec): AquamanResult<List<ClusterEnvironment?>> {
        throw FunctionDeprecatedException()
    }

    companion object {
        const val ALIBABA_GROUP = "alibaba" // alibaba group
        const val ADMIN = "admin"
        const val KOASTLINE = "koastline"
        const val CSE_API_SERVER = "cse-api-server"
    }
}