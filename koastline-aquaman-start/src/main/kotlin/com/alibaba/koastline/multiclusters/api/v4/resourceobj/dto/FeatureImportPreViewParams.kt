package com.alibaba.koastline.multiclusters.api.v4.resourceobj.dto

import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import io.swagger.annotations.ApiModelProperty

data class FeatureImportPreViewParams(
    @ApiModelProperty("模版")
    val template: String,
    @ApiModelProperty("用户参数")
    val userParamMapStr: String?,
    @ApiModelProperty("用户参数格式")
    val userParamMapFormatEnum: ResourceObjectFormatEnum,
    @ApiModelProperty("系统参数")
    val systemParams: Map<String, Any>
)
