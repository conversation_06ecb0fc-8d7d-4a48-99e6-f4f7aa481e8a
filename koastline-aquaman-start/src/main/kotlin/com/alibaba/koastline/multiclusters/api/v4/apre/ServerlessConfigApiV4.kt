package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.StackServerlessBaseAppBindingService
import com.alibaba.koastline.multiclusters.apre.model.ServerlessBaseAppDataDO
import com.alibaba.koastline.multiclusters.apre.model.ServerlessBaseAppOfStackExtraParams
import com.alibaba.koastline.multiclusters.apre.model.StackServerlessBaseAppBindingDataDO
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.models.AquamanResult.Companion
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.Instant
import java.util.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4/serverless/config", produces = [MediaType.APPLICATION_JSON_VALUE])
class ServerlessConfigApiV4 {
    @Autowired
    lateinit var serverlessBaseAppBindingService: StackServerlessBaseAppBindingService

    @PostMapping("/createOrUpdateServerlessBaseAppOfStack")
    @Operation(summary = "创建或更新环境对应的基座应用")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateServerlessBaseAppOfStack(@RequestParam("serverlessBaseAppName") serverlessBaseAppName: String, @RequestParam("stackId") stackId: String,@RequestParam("employeeId")  employeeId: String,
                                               @RequestBody extraParams: ServerlessBaseAppOfStackExtraParams?): AquamanResult<Boolean> {
        serverlessBaseAppBindingService.createOrUpdateServerlessBaseAppOfStack(
            serverlessBaseAppName = serverlessBaseAppName,
            envStackId = stackId,
            employeeId = employeeId,
            extraParams = extraParams)
        return AquamanResult.ok(true)
    }

    @GetMapping("/listAllServerlessBaseAppInfo")
    @Operation(summary = "返回基座应用信息列表")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listAllServerlessBaseAppInfo(): AquamanResult<List<ServerlessBaseAppDataDO>> {
        return AquamanResult.ok(listOf(
            ServerlessBaseAppDataDO(
                appName = "pre-runtime-test01",
                title = "淘系基座应用",
                desc = "描述：XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
            )
        ))
    }

    @GetMapping("/delServerlessBaseAppOfStack")
    @Operation(summary = "删除环境对应的基座应用")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun delServerlessBaseAppOfStack(@RequestParam("envStackId") envStackId: String, @RequestParam("employeeId")  employeeId: String): AquamanResult<Boolean> {
        serverlessBaseAppBindingService.delByEnvStackId(envStackId, employeeId)
        return AquamanResult.ok(true)
    }

    @GetMapping("/getServerlessBaseAppNameOfStack")
    @Operation(summary = "获取环境对应的基座应用")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getServerlessBaseAppNameOfStack(@RequestParam("envStackId") envStackId: String): AquamanResult<String?> {
        return AquamanResult.ok(
            serverlessBaseAppBindingService.getServerlessBaseAppName(envStackId)
        )
    }

    @GetMapping("/getStackServerlessBaseAppBindingData")
    @Operation(summary = "获取环境对应的基座应用相关信息")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getStackServerlessBaseAppBindingData(@RequestParam("envStackId") envStackId: String): AquamanResult<StackServerlessBaseAppBindingDataDO?> {
        return AquamanResult.ok(
            serverlessBaseAppBindingService.getStackServerlessBaseAppBindingData(envStackId)
        )
    }
}