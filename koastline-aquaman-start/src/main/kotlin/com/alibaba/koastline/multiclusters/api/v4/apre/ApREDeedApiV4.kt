package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ApREDeclarationExtensionService
import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ClusterBalanceApREDeclaration
import com.alibaba.koastline.multiclusters.apre.FeatBalanceApREDeclaration
import com.alibaba.koastline.multiclusters.apre.SiteBalanceApREDeclaration
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.GroupDeclarationsDto
import com.alibaba.koastline.multiclusters.apre.model.GroupAuthorizedDeclarationsDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREDeclarationPatchCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.GroupAuthorizedDeclarationsReqDto
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.google.common.util.concurrent.ThreadFactoryBuilder
import io.swagger.v3.oas.annotations.Operation
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ApREDeedApiV4 {
    @Autowired
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService
    @Autowired
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService
    @Autowired
    lateinit var apREDeclaraionExtensionService: ApREDeclarationExtensionService
    @Autowired
    lateinit var apREService: ApREService
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService

    @PostMapping("/ApREDeedResourceGroupBinding/save")
    @Operation(summary = "save apre deed resource group binding")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun saveApREDeedResourceGroupBinding(
        @RequestParam appName: String,
        @RequestParam resourceGroup: String,
        @RequestParam(required = false) apREDeedKeys: List<String>?
    ): AquamanResult<Boolean> {
        apREDeedResourceGroupBindingService.save(appName, resourceGroup, apREDeedKeys ?:run { emptyList() })
        return AquamanResult.ok(true)
    }
    @PostMapping("/ApREDeedResourceGroupBinding/createOrUpdateWhileExist")
    @Operation(summary = "create or update while exist apre deed resource group binding")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateApREDeedResourceGroupBindingWhileExist(
        @RequestParam appName: String,
        @RequestParam resourceGroup: String,
        @RequestParam apREDeedKey: String
    ): AquamanResult<Boolean> {
        apREDeedResourceGroupBindingService.createOrUpdateWhileExist(appName, resourceGroup, apREDeedKey)
        return AquamanResult.ok(true)
    }

    @PostMapping("/ApREDeedResourceGroupBinding/createIgnoreWhileExist")
    @Operation(summary = "create apre deed resource group  binding ignore while exist")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREDeedResourceGroupBindingIgnoreWhileExist(
        @RequestParam appName: String,
        @RequestParam resourceGroup: String,
        @RequestParam apREDeedKey: String
    ): AquamanResult<Boolean> {
        apREDeedResourceGroupBindingService.createIgnoreWhileExist(appName, resourceGroup, apREDeedKey)
        return AquamanResult.ok(true)
    }

    @PostMapping("/ApREDeedResourceGroupBinding/delete")
    @Operation(summary = "delete apre deed resource group binding")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteApREDeedResourceGroupBindingIgnoreWhileExist(
        @RequestParam appName: String,
        @RequestParam resourceGroup: String,
        @RequestParam apREDeedKey: String
    ): AquamanResult<Boolean> {
        apREDeedResourceGroupBindingService.delete(appName, resourceGroup, apREDeedKey)
        return AquamanResult.ok(true)
    }

    @GetMapping("/ApREDeedResourceGroupBinding/queryByResourceGroup")
    @Operation(summary = "query apre deed resource group  binding relations by resource group")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryBindingDataByResourceGroup(
        @RequestParam appName: String,
        @RequestParam resourceGroup: String
    ): AquamanResult<List<ApREDeedResourceGroupBindingData>> {
        return AquamanResult.ok(apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup) ?.run {
            listOf(this)
        } ?: kotlin.run {
            emptyList()
        })
    }

    @GetMapping("/ApREDeedResourceGroupBinding/getApREDeedKeyByEnvStackId")
    @Operation(summary = "query apre deed key by env stack id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getApREDeedKeyByEnvStackId(@RequestParam envStackId: String): AquamanResult<String> {
        return AquamanResult.ok(apREDeedResourceGroupBindingService.getApREDeedKeyByEnvStackId(envStackId))
    }

    @GetMapping("/ApREDeedResourceGroupBinding/getApREDeedDetail")
    @Operation(summary = "get apRE deed detail")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getApREDeedDetail(@RequestParam resourceGroup: String): AquamanResult<Declaration?> {
        return AquamanResult.ok(apREDeedResourceGroupBindingService.getApREDeedByResourceGroup(resourceGroup))
    }

    @PostMapping("/ApREDeedResourceGroupBinding/queryGroupDeclarations")
    @Operation(summary = "query declarations which unify strong and weak declarations")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryGroupDeclarations(@RequestBody resourceGroup: List<String>): AquamanResult<List<GroupDeclarationsDto>> {
        val result = resourceGroup.map {
            GroupDeclarationsDto(
                resourceGroup = it,
                deed = apREService.requireApREDeedDOByResourceGroupWithRegion(it)
            )
        }.toList()
        return AquamanResult.ok(result)
    }

    @PostMapping("/ApREDeedResourceGroupBinding/queryGroupAuthorizedDeclarations")
    @Operation(summary = "query group authorized declarations")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryGroupAuthorizedDeclarations(@RequestBody req: GroupAuthorizedDeclarationsReqDto): AquamanResult<List<GroupAuthorizedDeclarationsDto>> {
        return AquamanResult.ok(runBlocking {
            req.resourceGroup.map {
                async(QUERY_GROUP_SCALABLE_DECLARATIONS_EXECUTOR) {
                    if (req.scheduleEnvType == ScheduleEnvType.SERVERLESS_APP) {
                        val baseAppName = scheduleStandardService.getStackServerlessBaseAppBindingData(resourceGroup = it).serverlessBaseAppName
                        val templateByBaseApp = scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(
                            appName = req.appName,
                            resourceGroup = it,
                            serverlessBaseAppName = baseAppName
                        )
                        apREService.requireGroupAuthorizedDeclarations(req.appName, it, req.envLevel, baseAppName, templateByBaseApp)
                    } else {
                        apREService.requireGroupAuthorizedDeclarations(req.appName, it, req.envLevel)
                    }
                }
            }.awaitAll()
        })
    }

    @GetMapping("/ApREDeclarationPatch/feat/listAppScopeByCondition")
    @Operation(summary = "list all app feature balance with properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listFeatBalanceApREDeclarationWithProperties(
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam externalId: String,
        @RequestParam externalType: String,
        @RequestParam includeScopeType: IncludeScopeType,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int
    ): AquamanResult<PageData<FeatBalanceApREDeclaration>> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.listFeatBalanceApREDeclarationWithProperties(
                stage, unit, site,
                externalId, externalType, includeScopeType,
                pageSize, pageNumber
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/cluster/listAppScopeByCondition")
    @Operation(summary = "list all app cluster balance with properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listClusterBalanceApREDeclarationWithProperties(
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam externalId: String,
        @RequestParam externalType: String,
        @RequestParam includeScopeType: IncludeScopeType,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int
    ): AquamanResult<PageData<ClusterBalanceApREDeclaration>> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.listClusterBalanceApREDeclarationWithProperties(
                stage, unit, site,
                externalId, externalType, includeScopeType,
                pageSize, pageNumber
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/site/listAppScopeByCondition")
    @Operation(summary = "list all app site balance with properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listSiteBalanceApREDeclarationWithProperties(
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam externalId: String,
        @RequestParam externalType: String,
        @RequestParam includeScopeType: IncludeScopeType,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int
    ): AquamanResult<PageData<SiteBalanceApREDeclaration>> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.listSiteBalanceApREDeclarationWithProperties(
                stage, unit, site,
                externalId, externalType, includeScopeType,
                pageSize, pageNumber
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/findFeatBalanceWithPropertiesVerify")
    @Operation(summary = "find appFeature balance with apREDeclarationId")
    fun findFeatBalanceApREDeclarationWithPropertiesVerify(
        @RequestParam apREDeclarationId: Long
    ): AquamanResult<FeatBalanceApREDeclaration?> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.findFeatBalanceApREDeclarationWithPropertiesVerify(
                apREDeclarationId = apREDeclarationId
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/findClusterBalanceWithPropertiesVerify")
    @Operation(summary = "find app cluster balance with apREDeclarationId")
    fun findClusterBalanceApREDeclarationWithPropertiesVerify(
        @RequestParam apREDeclarationId: Long
    ): AquamanResult<ClusterBalanceApREDeclaration?> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.findClusterBalanceApREDeclarationWithPropertiesVerify(
                apREDeclarationId = apREDeclarationId
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/findSiteBalanceWithPropertiesVerify")
    @Operation(summary = "find app site balance with apREDeclarationId")
    fun findSiteBalanceApREDeclarationWithPropertiesVerify(
        @RequestParam apREDeclarationId: Long
    ): AquamanResult<SiteBalanceApREDeclaration?> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.findSiteBalanceApREDeclarationWithPropertiesVerify(
                apREDeclarationId = apREDeclarationId
            )
        )
    }

    @GetMapping("/ApREDeclarationPatch/findAvailableFeatLabels")
    @Operation(summary = "find app feature balance list")
    fun findAvailableFeatLabels(
        @RequestParam site: String,
        @RequestParam unit: String,
        @RequestParam stage: String
    ): AquamanResult<List<ClusterLabel>> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.findAvailableFeatLabels(unit = unit, stage = stage, site = site)
        )
    }

    @GetMapping("/ApREDeclarationPatch/findAvailableCluster")
    @Operation(summary = "find app cluster balance list")
    fun findAvailableCluster(
        @RequestParam site: String,
        @RequestParam unit: String,
        @RequestParam stage: String
    ): AquamanResult<List<String>> {
        return AquamanResult.ok(
            apREDeclaraionExtensionService.findAvailableClusters(unit = unit, stage = stage, site = site)
        )
    }

    @PostMapping("/ApREDeclarationPatch/createWithMatchScopeAndAttorney")
    @Operation(summary = "create apre declaration patch data with match scope and attorney")
    fun createApREDeclarationPatchWithMatchScopeAndAttorney(
        @RequestBody apREDeclarationPatchCreateReqDto: ApREDeclarationPatchCreateReqDto
    ): AquamanResult<Boolean> {
        apREDeclaraionExtensionService.createApREDeclarationPatchWithMatchScopeAndAttorney(apREDeclarationPatchCreateReqDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/ApREDeclarationPatch/createWithMatchScope")
    @Operation(summary = "create apre declaration patch data with match scope")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREDeclarationPatchWithMatchScope(@RequestBody apREDeclarationPatchCreateReqDto: ApREDeclarationPatchCreateReqDto): AquamanResult<Boolean> {
        apREDeclarationPatchService.createApREDeclarationPatchWithMatchScope(apREDeclarationPatchCreateReqDto)
        return AquamanResult.ok(true)
    }

    @PutMapping("/ApREDeclarationPatch/updateApREDeclarationPatchById")
    @Operation(summary = "update apre declaration patch by id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREDeclarationPatchById(@RequestParam id: Long,@RequestParam modifier: String, @RequestBody declarationPatch: DeclarationPatch): AquamanResult<Boolean>  {
        apREDeclarationPatchService.updateApREDeclarationPatchById(id, modifier, declarationPatch)
        return AquamanResult.ok(true)
    }

    @GetMapping("/ApREDeclarationPatch/findWithMatchScopeById")
    @Operation(summary = "find apre declaration patch data with match scope")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findApREDeclarationPatchWithMatchScopeById(id:Long):  AquamanResult<ApREDeclarationPatchDataDO?>{
        return AquamanResult.ok(
            apREDeclarationPatchService.findApREDeclarationPatchById(id)
        )
    }

    @GetMapping("/ApREDeclarationPatch/listWithMatchScopeByCondition")
    @Operation(summary = "list apre declaration patch data with match scope")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listApREDeclarationPatchWithMatchScopeByCondition(region: String?, stage: String, unit: String): AquamanResult<List<ApREDeclarationPatchDataDO>> {
        return AquamanResult.ok(
            apREDeclarationPatchService.listApREDeclarationPatchWithMatchScopeByCondition(region, stage, unit)
        )
    }

    @PostMapping("/ApREDeclarationPatch/listByProperties")
    @Operation(summary = "list apREDeclarationData data by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listApREDeclarationDataByProperties(
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) keyWords: String?,
        @RequestParam(required = true) pageSize: Int,
        @RequestParam(required = true) pageNumber: Int,
    ):AquamanResult<PageData<ApREDeclarationPatchDataDO>>{
        return AquamanResult.ok(apREDeclarationPatchService.listApREDeclarationPatchWithMatchScopeByProperties(
            stage = stage, unit = unit, keyWords = keyWords, pageNumber = pageNumber, pageSize = pageSize
        ))
    }

    @DeleteMapping("/ApREDeclarationPatch/delete")
    @Operation(summary = "delete apREDeclarationData data")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteApREDeclarationData(
        @RequestParam apREDeclarationPatchId: Long,
        @RequestParam modifier: String,
    ):AquamanResult<Boolean>{
        apREDeclarationPatchService.deleteApREDeclarationPatchWithMatchScope(
            id = apREDeclarationPatchId,
            modifier = modifier
        )
        return AquamanResult.ok(true)
    }

    companion object {
        val QUERY_GROUP_SCALABLE_DECLARATIONS_EXECUTOR = ThreadPoolExecutor(
            16, 16, 0L, TimeUnit.MILLISECONDS,
            ArrayBlockingQueue(1024),
            ThreadFactoryBuilder().setNameFormat("query-group-scalable-declarations-%d").build(),
            ThreadPoolExecutor.CallerRunsPolicy()
        ).asCoroutineDispatcher()
    }
}