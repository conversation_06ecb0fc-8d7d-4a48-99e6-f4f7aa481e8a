package com.alibaba.koastline.multiclusters.api.v4.datachange

import com.alibaba.koastline.multiclusters.aop.datachange.DataChangeManager
import com.alibaba.koastline.multiclusters.aop.datachange.DateChangeOrderQueryCondition
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.DataChangeOrder
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*
import javax.ws.rs.GET

@RestController
@RequestMapping("/apis/data-change/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class DataChangeApiV4 @Autowired constructor(
    val dataChangeManager: DataChangeManager
) {
    @GetMapping("/order")
    @Operation(summary = "通过id查询变更")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findDataChange(@RequestParam("id") id: Long): AquamanResult<DataChangeOrder> {
        return dataChangeManager.findDataChange(id).let { AquamanResult.ok(it) }
    }


    @GetMapping("/order/operator")
    @Operation(summary = "按照变更人查询")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listByOperator(
        @RequestParam("operator") operator: String,
        @RequestParam("limit") limit: Int
    ): AquamanResult<List<DataChangeOrder>> {
        return dataChangeManager.listByOperator(operator = operator, limit = limit).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/order/listByProperties")
    @Operation(summary = "按照条件对fed集群进行搜索")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listByProperties(@RequestBody dateChangeOrderQueryCondition: DateChangeOrderQueryCondition): AquamanResult<PageData<DataChangeOrder>> {
        return dataChangeManager.listByProperties(dateChangeOrderQueryCondition).let {
            AquamanResult.ok(it)
        }
    }
}