package com.alibaba.koastline.multiclusters.api.inner

import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/admin/redis", produces = [MediaType.APPLICATION_JSON_VALUE])
class RedisServiceApi {
    val log by logger()

    @Autowired
    lateinit var redisService: RedisService

    @GetMapping("/{key}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun getValueByKey(@PathVariable("key") key: String): String? {
        return redisService.getValue(key)
    }
    @PutMapping("/set/{key}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun setValue(@PathVariable("key") key: String, @RequestBody value: String): Map<String, String>{
        redisService.setValue(key, value)
        return mutableMapOf(key to redisService.getValue(key)!!)
    }

    @DeleteMapping("/{key}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteValueByKey(@PathVariable("key") key: String): Boolean {
        redisService.deleteValue(key)
        return true
    }
}