package com.alibaba.koastline.multiclusters.api.v4.resourceobj

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/resource/object/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ResourceObjectApiV4 {
    @Autowired
    lateinit var resourceObjectService: ResourceObjectService

    @PostMapping("/getAssembledResourceObjectToScaleOut")
    @Operation(summary = "扩容获取组装的资源对象")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAssembledResourceObjectToScaleOut(@RequestBody assembledResourceObjectRequest: ScaleOutAssembledResourceObjectRequest): AquamanResult<AssembledResourceObjectResult> {
        return AquamanResult.ok(resourceObjectService.getAssembledResourceObjectToScaleOut(assembledResourceObjectRequest))
    }

    @PostMapping("/getAssembledResourceObjectToDeploy")
    @Operation(summary = "发布获取组装的资源对象")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAssembledResourceObjectToDeploy(@RequestBody assembledResourceObjectRequest: DeployAssembledResourceObjectRequest): AquamanResult<AssembledResourceObjectResult> {
        return AquamanResult.ok(resourceObjectService.getAssembledResourceObjectToDeploy(assembledResourceObjectRequest))
    }

    @PostMapping("/getAssembledVersionToDeploy")
    @Operation(summary = "发布获取组装的版本")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAssembledVersionToDeploy(@RequestBody assembledVersionRequest: DeployAssembledVersionRequest): AquamanResult<AssembledVersionResult> {
        return AquamanResult.ok(resourceObjectService.getAssembledVersionToDeploy(assembledVersionRequest))
    }

    @PostMapping("/getAssembledCRListToScaleOut")
    @Operation(summary = "扩容获取组装的CR列表")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAssembledCRListToScaleOut(@RequestBody assembledCRListRequest: ScaleOutAssembledCRListRequest): AquamanResult<AssembledCRListResult> {
        return AquamanResult.ok(
            resourceObjectService.getAssembledCRListToScaleOut(assembledCRListRequest)
        )
    }

    @PostMapping("/getAssembledCRListToDeploy")
    @Operation(summary = "发布获取组装的CR列表")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAssembledCRListToDeploy(@RequestBody assembledCRListRequest: DeployAssembledCRListRequest): AquamanResult<AssembledCRListResult> {
        return AquamanResult.ok(
            resourceObjectService.getAssembledCRListToDeploy(assembledCRListRequest)
        )
    }

    @PostMapping("/getResourceRequirementSpec")
    @Operation(summary = "获取资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getResourceRequirementSpec(@RequestBody resourceRequirementRequest: ResourceRequirementRequest): AquamanResult<ResourceRequirementResp> {
        return AquamanResult.ok(
            resourceObjectService.getResourceRequirementSpecWithExtra(resourceRequirementRequest)
        )
    }

    @PostMapping("/getResourceRequirementSpecBatch")
    @Operation(summary = "批量获取资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getResourceRequirementSpecBatch(@RequestBody resourceRequirementRequestList: List<ResourceRequirementRequest>): AquamanResult<List<ResourceRequirementResp>> {
        return AquamanResult.ok(
            resourceObjectService.getResourceRequirementSpecBatchWithExtra(resourceRequirementRequestList)
        )
    }

    @GetMapping("/getResourceBaselineSpec")
    @Operation(summary = "获取资源基线规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getResourceBaselineSpec(
        @ApiParam("应用名") @RequestParam appName: String,
        @ApiParam("分组名") @RequestParam(required = false) resourceGroup: String?
    ): AquamanResult<ResourceSpec> {
        return AquamanResult.ok(
            resourceObjectService.getResourceBaselineSpec(
                ResourceRequirementRequest(
                    appName = appName,
                    resourceGroup = resourceGroup
                )
            ).first
        )
    }

    @PostMapping("/batchQueryResourceGroupBaselineSpec")
    @Operation(summary = "批量获取分组基线规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchQueryResourceGroupBaselineSpec(
        @ApiParam("应用名") @RequestParam appName: String,
        @ApiParam("分组名列表") @RequestBody resourceGroupList: List<String>
    ): AquamanResult<Map<String,ResourceSpec>> {
        return AquamanResult.ok(
            resourceObjectService.batchQueryResourceGroupBaselineSpec(
                appName = appName,
                resourceGroupList = resourceGroupList
            )
        )
    }

    @PostMapping("/getFeatureImport")
    @Operation(summary = "获取用户特性输入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureImport(@RequestBody featureImport: FeatureImportRequest): AquamanResult<ResourceObjectGetFeatureImportDO?> {
        return AquamanResult.ok(
            resourceObjectService.getFeatureImportByResourceGroup(featureImport)
        )
    }

    @PostMapping("/getFeatureImportBatch")
    @Operation(summary = "批量获取用户特性输入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureImportBatch(@RequestBody featureImportResourceGroupBatchRequest: FeatureImportResourceGroupBatchRequest): AquamanResult<List<ResourceObjectGetFeatureImportDO>> {
        return AquamanResult.ok(
            resourceObjectService.getFeatureImportByResourceGroupBatch(featureImportResourceGroupBatchRequest)
        )
    }

    @GetMapping("/checkASI")
    @Operation(summary = "是否已经完成ASI")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    @Deprecated("待上层扩容功能切换到checkBaseline后废弃")
    fun checkASI(
        @ApiParam("应用名") @RequestParam appName: String,
        @ApiParam("环境StackId") @RequestParam envStackId: String
    ): AquamanResult<CheckASIResultResp> {
        return AquamanResult.ok(
            resourceObjectService.checkBaseline(envStackId, ResourceObjectProtocolEnum.StatefulSet).run {
                CheckASIResultResp(this.check, this.errorMsg)
            }
        )
    }

    @GetMapping("/checkBaseline")
    @Operation(summary = "验证基线是否合法")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun checkBaseline(@ApiParam("应用名")@RequestParam appName: String, @ApiParam("环境StackId")@RequestParam envStackId: String, @ApiParam("协议")@RequestParam protocol: ResourceObjectProtocolEnum): AquamanResult<CheckBaselineResultResp> {
        return AquamanResult.ok(
            resourceObjectService.checkBaseline(envStackId, protocol)
        )
    }
}