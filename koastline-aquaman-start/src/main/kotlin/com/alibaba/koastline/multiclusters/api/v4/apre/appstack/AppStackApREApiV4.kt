package com.alibaba.koastline.multiclusters.api.v4.apre.appstack

import com.alibaba.koastline.multiclusters.apre.appstack.AppStackApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ModifyFeatureSpecStatusDO
import com.alibaba.koastline.multiclusters.apre.model.RegistryFeatureDO
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * TODO 兼容AppStack功能，待融合完删除
 */
@RestController
@RequestMapping("/apis/apre/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class AppStackApREApiV4 {

    val log by logger()

    @Autowired
    lateinit var appStackApREService: AppStackApREService

    @GetMapping("/apis/apre/v4/clusters/match-rule-with-deed-key")
    @Operation(summary = "query clusters by apre deed key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClustersByApREDeedKey(@RequestParam("key") apREDeedKey: String): AquamanResult<ApREDeedResult> {
        return AquamanResult.ok(appStackApREService.queryClustersByApREDeedKey(apREDeedKey))
    }

    /**
     * 勿修改
     * appstack runtime spec registry
     * runtime 标签类型均使用serverless
     */
    @PostMapping("/feature/spec/registry")
    @Operation(summary = "registry feature spec")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registryFeatureSpec(@RequestBody registryFeatureDO: RegistryFeatureDO): AquamanResult<Boolean> {
        appStackApREService.registryFeatureSpec(registryFeatureDO)
        return AquamanResult.ok(true)
    }

    /**
     * 勿修改
     * appstack runtime change spec status
     */
    @PostMapping("/feature/spec/modify/status")
    @Operation(summary = "modify feature spec status")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun modifyFeatureSpecStatus(@RequestBody modifyFeatureSpecStatusDO: ModifyFeatureSpecStatusDO): AquamanResult<Boolean> {
        appStackApREService.modifyApREFeatureSpecStatus(modifyFeatureSpecStatusDO)
        return AquamanResult.ok(true)
    }
}