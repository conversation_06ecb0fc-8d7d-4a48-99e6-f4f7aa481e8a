package com.alibaba.koastline.multiclusters.api.inner

import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.kms.AliyunKmsClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/admin/kms", produces = [MediaType.APPLICATION_JSON_VALUE])
class KmsServiceApi {

    @Autowired
    @Qualifier("aliyunKmsClient")
    lateinit var kmsClient: AliyunKmsClient

    val log by logger()

    @GetMapping("/encrypt")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun encrypt(@RequestParam("plainText") plainText: String): String {
        return kmsClient.encrypt(plainText)
    }

    @GetMapping("/decrypt")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun decrypt(encryptText: String): String {
        return kmsClient.decrypt(encryptText)
    }
}