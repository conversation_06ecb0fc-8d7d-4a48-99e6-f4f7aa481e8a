package com.alibaba.koastline.multiclusters.api.v4.schedule

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum.DEPLOY
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum.NON_DECLARATIVE
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/apis/schedule/dry/run/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ScheduleDryRunApiV4 {
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService

    @GetMapping("/doDeployScheduleDryRun")
    @Operation(summary = "发布调度DryRun")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun doDeployScheduleDryRun(@ApiParam("应用名")@RequestParam appName: String,
                               @ApiParam("环境StackId")@RequestParam(required = false) envStackId: String? = null,
                               @ApiParam("分组名")@RequestParam(required = false) resourceGroup: String? = null,
                               @ApiParam("调度环境类型")@RequestParam scheduleEnvType : ScheduleEnvType,
                               @ApiParam("基座应用名")@RequestParam serverlessBaseAppName: String? = null
    ): AquamanResult<ScheduleResult> {
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(appName, envStackId, resourceGroup, emptyList()),
            scheduleType = ScheduleType(NON_DECLARATIVE, DEPLOY),
            scheduleRequestParam = ScheduleRequestParam(
                scheduleEnvType = scheduleEnvType,
                serverlessBaseAppName = serverlessBaseAppName
            ),
            includeClusterInfoToResult = false
        )
        return AquamanResult.ok(
            scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType).doSchedule(scheduleRequestContent)
        )
    }
}