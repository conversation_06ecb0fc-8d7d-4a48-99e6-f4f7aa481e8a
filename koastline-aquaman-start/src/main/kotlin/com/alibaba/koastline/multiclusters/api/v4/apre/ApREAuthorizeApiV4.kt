package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ApREResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREResourceGroupBindingDataDO
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*


/**
 * <AUTHOR>
 * 授权系列全部合并到Class<AttorneyApiV4>旗下
 */
@RestController
@RequestMapping("/apis/apre/v4/apREBinding", produces = [MediaType.APPLICATION_JSON_VALUE])
class ApREAuthorizeApiV4 {
    @Autowired
    lateinit var apREResourceGroupBindingService: ApREResourceGroupBindingService

    @PostMapping("/createApREResourceGroupBindingData")
    @Operation(summary = "创建ApRE分组指定声明")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREResourceGroupBindingData(@RequestBody apREResourceGroupBindingDataDO: ApREResourceGroupBindingDataDO): AquamanResult<Long> {
        return AquamanResult.ok(
            apREResourceGroupBindingService.createApREResourceGroupBindingData(apREResourceGroupBindingDataDO)
        )
    }

    @PostMapping("/updateApREResourceGroupBindingData")
    @Operation(summary = "更新ApRE分组指定声明")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREResourceGroupBindingData(@RequestParam("id") id: Long, @RequestBody selector: ApREBindingTerm): AquamanResult<Boolean> {
        apREResourceGroupBindingService.updateApREResourceGroupBindingData(id, selector)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/delApREResourceGroupBindingDataById")
    @Operation(summary = "删除ApRE分组指定声明")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun delApREResourceGroupBindingDataById(@RequestParam("id") id: Long): AquamanResult<Boolean> {
        apREResourceGroupBindingService.delApREResourceGroupBindingDataById(id = id)
        return AquamanResult.ok(true)
    }
}
