package com.alibaba.koastline.multiclusters.inceptor

import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.taobao.eagleeye.EagleEye
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


@Component
class TraceIdInterceptor : HandlerInterceptorAdapter() {

   companion object {
       val EAGLEEYE_TRACE_ID = "EAGLEEYE_TRACE_ID"
   }

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        EagleEye.getTraceId().also { traceId ->
            val finalTraceId = traceId ?: TraceUtils.generateNewTraceId()
            TraceUtils.setTraceId(finalTraceId)
            MDC.put(EAGLEEYE_TRACE_ID, finalTraceId)
        }
        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        TraceUtils.unsetTraceId()
    }
}