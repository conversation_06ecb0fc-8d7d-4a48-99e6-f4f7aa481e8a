package com.alibaba.koastline.multiclusters.api.inner

import com.alibaba.koastline.multiclusters.common.exceptions.UserHasNoRightException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.AuthTool
import com.alibaba.koastline.multiclusters.data.dao.env.*
import com.alibaba.koastline.multiclusters.data.vo.env.*
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.Instant
import java.util.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/admin/virtual-environments", produces = [MediaType.APPLICATION_JSON_VALUE])
class ClusterEnvironemntInnerApi {
    val log by logger()
    @Autowired
    lateinit var koastlineGatewayRepo: KoastlineGatewayRepo
    @Autowired
    lateinit var koastlineClusterProfileRepo: KoastlineClusterProfileRepo
    @Autowired
    lateinit var clusterEnvironmentRepo: ClusterEnvironmentRepo
    @Autowired
    lateinit var clusterBindingRepo: ClusterBindingRepo
    @Autowired
    lateinit var systemComponentsRepo: SystemComponentsRepo
    @Autowired
    lateinit var availableZoneSiteMappingRepo: AvailableZoneSiteMappingRepo


    @PostMapping("/koastline-gateway/create")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun createKoastlineGateway(@RequestBody koastlineGateway: KoastlineGatewayData): Int? {
        return koastlineGatewayRepo.insertGateway(koastlineGateway)
    }

    @GetMapping("/koastline-gateway/query/{id}")
    @PreAuthorize("hasAnyAuthority('DEVOPS_SYSTEM_RIGHT','ADMIN_ROOT_RIGHT')")
    fun findKoastlineGatewayById(@PathVariable("id") id : String): KoastlineGatewayData? {
        val koastlineGateway: KoastlineGatewayData? = koastlineGatewayRepo.findGatewayByGatewayId(id.toLong())
        log.info("query koastline gateway $koastlineGateway")
        val userInfo = AuthTool.getAuth()
        log.info("get user info ${userInfo.userName}")
        return koastlineGateway
    }

    @PostMapping("/koastline-gateway/gateways")
    @PreAuthorize("hasAnyAuthority('DEVOPS_SYSTEM_RIGHT','ADMIN_ROOT_RIGHT')")
    fun findKoastlineGatewayById(@RequestBody idList : List<Long>): List<KoastlineGatewayData?>? {
        val koastlineGateways = koastlineGatewayRepo.findGatewaysByGatewayIds(idList)
        log.info("query koastline gateway $koastlineGateways")
        return koastlineGateways
    }

    @DeleteMapping("/koastline-gateway/delete/{id}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteKoastlineGateway(@PathVariable("id") id: String): Int{
        return koastlineGatewayRepo.deleteGatewayByGatewayId(id)
    }

    @PostMapping("/koastlineClusterProfile/create")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun createClusterProfile(@RequestBody clusterProfileData: ClusterProfileData): Int? {
        log.info("receive cluster profile create request $clusterProfileData")
        return koastlineClusterProfileRepo.insertClusterProfile(clusterProfileData)
    }

    @GetMapping("/koastlineClusterProfile/queryById/{id}")
    @PreAuthorize("hasAnyAuthority('DEVOPS_SYSTEM_RIGHT','ADMIN_ROOT_RIGHT')")
    fun queryClusterProfileById(@PathVariable("id") id: String): ClusterProfileData? {
        return koastlineClusterProfileRepo.findClusterProfileById(id.toLong())
    }

    @GetMapping("/koastlineClusterProfile/queryByClusterId/{id}")
    @PreAuthorize("hasAnyAuthority('DEVOPS_SYSTEM_RIGHT','ADMIN_ROOT_RIGHT')")
    fun findKoastlineClusterProfile(@PathVariable("id") id: String): ClusterProfileData? {
        return koastlineClusterProfileRepo.findClusterProfileByClusterId(id)
    }

    @DeleteMapping("/koastlineClusterProfile/deleteByClusterId/{clusterid}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteClusterProfileByClusterId(@PathVariable("clusterid") id: String): Int? {
        return koastlineClusterProfileRepo.deleteClusterProfileByClusterId(id)
    }

    @DeleteMapping("/koastlineClusterProfile/deleteById/{id}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteClusterProfileById(@PathVariable("id") id: String): Int? {
        return koastlineClusterProfileRepo.deleteClusterProfileById(id)
    }

    @DeleteMapping("/koastline-system-components/{id}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteSystemComponentsData(@PathVariable("id") id: Long): Int? {
        return systemComponentsRepo.deleteSystemComponentsById(id)
    }

    @PostMapping("/clusterEnvironment/create")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun createClusterEnvironment(@RequestBody clusterEnvironmentData: ClusterEnvironmentData): Int? {
        return clusterEnvironmentRepo.insertClusterEnvironment(clusterEnvironmentData)
    }

    @PostMapping("/clusterEnvironemntBindingInfo/create")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun createClusterEnvironmentBindingData(@RequestParam("clusterEnvKey") clusterEnvKey: String,
                                            @RequestParam("externalId") externalId: String,
                                            @RequestParam("externalType") externalType: String,
                                            @RequestParam("selector", required = false) selector: String?) : Int? {
        val now = Date(Instant.now().toEpochMilli())

        return ClusterEnvironmentBindingData(
                null,
                clusterEnvKey,
                externalId,
                externalType,
                now,
                now,
                selector
        ).run {
            clusterBindingRepo.createClusterBindingData(this)
        }
    }

    @GetMapping("/clusterEnvironemntBindingInfo")
    @PreAuthorize("hasAnyAuthority('DEVOPS_SYSTEM_RIGHT','ADMIN_ROOT_RIGHT')")
    fun queryClusterEnvironemntBindingData(externalId: String, externalType: String): List<ClusterEnvironmentBindingData?>? {
        val externalIdPattern = "%$externalId%"
        log.info("externalId template $externalIdPattern")
        // externalType should belong to the user
        AuthTool.getAuth().userName.apply {
            if (this != "admin" && !externalType.contains(Regex(this))) throw UserHasNoRightException()
        }

        return clusterBindingRepo.listClusterBindingDataByExternalId(externalType, externalIdPattern)
    }

    @PutMapping("/clusterEnvironemntBindingInfo/{clusterEnvironmentKey}/{externalType}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun replaceClusterEnvironmentBindingData(@PathVariable("clusterEnvironmentKey") clusterEnvironmentKey: String,
                                             @PathVariable("externalType") externalType: String,
                                             @RequestParam("externalId") externalId: String,
                                             @RequestParam("externalIdBase") externalIdBase: String): Boolean {
        clusterBindingRepo.listClusterBindingData(clusterEnvironmentKey).run {
            if (this.isNotEmpty()) {
                return clusterBindingRepo.updateClusterBindingDataByClusterEnvironmentKey(clusterEnvironmentKey, externalType, externalId, externalIdBase) !=0
            }
        }
        return false
    }

    @DeleteMapping("/clusterEnvironment/delete/{clusterEnvKey}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteClusterEnvironment(@PathVariable("clusterEnvKey") clusterEnvKey: String): Int? {
        return clusterEnvironmentRepo.deleteClusterEnvironmentClusterEnvKey(clusterEnvKey).also {
            clusterBindingRepo.deleteClusterBindingDataByClusterEnvKey(clusterEnvKey)
        }
    }

    @PostMapping("/clusterEnvironment/{region}/{az}/{site}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun addAzSiteMapping(@PathVariable("az") az: String,
                         @PathVariable("site") site: String,
                         @PathVariable("region") region: String): Boolean {
        val now = Date(Instant.now().toEpochMilli())
        val mapping = AvailableZoneSiteMappingData(
                null,
                az,
                site,
                region,
                now,
                now
        )
        return availableZoneSiteMappingRepo.insertAvailableZoneSiteMapping(mapping) != 0
    }

    @GetMapping("/clusterEnvironment/{region}/{az}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun queryAzSiteMapping(@PathVariable("region") region: String,
                           @PathVariable("az") az: String): AvailableZoneSiteMappingData {
        return availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region)?: throw RuntimeException("no az to site mapping found")
    }

    @PutMapping("/clusterEnvironment/{region}/{az}/{site}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun updateAzSiteMapping(@PathVariable("region") region: String,
                            @PathVariable("az") az: String,
                            @PathVariable("site") site: String): Boolean {
        return availableZoneSiteMappingRepo.updateAvailableZoneSiteMappingByAvailableZoneAndRegion(az, site, region) !=0
    }

    @PutMapping("/clusterEnvironment/{id}/{region}/{az}/{site}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun updateAzSiteMappingById(@PathVariable("id") id: String,
                                @PathVariable("region") region: String,
                                @PathVariable("az") az: String,
                                @PathVariable("site") site: String): Boolean {
        return availableZoneSiteMappingRepo.updateAvailableZoneSiteMappingById(id, az, region, site) != 0
    }

    @DeleteMapping("/clusterEnvironment/{region}/{az}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun removeAzSiteMapping(@PathVariable("region") region: String,
                            @PathVariable("az") az: String): Boolean {
        return availableZoneSiteMappingRepo.deleteAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region) != 0
    }

    @DeleteMapping("/clusterEnvironment/{id}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun removeAzSiteMappingById(@PathVariable("id") id: String): Boolean {
        return availableZoneSiteMappingRepo.deleteAvailableZoneSiteMappingById(id) != 0
    }

    @PutMapping("/clusterEnvironment/moving/{clusterEnvironmentKey}/{managedClusterKey}")
    @Operation(summary = "move a cluster env to a new koastline managedCluster")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun moveClusterEnvToNewManagedCluster(@PathVariable("clusterEnvironmentKey") clusterEnvKey: String,
                                          @PathVariable("managedClusterKey") managedClusterKey: String): Boolean {
        return clusterEnvironmentRepo.updateClusterEnvironmentDependencyByManagedClusterKey(clusterEnvKey, managedClusterKey)!=0
    }
}