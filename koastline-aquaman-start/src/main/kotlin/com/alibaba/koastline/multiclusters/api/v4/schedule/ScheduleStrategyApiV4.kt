package com.alibaba.koastline.multiclusters.api.v4.schedule

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyReqDto
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyResult
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/schedule/strategy/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ScheduleStrategyApiV4 {
    @Autowired
    lateinit var scheduleStrategyService: ScheduleStrategyService

    @PostMapping("/computeScheduleStrategy")
    @Operation(summary = "获取调度策略")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun computeScheduleStrategy(@RequestBody scheduleStrategyReqDto: ScheduleStrategyReqDto): AquamanResult<ScheduleStrategyResult> {
        return AquamanResult.ok(scheduleStrategyService.computeScheduleStrategy(scheduleStrategyReqDto))
    }
}