package com.alibaba.koastline.multiclusters.api.v4.resource

import com.alibaba.env.orchestration.protocol.common.V2ResourceStatus
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceDesc
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceOwnerReference
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resource.ResourceService
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceCreateReq
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceDelReq
import io.swagger.v3.oas.annotations.Operation
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author:    <EMAIL>
 * @date:    2024/5/15 7:48 PM
 */
@RestController
@RequestMapping("/apis/resource/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ResourceApiV4(
    val resourceService: ResourceService,
) {
    @PostMapping("/applyResourceSpec")
    @Operation(summary = "提交资源协议")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun applyResourceSpec(@RequestBody resourceSpec: String): AquamanResult<Boolean> {
        resourceService.applyResourceSpec(resourceSpec)
        return AquamanResult.ok(true)
    }

    @PostMapping("/setBaselineOfResourceBox")
    @Operation(summary = "设置资源版本基线")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun setBaselineOfResourceBox(@RequestBody resourceSpec: String): AquamanResult<Boolean> {
        resourceService.setBaselineOfResourceBox(resourceSpec)
        return AquamanResult.ok(true)
    }

    @PostMapping("/updateResourceStatus")
    @Operation(summary = "更新资源状态")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateResourceStatus(@RequestParam kind: String, @RequestParam name: String,
                             @RequestParam version: Int, @RequestBody resourceStatus: V2ResourceStatus): AquamanResult<Boolean> {
        resourceService.updateResourceStatus(kind, name, version, resourceStatus)
        return AquamanResult.ok(true)
    }

    @GetMapping("/queryCascadedResourceDescList")
    @Operation(summary = "级联查询当前资源&子资源明细")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryCascadedResourceDescList(@RequestParam kind: String,@RequestParam name: String,@RequestParam(required = false) expectSubResourceLevel: Int? = 1): AquamanResult<List<ResourceDesc>> {
        return AquamanResult.ok(resourceService.queryCascadedResourceDescList(
                kind = kind,
                name = name,
                expectSubResourceLevel = expectSubResourceLevel,
            )
        )
    }

    @GetMapping("/queryResourceDesc")
    @Operation(summary = "查询资源明细")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryResourceDesc(@RequestParam kind: String,@RequestParam name: String): AquamanResult<ResourceDesc> {
        return AquamanResult.ok(resourceService.queryResourceDesc(kind = kind, name = name, sub = true))
    }

    @DeleteMapping("/deleteResource")
    @Operation(summary = "删除资源")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteResource(@RequestParam kind: String,@RequestParam name: String): AquamanResult<Boolean> {
        resourceService.deleteResource(kind = kind, name = name)
        return AquamanResult.ok(true)
    }

    @PostMapping("/createResourceOwnerReference")
    @Operation(summary = "创建资源关联关系")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createResourceOwnerReference(@RequestBody resourceOwnerReferenceCreateReq: ResourceOwnerReferenceCreateReq): AquamanResult<Boolean> {
        resourceService.createResourceOwnerReference(resourceOwnerReferenceCreateReq)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/delResourceOwnerReference")
    @Operation(summary = "删除资源关联关系")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun delResourceOwnerReference(@RequestBody resourceOwnerReferenceDelReq: ResourceOwnerReferenceDelReq): AquamanResult<Boolean> {
        resourceService.delResourceOwnerReference(resourceOwnerReferenceDelReq)
        return AquamanResult.ok(true)
    }
}