package com.alibaba.koastline.multiclusters.api.inner

import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.vo.Role
import com.alibaba.koastline.multiclusters.models.AquamanResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/multiclusters/admin", produces = [MediaType.APPLICATION_JSON_VALUE])
class DataSourceApi {
    val log by logger()

    @Autowired
    lateinit var userAuthenticationManager: AuthenticationManager

    @GetMapping("/{username}")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun getUserByName(@PathVariable username: String): Any {
        log.info("get user by username $username")
        val userInfo = userAuthenticationManager.queryUserDetails(username)
        log.info("finished get user's info $userInfo")
        return userInfo.username
    }

    @PostMapping("/username")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun registerUser(@RequestParam("username") userName: String, @RequestParam("employeename") employeeName: String, @RequestParam("employeeid") employeeId: String, @RequestParam("role") role: String,
                     @RequestParam("openApiAcl") openApiAcl: String?): Boolean {
        try {
            Role.valueOf(role).apply {
                userAuthenticationManager.registerNewUser(userName, employeeName, employeeId, this, openApiAcl)
            }
        } catch (e: Exception) {
            log.info("cannot register the user due to ${e.message}")
            return false
        }
        return true
    }

    @PostMapping("/updateOpenApiAcl")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun updateOpenApiAcl(@RequestParam("owner") owner: String, @RequestParam("openApiAcl", required = false) openApiAcl: String?): AquamanResult<Boolean> {
        userAuthenticationManager.updateOpenApiAcl(owner, openApiAcl)
        return AquamanResult.ok(true)
    }

    @PostMapping("/appendOpenApiAcl")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun appendOpenApiAcl(@RequestParam("owner") owner: String, @RequestParam("openApiAcl") openApiAcl: String): AquamanResult<Boolean> {
        userAuthenticationManager.appendOpenApiAcl(owner, openApiAcl)
        return AquamanResult.ok(true)
    }
}