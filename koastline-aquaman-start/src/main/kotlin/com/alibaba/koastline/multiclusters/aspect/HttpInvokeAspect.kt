package com.alibaba.koastline.multiclusters.aspect

import com.alibaba.koastline.multiclusters.aspect.AspectUtils.EAGLEEYE_USER_DATA_KEY_BIZ_SCENE
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.taobao.eagleeye.EagleEye
import java.lang.reflect.UndeclaredThrowableException
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestMapping

import com.alibaba.csp.sentinel.Entry
import com.alibaba.csp.sentinel.SphU
import com.alibaba.csp.sentinel.context.ContextUtil
import com.alibaba.csp.sentinel.slots.block.BlockException


/**
 * <AUTHOR>
 */
@Aspect
@Component
class HttpInvokeAspect(
    val authenticationManager: AuthenticationManager
) {
    val log by logger()

    @Around("execution(* com.alibaba.koastline.multiclusters.api..*.*(..))")
    @Throws(Throwable::class)
    fun around(joinPoint: ProceedingJoinPoint): Any? {
        if (!isValidOpenAPICall(
                uri = getMethodUri(joinPoint)
            )) {
            return AquamanResult.fail<String>("Unauthorized")
        }
        val startTime = System.currentTimeMillis()
        val method = AspectUtils.getMethodName(joinPoint)
        val caller = authenticationManager.getLoginAccount() ?.authentication ?.userName ?:UNKNOWN_CALLER
        EagleEye.getRpcContext() ?.let {
            it.putUserData(EAGLEEYE_USER_DATA_KEY_BIZ_SCENE, method)
        }
        val aspectPrefix = this.javaClass.simpleName
        var re: Any? = null
        var result = false
        var errorMessage: String? = null
        var bizError = false
        try {
            // 限流
            limitApiCallBySentinel2(method = method, caller = caller)
            re = joinPoint.proceed()
            result = true
        } catch (e: BizException) {
            errorMessage = e.message
            bizError = true
            getMessage(e).let {
                re = AquamanResult.fail<String>(message = it, bizError = bizError)
                log.error("$aspectPrefix ERROR,${e.javaClass}:${it}", e)
            }
        } catch (e : BlockException) {
            // 限流异常
            errorMessage = "Blocked by Sentinel"
            bizError = false
            re = AquamanResult.fail<String>(message = errorMessage, bizError = bizError)
            log.error("$aspectPrefix ERROR,${e.javaClass}:${errorMessage}", e)
        } catch (e: Exception) {
            errorMessage = e.message
            bizError = false
            getMessage(e).let {
                re = AquamanResult.fail<String>(message = it, bizError = bizError)
                log.error("$aspectPrefix ERROR,${e.javaClass}:${it},stackTrace:${e.stackTraceToString()}", e)
            }
        } finally {
            AspectUtils.printAspectLog(
                joinPoint = joinPoint,
                result = result,
                re = re,
                errorMessage = errorMessage,
                bizError = bizError,
                startTime = startTime,
                method = method,
                aspectPrefix = aspectPrefix,
                caller = caller
            )
        }
        return re
    }

    private fun isValidOpenAPICall(uri: String): Boolean {
        val authentication = authenticationManager.getLoginAccount() ?.authentication
        val openApiAcl = authentication ?.openApiAcl
        if (openApiAcl.isNullOrBlank()) {
            return false
        }
        return openApiAcl.split(",").firstOrNull {
            matchOpenApiAclItem(uri, it)
        } ?.run { true } ?:run {
            log.error("${this.javaClass.simpleName} ERROR, Unauthorized for ${authentication.userName} - ${uri}]")
            false
        }
    }

    /**
     *  判断是否匹配授权项
     *  匹配规则:前缀匹配
     *  @param uri 调用方法uri
     *  @param openApiAclItem 授权项
     *  @return 是否匹配授权项
     */
    private fun matchOpenApiAclItem(uri: String, openApiAclItem: String): Boolean {
        return uri.startsWith(openApiAclItem)
                || openApiAclItem == "*"
    }

    private fun getMessage(e: Exception): String? {
        return e.message ?: run {
            if (e is UndeclaredThrowableException) {
                e.undeclaredThrowable.message
            } else {
                null
            }
        }
    }

    private fun getMethodUri(joinPoint: ProceedingJoinPoint): String {
        if (joinPoint.signature is MethodSignature) {
            val method = (joinPoint.signature as MethodSignature).method
            val classMappingUri = joinPoint.target.javaClass.getAnnotation(RequestMapping::class.java).value
            val methodMappingUri = method.getAnnotation(GetMapping::class.java) ?.value
                ?: method.getAnnotation(PostMapping::class.java) ?.value
                ?: method.getAnnotation(PutMapping::class.java) ?.value
                ?: method.getAnnotation(DeleteMapping::class.java) ?.value
                ?: method.getAnnotation(RequestMapping::class.java) ?.value
                ?: throw RuntimeException("获取方法调用methodMappingUri失败.")
            return "${classMappingUri[0]}${
                if (methodMappingUri.isNotEmpty()) methodMappingUri[0] else ""
            }"
        }
        throw RuntimeException("获取方法调用URI失败.")
    }

    /**
     * 限流
     * @param method 访问方法锚点，跟监控项对齐
     * @param caller 访问账户
     */
    private fun limitApiCallBySentinel2(method: String, caller: String) {
        var allEntry: Entry? = null
        var urlEntry: Entry? = null
        try {
            // 设置全局账户&本地访问账户
            ContextUtil.enter(SENTINEL_RESOURCE_ALL_USER, caller)
            // 设置全局锚点和本次访问锚点
            allEntry = SphU.entry(SENTINEL_RESOURCE_ALL_URI)
            urlEntry = SphU.entry(method)
        } finally {
            urlEntry?.exit()
            allEntry?.exit()
            ContextUtil.exit()
        }
    }
    companion object {
        private const val SENTINEL_RESOURCE_ALL_USER = "all-user"
        private const val SENTINEL_RESOURCE_ALL_URI = "all-in"
        private const val UNKNOWN_CALLER = "UNKNOWN"
    }
}