package com.alibaba.koastline.multiclusters.api.v4.schedule

import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.runtime.RuntimeCoordinateResourceDeleteReq
import com.alibaba.koastline.multiclusters.runtime.RuntimeCoordinateResourceCreateReq
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeRefEnvStackRespDto
import com.alibaba.koastline.multiclusters.schedule.model.AppOfRuntimeBaseReq
import com.alibaba.koastline.multiclusters.schedule.model.AppOfRuntimeBaseResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RuntimeScheduleService
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/apis/schedule/runtime/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
@Service
class RuntimeScheduleApiV4 {

    @Autowired
    lateinit var runtimeScheduleService: RuntimeScheduleService
    @Autowired
    lateinit var runtimeWorkloadMetaService: RuntimeWorkloadMetaService

    @GetMapping("/runtimeWorkloadMeta")
    @Operation(summary = "按照runtime workload id 查询详细描述关系")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findRuntimeWorkloadMeta(@RequestParam runtimeWorkloadId: String): AquamanResult<RuntimeWorkloadMeta?> {
        runtimeScheduleService.runtimeWorkloadMetaService.findRuntimeWorkloadMeta(runtimeWorkloadId).let {
            return AquamanResult.ok(it)
        }
    }

    @GetMapping("/seekCoordinates")
    @Operation(summary = "找到可以注册的runtime坐标")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun seekAvailableRuntimeCoordinates(
        @ApiParam("应用名") @RequestParam(required = true) appName: String,
        @ApiParam("分组名") @RequestParam(required = true) resourceGroup: String,
        @ApiParam("调度类型") @RequestParam(required = true) scheduleEnvType: ScheduleEnvType,
    ): AquamanResult<ScheduleResult> {
        return AquamanResult.ok(
            runtimeScheduleService.seekAvailableRuntimeCoordinates(
                appName = appName, resourceGroup = resourceGroup, scheduleEnvType = scheduleEnvType
            )
        )
    }

    @PostMapping("/registerRuntime")
    @Operation(summary = "注册资源")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registerWorkloadAndMountResource(
        @RequestBody registerReq: RuntimeCoordinateResourceCreateReq,
    ): AquamanResult<Boolean> {
        runtimeScheduleService.registerWorkloadAndMountResource(
            registerReq = registerReq
        )
        return AquamanResult.ok(true)
    }

    @GetMapping("/requireRunningRuntime")
    @Operation(summary = "获取可以运行时的runtimeWorkload")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireRunningWorkload(
        @ApiParam("应用名") @RequestParam(required = true) appName: String,
        @ApiParam("分组名") @RequestParam(required = false) resourceGroup: String?,
        @ApiParam("环境id") @RequestParam(required = false) envStackId: String?,
        @ApiParam("调度类型") @RequestParam(required = true) scheduleEnvType: ScheduleEnvType,
    ): AquamanResult<ScheduleResult> {
        return AquamanResult.ok(
            runtimeScheduleService.requireRunningWorkload(
                appName = appName,
                resourceGroup = resourceGroup,
                envStackId = envStackId,
                scheduleEnvType = scheduleEnvType
            )
        )
    }

    @PostMapping("/cancelRuntime")
    @Operation(summary = "取消已经注册的workload坐标")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun cancelWorkloadWhileUnmountResource(
        @RequestBody cancelReq: RuntimeCoordinateResourceDeleteReq,
    ): AquamanResult<Boolean> {
        runtimeScheduleService.cancelWorkloadWhileUnmountResource(
            cancelReq.workloadMetadataConstraint, cancelReq.modifier
        )
        return AquamanResult.ok(true)
    }

    @GetMapping("/setGroupHided")
    @Operation(summary = "设置分组可见/不可见状态")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun setGroupHided(
        @ApiParam("分组名") @RequestParam(required = true) resourceGroup: String,
        @ApiParam("状态") @RequestParam(required = true) status: String,
        @ApiParam("修改人") @RequestParam(required = true) modifier: String,
    ): AquamanResult<Boolean> {
        runtimeScheduleService.setGroupHided(
            resourceGroup = resourceGroup, status = status, modifier = modifier
        )
        return AquamanResult.ok(true)
    }

    @GetMapping("/setGroupBuffer")
    @Operation(summary = "设置分组buffer")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun setGroupBuffer(
        @ApiParam("分组名") @RequestParam(required = true) resourceGroup: String,
        @ApiParam("状态") @RequestParam(required = true) bufferConfig: String,
        @ApiParam("修改人") @RequestParam(required = true) modifier: String,
    ): AquamanResult<Boolean> {
        runtimeScheduleService.setGroupBuffer(
            resourceGroup = resourceGroup, bufferConfig = bufferConfig, modifier = modifier
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/batchListAppOfRuntimeBase")
    @Operation(summary = "批量获取基座资源分布")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchListAppOfRuntimeBase(@RequestBody(required = true) appOfRuntimeBaseReqList: List<AppOfRuntimeBaseReq>): AquamanResult<List<AppOfRuntimeBaseResult>> {
        return AquamanResult.ok(
            runtimeScheduleService.batchListAppOfRuntimeBase(appOfRuntimeBaseReqList)
        )
    }

    @PostMapping("/listEnvStackIdByRuntimeWorkloadId")
    @Operation(summary = "获取基座RuntimeId归属环境")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listEnvStackIdByRuntimeWorkloadId(@RequestBody(required = true) runtimeWorkloadIdList: List<String>): AquamanResult<List<RuntimeRefEnvStackRespDto>> {
        return AquamanResult.ok(
            runtimeWorkloadMetaService.listEnvStackIdByRuntimeWorkloadId(runtimeWorkloadIdList)
        )
    }
}