package com.alibaba.koastline.multiclusters.api.v4.metadata

import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import com.alibaba.koastline.multiclusters.apre.base.MetadataService
import com.alibaba.koastline.multiclusters.apre.model.MetadataConstraintDO
import com.alibaba.koastline.multiclusters.apre.model.MetadataOfSiteDO
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.external.model.UnitGroup
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/metadata/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class MetadataApiV4 @Autowired constructor(
    val metadataService: MetadataService,
    val envLevelStageMappingService: EnvLevelStageMappingService
) {

    @PostMapping("/metadataConstraint/create")
    @Operation(summary = "create an metadata constraint")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createMetadataConstraint(
        @RequestParam site: String,
        @RequestParam unit: String,
        @RequestParam stage: String,
        @RequestParam creator: String
    ): AquamanResult<MetadataConstraintDO> {
        return metadataService.createMetadataConstraint(site, unit, stage, creator).let {
            AquamanResult.ok(it)
        }
    }

    @GetMapping("/metadataConstraint/listAll")
    @Operation(summary = "list all metadata constraint")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listAllMetadataConstraint(): AquamanResult<List<MetadataConstraintDO>> {
        return metadataService.listAllMetadataConstraint().let { 
            AquamanResult.ok(it)
        }
    }

    @GetMapping("/metadataConstraint/list")
    @Operation(summary = "list all metadata constraint by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listMetadataConstraintByProperties(
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam(required = false) keyWords: String?,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int,
    ): AquamanResult<PageData<MetadataConstraintDO>> {
        return metadataService.listMetadataConstraintByProperties(
            unit = unit, stage = stage, site = site, keyWords = keyWords,
            pageNumber = pageNumber, pageSize = pageSize
        ).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/metadataConstraint/deleteByMetadataConstraint")
    @Operation(summary = "delete metadata constraint")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteByMetadataConstraint(
        @RequestParam site: String,
        @RequestParam unit: String,
        @RequestParam stage: String,
        @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        metadataService.deleteByMetadataConstraint(site, unit, stage, modifier)
        return AquamanResult.ok(true)
    }

    @GetMapping("/metadataConstraint/isValid")
    @Operation(summary = "check metadata constraint valid")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun checkMetadataConstraint(@RequestParam site: String, @RequestParam unit: String, @RequestParam stage: String): AquamanResult<Boolean> {
        return AquamanResult.ok(metadataService.checkMetadataConstraint(site, unit, stage))
    }

    @GetMapping("/metadata/site/listAll")
    @Operation(summary = "list All  metadata of site")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listAllMetadataOfSite(): AquamanResult<List<MetadataOfSiteDO>> {
        return AquamanResult.ok(metadataService.listAllMetadataOfSite())
    }

    @GetMapping("/metadata/site/list")
    @Operation(summary = "list metadata of site by its properties, site using prefix match method")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listSitesByProperties(
        @RequestParam(required = false) region: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam(required = true) pageSize: Int,
        @RequestParam(required = true) pageNumber: Int,
    ): AquamanResult<PageData<MetadataOfSiteDO>> {
        return AquamanResult.ok(
            metadataService.listMetadataOfSiteByProperties(
                site = site, region = region, pageSize = pageSize, pageNumber = pageNumber
            )
        )
    }

    @PostMapping("/metadata/site/create")
    @Operation(summary = "create an metadata of site")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createMetadataOfSite(site: String, region: String, creator: String): AquamanResult<MetadataOfSiteDO> {
        return AquamanResult.ok(metadataService.createMetadataOfSite(site, region, creator))
    }

    @GetMapping("/metadata/site")
    @Operation(summary = "get an metadata of site")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getMetadataOfSite(site: String): AquamanResult<MetadataOfSiteDO?> {
        return AquamanResult.ok(metadataService.getMetadataOfSite(site))
    }

    @DeleteMapping("/metadata/site")
    @Operation(summary = "delete an metadata of site")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteMetadataOfSite(
        @RequestParam("site") site: String,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        metadataService.deleteMetadataOfSite(site, modifier)
        return AquamanResult.ok(true)
    }

    @GetMapping("/metadata/listUnitizationUnitGroup")
    @Operation(summary = "list unitization unit group")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT')")
    fun listUnitizationUnitGroup(@RequestParam appName: String, @RequestParam appGroupName: String): AquamanResult<List<UnitGroup>> {
        return AquamanResult.ok(
            metadataService.listUnitizationUnitGroup(
                appName = appName,
                appGroupName = appGroupName,
            )
        )
    }

    @PostMapping("/envLevelStageMapping/create")
    @Operation(summary = "create env level stage mapping")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createEnvLevelStageMapping(@RequestParam("envLevel") envLevel: String, @RequestParam("stage") stage: String): AquamanResult<Boolean> {
        envLevelStageMappingService.create(envLevel, stage)
        return AquamanResult.ok(true)
    }
    @GetMapping("/envLevelStageMapping/listByEnvLevel")
    @Operation(summary = "list stage by envLevel")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listStageByEnvLevel(@RequestParam envLevel: String): AquamanResult<List<String>> {
        return AquamanResult.ok(envLevelStageMappingService.listStageByEnvLevel(envLevel))
    }

    @GetMapping("/envLevelStageMapping/listByEnvLevelWithNormalization")
    @Operation(summary = "list stage by envLevel with normalization")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listByEnvLevelWithNormalization(@RequestParam envLevel: String): AquamanResult<List<String>> {
        return AquamanResult.ok(envLevelStageMappingService.listByEnvLevelWithNormalization(envLevel))
    }

    @GetMapping("/envLevelStageMapping/listAll")
    @Operation(summary = "list all envLevel to stage mapping")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listAllStageEnvLevelMapping(): AquamanResult<Map<String,List<String>>> {
        return AquamanResult.ok(envLevelStageMappingService.listAllEnvLevelStagesMapping())
    }

    @PostMapping("/envLevelStageMapping/deleteByEnvLevel")
    @Operation(summary = "delete env level stage mapping by envLevel")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT')")
    fun deleteEnvLevelStageMappingByEnvLevel(@RequestParam("envLevel") envLevel: String): AquamanResult<Boolean> {
        envLevelStageMappingService.deleteMappingByEnvLevel(envLevel)
        return AquamanResult.ok(true)
    }
}