package com.alibaba.koastline.multiclusters.api.v4.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.RegExReplaceData
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourceobj.TempService
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/*
all the apis will be pauced to service
 */
@RestController
@RequestMapping("/apis/temp/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class TempApiV4 {
    @Autowired
    lateinit var tempService: TempService

    @GetMapping("/asiPodVolumeInjectCheckAndConfig")
    @Operation(summary = "检查是否可以进行 ASI volume 注入，不能则报错返回，能则完成配置供发布运维使用")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun asiPodVolumeInjectCheckAndConfig(@RequestParam appName: String): AquamanResult<Boolean> {
        tempService.asiPodVolumeInjectCheckAndConfig(appName)
        return AquamanResult.ok(true)
    }

    @PostMapping("/regExReplace")
    @Operation(summary = "正则替换")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun regExReplace(@RequestBody regExReplaceData: RegExReplaceData): String {
        return tempService.regExReplace(regExReplaceData.regEx, regExReplaceData.text, regExReplaceData.replace)
    }
}