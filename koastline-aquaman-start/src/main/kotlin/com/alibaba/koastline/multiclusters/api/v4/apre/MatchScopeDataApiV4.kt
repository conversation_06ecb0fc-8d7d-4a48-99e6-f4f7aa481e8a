package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4/matchScope", produces = [MediaType.APPLICATION_JSON_VALUE])
class MatchScopeDataApiV4 {
    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @PostMapping("/create")
    @Operation(summary = "create")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun create(@RequestParam matchScopeDataDO: MatchScopeDataDO): AquamanResult<Boolean> {
        matchScopeService.createMatchScopeIgnoreWhileExist(matchScopeDataDO)
        return AquamanResult.ok(true)
    }

    @PostMapping("/initResourceGroup2AppName")
    @Operation(summary = "initResourceGroup2AppName")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun initResourceGroup2AppName(@RequestParam matchScopeDataDO: MatchScopeDataDO): AquamanResult<Boolean> {
        matchScopeService.saveAppName2ResourceGroup(matchScopeDataDO)
        return AquamanResult.ok(true)
    }
}