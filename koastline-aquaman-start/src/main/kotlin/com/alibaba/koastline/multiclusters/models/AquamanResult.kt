package com.alibaba.koastline.multiclusters.models

import com.alibaba.koastline.multiclusters.common.utils.TraceUtils

/**
 * <AUTHOR>
 */
data class AquamanResult<T>(
        val requestId: String = TraceUtils.getTraceId(),
        val success: Boolean,
        val errorCode: Int? = null,
        val message: String? = null,
        val data: T? = null,
        val bizError: Boolean = false
) {
    companion object {

        fun <T> ok(data: T): AquamanResult<T> {
            return ok(data, null)
        }

        fun <T> ok(data: T?, message: String?): AquamanResult<T> {
            return AquamanResult(data = data, success = true, message = message)
        }

        fun <T> fail(message: String?): AquamanResult<T> {
            return AquamanResult(success = false, message = message)
        }

        fun <T> fail(message: String?, errorCode: Int?): AquamanResult<T> {
            return AquamanResult(success = false, errorCode = errorCode, message = message)
        }

        fun <T> fail(message: String?, bizError: <PERSON>olean): AquamanResult<T> {
            return AquamanResult(success = false, message = message, bizError = bizError)
        }
    }
}
