package com.alibaba.koastline.multiclusters.api.v4.fedcluster

import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.common.exceptions.FedClusterException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.fed.FedClusterService
import com.alibaba.koastline.multiclusters.fed.model.FedClusterDO
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyCreateApiResponse
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyDetailsPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyRatioPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySelectorPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedSource.NORMANDY_FED_SOURCE
import com.alibaba.koastline.multiclusters.fed.model.MatchExpressionsProperties
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterDeleteReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterConditionQueryReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiDeleteReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiQueryCondition
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyQueryCondition
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterRegisterReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedPolicy
import com.alibaba.koastline.multiclusters.fed.model.req.FedPolicyDetails
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/apis/fed/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class FedClusterManagerApiV4(
    val fedClusterService: FedClusterService,
    val resourcePoolService: ResourcePoolService,
    val authenticationManager: AuthenticationManager,
) {

    val log by logger()

    @PostMapping("/fed-cluster/list-by-condition")
    @Operation(summary = "按照条件对fed集群进行搜索")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listFedClusterByConditions(@RequestBody fedClusterConditionQueryReq: FedClusterConditionQueryReq): AquamanResult<PageData<FedClusterDO>> {
        val rs = fedClusterService.listFedClusterByConditions(fedClusterConditionQueryReq)
        return AquamanResult.ok(rs)
    }

    @GetMapping("/fed-cluster/info")
    @Operation(summary = "按照id对fed集群进行搜索")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryFedClusterById(@RequestParam("id") id: Long): AquamanResult<FedClusterDO?> {
        return AquamanResult.ok(fedClusterService.queryFedClusterById(id = id))
    }

    @PostMapping("/fed-cluster/register")
    @Operation(summary = "注册fed集群")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registerFedCluster(@RequestBody fedClusterRegisterReq: FedClusterRegisterReq): AquamanResult<Boolean> {
        fedClusterService.registerFedCluster(fedClusterRegisterReq)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-cluster/modify-base-info")
    @Operation(summary = "更新fed集群环境")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFedClusterEnv(@RequestBody fedClusterUpdateReq: FedClusterUpdateReq): AquamanResult<Boolean> {
        fedClusterService.updateFedClusterEnv(fedClusterUpdateReq)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/fed-cluster/delete")
    @Operation(summary = "删除fed集群环境")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteFedCluster(
        @RequestParam("fedClusterId") fedClusterId: Long,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        fedClusterService.deleteFedClusterEnv(fedClusterId = fedClusterId, modifier = modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-cluster/member-cluster/add")
    @Operation(summary = "添加member集群")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun fedClusterAddMemberCluster(@RequestBody fedMemberClusterCreateReq: FedMemberClusterCreateReq): AquamanResult<Boolean> {
        fedClusterService.addMemberCluster(fedMemberClusterCreateReq = fedMemberClusterCreateReq)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-cluster/member-cluster/remove")
    @Operation(summary = "删除member集群")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun fedClusterRemoveMemberCluster(@RequestBody fedMemberClusterDeleteReq: FedMemberClusterDeleteReq): AquamanResult<Boolean> {
        fedClusterService.deleteMemberCluster(fedMemberClusterDeleteReq = fedMemberClusterDeleteReq)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-policy/brief/list-by-condition")
    @Operation(summary = "按照条件展示fedPolicy属性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listFedPolicyByConditions(@RequestBody fedClusterPolicyQueryCondition: FedClusterPolicyQueryCondition): AquamanResult<PageData<FedPolicy>> {
        return AquamanResult.ok(fedClusterService.listFedPolicyByConditions(fedClusterPolicyQueryCondition))
    }

    @PostMapping("/fed-policy/openapi/list-by-condition")
    @Operation(summary = "查询接入集群下的policy OpenAPI")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listFedPolicyByConditionsApi(@RequestBody fedClusterPolicyApiQueryCondition: FedClusterPolicyApiQueryCondition): AquamanResult<PageData<FedPolicy>> {
        fedClusterPolicyApiQueryCondition.validate()
        val tenantClusterName = fedClusterPolicyApiQueryCondition.tenantCluster!!
        val fedClusterId = fedClusterService.queryFedByTenantClusterName(tenantClusterName)?.id ?:
            throw FedClusterException("tenant cluster:${tenantClusterName} not found!")
        val queryCondition = FedClusterPolicyQueryCondition(
            fedClusterTargetId = fedClusterId,
            policySelectors = fedClusterPolicyApiQueryCondition.policySelectors,
            pageSize = fedClusterPolicyApiQueryCondition.pageSize!!,
            pageNumber = fedClusterPolicyApiQueryCondition.pageNumber!!
        )
        return AquamanResult.ok(fedClusterService.listFedPolicyByConditions(queryCondition))
    }

    @GetMapping("/fed-policy/details/{fedPolicyId}")
    @Operation(summary = "查询fedPolicy的详细信息")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryFedPolicyDetails(@PathVariable("fedPolicyId") fedPolicyId: Long): AquamanResult<FedPolicyDetails?> {
        return AquamanResult.ok(fedClusterService.queryFedPolicyDetails(fedPolicyId))
    }

    @PostMapping("/fed-policy/create")
    @Operation(summary = "创建fedPolicy")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFedPolicy(@RequestBody fedClusterPolicyCreateReq: FedClusterPolicyCreateReq): AquamanResult<Boolean> {
        fedClusterService.createFedPolicy(fedClusterPolicyCreateReq, NORMANDY_FED_SOURCE)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-policy/openapi/create")
    @Operation(summary = "创建fedPolicy OpenAPI")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFedPolicyApi(@RequestBody fedClusterPolicyApiCreateReq: FedClusterPolicyApiCreateReq): AquamanResult<FedPolicyCreateApiResponse> {
        fedClusterPolicyApiCreateReq.validate()
        val tenantClusterName = fedClusterPolicyApiCreateReq.tenantCluster!!
        val fedCluster = fedClusterService.queryFedByTenantClusterName(tenantClusterName)
            ?: throw FedClusterException("tenant cluster:${tenantClusterName} not found!")
        validatePolicySpec(fedCluster.memberManageClusterKey, fedClusterPolicyApiCreateReq.policySpec!!)

        val fedClusterPolicyCreateReq = FedClusterPolicyCreateReq(
            policyTargetClusterId = fedCluster.id!!,
            policyName = fedClusterPolicyApiCreateReq.policyName!!,
            policyPriority = fedClusterPolicyApiCreateReq.policyPriority!!,
            resourceKind = fedClusterPolicyApiCreateReq.resourceKind!!,
            policySpec = fedClusterPolicyApiCreateReq.policySpec!!,
            creator = fedClusterPolicyApiCreateReq.creator!!
        )
        val source = authenticationManager.getLoginAccount()?.authentication?.userName
            ?: throw FedClusterException("login user required!")
        val fedUniqueName = fedClusterService.createFedPolicy(fedClusterPolicyCreateReq, source)
        return AquamanResult.ok(FedPolicyCreateApiResponse(fedUniqueName))
    }

    private fun validatePolicySpec(memberManageClusterKey: String, policySpec: Map<String, Any>) {
        // policy 校验
        val policy = policySpec[FedPolicySpecPropertiesKey.POLICY] as? Map<*, *>
        require(!policy.isNullOrEmpty()) { "policySpec.policy required" }
        val schedulePlugins = policy[FedPolicyDetailsPropertiesKey.SCHEDULE_PLUGIN] as? List<*>
        require(!schedulePlugins.isNullOrEmpty()) { "policySpec.policy.schedulePlugins required" }

        // policy selector 校验
        val policySelector = policySpec[FedPolicySpecPropertiesKey.POLICY_SELECTOR] as? Map<*, *>
        require(!policySelector.isNullOrEmpty()) { "policySpec.policySelector required" }
        val matchExpressions = policySelector[FedPolicySelectorPropertiesKey.MATCH_EXPRESSIONS] as? List<*>
        require(!matchExpressions.isNullOrEmpty()) { "policySpec.policySelector.matchExpressions required" }
        matchExpressions.forEachIndexed { i, it ->
            val matchExpression = it as? Map<*, *>
            require(!matchExpression.isNullOrEmpty()) { "policySpec.policySelector.matchExpressions[$i] required" }
            val key = matchExpression[MatchExpressionsProperties.KEY] as? String
            require(!key.isNullOrBlank()) { "policySpec.policySelector.matchExpressions[$i].key required" }
            val operator = matchExpression[MatchExpressionsProperties.OPERATOR] as? String
            require(!operator.isNullOrBlank()) { "policySpec.policySelector.matchExpressions[$i].operator required" }
            if (operator in setOf("In", "NotIn")) {
                val values = matchExpression[MatchExpressionsProperties.VALUES] as? List<*>
                require(!values.isNullOrEmpty()) { "policySpec.policySelector.matchExpressions[$i].values required" }
            }
        }

        // ratio cluster name 必须是 member cluster 的子集
        val ratioClusterNames = validateRatios(policy[FedPolicyDetailsPropertiesKey.RATIOS] as? List<*>)
        if (ratioClusterNames.isEmpty()) {
            return
        }

        val memberClusterNames = resourcePoolService.listClusterProfileByManagedClusterKey(memberManageClusterKey)
            .map { it.clusterName }.toSet()
        require(ratioClusterNames.all { it in memberClusterNames }) {
            "policySpec.policy.ratios.clusterName must in fed member clusters"
        }
    }

    private fun validateRatios(ratios: List<*>?): List<String> {
        if (ratios.isNullOrEmpty()) {
            return emptyList()
        }
        return ratios.map {
            val ratio = it as? Map<*, *>
            require(!ratio.isNullOrEmpty()) { "policySpec.policy.ratios required" }
            val clusterName = ratio[FedPolicyRatioPropertiesKey.CLUSTER_NAME] as? String
            require(!clusterName.isNullOrBlank()) { "policySpec.policy.ratios.clusterName required" }
            val value = ratio[FedPolicyRatioPropertiesKey.VALUE] as? Int
            require(value != null) { "policySpec.policy.ratios.value required" }
            clusterName
        }
    }

    @PostMapping("/fed-policy/update")
    @Operation(summary = "更新fedPolicy")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFedPolicy(@RequestBody fedClusterPolicyUpdateReq: FedClusterPolicyUpdateReq): AquamanResult<Boolean> {
        fedClusterService.updateFedPolicy(fedClusterPolicyUpdateReq, NORMANDY_FED_SOURCE)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-policy/openapi/update")
    @Operation(summary = "更新fedPolicy OpenAPI")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFedPolicyAPI(@RequestBody fedClusterPolicyApiUpdateReq: FedClusterPolicyApiUpdateReq): AquamanResult<Boolean> {
        fedClusterPolicyApiUpdateReq.validate()
        val tenantClusterName = fedClusterPolicyApiUpdateReq.tenantCluster!!
        val fedCluster = fedClusterService.queryFedByTenantClusterName(tenantClusterName)
            ?: throw FedClusterException("tenant cluster:${tenantClusterName} not found!")
        validatePolicySpec(fedCluster.memberManageClusterKey, fedClusterPolicyApiUpdateReq.policySpec!!)

        val fedPolicy = fedClusterService.listFedPolicyByFedClusterId(fedCluster.id!!).find {
            it.fedUniqueName == fedClusterPolicyApiUpdateReq.fedUniqueName!!
        } ?: throw FedClusterException("fed policy:${fedClusterPolicyApiUpdateReq.fedUniqueName} not found!")
        val updateReq = FedClusterPolicyUpdateReq(
            policyId = fedPolicy.id,
            policyPriority = fedClusterPolicyApiUpdateReq.policyPriority!!,
            resourceKind = fedClusterPolicyApiUpdateReq.resourceKind!!,
            policySpec = fedClusterPolicyApiUpdateReq.policySpec!!,
            modifier = fedClusterPolicyApiUpdateReq.modifier!!
        )
        val source = authenticationManager.getLoginAccount()?.authentication?.userName
            ?: throw FedClusterException("login user required!")
        fedClusterService.updateFedPolicy(updateReq, source)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/fed-policy/delete")
    @Operation(summary = "删除fedPolicy")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteFedPolicy(
        @RequestParam("policyId") policyId: Long,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        fedClusterService.deleteFedPolicy(policyId = policyId, modifier = modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/fed-policy/openapi/delete")
    @Operation(summary = "删除fedPolicy OpenAPI")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteFedPolicyAPI(@RequestBody deleteReq: FedClusterPolicyApiDeleteReq): AquamanResult<Boolean> {
        deleteReq.validate()
        val tenantClusterName = deleteReq.tenantCluster!!
        val fedCluster = fedClusterService.queryFedByTenantClusterName(tenantClusterName)
            ?: throw FedClusterException("tenant cluster:${tenantClusterName} not found!")
        val fedPolicy = fedClusterService.listFedPolicyByFedClusterId(fedCluster.id!!).find {
            it.fedUniqueName == deleteReq.fedUniqueName!!
        } ?: throw FedClusterException("fed policy:${deleteReq.fedUniqueName} not found!")
        fedClusterService.deleteFedPolicy(fedPolicy.id, deleteReq.modifier!!)
        return AquamanResult.ok(true)
    }

    @GetMapping("/fed-policy/policy-selectors/{fedClusterTargetId}")
    @Operation(summary = "获取某个fed集群")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listPolicySelectors(@PathVariable("fedClusterTargetId") fedClusterTargetId: Long): AquamanResult<List<String>> {
        return AquamanResult.ok(fedClusterService.listPolicySelectors(fedClusterTargetId))
    }
}