package com.alibaba.koastline.multiclusters.api.v4.schedule.dto

import com.alibaba.koastline.multiclusters.schedule.model.OrientedDeclaration
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModelProperty
import io.swagger.annotations.ApiParam


@JsonIgnoreProperties(ignoreUnknown = true)
data class NonDeclarativeScaleOutScheduleRequest (
    @ApiModelProperty("应用名")
    val appName: String,
    @ApiModelProperty("环境StackId")
    val envStackId: String?,
    @ApiModelProperty("分组名")
    val resourceGroup: String,
    @ApiModelProperty("变更副本数")
    val replicas: Int,
    @ApiModelProperty("定向声明")
    val declaration: OrientedDeclaration,
    @ApiModelProperty("是否为Serverless")
    val serverless : Boolean? = false,
    @ApiParam("调度环境类型")
    val scheduleEnvType : ScheduleEnvType?
)