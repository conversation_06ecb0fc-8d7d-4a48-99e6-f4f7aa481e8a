package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/inner/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class AppRuntimeEnvironmentInnerApiV4 {

    val log by logger()

    @Autowired
    lateinit var appRuntimeEnvironmentService: ApREService
    @Autowired
    lateinit var envLevelStageMappingService: EnvLevelStageMappingService
    
    @GetMapping("/syncClusterEnvToApRE")
    @Operation(summary = "sync cluster env to apRE")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT')")
    fun syncClusterEnvToApRE(): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.syncClusterEnvToApRE()
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/physicalDelApREByRuntimeEnvKey")
    @Operation(summary = "physical delete  apre by runtime env key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT')")
    fun physicalDelAppRuntimeEnvironmentByRuntimeEnvKey(@RequestParam("runtimeEnvKey")runtimeEnvKey: String): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.physicalDelAppRuntimeEnvironmentByRuntimeEnvKey(runtimeEnvKey)
        return AquamanResult.ok(true)
    }
}