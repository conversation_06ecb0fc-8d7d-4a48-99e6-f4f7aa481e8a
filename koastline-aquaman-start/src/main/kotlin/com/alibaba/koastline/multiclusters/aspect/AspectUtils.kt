package com.alibaba.koastline.multiclusters.aspect

import com.alibaba.fastjson.JSONObject
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import org.aspectj.lang.ProceedingJoinPoint

object AspectUtils {
    private var objectMapper = ObjectMapperFactory.newTolerant()
    const val EAGLEEYE_USER_DATA_KEY_BIZ_SCENE = "BIZ_SCENE"
    private const val RESULT_CODE_SUCCESS = "SUCCESSFUL"
    private const val RESULT_CODE_FAILED = "FAILED"
    private val log by logger()
    /**
     * 对request以及response等写入log
     * @param method
     * @param latency
     * @param result
     * @param message
     */
    fun log(aspectPrefix: String, method: String, latency: Long, result: Boolean, message: String, bizError: Boolean = false, caller: String? = null, bizScene: String? = null) {
        log.info("$aspectPrefix Method:$method,cost:$latency,result:${buildResultCode(result)},bizError:$bizError,mixResult:$result&$bizError,caller:$caller,bizScene:$bizScene,traceId:${TraceUtils.getTraceId()},msg:$message")
    }

    /**
     * 获取log方法名称
     * @param proceedingJoinPoint
     * @return
     */
    fun getMethodName(proceedingJoinPoint: ProceedingJoinPoint): String {
        return proceedingJoinPoint.target.javaClass.simpleName + "[" + proceedingJoinPoint.signature.name + "]"
    }

    /**
     * 获取请求参数
     * @param args
     * @return
     */
    fun getParamsString(args: Array<Any>?): List<String> {
        if (args == null || args.isEmpty()) {
            return emptyList()
        }
        val params = mutableListOf<String>()
        args.forEach {
            params.add(objectMapper.writeValueAsString(it))
        }
        return params
    }

    /**
     *
     */
    fun printAspectLog(joinPoint: ProceedingJoinPoint, result: Boolean, re: Any?, errorMessage: String?, aspectPrefix: String, startTime: Long, method: String, bizError: Boolean, caller: String? = null,
                       bizScene: String? = null) {
        val endTime = System.currentTimeMillis()
        val jsonObject = JSONObject()
        jsonObject["request"] = getParamsString(joinPoint.args)
        if (result) {
            jsonObject["response"] = objectMapper.writeValueAsString(re)
        } else {
            jsonObject["response"] = errorMessage
        }
        log(aspectPrefix, method, endTime - startTime, result, jsonObject.toJSONString(),bizError,caller, bizScene);
    }

    /**
     *  为了明确定位错误结果，定义特性的Code代表是否成功
     */
    private fun buildResultCode(result: Boolean): String {
        return if (result) RESULT_CODE_SUCCESS else RESULT_CODE_FAILED
    }
}