package com.alibaba.koastline.multiclusters.aspect

import com.alibaba.koastline.multiclusters.aspect.AspectUtils.EAGLEEYE_USER_DATA_KEY_BIZ_SCENE
import com.alibaba.koastline.multiclusters.aspect.AspectUtils.printAspectLog
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ALL
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.DATA_BASE_OPEN_LOG_SCENE_CONFIG
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.logger
import com.fasterxml.jackson.databind.ObjectMapper
import com.taobao.eagleeye.EagleEye
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 */
@Aspect
@Component
class ExternalCallAspect(val objectMapper: ObjectMapper) {
    val log by logger()
    @Autowired
    lateinit var commonProperties: CommonProperties

    /**
     * 三方调用切面
     */
    @Around(value = "@annotation(com.alibaba.koastline.multiclusters.external.annotation.ExternalCall)")
    @Throws(Throwable::class)
    fun around(joinPoint: ProceedingJoinPoint): Any? {
        return execute(joinPoint, AspectUtils.getMethodName(joinPoint))
    }


    /**
     * DB调用切面
     */
    @Around("execution(* com.alibaba.koastline.multiclusters.data..*.*(..))")
    @Throws(Throwable::class)
    fun aroundRepo(joinPoint: ProceedingJoinPoint): Any? {
        if (matchDataBaseOpenLogScene()){
            return execute(joinPoint,  "DataBase-${joinPoint.signature.toShortString()}:")
        }
        return joinPoint.proceed()
    }

    private fun matchDataBaseOpenLogScene(): Boolean {
        val bizScene = EagleEye.getUserData(EAGLEEYE_USER_DATA_KEY_BIZ_SCENE) ?: return false
        return commonProperties.contains(DATA_BASE_OPEN_LOG_SCENE_CONFIG, ALL) ||
                commonProperties.contains(DATA_BASE_OPEN_LOG_SCENE_CONFIG, bizScene)
    }

    private fun execute(joinPoint: ProceedingJoinPoint, method: String): Any? {
        val startTime = System.currentTimeMillis()
        val aspectPrefix = this.javaClass.simpleName
        var re: Any? = null
        var result = false
        var errorMessage: String? = null
        var bizError = false
        try {
            re = joinPoint.proceed()
            result = true
        } catch (e: BizException) {
            errorMessage = "BizException:${e.message}"
            bizError = true
            log.error("$aspectPrefix ERROR,BizException,${e.errorCode}:${e.message}", e)
            throw e
        } catch (e: Exception) {
            errorMessage = "Exception:${e.message}"
            bizError = false
            log.error("$aspectPrefix ERROR, joinPointMethodName:${joinPoint.signature.name}  ${e.javaClass}:${e.message}", e)
            throw e
        } finally {
            printAspectLog(
                joinPoint = joinPoint,
                result = result,
                re = re,
                errorMessage = errorMessage,
                bizError = bizError,
                startTime = startTime,
                method = method,
                aspectPrefix = aspectPrefix,
                bizScene = EagleEye.getUserData(EAGLEEYE_USER_DATA_KEY_BIZ_SCENE)
            )
        }
        return re
    }
}