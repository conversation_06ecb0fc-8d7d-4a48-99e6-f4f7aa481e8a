package com.alibaba.koastline.multiclusters.api.inner

import com.alibaba.koastline.multiclusters.clusterproviders.AsiClusterRemoteSyncService
import com.fasterxml.jackson.databind.JsonNode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/multiclusters/cluster-providers", produces = [MediaType.APPLICATION_JSON_VALUE])
class ClusterProviderApi {

    @Autowired
    lateinit var asiClusterRemoteSyncService: AsiClusterRemoteSyncService

    @GetMapping("/v1/clusters/{clusterID}/detail")
    @PreAuthorize("hasAuthority('ADMIN_ROOT_RIGHT')")
    fun getClusterDetailsByClusterId(@PathVariable("clusterID") clusterId: String) : JsonNode? {
        return asiClusterRemoteSyncService.get("/api/v1/clusters/$clusterId/detail", mutableMapOf())
    }
}