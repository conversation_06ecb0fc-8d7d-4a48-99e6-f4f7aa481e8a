package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.attorney.ApREAttorneyService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ExtraApREBindingDataService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.AttorneyScope
import com.alibaba.koastline.multiclusters.apre.model.ServerlessAttorney
import com.alibaba.koastline.multiclusters.apre.model.ServerlessAttorneyGroup
import com.alibaba.koastline.multiclusters.apre.model.req.AttorneyCreateDto
import com.alibaba.koastline.multiclusters.apre.model.req.CreateServerlessAttorneyDto
import com.alibaba.koastline.multiclusters.apre.model.req.UpdateServerlessAttorneyDto
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/apis/apre/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class AttorneyApiV4 @Autowired constructor(
    val extraApREBindingDataService: ExtraApREBindingDataService,
    val apREBindingService: ApREBindingService,
    val apREAttorneyService: ApREAttorneyService
) {
    val log by logger()

    /**
     * 根据ApREBindingDataId查找Attorney
     *
     * @param apREBindingDataId
     * @return
     */
    @GetMapping("/attorney/find")
    @Operation(summary = "find attorney by apREBindingData id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findAttorneyByApREBindingDataId(
        @RequestParam apREBindingDataId: Long
    ): AquamanResult<Attorney?> {
        return AquamanResult.ok(apREAttorneyService.findAttorneyByApREBindingDataId(apREBindingDataId))
    }

    /**
     * 创建集群环境授权
     *
     * @param attorneyCreateDto
     * @return
     */
    @PostMapping("/attorney/create")
    @Operation(summary = "create attorney")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createAttorney(
        @RequestBody attorneyCreateDto: AttorneyCreateDto
    ): AquamanResult<Boolean> {
        apREAttorneyService.createAttorney(attorneyCreateDto.convertToAttorney())
        return AquamanResult.ok(true)
    }

    /**
     * 批量进行集群环境授权创建
     *
     * @param batchAttorneyCreateDto
     * @return
     */
    @PostMapping("/attorney/batchCreate")
    @Operation(summary = "create attorney")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchCreateAttorney(
        @RequestBody batchAttorneyCreateDto: List<AttorneyCreateDto>
    ): AquamanResult<Boolean> {
        apREAttorneyService.batchCreateAttorney(
            batchAttorneyCreateDto.map { it.convertToAttorney() }
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/updateSelectorData")
    @Operation(summary = "更新ApRE权限点")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREBindingSelectorData(
        @RequestParam("apREBindingDataId") apREBindingDataId: Long,
        @RequestBody selector: ApREBindingTerm?
    ): AquamanResult<Boolean> {
        apREBindingService.updateApREBindingSelectorData(apREBindingDataId, selector)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/attorney/delete")
    @Operation(summary = "delete attorney by id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteAttorney(
        @RequestParam id: Long, @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        apREBindingService.deleteApREBindingDataById(
            id = id, modifier = modifier
        )
        return AquamanResult.ok(true)
    }

    /**
     * 按照条件对Attorney进行展示
     *
     * @param site
     * @param unit
     * @param stage
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @PostMapping("/attorney/findScope")
    @Operation(summary = "list all attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findAttorneyScopeByProperties(
        @RequestParam(required = false) site: String?,
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) stage: String?,
        @RequestParam externalId: String,
        @RequestParam externalType: String,
        @RequestParam includeScopeType: IncludeScopeType,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int,
    ): AquamanResult<AttorneyScope> {
        return AquamanResult.ok(
            apREAttorneyService.findAttorneyScopeByProperties(
                site = site,
                unit = unit,
                stage = stage,
                externalId = externalId,
                externalType = externalType,
                includeScopeType = includeScopeType,
                pageSize = pageSize,
                pageNumber = pageNumber
            )
        )
    }

    @PostMapping("/attorney/cancel")
    @Operation(summary = "cancel attorney by id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun cancelAttorney(
        apREBindingDataId: Long,
        externalId: String,
        externalType: String,
        modifier: String
    ): AquamanResult<Boolean> {
        apREAttorneyService.cancelAttorney(
            apREBindingDataId = apREBindingDataId,
            externalId = externalId,
            externalType = externalType,
            modifier = modifier
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/serverless/attorney/list")
    @Operation(summary = "list serverless attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listServerlessAttorney(
        @RequestParam(required = false) keyWords: String?,
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int,
    ): AquamanResult<PageData<ServerlessAttorneyGroup>> {
        return AquamanResult.ok(
            extraApREBindingDataService.listServerlessAttorneyByProperties(
                pageNumber = pageNumber,
                pageSize = pageSize,
                keyWords = keyWords
            )
        )
    }

    @PostMapping("/serverless/attorney/create")
    @Operation(summary = "create serverless attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createServerlessAttorney(
        @RequestBody createServerlessAttorneyDto: CreateServerlessAttorneyDto,
    ): AquamanResult<ServerlessAttorney> {
        createServerlessAttorneyDto.validate()
        return AquamanResult.ok(
            extraApREBindingDataService.createServerlessAttorneyWhileExisted2Update(
                serverlessBaseApp = createServerlessAttorneyDto.serverlessBaseApp,
                externalId = createServerlessAttorneyDto.externalId,
                externalType = createServerlessAttorneyDto.externalType,
                properties = createServerlessAttorneyDto.properties,
                description = createServerlessAttorneyDto.description,
                creator = createServerlessAttorneyDto.creator
            )
        )
    }

    @PostMapping("/serverless/attorney/batch/create")
    @Operation(summary = "batch create serverless attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchCreateServerlessAttorney(
        @RequestBody createServerlessAttorneyDtoList: List<CreateServerlessAttorneyDto>,
    ): AquamanResult<List<ServerlessAttorney>> {
        return AquamanResult.ok(
            extraApREBindingDataService.batchCreateServerlessAttorneyWhileExisted2Update(
                createServerlessAttorneyDtoList
            )
        )
    }

    @PostMapping("/serverless/attorney/update")
    @Operation(summary = "update serverless attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateServerlessAttorney(
        @RequestBody updateServerlessAttorneyDto: UpdateServerlessAttorneyDto,
    ): AquamanResult<ServerlessAttorney> {
        updateServerlessAttorneyDto.validate()
        return AquamanResult.ok(
            extraApREBindingDataService.updateServerlessAttorney(
                serverlessBaseApp = updateServerlessAttorneyDto.serverlessBaseApp,
                externalId = updateServerlessAttorneyDto.externalId,
                externalType = updateServerlessAttorneyDto.externalType,
                properties = updateServerlessAttorneyDto.properties,
                description = updateServerlessAttorneyDto.description,
                modifier = updateServerlessAttorneyDto.modifier
            )
        )
    }

    @DeleteMapping("/serverless/attorney/delete")
    @Operation(summary = "delete serverless attorney by Properties ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteServerlessAttorney(
        @RequestParam serverlessBaseApp: String,
        @RequestParam externalId: String,
        @RequestParam externalType: MatchScopeExternalTypeEnum,
        @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        extraApREBindingDataService.deleteServerlessAttorney(
            serverlessBaseApp = serverlessBaseApp,
            externalId = externalId,
            externalType = externalType,
            modifier = modifier
        )
        return AquamanResult.ok(true)
    }
}