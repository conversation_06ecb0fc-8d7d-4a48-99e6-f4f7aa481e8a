package com.alibaba.koastline.multiclusters.api.v4.runtime

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.runtime.RuntimeBaseService
import com.alibaba.koastline.multiclusters.runtime.RuntimeCreateReqDto
import com.alibaba.koastline.multiclusters.runtime.RuntimeService
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadCreateReqDto
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeWorkloadAndRouteRegistryDto
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeWorkloadAndRouteUnRegistryDto
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/runtime/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class RuntimeApiV4 {
    @Autowired
    lateinit var runtimeBaseService: RuntimeBaseService
    @Autowired
    lateinit var runtimeService: RuntimeService
    @PostMapping("/registry")
    @Operation(summary = "注册Runtime")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registryRuntime(@RequestBody runtimeCreateReqDto: RuntimeCreateReqDto): AquamanResult<Long> {
        return AquamanResult.ok(runtimeBaseService.registryRuntime(runtimeCreateReqDto))
    }

    @GetMapping("/unRegistry")
    @Operation(summary = "注销Runtime")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun unRegistryRuntime(@RequestParam appName: String,@RequestParam runtimeKey: String,@RequestParam operator: String): AquamanResult<Boolean> {
        runtimeBaseService.unRegistryRuntime(appName, runtimeKey, operator)
        return AquamanResult.ok(true)
    }

    @PostMapping("/registryRuntimeWorkloadAndRoute")
    @Operation(summary = "注册RuntimeWorkload以及路由")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registryRuntimeWorkloadAndRoute(@RequestBody runtimeWorkloadAndRouteRegistryDto: RuntimeWorkloadAndRouteRegistryDto): AquamanResult<Boolean>  {
        runtimeService.registryRuntimeWorkloadAndRoute(runtimeWorkloadAndRouteRegistryDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/unRegistryRuntimeWorkloadAndRoute")
    @Operation(summary = "注销RuntimeWorkload以及路由")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun unRegistryRuntimeWorkloadAndRoute(@RequestBody runtimeWorkloadAndRouteUnRegistryDto: RuntimeWorkloadAndRouteUnRegistryDto): AquamanResult<Boolean>  {
        runtimeService.unRegistryRuntimeWorkloadAndRoute(runtimeWorkloadAndRouteUnRegistryDto)
        return AquamanResult.ok(true)
    }



    @PostMapping("/workload/create")
    @Operation(summary = "创建RuntimeWorkload")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createRuntimeWorkload(@RequestBody runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto): AquamanResult<Boolean> {
        runtimeBaseService.registryRuntimeWorkload(runtimeWorkloadCreateReqDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/workload/updateStatus")
    @Operation(summary = "更新RuntimeWorkload状态")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateRuntimeWorkloadStatus(@RequestBody runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto): AquamanResult<Boolean> {
        runtimeBaseService.updateRuntimeWorkloadStatus(runtimeWorkloadCreateReqDto)
        return AquamanResult.ok(true)
    }

    @GetMapping("/workload/computeRuntimeInstalledWorkload")
    @Operation(summary = "获取已安装的WorkloadList")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun computeRuntimeInstalledWorkload(@RequestParam appName: String, @RequestParam runtimeKey: String): AquamanResult<ScheduleResult> {
        return AquamanResult.ok( ScheduleResult(
                workloadExpectedStates = runtimeService.computeRuntimeInstalledWorkload(
                    appName = appName,
                    runtimeKey = runtimeKey
                )
            )
        )
    }
}