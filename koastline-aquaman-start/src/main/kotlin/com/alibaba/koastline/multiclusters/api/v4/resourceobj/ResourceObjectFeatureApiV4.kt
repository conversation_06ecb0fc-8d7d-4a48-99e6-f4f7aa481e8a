package com.alibaba.koastline.multiclusters.api.v4.resourceobj

import com.alibaba.koastline.multiclusters.api.v4.resourceobj.dto.FeatureImportPreViewParams
import com.alibaba.koastline.multiclusters.apre.model.req.ActionLogsReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.NextTokenData
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.req.*
import com.alibaba.koastline.multiclusters.resourceobj.specific.ResourceSpecFeatureService
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/resource/object/feature/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ResourceObjectFeatureApiV4 {
    @Autowired
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService
    @Autowired
    lateinit var resourceSpecFeatureService: ResourceSpecFeatureService
    @Autowired
    lateinit var userLabelService: UserLabelService



    @PostMapping("/resourceGroup/createOrUpdateResourceSpec")
    @Operation(summary = "分组创建或更新资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateResourceSpecByResourceGroup(@RequestBody resourceGroupResourceSpec: ResourceGroupResourceSpec): AquamanResult<Boolean> {
        resourceSpecFeatureService.createOrUpdateResourceSpec(
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            resourceGroupResourceSpec.resourceGroup,
            resourceGroupResourceSpec.resourceSpec,
            resourceGroupResourceSpec.employeeId
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/resourceGroup/createOrUpdateResourceSpecForCpuModel")
    @Operation(summary = "分组创建或更新CPU模式资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateResourceSpecForCpuModelByResourceGroup(@RequestBody resourceGroupCpuModelResourceSpec: ResourceGroupCpuModelResourceSpec): AquamanResult<Boolean> {
        resourceSpecFeatureService.createOrUpdateResourceSpecForCpuModelByResourceGroup(resourceGroupCpuModelResourceSpec)
        return AquamanResult.ok(true)
    }

    @PostMapping("/resourceGroup/createOrUpdateResourceSpecForGpuModel")
    @Operation(summary = "分组创建或更新GPU模式资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateResourceSpecForGpuModelByResourceGroup(@RequestBody resourceGroupGpuModelResourceSpec: ResourceGroupGpuModelResourceSpec): AquamanResult<Boolean> {
        resourceSpecFeatureService.createOrUpdateResourceSpecForGpuModelByResourceGroup(resourceGroupGpuModelResourceSpec)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/resourceGroup/deleteResourceSpecForCpuModel")
    @Operation(summary = "删除分组CPU模式资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteResourceSpecForCpuModelByResourceGroup(@RequestParam("resourceGroup") resourceGroup: String,@RequestParam("employeeId")  employeeId: String): AquamanResult<Boolean> {
        resourceSpecFeatureService.deleteResourceSpecForCpuModelByResourceGroup(
            resourceGroup = resourceGroup,
            modifier = employeeId,
        )
        return AquamanResult.ok(true)
    }

    @GetMapping("/resourceGroup/getResourceSpec")
    @Operation(summary = "获取分组资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getResourceSpecByResourceGroup(@RequestParam resourceGroup: String): AquamanResult<ResourceSpec?> {
        return AquamanResult.ok(
            resourceSpecFeatureService.getResourceSpecByMatchScope(
                MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                resourceGroup
            )
        )
    }

    @PostMapping("/app/createOrUpdateResourceSpec")
    @Operation(summary = "应用创建或更新资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateResourceSpecByApp(@RequestBody appResourceSpec: AppResourceSpec): AquamanResult<Boolean> {
        userLabelService.createOrUpdateAppResourceSpec(appResourceSpec)
        return AquamanResult.ok(true)
    }

    @GetMapping("/app/getResourceSpec")
    @Operation(summary = "获取应用资源规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getResourceSpecByApp(@RequestParam appName: String): AquamanResult<ResourceSpec> {
        return AquamanResult.ok(
            userLabelService.getAppResourceSpec(appName)
        )
    }

    @PostMapping("/createFeature")
    @Operation(summary = "创建资源对象特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeature(@RequestBody resourceObjectFeatureCreateReqDto: ResourceObjectFeatureCreateReqDto): AquamanResult<Long> {
        return AquamanResult.ok(resourceObjectFeatureService.createFeature(resourceObjectFeatureCreateReqDto))
    }

    @PutMapping("/updateFeature")
    @Operation(summary = "更新资源对象特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFeature(@RequestBody resourceObjectFeatureUpdateReqDto: ResourceObjectFeatureUpdateReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.updateFeature(resourceObjectFeatureUpdateReqDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/getFeature")
    @Operation(summary = "获取资源对象特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeature(@RequestBody resourceObjectFeatureGetReqDto: ResourceObjectFeatureGetReqDto): AquamanResult<ResourceObjectGetFeatureListDO> {
        return AquamanResult.ok(
            resourceObjectFeatureService.getFeature(resourceObjectFeatureGetReqDto)
        )
    }

    @PostMapping("/getFeatureByKeyAndVersion")
    @Operation(summary = "根据特性Key和特性Version获取资源对象特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureByKeyAndVersion(@RequestBody resourceObjectFeatureGetByKeyAndVersionReqDto: ResourceObjectFeatureGetByKeyAndVersionReqDto): AquamanResult<ResourceObjectGetFeatureListDO> {
        return AquamanResult.ok(
            resourceObjectFeatureService.getFeatureByKeyAndVersion(resourceObjectFeatureGetByKeyAndVersionReqDto)
        )
    }



    @PostMapping("/createFeatureProtocol")
    @Operation(summary = "创建资源对象特性协议")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeatureProtocol(@RequestBody resourceObjectFeatureProtocolCreateReqDto: ResourceObjectFeatureProtocolCreateReqDto): AquamanResult<Long> {
        return AquamanResult.ok(
            resourceObjectFeatureService.createFeatureProtocol(
                resourceObjectFeatureProtocolCreateReqDto
            )
        )
    }

    @PostMapping("/createFeatureProtocolExt")
    @Operation(summary = "创建资源对象特性扩展注入模板")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeatureProtocolExt(@RequestBody resourceObjectFeatureProtocolExtCreateReqDto: ResourceObjectFeatureProtocolExtCreateReqDto): AquamanResult<Long> {
        return AquamanResult.ok(
            resourceObjectFeatureService.createFeatureProtocolExt(
                resourceObjectFeatureProtocolExtCreateReqDto
            )
        )
    }

    @PutMapping("/updateFeatureProtocol")
    @Operation(summary = "更新资源对象特性协议")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFeatureProtocol(@RequestBody resourceObjectFeatureProtocolUpdateReqDto: ResourceObjectFeatureProtocolUpdateReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.updateFeatureProtocol(resourceObjectFeatureProtocolUpdateReqDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/createFeatureImport")
    @Operation(summary = "创建资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeatureImport(@RequestBody resourceObjectFeatureImportCreateReqDto: ResourceObjectFeatureImportCreateReqDto): AquamanResult<Long> {
        return AquamanResult.ok(resourceObjectFeatureService.createFeatureImport(resourceObjectFeatureImportCreateReqDto.validate()))
    }

    @PostMapping("/createOrUpdateFeatureImport")
    @Operation(summary = "批量创建或者更新资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateFeatureImport(@RequestBody req: ResourceObjectFeatureImportCreateOrUpdateReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.createOrUpdateFeatureImport(req.validate())
        return AquamanResult.ok(true)
    }

    @PutMapping("/updateFeatureImport")
    @Operation(summary = "更新资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFeatureImport(@RequestBody resourceObjectFeatureImportUpdateReqDto: ResourceObjectFeatureImportUpdateReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.updateFeatureImport(resourceObjectFeatureImportUpdateReqDto)
        return AquamanResult.ok(true)
    }

    @PutMapping("/modifyTraits")
    @Operation(summary = "增加/修改/删除特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun modifyTraits(@RequestBody traitModifyReq: TraitModifyReq): AquamanResult<Boolean> {
        resourceObjectFeatureService.modifyTraits(traitModifyReq.validate())
        return AquamanResult.ok(true)
    }

    @PostMapping("/getActionLogs")
    @Operation(summary = "获取操作历史记录")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getActionLogs(@RequestBody req: ActionLogsReqDto): AquamanResult<ResourceObjectGetActionLogDO> {
        return AquamanResult.ok(resourceObjectFeatureService.getActionLogs(req.validate()))
    }

    @DeleteMapping("/deleteFeatureImportById")
    @Operation(summary = "删除资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteFeatureImportById(@RequestParam("id") id: Long,@RequestParam("modifier")  modifier: String): AquamanResult<Boolean> {
        resourceObjectFeatureService.deleteFeatureImportById(id, modifier)
        return AquamanResult.ok(true)
    }

    @PutMapping("/updateFeatureImportByMatchScope")
    @Operation(summary = "根据生效范围更新资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFeatureImportByMatchScope(
        @RequestBody req: ResourceObjectFeatureImportUpdateByMatchScopeReqDto
    ): AquamanResult<Boolean> {
        resourceObjectFeatureService.updateFeatureImportByMatchScope(req.validate())
        return AquamanResult.ok(true)
    }

    @PostMapping("/deleteFeatureImportByMatchScope")
    @Operation(summary = "根据生效范围删除资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteFeatureImportByMatchScope(@RequestBody req: ResourceObjectFeatureImportDeleteByMatchScopeReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.deleteFeatureImportByMatchScope(req.validate())
        return AquamanResult.ok(true)
    }

    @PostMapping("/batchDeleteFeatureImportByMatchScope")
    @Operation(summary = "根据生效范围批量删除资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchDeleteFeatureImportByMatchScope(@RequestBody req: BatchResourceObjectFeatureImportDeleteByMatchScopeReqDto): AquamanResult<Boolean> {
        resourceObjectFeatureService.batchDeleteFeatureImportByMatchScope(req.validate())
        return AquamanResult.ok(true)
    }

    @PostMapping("/getFeatureImportByMatchScope")
    @Operation(summary = "根据生效范围获取资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureImportByMatchScope(@RequestBody req: ResourceObjectFeatureImportGetByMatchScopeReqDto): AquamanResult<ResourceObjectGetFeatureImportListDO> {
        return AquamanResult.ok(
            resourceObjectFeatureService.getFeatureImportByMatchScope(req.validate())
        )
    }

    @PostMapping("/getFeatureImportByAppName")
    @Operation(summary = "根据应用名获取资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureImportByAppName(@RequestBody req: ResourceObjectFeatureImportGetByAppNameReqDto): AquamanResult<List<ResourceObjectGetFeatureImportDO>> {
        return AquamanResult.ok(
            resourceObjectFeatureService.getFeatureImportByAppName(req.validate())
        )
    }

    @PostMapping("/getFeatureImportByMatchScopeAndKey")
    @Operation(summary = "根据生效范围和特性Key获取资源对象特性导入，包括运维特性和环境特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getFeatureImportByMatchScopeAndKey(@RequestBody req: ResourceObjectFeatureImportGetByMatchScopeAndKeyReqDto): AquamanResult<List<ResourceObjectGetFeatureImportDO>> {
        return AquamanResult.ok(
            resourceObjectFeatureService.getFeatureImportByMatchScopeAndKey(req.validate())
        )
    }

    /**
     * 专为asi提供，支持分页拉取指定特性
     * 其中，合法的特性范围通过diamond约束
     * nextToken为分页游标，出于性能考虑具体的业务含义为match_scope_data表中的target_id
     * 两次查询结果的尾target_id可能重复，需要asi侧进行check before patch。
     *
     * @param req
     * @return
     */
    @PostMapping("/getAsiAwareTraitsByPage")
    @Operation(summary = "专为asi提供，用于支持查询特性")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getAsiAwareTraitsByPage(@RequestBody req: ResourceObjectFeatureImportAsiAwareReqDto): AquamanResult<NextTokenData<ResourceObjectFeatureImportForAsiDO>> {
        return AquamanResult.ok(
            resourceObjectFeatureService.pageGetCombinedFeatureImportByKeys(req)
        )
    }

    @PostMapping("/preViewFeatureImport")
    @Operation(summary = "预览资源对象特性导入")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun preViewFeatureImport(@RequestBody featureImportPreViewParams: FeatureImportPreViewParams): AquamanResult<String> {
        return AquamanResult.ok(resourceObjectFeatureService.parseTemplate(
                featureImportPreViewParams.template,
                featureImportPreViewParams.userParamMapStr,
                featureImportPreViewParams.userParamMapFormatEnum,
                featureImportPreViewParams.systemParams
            )
        )
    }
}