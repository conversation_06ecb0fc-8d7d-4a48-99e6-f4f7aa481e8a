package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ComponentDataService
import com.alibaba.koastline.multiclusters.apre.model.ComponentDataDO
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4/component", produces = [MediaType.APPLICATION_JSON_VALUE])
class ComponentDataApiV4 {
    @Autowired
    lateinit var componentDataService: ComponentDataService

    @PostMapping("/create")
    @Operation(summary = "create an component")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createComponent(@RequestBody componentDataDO: ComponentDataDO): AquamanResult<ComponentDataDO> {
       return AquamanResult.ok(componentDataService.createComponentData(componentDataDO));
    }

    @PutMapping("/update")
    @Operation(summary = "update an component")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateComponent(@RequestParam id: Long, @RequestBody annotations: Map<String, String>): AquamanResult<Boolean> {
        componentDataService.updateComponentData(id, annotations)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/delete")
    @Operation(summary = "delete an component by id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteComponentDataById(@RequestParam id: Long): AquamanResult<Boolean> {
        componentDataService.deleteComponentDataById(id)
        return AquamanResult.ok(true)
    }

    @GetMapping("/listByRef")
    @Operation(summary = "list component by ref")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listComponentDataByRef(@RequestParam refObjectType: String,@RequestParam refObjectId: String): AquamanResult<List<ComponentDataDO>> {
        return AquamanResult.ok(componentDataService.listComponentDataByRef(refObjectType, refObjectId))
    }
}