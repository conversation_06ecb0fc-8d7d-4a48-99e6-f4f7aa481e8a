package com.alibaba.koastline.multiclusters.api.v4.resourcescope

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostResourceScopeService
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostWorkloadMetaService
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeCreateDto
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeDO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author:    <EMAIL>
 * @description:  环境主机资源范围配置
 * @date:    2025/3/12 2:50 PM
 */
@RestController
@RequestMapping("/apis/env/host/resource/scope/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class EnvHostResourceScopeApiV4 {
    @Autowired
    lateinit var envHostResourceScopeService: EnvHostResourceScopeService
    @Autowired
    lateinit var envHostWorkloadMetaService: EnvHostWorkloadMetaService

    @PostMapping("/createAndOverrideWhileExist")
    fun createAndOverrideWhileExist(@RequestBody envHostResourceScopeCreateDto: EnvHostResourceScopeCreateDto): AquamanResult<Boolean> {
        envHostResourceScopeService.createAndOverrideWhileExist(envHostResourceScopeCreateDto)
        return AquamanResult.ok(true)
    }
    @GetMapping("/listByAppName")
    fun listByAppName(@RequestParam appName: String): AquamanResult<List<EnvHostResourceScopeDO>> {
        return AquamanResult.ok(
            envHostResourceScopeService.listByAppName(appName)
        )
    }
    @DeleteMapping("/deleteByEnvStackId")
    fun deleteByEnvStackId(@RequestParam envStackId: String,@RequestParam modifier: String): AquamanResult<Boolean> {
        envHostResourceScopeService.deleteByCurrentEnvStackId(
            currentEnvStackId = envStackId,
            modifier = modifier
        )
        return AquamanResult.ok(true)
    }
    @GetMapping("/findByEnvStackId")
    fun findByEnvStackId(@RequestParam envStackId: String): AquamanResult<EnvHostResourceScopeDO?> {
        return AquamanResult.ok(
            envHostResourceScopeService.findByCurrentEnvStackId(envStackId)
        )
    }

    @DeleteMapping("/deleteEnvHostWorkloadMetaByEnvStackId")
    fun deleteEnvHostWorkloadMetaByEnvStackId(@RequestParam envStackId: String): AquamanResult<Boolean> {
        envHostWorkloadMetaService.deleteByEnvStackId(envStackId)
        return AquamanResult.Companion.ok(true)
    }
}