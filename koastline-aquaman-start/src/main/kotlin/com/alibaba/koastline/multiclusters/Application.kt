package com.alibaba.koastline.multiclusters

import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.cache.annotation.EnableCaching
import java.util.*
import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = ["com.alibaba.koastline.multiclusters"])
@EnableCaching
class Application {
    @PostConstruct
    fun started() {
        // set JVM timezone as UTC
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"))
    }
}

fun main(args: Array<String>) {
    SpringApplication.run(Application::class.java, *args)
}
