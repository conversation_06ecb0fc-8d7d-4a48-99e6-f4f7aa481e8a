package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ApRELabelDefineService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_PREFIX
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import com.alibaba.koastline.multiclusters.apre.common.ApREDefaultFeatureService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecDefineService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREBaseDetailsUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRECreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREPropertiesUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.FeatureSpecConditionQueryReq
import com.alibaba.koastline.multiclusters.apre.model.req.FeatureSpecDefinitionCreateReq
import com.alibaba.koastline.multiclusters.apre.model.req.FeatureSpecDefinitionUpdateReq
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelDefinition
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.FeatureSpecDefinition
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class AppRuntimeEnvironmentApiV4 @Autowired constructor(
    val appRuntimeEnvironmentService: ApREService,
    val apREDefaultFeatureService: ApREDefaultFeatureService,
    val envLevelStageMappingService: EnvLevelStageMappingService,
    val apRELabelService: ApRELabelService,
    val scheduleStandardService: ScheduleStandardService,
    val apRELabelDefineService: ApRELabelDefineService,
    val apREFeatureSpecService: ApREFeatureSpecService,
    val apREFeatureSpecDefinitionService: ApREFeatureSpecDefineService,
) {

    val log by logger()

    @GetMapping("/clusters/match-rule-with-deed-key-multi")
    @Operation(summary = "query clusters by apre deed key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClustersByApREDeedKey(@RequestParam("key") apREDeedKey: String): AquamanResult<ApREDeedResult> {
        return AquamanResult.ok(appRuntimeEnvironmentService.queryClustersByApREDeedKey(apREDeedKey))
    }

    @PostMapping("/clusters/match-rule-with-deed-content")
    @Operation(summary = "query clusters by apre deed content")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClustersByApREDeedContent(@RequestBody @Validated apREDeedDO: ApREDeedDO): AquamanResult<ApREDeedResult> {
        return AquamanResult.ok(appRuntimeEnvironmentService.queryClustersByApREDeedContent(apREDeedDO))
    }

    @PostMapping("/match-rule-with-deed-content")
    @Operation(summary = "query apres by match rules")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireApREDeedResultByApREDeedContent(@RequestBody @Validated apREDeedDO: ApREDeedDO): AquamanResult<ApREDeedResultVBeta> {
        return appRuntimeEnvironmentService.requireApREDeedResultByApREDeedContent(
            apREDeedDO = ApREDeedDO(
                null,
                apREDeedDO.identityInfo,
                apREDeedDO.declarations
            )
        ).let {
            AquamanResult.ok(
                ApREDeedResultVBeta(
                    it.deedDO.key!!,
                    it.deedDO.identityInfo!!,
                    it.deedDO.declarations!!,
                    it.matchDeclarations,
                    it.apREDeclarationPatchDatas
                )
            )
        }
    }

    @PostMapping("/match-rule-simple-with-deed-content")
    @Operation(summary = "query simple apres by match rules")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireApREDeedSimpleResultByApREDeedContent(@RequestBody @Validated apREDeedDO: ApREDeedDO): AquamanResult<ApREDeedResultVBeta> {
        return appRuntimeEnvironmentService.requireApREDeedResultByApREDeedContent(
            ApREDeedDO(
                null,
                apREDeedDO.identityInfo,
                apREDeedDO.declarations
            )
        ).let {
            AquamanResult.ok(
                ApREDeedResultVBeta(
                    it.deedDO.key!!,
                    it.deedDO.identityInfo!!,
                    it.deedDO.declarations!!,
                    it.matchDeclarations.map { matchDeclaration ->
                        matchDeclaration.copy(
                            apres = matchDeclaration.apres.map { apre ->
                                apre.copy(apRELabels = emptyList())
                            }
                        )
                    },
                    emptyList()
                )
            )
        }
    }

    @GetMapping("/match-rule-with-deed-key")
    @Operation(summary = "query apres by match key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireApREDeedResultByApREDeedKey(key: String): AquamanResult<ApREDeedResultVBeta> {
        return appRuntimeEnvironmentService.requireApREDeedResultByApREDeedKey(
            key
        ).let {
            AquamanResult.ok(
                ApREDeedResultVBeta(
                    it.deedDO.key!!,
                    it.deedDO.identityInfo!!,
                    it.deedDO.declarations!!,
                    it.matchDeclarations,
                    it.apREDeclarationPatchDatas
                )
            )
        }
    }

    @GetMapping("/requireOnlyDeclarationOfApREDeedResultByApREDeedKey")
    @Operation(summary = "根据ApREDeedKey查询声明信息")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireOnlyDeclarationOfApREDeedResultByApREDeedKey(key: String): AquamanResult<ApREDeedResultVBeta> {
        return appRuntimeEnvironmentService.requireOnlyDeclarationOfApREDeedResultByApREDeedKey(
            key
        ).let {
            AquamanResult.ok(
                ApREDeedResultVBeta(
                    key = it.deedDO.key!!,
                    identityInfo = it.deedDO.identityInfo!!,
                    declarations = it.deedDO.declarations!!,
                    matchDeclarations = it.matchDeclarations,
                    apREDeclarationPatchDatas = it.apREDeclarationPatchDatas,
                )
            )
        }
    }

    @PostMapping("/match-rule-with-mix-deed-content-batch")
    @Operation(summary = "query apres by mix match rules")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun requireApREDeedResultByMixApREDeedContentBatch(
        @RequestBody @Validated mixApREDeedDOList: List<MixApREDeedDO>,
        @ApiParam("是否为Serverless") @RequestParam(required = false) serverless: Boolean? = false,
        @ApiParam("Serverless基座应用名") @RequestParam(required = false) serverlessBaseAppName: String?
    ): AquamanResult<List<MixApREDeedResultVBeta>> {
        return appRuntimeEnvironmentService.requireApREDeedResultByMixApREDeedBatch(mixApREDeedDOList.map {
            if (serverless != null && serverless) {
                it.declarations.add(
                    Declaration(
                        matchApRELabels = listOf(
                            MatchApRELabel(
                                name = APRE_LABEL_FEATURE_NAME,
                                value = RUNTIME_TEMPLATE_PREFIX + checkNotNull(serverlessBaseAppName),
                                matchApREFeatureSpecs = listOf(
                                    MatchApREFeatureSpec(
                                        specType = scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(
                                            it.identityInfo.appName,
                                            it.identityInfo.nodeGroup,
                                            serverlessBaseAppName
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            }
            it
        }).let { apREDeedResultList ->
            AquamanResult.ok(
                apREDeedResultList.map {
                    MixApREDeedResultVBeta(
                        it.deedDO.identityInfo!!,
                        it.deedDO.declarations!!,
                        it.matchDeclarations
                    )
                }
            )
        }
    }

    @PostMapping("/create")
    @Operation(summary = "create an app runtime environment")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREBaseDetails(@RequestBody @Validated apRECreateReqDto: ApRECreateReqDto): AquamanResult<ApREDO> {
        return appRuntimeEnvironmentService.createApREBaseDetails(apRECreateReqDto).let {
            AquamanResult.ok(it)
        }
    }

    @GetMapping("/get")
    @Operation(summary = "get an app runtime environment")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getApREWithDetails(@RequestParam runtimeEnvKey: String): AquamanResult<ApREDO?> {
        return appRuntimeEnvironmentService.findApREDetailByKey(runtimeEnvKey).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/createApREIgnoreWhileExistWithLabel")
    @Operation(summary = "create an app runtime environment")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREIgnoreWhileExistWithLabel(@RequestBody apRECreateReqDto: ApRECreateReqDto): AquamanResult<ApREDO> {
        return appRuntimeEnvironmentService.createApREBaseDetails(apRECreateReqDto).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/createApREAndRelativeObject")
    @Operation(summary = "create apRE and relative object")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREAndRelativeObjects(
        @RequestBody apREAndRelativeCreateReqDto: ApREAndRelativeCreateReqDto
    ): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.createApREAndRelativeObjects(apREAndRelativeCreateReqDto)
        return AquamanResult.ok(true)
    }

    @PostMapping("/updateApREAndRelativeObject")
    @Operation(summary = "update apRE and relative object, all params are final state!")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREAndRelativeObjects(
        @RequestBody apREAndRelativeUpdateReqDto: ApREAndRelativeUpdateReqDto
    ): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.updateApREAndRelativeObjects(apREAndRelativeUpdateReqDto)
        return AquamanResult.ok(true)
    }

    @PutMapping("/update")
    @Operation(summary = "update apRE and it labels, all params are final state!")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREBaseDetails(
        @RequestBody apREBaseDetailsUpdateReqDto: ApREBaseDetailsUpdateReqDto
    ): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.updateApREBaseDetails(apREBaseDetailsUpdateReqDto)
        return AquamanResult.ok(true)
    }

    @PutMapping("/properties")
    @Operation(summary = "update apRE properties!")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateApREBaseDetails(
        @RequestBody apREPropertiesUpdateReqDto: ApREPropertiesUpdateReqDto
    ): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.updateApREBaseProperties(
            runtimeEnvKey = apREPropertiesUpdateReqDto.runtimeEnvKey,
            name = apREPropertiesUpdateReqDto.name,
            status = apREPropertiesUpdateReqDto.status,
            modifier = apREPropertiesUpdateReqDto.modifier
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/listByProperties")
    @Operation(summary = "list apRE details with properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listApREDetailsByProperties(
        @RequestParam(required = false) unit: String?,
        @RequestParam(required = false) site: String?,
        @RequestParam(required = false) stage: String?,
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) keyWords: String?,
        @RequestParam(required = true) pageSize: Int,
        @RequestParam(required = true) pageNumber: Int,
    ): AquamanResult<PageData<ApREDO>> {
        return AquamanResult.ok(
            appRuntimeEnvironmentService.listApREDetailsByProperties(
                unit = unit, site = site, stage = stage, status = status,
                keyWords = keyWords, pageNumber = pageNumber, pageSize = pageSize
            )
        )
    }

    @GetMapping("/listByUnitAndStageAndSite")
    @Operation(summary = "get an app runtime environment")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listApREWithDetailsByUnitAndStageAndSite(
        @RequestParam unit: String,
        @RequestParam stage: String,
        @RequestParam site: String
    ): AquamanResult<List<ApREDO>> {
        return appRuntimeEnvironmentService.listApREDetailsBySiteAndUnitAndStage(
            unit = unit, stage = stage, site = site
        ).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/listApREByMetadataConstraint")
    @Operation(summary = "list runtime environment by metadata constraint")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listApREByMetadataConstraint(
        @RequestParam region: String,
        @RequestParam az: String,
        @RequestParam stage: String,
        @RequestParam unit: String
    ): AquamanResult<List<ApREDO>> {
        return AquamanResult.ok(appRuntimeEnvironmentService.listApREByMetadataConstraint(region, az, stage, unit))
    }

    @GetMapping("/del")
    @Operation(summary = "del an app runtime environment by runtime env key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun delAppRuntimeEnvironmentByRuntimeEnvKey(@RequestParam runtimeEnvKey: String): AquamanResult<Boolean> {
        appRuntimeEnvironmentService.delAppRuntimeEnvironmentByRuntimeEnvKey(runtimeEnvKey)
        return AquamanResult.ok(true)
    }

    @PostMapping("/createApREFeatureIgnoreWhileExist")
    @Operation(summary = "create an default feature")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApREFeatureIgnoreWhileExistWithFeatureSpec(@RequestBody apREDefaultFeatureDO: ApREDefaultFeatureDO): AquamanResult<ApREDefaultFeatureDO> {
        return apREDefaultFeatureService.createApREFeatureIgnoreWhileExistWithFeatureSpec(apREDefaultFeatureDO).let {
            AquamanResult.ok(it)
        }
    }

    @GetMapping("/apRELabel/queryWithFeatureSpec")
    @Operation(summary = "query ApRELabel with feature specs")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getApRELabelWithFeatureSpec(targetType: String, targetKey: String): AquamanResult<List<ApRELabelDO>> {
        return apRELabelService.findApRELabelByTarget(targetKey, targetType).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/apRELabel/createIgnoreWhileExistWithFeatureSpec")
    @Operation(summary = "create ApRELabel with feature specs, if feature spec is exist, ignore it")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createApRELabelIgnoreWhileExistWithFeature(@RequestBody apRELabelCreateReqDto: ApRELabelCreateReqDto): AquamanResult<ApRELabelDO> {
        return apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(apRELabelCreateReqDto).let {
            AquamanResult.ok(it)
        }
    }

    @DeleteMapping("/apRELabel/deleteByTargetKeyAndTargetType")
    @Operation(summary = "delete ApRELabel with tatrget type and target key ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteApRELabelByTargetKeyAndTargetType(
        @RequestParam targetKey: String,
        @RequestParam targetType: String,
        @RequestParam type: ApRELabelType
    ): AquamanResult<Boolean> {
        return apRELabelService.deleteApRELabelByTargetAndType(
            targetKey = targetKey,
            targetType = targetType,
            type = type
        ).let {
            AquamanResult.ok(true)
        }
    }

    @DeleteMapping("/apRELabel/deleteByApRELabelKey")
    @Operation(summary = "delete ApRELabel with target type and target key ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteApRELabelByApRELabelKey(
        @RequestParam apRELabelKey: String
    ): AquamanResult<Boolean> {
        return apRELabelService.deleteApRELabelByApRELabelKey(apRELabelKey).let {
            AquamanResult.ok(true)
        }
    }

    @DeleteMapping("/apRELabel/spec/deleteByLabelKeyAndSpecCode")
    @Operation(summary = "删除ApRE标签规格")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteApRELabelSpecByLabelKeyAndSpecCode(
        @RequestParam apRELabelKey: String,
        @RequestParam specCode: String
    ): AquamanResult<Boolean> {
        return apREFeatureSpecService.deleteByLabelKeyAndSpecCode(labelKey = apRELabelKey, specCode = specCode).let {
            AquamanResult.ok(true)
        }
    }

    @PostMapping("/apREFeatureSpec/createOrUpdateWhileExist")
    @Operation(summary = "create feature specs, if it has existed, update it")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeatureSpecUpdateWhileExist(@RequestBody apREFeatureSpecCreateReqDto: ApREFeatureSpecCreateReqDto): AquamanResult<ApREFeatureSpecDO> {
        return apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(apREFeatureSpecCreateReqDto).let {
            AquamanResult.ok(it)
        }
    }



    //新的路径已经收录在了/apis/metadata/v4/envLevelStageMapping/listAll 此路径在二期即将下线
    @GetMapping("/listStageByEnvLevel")
    @Operation(summary = "list stage by envLevel")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listStageByEnvLevel(@RequestParam envLevel: String): AquamanResult<List<String>> {
        return AquamanResult.ok(envLevelStageMappingService.listStageByEnvLevel(envLevel))
    }

    @PostMapping("/labelDefinition")
    @Operation(summary = "label definition create")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createLabelDefinition(
        @RequestBody apRELabelDefinition: ApRELabelDefinition
    ): AquamanResult<Boolean> {
        return AquamanResult.ok(apRELabelDefineService.createApRELabelDefinition(apRELabelDefinition))
    }

    @PutMapping("/labelDefinition")
    @Operation(summary = "label definition upate")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateLabelDefinition(
        @RequestBody apRELabelDefinition: ApRELabelDefinition
    ): AquamanResult<Boolean> {
        return AquamanResult.ok(apRELabelDefineService.updateApRELabelDefinition(apRELabelDefinition))
    }

    @DeleteMapping("/labelDefinition")
    @Operation(summary = "label definition delete")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteLabelDefinition(
        @RequestParam(required = true) apRELabelDefinitionId: String,
        @RequestParam(required = true) modifier: String
    ): AquamanResult<Boolean> {
        apRELabelDefineService.deleteByApRELabelDefinitionId(apRELabelDefinitionId, modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/labelDefinition/list")
    @Operation(summary = "label definition list by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listLabelDefinition(
        @RequestParam(required = false) title: String?,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) value: String?,
        @RequestParam(required = true) pageSize: Int,
        @RequestParam(required = true) pageNumber: Int,
    ): AquamanResult<PageData<ApRELabelDefinition>> {
        return AquamanResult.ok(
            apRELabelDefineService.listApRELabelDefinitionsByProperties(
                title, name, value, pageSize, pageNumber
            )
        )
    }

    @GetMapping("/labelDefinition/find")
    @Operation(summary = "label definition list by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getLabelDefinitionByApRELabelDefKey(
        @RequestParam(required = true) apRELabelDefinitionId: String,
    ): AquamanResult<ApRELabelDefinition?> {
        apRELabelDefineService.findApRELabelDefinitionsByApRELabelDefKey(apRELabelDefinitionId).let {
            return AquamanResult.ok(it)
        }
    }

    @PostMapping("/featureSpecDefinition")
    @Operation(summary = "label definition create")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createFeatureSpecDefinition(
        @RequestBody featureSpecDefinitionCreateReq: FeatureSpecDefinitionCreateReq
    ): AquamanResult<Boolean> {
        apREFeatureSpecDefinitionService.createApREFeatureSpecDefinition(featureSpecDefinitionCreateReq)
        return AquamanResult.ok(true)
    }

    @GetMapping("/featureSpecDefinition")
    @Operation(summary = "find label definition by feature label definition id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findFeatureSpecDefinitionById(
        @RequestParam featureSpecDefinitionId: String
    ): AquamanResult<FeatureSpecDefinition?> {
        return AquamanResult.ok(apREFeatureSpecDefinitionService.findByFeatureSpecDefinitionId(featureSpecDefinitionId))
    }

    @PostMapping("/featureSpecDefinition/listByProperties")
    @Operation(summary = "list label spec definition by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listFeatureSpecDefinitionByProperties(
        @RequestBody featureSpecConditionQueryReq: FeatureSpecConditionQueryReq
    ): AquamanResult<PageData<FeatureSpecDefinition>> {
        return AquamanResult.ok(
            apREFeatureSpecDefinitionService.listByProperties(
                pageSize = featureSpecConditionQueryReq.pageSize,
                pageNumber = featureSpecConditionQueryReq.pageNumber,
                title = featureSpecConditionQueryReq.title,
                specCode = featureSpecConditionQueryReq.specCode,
                scope = featureSpecConditionQueryReq.scope,
                status = featureSpecConditionQueryReq.status,
                refKey = featureSpecConditionQueryReq.refKey
            )
        )
    }

    @PutMapping("/featureSpecDefinition")
    @Operation(summary = "label spec definition update")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateFeatureSpecDefinitionById(
        @RequestBody featureSpecDefinitionUpdateReq: FeatureSpecDefinitionUpdateReq
    ): AquamanResult<Boolean> {
        apREFeatureSpecDefinitionService.updateByFeatureSpecDefinitionId(
            featureSpecDefinitionUpdateReq
        )
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/featureSpecDefinition/deleteByDefinitionId")
    @Operation(summary = "label spec definition delete by featureSpecKey")
    fun deleteFeatureSpecDefinitionByDefinitionId(
        @RequestParam featureSpecDefinitionId: String, @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        apREFeatureSpecDefinitionService.deleteApREFeatureSpecDefinitionByFeatureSpecDefinitionId(
            featureSpecDefinitionId, modifier
        )
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/featureSpecDefinition/deleteByRefKey")
    @Operation(summary = "label definition delete by ref key")
    fun deleteFeatureSpecDefinitionByRefKey(
        @RequestParam refKey: String, @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        apREFeatureSpecDefinitionService.deleteApREFeatureSpecDefinitionByRefKey(
            refKey, modifier
        )
        return AquamanResult.ok(true)
    }

    @PostMapping("/serverless/list")
    @Operation(summary = "list serverless resource by properties")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listServerlessBaseApp(
        @RequestParam pageSize: Int,
        @RequestParam pageNumber: Int,
        @RequestParam(required = false) target: String?
    ): AquamanResult<PageData<String>> {
        return AquamanResult.ok(
            apRELabelService.findServerlessBaseApp(
                pageSize = pageSize,
                pageNumber = pageNumber,
                target = target
            )
        )
    }
}