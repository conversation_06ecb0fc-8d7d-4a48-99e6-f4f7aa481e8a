package com.alibaba.koastline.multiclusters.api.v1.virtualenv

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.model.ClusterInstanceDto
import com.alibaba.koastline.multiclusters.appenv.model.ClusterMetaData
import com.alibaba.koastline.multiclusters.appenv.model.ClusterProfile
import com.alibaba.koastline.multiclusters.appenv.model.GatewayConfig
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.appenv.params.ManagedClusterWithEnvironmentsTemplateSpec
import com.alibaba.koastline.multiclusters.common.exceptions.FunctionDeprecatedException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.AuthTool
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * 集群元数据管理API
 * 其中ClusterProfile为原koastline模型中注册原生k8s集群信息并捎带k8s api server 等完整集群部署通信的全部信息
 * 在后续融合后 集群管理信息将简化为ClusterProfileData元数据
 * 新构建的元数据采用ClusterProfileData进行录入及管理
 * 除注册修改API分别采用新的接口 查询删除采用公用接口
 */
@RestController
@RequestMapping("/apis/multiclusters/cluster-manage", produces = [MediaType.APPLICATION_JSON_VALUE])
class ClusterManagementApi {

    val log by logger()

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var defaultClusterEnvironmentService: DefaultClusterEnvironmentService

    /**
     * koastline 模型中注册集群数据接口
     */
    @PostMapping("/cluster")
    @Operation(summary = "register a native k8s cluster information")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registerClusterProfile(@RequestBody clusterProfile: ClusterProfile): AquamanResult<Boolean> {
        return try {
            log.info("${TraceUtils.getTraceId()}, register cluster $clusterProfile")
            defaultClusterService.registerClusterProfile(clusterProfile).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    /**
     * koastline 模型中修改集群数据接口
     */
    @PutMapping("/cluster")
    @Operation(summary = "update a native k8s cluster information")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateClusterProfile(@RequestBody clusterProfile: ClusterProfile): AquamanResult<Boolean> {
        return try {
            log.info("${TraceUtils.getTraceId()}, update cluster $clusterProfile")
            defaultClusterService.updateClusterProfile(clusterProfile).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    /**
     * 简化模型中注册集群数据接口
     */
    @PostMapping("/clusterProfileData")
    @Operation(summary = "register a cluster information")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registerClusterProfileData(@RequestBody clusterInstanceDto: ClusterInstanceDto): AquamanResult<Boolean> {
        return try {
            log.info("${TraceUtils.getTraceId()}, register cluster $clusterInstanceDto")
            defaultClusterService.registerSimpleClusterProfileData(clusterInstanceDto).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    /**
     * 简化模型中修改集群数据接口
     */
    @PutMapping("/clusterProfileData")
    @Operation(summary = "update a cluster information")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateClusterProfileData(@RequestBody clusterInstanceDto: ClusterInstanceDto): AquamanResult<Boolean> {
        return try {
            log.info("${TraceUtils.getTraceId()}, update cluster $clusterInstanceDto")
            defaultClusterService.updateSimpleClusterProfileData(clusterInstanceDto).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("/clusters")
    @Operation(summary = "query clusters by its properties, but you should have more than 1 condition, clusterId&clusterName using prefix match method")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listClustersByProperties(
        @RequestParam("clusterId", required = false) clusterId: String?,
        @RequestParam("clusterName" , required = false) clusterName: String?,
        @RequestParam("clusterProvider", required = false) clusterProvider: String?,
        @RequestParam("clusterType", required = false) clusterType: String?,
        @RequestParam("region", required = false) region: String?,
        @RequestParam("status", required = false) status: String?,
        @RequestParam("pageSize", required = true) pageSize: Int,
        @RequestParam("pageNumber", required = true) pageNumber: Int
    ): AquamanResult<PageData<ClusterProfileData>> {
        return try {
            defaultClusterService.listClustersByProperties(
                clusterId,clusterName,clusterProvider,clusterType,region,status,
                pageSize, pageNumber
            ).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @GetMapping("/cluster/cluster-id/{clusterId}")
    @Operation(summary = "query a k8s cluster by its cluster id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClusterByClusterId(@PathVariable("clusterId") clusterId: String): AquamanResult<ClusterProfile?> {
        throw FunctionDeprecatedException()
    }

    @GetMapping("/cluster/cluster-name/{clusterName}")
    @Operation(summary = "query a k8s cluster by its cluster name")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryClusterByClusterName(@PathVariable("clusterName") clusterName: String): AquamanResult<ClusterProfile?> {
        throw FunctionDeprecatedException()
    }

    @DeleteMapping("/cluster/{clusterId}")
    @Operation(summary = "delete a k8s cluster by its cluster id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteClusterByClusterId(@PathVariable("clusterId") clusterId: String): AquamanResult<Boolean> {
        return try {
            log.info("${TraceUtils.getTraceId()}, delete cluster by id $clusterId")
            defaultClusterService.deleteClusterProfile(clusterId).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    @PostMapping("/managedcluster/{clusterId}/{namespace}")
    @Operation(summary = "create a new managed cluster inside a namespace provided by a underlay k8s cluster ")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createManagedCluster(@PathVariable("clusterId") clusterId: String, @PathVariable("namespace") namespace: String, @RequestBody clusterMetaData: ClusterMetaData): AquamanResult<ClusterProfile?> {
        return try {
            defaultClusterService.createManagedCluster(clusterId, namespace, clusterMetaData).let {
                AquamanResult.ok(it)
            }
        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }


    @GetMapping("/managedcluster/{clusterId}/{namespace}")
    @Operation(summary = "list koastline-managed clusters(eg. cse-system, cse-system-pre) based on a k8s cluster id and namespace(eg. cse-system)")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryManagedClustersByClusterIdAndNamespace(@PathVariable("clusterId") clusterId: String, @PathVariable("namespace") namespace: String): AquamanResult<ClusterProfile?> {
        throw FunctionDeprecatedException()
    }

    @PutMapping("/managedcluster/{clusterId}/{gatewayId}")
    @Operation(summary = "update gateway config for a certain cluster id")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun updateManagedClusterByClusterIdAndGatewayId(@PathVariable("clusterId") clusterId: String, @PathVariable("gatewayId") gatewayId: Long, @RequestBody clusterGatewayConfig: GatewayConfig): AquamanResult<Boolean> {
        throw FunctionDeprecatedException()
    }

    @DeleteMapping("/managedcluster/{clusterId}/{namespace}")
    @Operation(summary = "delete a koastline-managed cluster based on its underlay k8s cluster id and its namespace")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteManagedClustersByClusterIdAndNamespace(@PathVariable("clusterId") clusterId: String, @PathVariable("namespace") namespace: String): AquamanResult<Int?> {
        throw FunctionDeprecatedException()
    }

    @PostMapping("/managedcluster/cluster-environments")
    @Operation(summary = "create a managed cluster from scratch with cluster environment declared")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun registerManagedClusterWithEnvironmentsFromTemplate(@RequestBody managedClusterWithEnvironmentsTemplateSpec: ManagedClusterWithEnvironmentsTemplateSpec): AquamanResult<List<ClusterEnvironment?>> {
        val source = AuthTool.getAuth().run {
            if(this.userName == ADMIN) {
                KOASTLINE
            } else {
                this.userName
            }
        }
        return try {
            defaultClusterService.registerManagedClusterWithEnvironmentsFromTemplate(managedClusterWithEnvironmentsTemplateSpec).let {
                AquamanResult.ok(it)
            }

        } catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }

    companion object {
        const val ALIBABA_GROUP = "alibaba" // alibaba group
        const val ADMIN = "admin"
        const val KOASTLINE = "koastline"
    }
}