package com.alibaba.koastline.multiclusters

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController
import com.alibaba.koastline.multiclusters.common.logger
import java.io.File
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 */
@RestController
class MainController {
    private val newborn = "welcome to the world of aquaman"
    private val HEALTH_STATUS_SUCCESS = "success"
    private val HEALTH_STATUS_FAIL = "fail"
    private final val HEALTH_STATUS_FILE_PATH = "${System.getProperty("user.dir")}/htdocs/status.html"
    private val logger by logger()

    @GetMapping("/")
    fun root(): String {
        return newborn
    }

    /**
     * 健康检查，系统部署需要
     * 请不要删除！！
     */
    @GetMapping("/checkpreload.htm")
    fun checkPreload(): String {
        return HEALTH_STATUS_SUCCESS
    }

    @GetMapping("/status.html")
    fun checkStatus(response: HttpServletResponse): String {
        val file = File(HEALTH_STATUS_FILE_PATH)
        if (file.exists()) {
            return HEALTH_STATUS_SUCCESS
        }
        response.sendError(HttpServletResponse.SC_NOT_FOUND)
        return HEALTH_STATUS_FAIL
    }
}