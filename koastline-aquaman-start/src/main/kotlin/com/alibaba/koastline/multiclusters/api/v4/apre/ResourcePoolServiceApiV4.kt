package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ResourcePoolCreateOrUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/apre/v4/resourcePool", produces = [MediaType.APPLICATION_JSON_VALUE])
class ResourcePoolServiceApiV4 @Autowired constructor(
    val resourcePoolService: ResourcePoolService,
    val apRELabelService: ApRELabelService,
) {

    /**
     * 级联创建 ApRE label 和对应的 feature spec
     */
    @PostMapping("/createIgnoreWhileExistWithLabel")
    @Operation(summary = "create resource pool with labels and specs")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createResourcePoolIgnoreWhileExistWithLabel(@RequestBody resourcePoolCreateReqDto: ResourcePoolCreateOrUpdateReqDto): AquamanResult<ResourcePoolDO> {
        return resourcePoolService.createResourcePoolIgnoreWhileExistWithLabel(resourcePoolCreateReqDto).let {
            AquamanResult.ok(it)
        }
    }

    @PostMapping("/createWithClusterName")
    @Operation(summary = "create an resource pool with clusterName without creating label and spec")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createResourcePoolWithClusterName(@RequestParam managedClusterKey: String, @RequestParam clusterName: String, @RequestParam creator: String): AquamanResult<ResourcePoolDataDO>{
        return AquamanResult.ok(
            resourcePoolService.createResourcePoolWithClusterName(managedClusterKey, clusterName, creator)
        )
    }

    @PostMapping("/createWithClusterId")
    @Operation(summary = "create an resource pool with clusterId without creating label and spec")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createResourcePoolWithClusterId(@RequestParam managedClusterKey: String, @RequestParam clusterId: String, @RequestParam creator: String): AquamanResult<ResourcePoolDataDO>{
        return AquamanResult.ok(
            resourcePoolService.createResourcePoolWithClusterId(managedClusterKey, clusterId, creator)
        )
    }

    @PostMapping("/createNewLabel")
    @Operation(summary = "create resource pool  label")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createResourcePoolLabel(@RequestBody apRELabelCreateReqDto: ApRELabelCreateReqDto): AquamanResult<Boolean> {
        resourcePoolService.createResourcePoolLabel(apRELabelCreateReqDto)
        return AquamanResult.ok(true)
    }

    /**
     * 返回 resource pool 级联返回 resource pool 对应特性标签和特性规格
     */
    @GetMapping("/listByManagedClusterKey")
    @Operation(summary = "list resource pool details by managedClusterKey")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listByKManagedClusterKey(@RequestParam kManagedClusterKey: String): AquamanResult<List<ResourcePoolDO>> {
        return AquamanResult.ok(resourcePoolService.listByManagedClusterKey(kManagedClusterKey).let {
            it.map { resourcePoolDataDO ->
                ResourcePoolDO.assemble(
                    resourcePoolDataDO,
                    apRELabelService.findApRELabelByTarget(resourcePoolDataDO.resourcePoolKey, ApRELabelTargetTypeEnum.RESOURCE_POOL.name)
                )
            }
        })
    }

    /**
     * 返回 resource pool 级联返回 resource pool 对应特性标签和特性规格
     */
    @GetMapping("/listByClusterId")
    @Operation(summary = "list resource pool details by clusterId")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listByClusterId(@RequestParam clusterId: String): AquamanResult<List<ResourcePoolDO>> {
        return AquamanResult.ok(resourcePoolService.listByClusterId(clusterId).let {
            it.map { resourcePoolDataDO ->
                ResourcePoolDO.assemble(
                    resourcePoolDataDO,
                    apRELabelService.findApRELabelByTarget(resourcePoolDataDO.resourcePoolKey, ApRELabelTargetTypeEnum.RESOURCE_POOL.name)
                )
            }
        })
    }

    @DeleteMapping("/check/deleteWithLabelsByResourcePoolKey")
    @Operation(summary = "delete an resource pool by resource pool key")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteWithLabelsByResourcePoolKeyWhileCheck(@RequestParam resourcePoolKey: String, @RequestParam modifier: String): AquamanResult<Boolean>{
        resourcePoolService.deleteWithLabelsByResourcePoolKeyWhileCheck(resourcePoolKey, modifier)
        return AquamanResult.ok(true)
    }

    /**
     * TODO:需要添加级联删除 ApRE label 的能力
     */
    @DeleteMapping("/deleteByManagedClusterKeyAndClusterId")
    @Operation(summary = "delete an resource pool by managedClusterKey and clusterId")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteBykManagedClusterKeyAndClusterId(@RequestParam managedClusterKey: String,@RequestParam clusterId: String, @RequestParam modifier: String): AquamanResult<Boolean>{
        resourcePoolService.deleteByManagedClusterKeyAndClusterId(managedClusterKey, clusterId, modifier)
        return AquamanResult.ok(true)
    }

    @DeleteMapping("/apre-label")
    @Operation(summary = "delete resourcePool label")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteResourcePoolApRELabel(
        @RequestParam apRELabelKey: String,
        @RequestParam modifier: String
    ): AquamanResult<Boolean> {
        resourcePoolService.deleteResourcePoolApRELabelWithCheck(apRELabelKey, modifier)
        return AquamanResult.ok(true)
    }

    @GetMapping("/listRelativeClusters")
    @Operation(summary = "list all cluster list that a certain resource pool has be linked with the cluster")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','CLUSTER_SRE_RIGHT')")
    fun listRelativeClusters(): AquamanResult<List<String>>{
        return AquamanResult.ok(resourcePoolService.listRelativeClusters())
    }
}