package com.alibaba.koastline.multiclusters.api.v4.schedule

import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.schedule.service.schedule.SpeScheduleService
import io.swagger.annotations.ApiParam
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author:    <EMAIL>
 * @description:  调度辅助服务
 * @date:    2024/2/25 7:19 PM
 */

@RestController
@RequestMapping("/apis/schedule/assist/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class ScheduleAssistApiV4 {

    @Autowired
    lateinit var speScheduleService: SpeScheduleService

    @GetMapping("/spe/querySpeAppIps")
    @Operation(summary = "查询SPE发布资源IPList")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun querySpeAppIps(
        @ApiParam("应用名")@RequestParam appName: String,
        @ApiParam("参数URL")@RequestParam(required = false) paramUrl: String? = null
    ): AquamanResult<List<String>> {
        return AquamanResult.ok(
            speScheduleService.querySpeAppIps(
                appName = appName,
                paramUrl = paramUrl
            )
        )
    }
}