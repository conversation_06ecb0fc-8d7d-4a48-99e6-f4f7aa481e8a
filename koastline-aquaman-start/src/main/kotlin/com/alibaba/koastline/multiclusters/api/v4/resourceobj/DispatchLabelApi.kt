package com.alibaba.koastline.multiclusters.api.v4.resourceobj

import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValueWithMetadata
import com.alibaba.koastline.multiclusters.external.AtomApi
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourceobj.DispatchLabelService
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelSpecificValueDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelValueDTO
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(
    value = ["/api/config/dispatch", "/apis/config/dispatch"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class DispatchLabelApi {
    @Autowired
    lateinit var dispatchLabelService: DispatchLabelService

    @Autowired
    lateinit var commonProperties: CommonProperties

    @Autowired
    lateinit var atomApi: AtomApi

    @Autowired
    lateinit var userLabelBaseService: UserLabelBaseService

    @PostMapping("/saveLabel")
    @Operation(summary = "新建或保存调度路由标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateLabel(
        @RequestBody dispatchLabelDTO: DispatchLabelDTO,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        dispatchLabelService.createOrUpdateDispatchLabel(dispatchLabelDTO, modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/syncLabel")
    @Operation(summary = "同步标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun syncLabel(
        @RequestBody configDispatchLabel: DispatchLabelDTO,
        @RequestParam("id") id: Long
    ): AquamanResult<Boolean> {
        dispatchLabelService.syncLabel(configDispatchLabel, id)
        return AquamanResult.ok(true)
    }

    @PostMapping("/syncLabelValue")
    @Operation(summary = "同步标签取值")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun syncLabelValue(
        @RequestBody configDispatchLabelValue: DispatchLabelValueDTO,
        @RequestParam("id") id: Long
    ): AquamanResult<Boolean> {
        dispatchLabelService.syncLabelValue(configDispatchLabelValue, id)
        return AquamanResult.ok(true)
    }

    @PostMapping("/label/saveLabelSpecificValue")
    @Operation(summary = "保存调度路由标签取值范围")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun saveLabelSpecificValue(
        @RequestBody configDispatchLabelSpecificValueDTO: DispatchLabelSpecificValueDTO,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        dispatchLabelService.updateSpecifiedValueOfLabelByCode(configDispatchLabelSpecificValueDTO, modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/deleteLabel")
    @Operation(summary = "删除调度路由标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteLabelByCode(
        @RequestParam("code") code: String,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        dispatchLabelService.deleteLabelByCode(code, modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/saveLabelValue")
    @Operation(summary = "保存调度路由标签取值")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createOrUpdateLabelValue(
        @RequestBody dispatchLabelValueDTO: DispatchLabelValueDTO,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        dispatchLabelService.createOrUpdateLabelValue(dispatchLabelValueDTO, modifier)
        return AquamanResult.ok(true)
    }

    @PostMapping("/deletedLabelValue")
    @Operation(summary = "删除调度路由标签取值")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deletedLabelValueById(
        @RequestBody dispatchLabelValueDTO: DispatchLabelValueDTO,
        @RequestParam("modifier") modifier: String
    ): AquamanResult<Boolean> {
        dispatchLabelService.deleteLabelValueByUK(dispatchLabelValueDTO, modifier)
        return AquamanResult.ok(true)
    }

    @GetMapping("/getSigmaConfigMap")
    @Operation(summary = "获取合并后的调度路由标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getSigmaConfigMap(
        @RequestParam("appName") appName: String,
        @RequestParam(value = "groupName", required = false) groupName: String?,
        @RequestParam(value = "idc", required = false) idc: String?,
        @RequestParam(value = "unit", required = false) unit: String?,
        @RequestParam(value = "env", required = false) env: String?,
    ): AquamanResult<String> {
        return AquamanResult.ok(
            dispatchLabelService.getSigmaConfigMap(
                appName, groupName, idc, unit, env
            )
        )
    }

    @GetMapping("/getLabelValue")
    @Operation(summary = "获取不合并的调度路由标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun getLabelValue(
        @RequestParam("appName") appName: String,
        @RequestParam(value = "groupName", required = false) groupName: String?,
        @RequestParam(value = "idc", required = false) idc: String?,
        @RequestParam(value = "unit", required = false) unit: String?,
        @RequestParam(value = "env", required = false) env: String?,
    ): AquamanResult<List<ConfigDispatchLabelValueWithMetadata>> {
        return AquamanResult.ok(
            dispatchLabelService.getLabelValue(
                appName, groupName, idc, unit, env
            )
        )
    }

    @GetMapping("/queryAppLabel")
    @Operation(summary = "获取Atom应用标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryAppLabel(
        @RequestParam("appName") appName: String,
    ): AquamanResult<List<Map<String, Any>>> {

        return AquamanResult.ok(
            userLabelBaseService.queryAppLabel(appName)
        )


    }

    @GetMapping("/queryStrategy")
    @Operation(summary = "获取Atom应用标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryStrategy(
        @RequestParam("strategyParam") strategyParam: String,
    ): AquamanResult<StrategiesResultVO> {

        return AquamanResult.ok(
            userLabelBaseService.queryStrategy(strategyParam)
        )

    }


}