package com.alibaba.koastline.multiclusters.api.v3.virtualenv

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.config.RegionPropertiesConfig
import com.alibaba.koastline.multiclusters.models.AquamanResult
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/multiclusters/cluster-environments/v3", produces = [MediaType.APPLICATION_JSON_VALUE])
class ClusterEnvironmentApiV3 {
    val log by logger()

    @Autowired
    lateinit var defaultClusterEnvironmentService: DefaultClusterEnvironmentService

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var regionPropertiesConfig: RegionPropertiesConfig

    @GetMapping("/match-rule")
    @Operation(summary = "query all cluster environments by match rules")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun queryByMatchRuleReal(
        @RequestParam("envLevel") envLevel: String,
        @RequestParam("externalId") externalId: String? = null,
        @RequestParam("externalType") externalType: String,
        @RequestParam("includeEnvType") includeEnvType: String,
        @RequestParam("region") region: String? = null,
        @RequestParam("az") az: String? = null,
        @RequestParam("stage") stage: String? = null,
        @RequestParam("unit") unit: String? = null,
        @RequestParam("roleName") roleName: String? = null,
    ): AquamanResult<List<ClusterEnvironment?>?> {
        return try{
            val result = defaultClusterEnvironmentService.queryByMatchRuleV3(
                envLevel,
                externalId,
                externalType,
                includeEnvType,
                region,
                az,
                stage,
                unit,
                roleName
            )
            log.info("region properties config ${regionPropertiesConfig.regionProperties}")
            for (item in result) {
                item?.apply {
                    if (!this.envTags.containsKey("regionName")) {
                        val regionName = regionPropertiesConfig.regionProperties.regions[this.region] ?: ""
                        this.envTags["regionName"] = regionName
                    }
                    if (!this.envTags.containsKey("azName")) {
                        val azName = regionPropertiesConfig.regionProperties.az[this.az] ?: "可用区${this.az}"
                        this.envTags["azName"] = azName
                    }
                }
            }

            log.info("${TraceUtils.getTraceId()}, cluster environment list $result")
            AquamanResult.ok(result)
        }catch (e: Exception) {
            AquamanResult.fail(e.message)
        }
    }
}