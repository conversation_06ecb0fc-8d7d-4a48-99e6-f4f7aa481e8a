package com.alibaba.koastline.multiclusters.api.rest

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpMethod.DELETE
import org.springframework.http.HttpMethod.GET
import org.springframework.http.HttpMethod.OPTIONS
import org.springframework.http.HttpMethod.POST
import org.springframework.http.HttpMethod.PUT
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 */
@Component
class HttpInvokeInterceptor : HandlerInterceptor {

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        val uri = request.requestURL
        val method = request.method
        val query = request.queryString
        DEBUG_LOGGER.info(
                "before method=${method}, url=${uri}, query=$query, remoteIp:${request.remoteHost}"
        )
        return super.preHandle(request, response, handler)
    }

    override fun afterCompletion(
            request: HttpServletRequest,
            response: HttpServletResponse,
            handler: Any,
            ex: Exception?
    ) {
        try {
            val uri = request.requestURL
            val method = request.method
            val query = request.queryString
            DEBUG_LOGGER.info(
                    "after method=${method}, url=${uri}, query=$query, status=${response.status}, remoteIp:${request.remoteHost}"
            )
            super.afterCompletion(request, response, handler, ex)
        } catch (ex: Throwable) {
            LOGGER.error("HttpInvokeInterceptor", ex)
        }
    }

    companion object {
        val DEBUG_LOGGER: Logger = LoggerFactory.getLogger("DEBUG_LOGGER")
        val LOGGER: Logger = LoggerFactory.getLogger(HttpInvokeInterceptor::class.java)
    }
}

//@Component
class HttpInvokerInterceptorAppConfig : WebMvcConfigurer {

    @Autowired
    lateinit var httpInvokeInterceptor: HttpInvokeInterceptor

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(httpInvokeInterceptor)
    }
}
