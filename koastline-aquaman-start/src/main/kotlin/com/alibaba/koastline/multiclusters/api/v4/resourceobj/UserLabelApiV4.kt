package com.alibaba.koastline.multiclusters.api.v4.resourceobj

import com.alibaba.koastline.multiclusters.api.v4.resourceobj.dto.BatchQueryByMultiExternalAndLabelReq
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.resourceobj.UserLabel
import com.alibaba.koastline.multiclusters.models.AquamanResult
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apis/user/label/v4", produces = [MediaType.APPLICATION_JSON_VALUE])
class UserLabelApiV4 {
    @Autowired
    lateinit var userLabelService: UserLabelService
    @Autowired
    lateinit var userLabelBaseService: UserLabelBaseService

    @PostMapping("/createAndOverrideWhileExist")
    @Operation(summary = "创建标签[存在则覆盖]")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun createAndOverrideWhileExist(@RequestBody userLabelCreateReqDto: UserLabelCreateReqDto): AquamanResult<Boolean> {
        userLabelService.createAndOverrideWhileExist(userLabelCreateReqDto)
        return AquamanResult.ok(true)
    }

    @GetMapping("/findByExternalAndLabel")
    @Operation(summary = "查询标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findByExternalAndLabel(@RequestParam("externalId") externalId: String, @RequestParam("externalType") externalType: UserLabelExternalType, @RequestParam("labelName") labelName: String): AquamanResult<UserLabel?> {
        return AquamanResult.ok(userLabelBaseService.findByExternalAndLabelWithCache(externalId = externalId , externalType = externalType.name, labelName = labelName))
    }

    @GetMapping("/findByExternalTypeAndName")
    @Operation(summary = "查询所有标签(指定externalType和labelName)")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findByExternalTypeAndName(
        @RequestParam("externalType") externalType: UserLabelExternalType,
        @RequestParam("labelName") labelName: String,
        @RequestParam("pageNumber") pageNumber: Int,
        @RequestParam("pageSize") pageSize: Int,
        @RequestParam(required = false, value = "nextToken") nextToken: String?,
        @RequestParam(required = false, value = "externalId") externalId: String?,
        @RequestParam(required = false, value = "labelValue") labelValue: String?
    ): AquamanResult<PageData<UserLabel>> {
        require(pageNumber > 0) { "pageNumber must great than 0" }
        require(pageSize in 1..1000) { "pageSize must in range [0, 1000]" }

        return AquamanResult.ok(userLabelBaseService.findByExternalTypeAndName(
            externalType = externalType.name, labelName = labelName, pageNumber = pageNumber, pageSize = pageSize, nextToken = nextToken, externalId = externalId, labelValue = labelValue)
        )
    }

    @GetMapping("/findDistinctValueByExternalTypeAndName")
    @Operation(summary = "查询Distinct标签值(指定externalType和labelName)")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun findDistinctValueByExternalTypeAndName(
        @RequestParam("externalType") externalType: UserLabelExternalType,
        @RequestParam("labelName") labelName: String,
    ): AquamanResult<List<String>> {
        return AquamanResult.ok(userLabelBaseService.findDistinctValueByExternalTypeAndName(
            externalType = externalType.name, labelName = labelName)
        )
    }

    @PostMapping("/batchQueryByMultiExternalAndLabel")
    @Operation(summary = "批量查询标签(多ExternalId)")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchQueryByMultiExternalAndLabel(@RequestParam("externalIdList") externalIdList: List<String>, @RequestParam("externalType") externalType: UserLabelExternalType, @RequestParam("labelName") labelName: String): AquamanResult<List<UserLabel>> {
        return AquamanResult.ok(userLabelBaseService.batchQueryByMultiExternalAndLabel(
            externalIdList = externalIdList,
            externalType = externalType.name,
            labelName = labelName
        ))
    }

    @PostMapping("/batchQueryByMultiExternalAndLabelWithBody")
    @Operation(summary = "批量查询标签(多ExternalId, body入参)")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun batchQueryByMultiExternalAndLabelWithBody(@RequestBody req: BatchQueryByMultiExternalAndLabelReq): AquamanResult<List<UserLabel>> {
        require(req.externalIdList.isNotEmpty()) { "batch query label, externalId must be non-empty" }
        return AquamanResult.ok(userLabelBaseService.batchQueryByMultiExternalAndLabel(
            externalIdList = req.externalIdList,
            externalType = req.externalType.name,
            labelName = req.labelName,
            labelValue = req.labelValue
        ))
    }

    @DeleteMapping("/deleteByExternalAndLabel")
    @Operation(summary = "删除标签")
    @PreAuthorize("hasAnyAuthority('ADMIN_ROOT_RIGHT','DEVOPS_SYSTEM_RIGHT','CLUSTER_SRE_RIGHT')")
    fun deleteByExternalAndLabel(@RequestParam("externalId") externalId: String, @RequestParam("externalType") externalType: UserLabelExternalType, @RequestParam("labelName") labelName: String, @RequestParam("modifier") modifier: String): AquamanResult<Boolean> {
        userLabelBaseService.deleteByExternalAndLabelWithCache(externalId = externalId , externalType = externalType.name, labelName = labelName, modifier = modifier)
        return AquamanResult.ok(true)
    }
}