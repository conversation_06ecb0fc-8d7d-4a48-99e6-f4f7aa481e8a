package com.alibaba.koastline.multiclusters.api.v4.schedule.dto

import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModelProperty

/**
 * @author:    <EMAIL>
 * @date:    2023/12/4 3:22 PM
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class PodListScheduleRequest(
    @ApiModelProperty("应用名") val appName: String,
    @ApiModelProperty("环境StackId") val envStackId: String? = null,
    @ApiModelProperty("分组名") val resourceGroup: String? = null,
    @ApiModelProperty("sn列表") val snList: List<String> = emptyList(),
    @ApiModelProperty("调度类型") val scheduleEnvType : ScheduleEnvType? = null
) {
    fun validate(){
        if (envStackId.isNullOrBlank() && resourceGroup.isNullOrBlank() && snList.isEmpty()) {
            throw ScheduleException("PodListScheduleRequest,envStackId/resourceGroup/snList 三者不能同时为空.")
        }
    }
}