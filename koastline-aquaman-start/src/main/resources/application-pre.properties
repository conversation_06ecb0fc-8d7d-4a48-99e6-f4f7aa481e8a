# tddl configuration.
spring.datasource.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.url = *******************************************************************************************

# data source
asiops.endpoint=asiops.alibaba-inc.com
asiops.accesskey=cbf9a58a-38d7-474c-bb2a-b34b0db4576b
asiops.accesssecret=f9504665-4825-4f3a-b83e-c42487495931

# redis service
redis.aquaman.host=r-8vb8926684a20fc4.redis.zhangbei.rds.aliyuncs.com
redis.aquaman.port=6379

deploymentApi.host = https://deployapipre.alibaba-inc.com
deploymentApi.app-key = aquaman
deploymentApi.app-sec = F3MO5mDMkgtom1cREEtVWpRQ8EL2mPZG

fedConfig.host=http://pre.fed-api.alibaba-inc.com:7100

# hcrm
hcrm.host = https://pre-grop.alibaba-inc.com
hcrm.account = normandy
hcrm.access.key = Y^PPw5NxZ2fFxpz5

# env center
env.center.host=https://pre-env-center.alibaba-inc.com