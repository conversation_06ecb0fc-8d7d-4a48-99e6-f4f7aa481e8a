project.name=koastline-aquaman

management.endpoints.web.exposure.include=*

# httpæå¡å¨ç«¯å£
server.port=7001

# endpointéç½®
management.server.port=7002

# alibaba spring boot necessary properties.
spring.application.name = koastline-aquaman
spring.application.group = com.alibaba.koastline.multiclusters

# spring profile
spring.profiles.active=dev
## h2 database
#spring.datasource.url=jdbc:h2:mem:localhost;DB_CLOSE_ON_EXIT=FALSE
#spring.datasource.username=admin
#spring.datasource.password=
#spring.datasource.driver-class-name=org.h2.Driver

# tddl configuration.
spring.datasource.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.url = *******************************************************************************************

spring.main.allow-bean-definition-overriding=true

# Spring Security CSRFéç½®ï¼è¯¦è§ https://lark.alipay.com/sp/securityjar/csrf-token-validation
# æ¯æHTTP è¯·æ±æ¹æ³ï¼å¤å¼ä½¿ç¨éå·åéï¼é»è®¤å¼:"POST"
spring.security.csrf.supportedMethods = POST,PUT
# æ ¡éªè¯·æ±URLæ¨¡å¼é£æ ¼ï¼å¯ç¨å¼:"ant"å"regex"ï¼é»è®¤å¼ä¸º:"ant"
spring.security.csrf.url.style = regex
spring.security.csrf.url.included = /.*?
spring.security.csrf.url.excluded = ^/csrf/nocheck

# Spring Security XSSéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-security-xss
spring.security.xss.enabled=true
spring.security.xss.ignored.files=security/xss/ignored.vm
spring.security.xss.ignored.context.names=ignoredName

# Spring Security JSONPéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-security-jsonp
spring.security.jsonp.enabled=true
spring.security.jsonp.queryParamNames=callback,_callback

# Spring Security HTTP å®å¨åååè¡¨ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-security-http
spring.security.http.safe.domains=\
  *.alibaba-inc.com,\
  .alibaba-inc.net,\
  localhost
spring.security.filters.redirect-validation.enabled=true
spring.security.filters.referer-validation.enabled=true
spring.security.filters.referer-validation.url.style=regex
spring.security.filters.referer-validation.urls=^/jsonp/.*?

# data source
asiops.endpoint=http://asiops.alibaba.net
asiops.accesskey=8c9d9c71-0523-4838-a4c3-a9f0dee54775
asiops.accesssecret=f9504665-4825-4f3a-b83e-c42487495931

# redis service
redis.aquaman.host=r-8vb23b581aba82c4.redis.zhangbei.rds.aliyuncs.com
redis.aquaman.port=6379

# skyline service
skyline.host = sky.alibaba-inc.com
skyline.account = koastline-aquaman
skyline.access.key = QxL6RHXP26o0T3pw

# atom service
atom.host = http://pre-atomcore.alibaba-inc.com
atom.account = psp
atom.access.key = cb7ba731-b2d3-4876-b092-135368f3f56c

# appcenter service
appcenter.host = http://pre-app-center.alibaba-inc.com
appcenter.account = koastline-aquaman
appcenter.access.key = koastline-aquaman#ak4fgnkhngkuii7fmglm20h3nk

jmenv.host = http://jmenv.tbsite.net:8080
jmenv.daily.host=http://**************:8080

# astro service
astro.host = http://astro.alibaba-inc.com
astro.account = kostline-aquaman
astro.access.key = Qmo-7Man-u7L

#kl service
kl.host = http://pre-kl.alibaba-inc.com
kl.account = koastline-aquaman
kl.access.key = 4b6255c415eb4baba7d4149fd924c455


#capacity service
capacity.host = http://pre-capacity.alibaba-inc.com
capacity.account = koastline-aquaman
capacity.access.key = c4c1e292-6748-4948-b07f-fb8ddd014b97

grop.host = https://pre-grop.alibaba-inc.com
grop.ak = aproc-api
grop.sk: 5m*Bg6%SNd2udzLYAQ17lWwI%UDRoV6!
normandy.host=https://pre-normandy-actor.alibaba-inc.com
normandy.grop.host=https://pre-normandy-grop.alibaba-inc.com

#pod aggregator service
pod.aggregator.host = http://pre-normandy-pod-aggregator.alibaba-inc.com
pod.aggregator.account = aquaman
pod.aggregator.access.key = 19b8c06f743e42a39903f0e56d

# acnihome service
acnihome.host = http://pre-acni-home.alibaba-inc.com
acnihome.app-key = uwUeqiuVQiXPYuPlKL9Bc12ABcAzhcS9
acnihome.app-sec = DEO5WzBcbSYsiaZCPYhfc4G5Nc5XoCq7

# VIP service
vipservice.host = https://pre-normandy-actor.alibaba-inc.com

#page helper config
pagehelper.auto-dialect=false
pagehelper.helper-dialect=mysql
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql
pagehelper.page-size-zero=true

#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

deploymentApi.host = https://deployapipre.alibaba-inc.com
deploymentApi.app-key = aquaman
deploymentApi.app-sec = F3MO5mDMkgtom1cREEtVWpRQ8EL2mPZG

#fed config
fedConfig.host=http://test.fed-api.alibaba-inc.com:7100

# spe service
spe.host = https://sp-env.alibaba-inc.com
spe.account = aone
spe.access.key = 78070d1e-a7c7-41dd-a458-1473a08608e3

# hcrm
hcrm.host = https://pre-grop.alibaba-inc.com
hcrm.account = normandy
hcrm.access.key = Y^PPw5NxZ2fFxpz5

# aone base
aone.base.host=https://acp.alibaba-inc.com

# env center
env.center.host=https://env-center.alibaba-inc.com