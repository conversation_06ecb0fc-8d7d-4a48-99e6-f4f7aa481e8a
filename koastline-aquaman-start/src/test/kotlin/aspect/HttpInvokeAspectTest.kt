package aspect

import com.alibaba.koastline.multiclusters.aspect.HttpInvokeAspect
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.authentication.models.AquamanUserDetails
import com.alibaba.koastline.multiclusters.authentication.models.UserAuthentication
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.InternalPlatformDsl
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import kotlin.test.assertEquals
import org.junit.Test

class HttpInvokeAspectTest {

    @Test
    fun `testIsValidOpenAPICall -- acl rules is null`() {
        val authenticationManager = mockk<AuthenticationManager>()
        val httpInvokeAspect = spyk(HttpInvokeAspect(authenticationManager), recordPrivateCalls = true) {
            every {
                authenticationManager.getLoginAccount()
            } returns AquamanUserDetails(
                authentication = UserAuthentication(
                    userName = "userName",
                    appKey = "XXXXX",
                    appSecKey = "XXXXX",
                    openApiAcl = null
                ),
                accessSecret = "XXXXXX"
            )
        }
        val uri = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative"
        val rs = InternalPlatformDsl.dynamicCall(
            httpInvokeAspect,
            "isValidOpenAPICall",
            arrayOf(uri)
        ) { mockk() }
        assertEquals(false, rs)
    }

    @Test
    fun `testIsValidOpenAPICall -- acl rules is not matched`() {
        val authenticationManager = mockk<AuthenticationManager>()
        val httpInvokeAspect = spyk(HttpInvokeAspect(authenticationManager), recordPrivateCalls = true) {
            every {
                authenticationManager.getLoginAccount()
            } returns AquamanUserDetails(
                authentication = UserAuthentication(
                    userName = "userName",
                    appKey = "XXXXX",
                    appSecKey = "XXXXX",
                    openApiAcl = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative_111"
                ),
                accessSecret = "XXXXXX"
            )
        }
        val uri = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative"
        val rs = InternalPlatformDsl.dynamicCall(
            httpInvokeAspect,
            "isValidOpenAPICall",
            arrayOf(uri)
        ) { mockk() }
        assertEquals(false, rs)
    }

    @Test
    fun `testIsValidOpenAPICall -- acl rules is exactly matched`() {
        val authenticationManager = mockk<AuthenticationManager>()
        val httpInvokeAspect = spyk(HttpInvokeAspect(authenticationManager), recordPrivateCalls = true) {
            every {
                authenticationManager.getLoginAccount()
            } returns AquamanUserDetails(
                authentication = UserAuthentication(
                    userName = "userName",
                    appKey = "XXXXX",
                    appSecKey = "XXXXX",
                    openApiAcl = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative,/apis/schedule/v4/doScaleOutScheduleByNonDeclarative_111"
                ),
                accessSecret = "XXXXXX"
            )
        }
        val uri = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative"
        val rs = InternalPlatformDsl.dynamicCall(
            httpInvokeAspect,
            "isValidOpenAPICall",
            arrayOf(uri)
        ) { mockk() }
        assertEquals(true, rs)
    }

    @Test
    fun `testIsValidOpenAPICall -- acl rules is batch matched`() {
        val authenticationManager = mockk<AuthenticationManager>()
        val httpInvokeAspect = spyk(HttpInvokeAspect(authenticationManager), recordPrivateCalls = true) {
            every {
                authenticationManager.getLoginAccount()
            } returns AquamanUserDetails(
                authentication = UserAuthentication(
                    userName = "userName",
                    appKey = "XXXXX",
                    appSecKey = "XXXXX",
                    openApiAcl = "/apis/schedule/v4"
                ),
                accessSecret = "XXXXXX"
            )
        }
        val uri = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative"
        val rs = InternalPlatformDsl.dynamicCall(
            httpInvokeAspect,
            "isValidOpenAPICall",
            arrayOf(uri)
        ) { mockk() }
        assertEquals(true, rs)
    }


    @Test
    fun `testIsValidOpenAPICall -- acl rules is all matched`() {
        val authenticationManager = mockk<AuthenticationManager>()
        val httpInvokeAspect = spyk(HttpInvokeAspect(authenticationManager), recordPrivateCalls = true) {
            every {
                authenticationManager.getLoginAccount()
            } returns AquamanUserDetails(
                authentication = UserAuthentication(
                    userName = "userName",
                    appKey = "XXXXX",
                    appSecKey = "XXXXX",
                    openApiAcl = "*"
                ),
                accessSecret = "XXXXXX"
            )
        }
        val uri = "/apis/schedule/v4/doScaleOutScheduleByNonDeclarative"
        val rs = InternalPlatformDsl.dynamicCall(
            httpInvokeAspect,
            "isValidOpenAPICall",
            arrayOf(uri)
        ) { mockk() }
        assertEquals(true, rs)
    }
}