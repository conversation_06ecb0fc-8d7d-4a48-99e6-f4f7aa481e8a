package com.alibaba.koastline.multiclusters.api.v4.apre

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.GroupAuthorizedDeclarationsDto
import com.alibaba.koastline.multiclusters.apre.model.req.GroupAuthorizedDeclarationsReqDto
import com.alibaba.koastline.multiclusters.common.exceptions.HcrmException
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import kotlin.test.assertFailsWith

/**
 * <AUTHOR>
 * date 2024/4/15 17:39
 */
class ApREDeedApiV4Test {

    @MockK
    lateinit var scheduleStandardService: ScheduleStandardService

    @MockK
    lateinit var apREService: ApREService

    @InjectMockKs
    lateinit var apREDeedApiV4: ApREDeedApiV4

    @Before
    fun setUpMockK() = MockKAnnotations.init(this, relaxed = true)

    @Test
    fun queryGroupAuthorizedDeclarationsTest() {
        every {
            apREService.requireGroupAuthorizedDeclarations(any(), any(), any())
        } answers { GroupAuthorizedDeclarationsDto(arg(1), listOf()) }
        val queryGroupAuthorizedDeclarations = apREDeedApiV4.queryGroupAuthorizedDeclarations(
            GroupAuthorizedDeclarationsReqDto(
                "test-app",
                listOf("g0", "g1", "g3", "g4"), ScheduleEnvType.ASI, "staging"
            )
        )
        Assert.assertEquals(listOf("g0", "g1", "g3", "g4"), queryGroupAuthorizedDeclarations.data!!.map { it.resourceGroup })
    }

    @Test
    fun queryGroupAuthorizedDeclarationsExceptionTest() {
        every {
            apREService.requireGroupAuthorizedDeclarations(any(), any(), any(), any(), any())
        } throws HcrmException("test exception")
        assertFailsWith<HcrmException> {
            apREDeedApiV4.queryGroupAuthorizedDeclarations(
                GroupAuthorizedDeclarationsReqDto(
                    "test-app",
                    listOf("g0"), ScheduleEnvType.SERVERLESS_APP, "staging"
                )
            )
        }
    }

}