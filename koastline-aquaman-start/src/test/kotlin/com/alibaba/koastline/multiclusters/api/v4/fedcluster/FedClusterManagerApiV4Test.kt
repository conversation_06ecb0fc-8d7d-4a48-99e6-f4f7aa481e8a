package com.alibaba.koastline.multiclusters.api.v4.fedcluster

import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import com.alibaba.koastline.multiclusters.fed.FedClusterService
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiDeleteReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyApiUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedPolicy
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.util.*

/**
 * <AUTHOR>
 * date 2024/3/19 16:11
 */
class FedClusterManagerApiV4Test {

    companion object {
        const val TENANT_CLUSTER_NAME = "test-tenant-cluster"
        const val MEMBER_CLUSTER_NAME = "test-member-cluster"
        val fedCluster = FedCluster(
            id = 123L,
            fedEnvName = "test",
            region = "",
            fedClusterKey = "",
            status = "",
            tenantManageClusterKey = TENANT_CLUSTER_NAME,
            memberManageClusterKey = MEMBER_CLUSTER_NAME,
            creator = "1111",
            modifier = "222",
            isDeleted = "N"
        )

        val clusterProfileNew = ClusterProfileNew(
            "", "", "", "", listOf(), listOf(), ""
        )

        val fedPolicy = FedPolicy(
            id = 123L,
            fedClusterId = 12L,
            fedPolicyPriority = 13,
            fedPolicyName = "xuant10",
            fedUniqueName = "fed-policy-1234",
            schedulePlugin = listOf("RoundRobin"),
            effectedMemberClusterList = listOf(),
            policySelectorList = listOf(),
            modifier = "177031",
            gmtModified = Date()
        )

        val podSpec = mapOf(
            "policy" to mapOf(
                "ratios" to listOf(
                    mapOf(
                        "clusterName" to "test-a",
                        "value" to 10
                    )
                ),
                "schedulePlugin" to listOf("RoundRobin")
            ),
            "policySelector" to mapOf(
                "matchExpressions" to listOf(
                    mapOf(
                        "key" to "aaa",
                        "operator" to "In",
                        "values" to listOf("bbb")
                    )
                )
            )
        )
    }

    @MockK
    lateinit var fedClusterService: FedClusterService

    @MockK
    lateinit var resourcePoolService: ResourcePoolService

    @MockK
    lateinit var authenticationManager: AuthenticationManager

    @Before
    fun setUpMockK() = MockKAnnotations.init(this, relaxed = true)

    @Test
    fun createFedPolicyApiSuccTest() {
        every {
            fedClusterService.queryFedByTenantClusterName(TENANT_CLUSTER_NAME)
        } answers { fedCluster }

        every {
            resourcePoolService.listClusterProfileByManagedClusterKey(MEMBER_CLUSTER_NAME)
        } answers { listOf(
            clusterProfileNew.copy(clusterName = "test-a"),
            clusterProfileNew.copy(clusterName = "test-b")
        ) }

        every {
            fedClusterService.createFedPolicy(any(), any())
        } answers { "fed-policy-a-b-c-d" }

        val fedClusterManagerApiV4 = FedClusterManagerApiV4(fedClusterService, resourcePoolService, authenticationManager)
        val req = FedClusterPolicyApiCreateReq(
            TENANT_CLUSTER_NAME,
            "test-policy",
            10,
            "Pod",
            policySpec = podSpec,
            creator = "1111223"
        )
        val fedUniqueName = fedClusterManagerApiV4.createFedPolicyApi(req).data!!.fedUniqueName
        Assert.assertEquals("fed-policy-a-b-c-d", fedUniqueName)
    }

    @Test
    fun validateRatioInMemberTest() {
        every {
            fedClusterService.queryFedByTenantClusterName(TENANT_CLUSTER_NAME)
        } answers { fedCluster }

        every {
            resourcePoolService.listClusterProfileByManagedClusterKey(MEMBER_CLUSTER_NAME)
        } answers { listOf(
            clusterProfileNew.copy(clusterName = "test-a"),
            clusterProfileNew.copy(clusterName = "test-b")
        ) }

        val fedClusterManagerApiV4 = FedClusterManagerApiV4(fedClusterService, resourcePoolService, authenticationManager)
        val policySpec = mapOf(
            "policy" to mapOf(
                "ratios" to listOf(
                    mapOf(
                        "clusterName" to "aaa",
                        "value" to 10
                    )
                ),
                "schedulePlugin" to listOf("RoundRobin")
            ),
            "policySelector" to mapOf(
                "matchExpressions" to listOf(
                    mapOf(
                        "key" to "aaa",
                        "operator" to "In",
                        "values" to listOf("bbb")
                    )
                )
            )
        )

        // create
        val req = FedClusterPolicyApiCreateReq(
            TENANT_CLUSTER_NAME,
            "test-policy",
            10,
            "Pod",
            policySpec = policySpec,
            creator = "1111223"
        )
        try {
            fedClusterManagerApiV4.createFedPolicyApi(req)
            Assert.fail()
        } catch (e : IllegalArgumentException) {
            Assert.assertEquals("policySpec.policy.ratios.clusterName must in fed member clusters", e.message)
        }

        // update
        val updateReq = FedClusterPolicyApiUpdateReq(
            TENANT_CLUSTER_NAME,
            "fed-policy-111",
            11,
            "Pod",
            policySpec, "177031"
        )
        try {
            fedClusterManagerApiV4.updateFedPolicyAPI(updateReq)
            Assert.fail()
        } catch (e : IllegalArgumentException) {
            Assert.assertEquals("policySpec.policy.ratios.clusterName must in fed member clusters", e.message)
        }
    }

    @Test
    fun createFedPolicyApiValidatePolicySpecTest() {
        every {
            fedClusterService.queryFedByTenantClusterName(TENANT_CLUSTER_NAME)
        } answers { fedCluster }

        val fedClusterManagerApiV4 = FedClusterManagerApiV4(fedClusterService, resourcePoolService, authenticationManager)
        val reqWithoutRadioValue = FedClusterPolicyApiCreateReq(
            TENANT_CLUSTER_NAME,
            "test-policy",
            10,
            "Pod",
            policySpec = mapOf(
                "policy" to mapOf(
                    "ratios" to listOf(
                        mapOf(
                            "clusterName" to "aaa"
                        )
                    ),
                    "schedulePlugin" to listOf("RoundRobin")
                ),
                "policySelector" to mapOf(
                    "matchExpressions" to listOf(
                        mapOf(
                            "key" to "aaa",
                            "operator" to "In",
                            "values" to listOf("bbb")
                        )
                    )
                )
            ),
            creator = "1111223"
        )
        try {
            fedClusterManagerApiV4.createFedPolicyApi(reqWithoutRadioValue)
            Assert.fail()
        } catch (e : IllegalArgumentException) {
            Assert.assertEquals("policySpec.policy.ratios.value required", e.message)
        }

        val reqWithoutSchedulePlugin = FedClusterPolicyApiCreateReq(
            "test-tenant-cluster",
            "test-policy",
            10,
            "Pod",
            policySpec = mapOf(
                "policy" to mapOf(
                    "ratios" to listOf(mapOf(
                        "clusterName" to "aaa",
                        "value" to 10
                    )),
                    "schedulePlugin" to listOf()
                ),
                "policySelector" to mapOf(
                    "matchExpressions" to listOf(
                        mapOf(
                            "key" to "aaa",
                            "operator" to "In",
                            "values" to listOf("bbb")
                        )
                    )
                )
            ),
            creator = "1111223"
        )
        try {
            fedClusterManagerApiV4.createFedPolicyApi(reqWithoutSchedulePlugin)
            Assert.fail()
        } catch (e : IllegalArgumentException) {
            Assert.assertEquals("policySpec.policy.schedulePlugins required", e.message)
        }

        val reqWithoutMatchExpressions = FedClusterPolicyApiCreateReq(
            "test-tenant-cluster",
            "test-policy",
            10,
            "Pod",
            policySpec = mapOf(
                "policy" to mapOf(
                    "ratios" to listOf(mapOf(
                        "clusterName" to "aaa",
                        "value" to 10
                    )),
                    "schedulePlugin" to listOf("a")
                ),
                "policySelector" to mapOf(
                    "matchExpressions" to listOf()
                )
            ),
            creator = "1111223"
        )
        try {
            fedClusterManagerApiV4.createFedPolicyApi(reqWithoutMatchExpressions)
            Assert.fail()
        } catch (e : IllegalArgumentException) {
            Assert.assertEquals("policySpec.policySelector.matchExpressions required", e.message)
        }
    }

    @Test
    fun fedPolicyNotFoundTest() {
        every {
            fedClusterService.queryFedByTenantClusterName(TENANT_CLUSTER_NAME)
        } answers { fedCluster }

        every {
            resourcePoolService.listClusterProfileByManagedClusterKey(MEMBER_CLUSTER_NAME)
        } answers { listOf(
            clusterProfileNew.copy(clusterName = "test-a"),
            clusterProfileNew.copy(clusterName = "test-b")
        ) }

        every {
            fedClusterService.listFedPolicyByFedClusterId(fedCluster.id!!)
        } answers {
            listOf(fedPolicy)
        }

        val fedClusterManagerApiV4 = FedClusterManagerApiV4(fedClusterService, resourcePoolService, authenticationManager)
        val req = FedClusterPolicyApiUpdateReq(
            TENANT_CLUSTER_NAME, "fed-policy-111", 11,
            "Pod", podSpec, "177031"
        )
        try {
            fedClusterManagerApiV4.updateFedPolicyAPI(req)
            Assert.fail()
        } catch (e : Exception) {
            Assert.assertEquals("fed policy:fed-policy-111 not found!", e.message)
        }

        val fedClusterPolicyApiDeleteReq = FedClusterPolicyApiDeleteReq(
            TENANT_CLUSTER_NAME, "fed-policy-111", "177031"
        )
        try {
            fedClusterManagerApiV4.deleteFedPolicyAPI(fedClusterPolicyApiDeleteReq)
        } catch (e: Exception) {
            Assert.assertEquals("fed policy:fed-policy-111 not found!", e.message)
        }
    }

    @Test
    fun updateFedPolicyAPISuccTest() {
        every {
            fedClusterService.queryFedByTenantClusterName(TENANT_CLUSTER_NAME)
        } answers { fedCluster }

        every {
            resourcePoolService.listClusterProfileByManagedClusterKey(MEMBER_CLUSTER_NAME)
        } answers { listOf(
            clusterProfileNew.copy(clusterName = "test-a"),
            clusterProfileNew.copy(clusterName = "test-b")
        ) }

        every {
            fedClusterService.listFedPolicyByFedClusterId(fedCluster.id!!)
        } answers {
            listOf(fedPolicy)
        }

        val fedClusterManagerApiV4 = FedClusterManagerApiV4(fedClusterService, resourcePoolService, authenticationManager)
        val req = FedClusterPolicyApiUpdateReq(
            TENANT_CLUSTER_NAME, "fed-policy-1234", 11,
            "Pod", podSpec, "177031"
        )
        fedClusterManagerApiV4.updateFedPolicyAPI(req)
        verify { fedClusterService.updateFedPolicy(any(), any()) }
    }
}