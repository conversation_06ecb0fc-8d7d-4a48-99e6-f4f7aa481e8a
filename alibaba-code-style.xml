<code_scheme name="Alibaba-CodeStyle" version="173">
  <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
  <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
  <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
  <option name="IMPORT_LAYOUT_TABLE">
    <value>
      <package name="java" withSubpackages="true" static="false" />
      <emptyLine />
      <package name="javax" withSubpackages="true" static="false" />
      <emptyLine />
      <package name="com.alibaba" withSubpackages="true" static="false" />
      <emptyLine />
      <package name="" withSubpackages="true" static="false" />
      <emptyLine />
      <package name="" withSubpackages="true" static="true" />
    </value>
  </option>
  <option name="FORMATTER_TAGS_ENABLED" value="true" />
  <option name="WRAP_COMMENTS" value="true" />
  <JavaCodeStyleSettings>
    <option name="ANNOTATION_PARAMETER_WRAP" value="1" />
    <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="java" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="javax" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="com.alibaba" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
      </value>
    </option>
    <option name="JD_ALIGN_EXCEPTION_COMMENTS" value="false" />
    <option name="JD_P_AT_EMPTY_LINES" value="false" />
    <option name="JD_PRESERVE_LINE_FEEDS" value="true" />
  </JavaCodeStyleSettings>
  <JetCodeStyleSettings>
    <option name="CONTINUATION_INDENT_IN_PARAMETER_LISTS" value="true" />
    <option name="CONTINUATION_INDENT_IN_ARGUMENT_LISTS" value="true" />
    <option name="CONTINUATION_INDENT_FOR_EXPRESSION_BODIES" value="true" />
    <option name="CONTINUATION_INDENT_FOR_CHAINED_CALLS" value="true" />
    <option name="CONTINUATION_INDENT_IN_SUPERTYPE_LISTS" value="true" />
    <option name="CONTINUATION_INDENT_IN_IF_CONDITIONS" value="true" />
    <option name="CONTINUATION_INDENT_IN_ELVIS" value="true" />
    <option name="WRAP_EXPRESSION_BODY_FUNCTIONS" value="0" />
    <option name="IF_RPAREN_ON_NEW_LINE" value="false" />
    <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
  </JetCodeStyleSettings>
  <MySQLCodeStyleSettings version="5">
    <option name="USE_GENERIC_STYLE" value="true" />
  </MySQLCodeStyleSettings>
  <ScalaCodeStyleSettings>
    <option name="MULTILINE_STRING_CLOSING_QUOTES_ON_NEW_LINE" value="true" />
  </ScalaCodeStyleSettings>
  <codeStyleSettings language="JAVA">
    <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
    <option name="BLOCK_COMMENT_AT_FIRST_COLUMN" value="false" />
    <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
    <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
    <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
    <option name="SPACE_WITHIN_FOR_PARENTHESES" value="true" />
    <option name="SPACE_AFTER_TYPE_CAST" value="false" />
    <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
    <option name="CALL_PARAMETERS_WRAP" value="1" />
    <option name="PREFER_PARAMETERS_WRAP" value="true" />
    <option name="METHOD_PARAMETERS_WRAP" value="1" />
    <option name="RESOURCE_LIST_WRAP" value="1" />
    <option name="EXTENDS_LIST_WRAP" value="1" />
    <option name="THROWS_LIST_WRAP" value="1" />
    <option name="EXTENDS_KEYWORD_WRAP" value="1" />
    <option name="THROWS_KEYWORD_WRAP" value="1" />
    <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
    <option name="BINARY_OPERATION_WRAP" value="1" />
    <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
    <option name="TERNARY_OPERATION_WRAP" value="1" />
    <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
    <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
    <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
    <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
    <option name="FOR_STATEMENT_WRAP" value="1" />
    <option name="ARRAY_INITIALIZER_WRAP" value="1" />
    <option name="ASSIGNMENT_WRAP" value="1" />
    <option name="PLACE_ASSIGNMENT_SIGN_ON_NEXT_LINE" value="true" />
    <option name="WRAP_COMMENTS" value="true" />
    <option name="ASSERT_STATEMENT_WRAP" value="1" />
    <option name="ASSERT_STATEMENT_COLON_ON_NEXT_LINE" value="true" />
    <option name="IF_BRACE_FORCE" value="3" />
    <option name="DOWHILE_BRACE_FORCE" value="3" />
    <option name="WHILE_BRACE_FORCE" value="3" />
    <option name="FOR_BRACE_FORCE" value="3" />
    <option name="WRAP_LONG_LINES" value="true" />
    <option name="PARAMETER_ANNOTATION_WRAP" value="1" />
    <option name="VARIABLE_ANNOTATION_WRAP" value="2" />
    <option name="ENUM_CONSTANTS_WRAP" value="2" />
  </codeStyleSettings>
</code_scheme>