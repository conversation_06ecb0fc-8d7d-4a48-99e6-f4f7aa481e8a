package com.alibaba.koastline.aquaman.facade.api;

import com.alibaba.koastline.aquaman.facade.api.model.AquamanResult;
import com.alibaba.koastline.aquaman.facade.api.model.req.AppResourceSpec;
import com.alibaba.koastline.aquaman.facade.api.model.req.ResourceGroupResourceSpec;
import com.alibaba.koastline.aquaman.facade.api.model.req.ResourceSpec;
import feign.Body;
import feign.Param;
import feign.RequestLine;

public interface ResourceSpecApi {

    /**
     * 创建或更新（如存在）应用资源规格
     * @param appResourceSpec
     * @return
     */
    @RequestLine("POST /apis/resource/object/feature/v4/app/createOrUpdateResourceSpec")
    @Body("{body}")
    AquamanResult<Boolean> createOrUpdateAppResourceSpec(@Param("body") AppResourceSpec appResourceSpec);

    /**
     * 获取应用资源规格
     * @param appName 应用名
     * @return
     */
    @RequestLine("GET /apis/resource/object/feature/v4/app/getResourceSpec?appName={appName}")
    AquamanResult<ResourceSpec> getAppResourceSpec(@Param("appName") String appName);

    /**
     * 创建或更新（如存在）分组资源规格
     * @param resourceGroupResourceSpec
     * @return
     */
    @RequestLine("POST /apis/resource/object/feature/v4/resourceGroup/createOrUpdateResourceSpec")
    @Body("{body}")
    AquamanResult<Boolean> createOrUpdateResourceGroupResourceSpec(@Param("body") ResourceGroupResourceSpec resourceGroupResourceSpec);

    /**
     * 获取应用资源规格
     * @param resourceGroup 分组
     * @return
     */
    @RequestLine("GET /apis/resource/object/feature/v4/resourceGroup/getResourceSpec?resourceGroup={resourceGroup}")
    AquamanResult<ResourceSpec> getResourceGroupResourceSpec(String resourceGroup);
}
