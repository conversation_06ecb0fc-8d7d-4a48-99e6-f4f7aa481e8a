package com.alibaba.koastline.aquaman.facade.api;

import com.alibaba.koastline.aquaman.facade.api.model.AquamanResult;
import com.alibaba.koastline.aquaman.facade.api.model.params.UserLabelExternalType;
import com.alibaba.koastline.aquaman.facade.api.model.req.UserLabelCreateReq;
import com.alibaba.koastline.aquaman.facade.api.model.resp.UserLabelResp;
import feign.Body;
import feign.Param;
import feign.RequestLine;

public interface UserLabelApi {

    /**
     * 创建标签[存在则覆盖]
     * @param userLabelCreateReq
     * @return
     */
    @RequestLine("POST /apis/user/label/v4/createAndOverrideWhileExist")
    @Body("{body}")
    AquamanResult<Boolean> createAndOverrideWhileExist(@Param("body") UserLabelCreateReq userLabelCreateReq);

    /**
     * 查询标签
     * @param externalId 目标范围ID
     * @param externalType 目标范围类型
     * @param labelName 标签名
     * @return
     */
    @RequestLine("GET /apis/user/label/v4/findByExternalAndLabel?externalId={externalId}&externalType={externalType}&labelName={labelName}")
    AquamanResult<UserLabelResp> findByExternalAndLabel(@Param("externalId") String externalId, @Param("externalType") UserLabelExternalType externalType, @Param("labelName") String labelName);
}
