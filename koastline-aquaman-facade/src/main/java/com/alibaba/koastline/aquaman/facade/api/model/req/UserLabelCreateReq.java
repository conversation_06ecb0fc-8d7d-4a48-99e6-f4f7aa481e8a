package com.alibaba.koastline.aquaman.facade.api.model.req;

import com.alibaba.fastjson.JSON;
import com.alibaba.koastline.aquaman.facade.api.model.params.UserLabelExternalType;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @description: 用户标签
 * @date: 2023/4/13 11:08 AM
 */
@Data
public class UserLabelCreateReq {
    /**
     * 范围ID
     */
    private String externalId;
    /**
     * 范围类型
     */
    private UserLabelExternalType externalType;
    /**
     * 标签
     */
    private String labelName;
    /**
     * 标签值
     */
    private String labelValue;
    /**
     * 创建人工号
     */
    private String creator;

    public String toString() {
        return JSON.toJSONString(this);
    }
}
