package com.alibaba.koastline.aquaman.facade.factory;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API 访问配置
 * <p>Date: 2017-08-03 Time: 16:05</p>
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AquamanApiConfig {
    /**
     * 服务地址
     */
    private String serverAddress;
    /**
     * api 帐号
     */
    private String accessId;
    /**
     * api 密钥
     */
    private String accessKey;
}
