package com.alibaba.koastline.aquaman.facade.factory;

import com.alibaba.koastline.aquaman.facade.support.JsonDecoder;
import com.alibaba.koastline.aquaman.facade.support.JsonEncoder;
import feign.Feign;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.form.FormEncoder;
import okhttp3.Credentials;

import java.nio.charset.Charset;
import java.util.Objects;

/**
 * API 接口工厂，生成指定类型的 API
 */
public final class AquamanApiFactoryBean {
    private static final String HEADER_ACCEPT = "Accept";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_AUTHORIZATION = "Authorization";
    /**
     * UTF-8: eight-bit UCS Transformation Format.
     */
    private static final Charset UTF_8 = Charset.forName("UTF-8");

    /**
     * 生成指定类型的 API 接口
     *
     * @param apiInterface API 接口类
     * @param config       API 服务配置
     * @param <T>          API 接口类型
     * @return API 接口
     */
    public static <T> T getInstance(Class<T> apiInterface, AquamanApiConfig config) {
        Objects.requireNonNull(apiInterface, "apiInterface cannot be null");
        Objects.requireNonNull(config, "config cannot be null");
        Objects.requireNonNull(config.getAccessId(), "accessId is null");
        Objects.requireNonNull(config.getAccessKey(), "accessKey is null");
        Objects.requireNonNull(config.getServerAddress(), "serverAddress is null");

        return Feign
            .builder()
            .encoder(new FormEncoder(new JsonEncoder()))
            .decoder(new JsonDecoder())
            .requestInterceptor(new AquamanRequestInterceptor(config))
            .target(apiInterface, config.getServerAddress());
    }

    private static class AquamanRequestInterceptor implements RequestInterceptor {
        private AquamanApiConfig config;

        AquamanRequestInterceptor(AquamanApiConfig config) {
            this.config = config;
        }

        @Override
        public void apply(RequestTemplate template) {
            template.header(HEADER_ACCEPT, "application/json;charset=utf-8");
            template.header(HEADER_AUTHORIZATION, Credentials.basic(config.getAccessId(), config.getAccessKey()));
            setContentTypeIfNotPresent(template);
        }

        private void setContentTypeIfNotPresent(RequestTemplate template) {
            if (template.headers().containsKey(HEADER_CONTENT_TYPE)) {
                return;
            }
            template.header(HEADER_CONTENT_TYPE, "application/json");
        }
    }
}
