package com.alibaba.koastline.aquaman.facade.api.model.req;

import lombok.Data;

/**
 * @author: <EMAIL>
 * @description: 资源规格
 * @date: 2022/12/25 4:05 PM
 */
@Data
public class ResourceSpec {
    private String cpu;
    private String memory;
    private String disk;
    private String gpu;

    public ResourceSpec(){}

    /**
     * @param model 对应astro的model,形如2-4-60
     * @param gpu
     */
    public ResourceSpec(String model, String gpu) {
        String[] specArray = model.split("-");
        this.cpu = specArray[0];
        this.memory = specArray[1];
        this.disk = specArray[2];
        this.gpu = gpu;
    }

    /**
     * @return 对应astro的model,形如2-4-60
     */
    public String getModel() {
        return cpu + "-" + memory + "-" + disk;
    }
}
