package com.alibaba.koastline.aquaman.facade.support;

import com.alibaba.fastjson.JSON;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

/**
 * decode response body using fastjson
 */
public class JsonDecoder implements Decoder {
    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        if (response.status() == 404) { return Util.emptyValueOf(type); }
        if (response.body() == null) { return null; }

        response.body().length();
        InputStream in = response.body().asInputStream();
        if (!in.markSupported()) {
            in = new BufferedInputStream(in, 1);
        }
        try {
            // Read the first byte to see if we have any data
            in.mark(1);
            if (in.read() == -1) {
                return null; // Eagerly returning null avoids "No content to map due to end-of-input"
            }
            in.reset();
            return JSON.parseObject(in, type);
        } catch (Exception e) {
            if (e.getCause() != null && e.getCause() instanceof IOException) {
                throw IOException.class.cast(e.getCause());
            }
            throw e;
        }
    }
}
