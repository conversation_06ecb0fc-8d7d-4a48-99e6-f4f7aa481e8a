package com.alibaba.koastline.aquaman.facade.api.model.resp;

import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @description: 用户标签
 * @date: 2023/4/13 2:00 PM
 */
@Data
public class UserLabelResp {
    /**
     * 范围ID
     */
    String externalId;
    /**
     * 范围类型
     */
    String externalType;
    /**
     * 标签
     */
    String labelName;
    /**
     * 标签值
     */
    String labelValue;
    /**
     * 提交系统
     */
    String submitter;
    /**
     * 通用属性
     */
    String creator;
    String modifier;
    String gmtCreate;
    String gmtModified;
    String isDeleted;
}
