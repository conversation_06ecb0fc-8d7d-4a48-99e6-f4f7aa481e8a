package com.alibaba.koastline.aquaman.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.koastline.aquaman.facade.api.ResourceSpecApi;
import com.alibaba.koastline.aquaman.facade.api.model.req.AppResourceSpec;
import com.alibaba.koastline.aquaman.facade.api.model.req.ResourceSpec;
import com.alibaba.koastline.aquaman.facade.factory.AquamanApiConfig;
import com.alibaba.koastline.aquaman.facade.factory.AquamanApiFactoryBean;
import org.junit.Ignore;
import org.junit.Test;

/**
 * @author: <EMAIL>
 * @description: TODO
 * @date: 2022/12/25 4:13 PM
 */
public class ResourceSpecApiTest {

    @Ignore
    @Test
    public void testGetAppResourceSpec() {
        AquamanApiConfig aquamanApiConfig = new AquamanApiConfig();
        aquamanApiConfig.setAccessId("admin");
        aquamanApiConfig.setAccessKey("secret1111");
        aquamanApiConfig.setServerAddress("http://localhost:7001/");
        ResourceSpecApi resourceSpecApi = AquamanApiFactoryBean.getInstance(ResourceSpecApi.class, aquamanApiConfig);
        ResourceSpec resourceSpec = resourceSpecApi.getAppResourceSpec("normandy-test-app4").getData();
        System.out.println(JSON.toJSON(resourceSpec));
    }

    @Ignore
    @Test
    public void testCreateOrUpdateAppResourceSpec() {
        AquamanApiConfig aquamanApiConfig = new AquamanApiConfig();
        aquamanApiConfig.setAccessId("admin");
        aquamanApiConfig.setAccessKey("secret1111");
        aquamanApiConfig.setServerAddress("http://localhost:7001/");
        ResourceSpecApi resourceSpecApi = AquamanApiFactoryBean.getInstance(ResourceSpecApi.class, aquamanApiConfig);
        AppResourceSpec appResourceSpec = new AppResourceSpec();
        appResourceSpec.setAppName("normandy-test-app4");
        appResourceSpec.setEmployeeId("137285");
        ResourceSpec resourceSpec = new ResourceSpec();
        resourceSpec.setCpu("2");
        resourceSpec.setMemory("4");
        resourceSpec.setDisk("60");
        appResourceSpec.setResourceSpec(resourceSpec);
        Boolean rs = resourceSpecApi.createOrUpdateAppResourceSpec(appResourceSpec).getData();
        System.out.println(rs);
    }
}
