package com.alibaba.koastline.aquaman.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.koastline.aquaman.facade.api.UserLabelApi;
import com.alibaba.koastline.aquaman.facade.api.model.params.UserLabelExternalType;
import com.alibaba.koastline.aquaman.facade.api.model.req.UserLabelCreateReq;
import com.alibaba.koastline.aquaman.facade.api.model.resp.UserLabelResp;
import com.alibaba.koastline.aquaman.facade.factory.AquamanApiConfig;
import com.alibaba.koastline.aquaman.facade.factory.AquamanApiFactoryBean;
import org.junit.Ignore;
import org.junit.Test;

/**
 * @author: <EMAIL>
 * @description: TODO
 * @date: 2022/12/25 4:13 PM
 */
public class UserLabelApiTest {

    @Ignore
    @Test
    public void testCreateAndOverrideWhileExist() {
        AquamanApiConfig aquamanApiConfig = new AquamanApiConfig();
        aquamanApiConfig.setAccessId("admin");
        aquamanApiConfig.setAccessKey("secret1111");
        aquamanApiConfig.setServerAddress("http://localhost:7001/");
        UserLabelApi userLabelApi = AquamanApiFactoryBean.getInstance(UserLabelApi.class, aquamanApiConfig);
        UserLabelCreateReq userLabelCreateReq = new UserLabelCreateReq();
        userLabelCreateReq.setCreator("admin");
        userLabelCreateReq.setLabelName("model");
        userLabelCreateReq.setLabelValue("4-8-60");
        userLabelCreateReq.setExternalId("normandy-test-app4");
        userLabelCreateReq.setExternalType(UserLabelExternalType.APPLICATION);
        boolean rs = userLabelApi.createAndOverrideWhileExist(userLabelCreateReq).getData();
        System.out.println(rs);
    }

    @Ignore
    @Test
    public void testFindByExternalAndLabel() {
        AquamanApiConfig aquamanApiConfig = new AquamanApiConfig();
        aquamanApiConfig.setAccessId("admin");
        aquamanApiConfig.setAccessKey("secret1111");
        aquamanApiConfig.setServerAddress("http://localhost:7001/");
        UserLabelApi userLabelApi = AquamanApiFactoryBean.getInstance(UserLabelApi.class, aquamanApiConfig);
        UserLabelResp userLabelResp = userLabelApi.findByExternalAndLabel("normandy-test-app4" , UserLabelExternalType.APPLICATION, "model").getData();
        System.out.println(JSON.toJSON(userLabelResp));
    }
}
