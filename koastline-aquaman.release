# 1. 你可以直接编辑本文件的内容，或者通过工具来帮你校验合法性和自动生成，请点击：http://aliwing.alibaba-inc.com/apprelease/home.htm
# 2. 更多关于Release文件的规范和约定，请点击: http://docs.alibaba-inc.com/pages/viewpage.action?pageId=252891532

# 构建打包使用jdk版本
baseline.jdk=ajdk11_11.0.17.16

# 构建打包所用的maven版本，pandora boot应用需要使用maven 3
build.tools.maven=maven3.2.5

# 构建打包使用的maven settings文件
build.tools.maven.settings=tao

# 构建源码语言类型
code.language=java

# 打包为tgz文件的目录，pom.xml里指定ant插件解压fat jar到target/${appName}目录
build.output=koastline-aquaman-start/target/koastline-aquaman.jar


# 设置Dockerfile里的APP_NAME变量，必须要配置
build.tools.docker.args=--build-arg APP_NAME=${APP_NAME}