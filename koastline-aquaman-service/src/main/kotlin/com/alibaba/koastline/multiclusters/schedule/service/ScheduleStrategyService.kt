package com.alibaba.koastline.multiclusters.schedule.service

import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_PREFIX
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelector
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchApRELabel
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeclarationPatchException
import com.alibaba.koastline.multiclusters.common.exceptions.ScheduleStrategyException
import com.alibaba.koastline.multiclusters.schedule.model.ApREStrategy
import com.alibaba.koastline.multiclusters.schedule.model.ClusterWorkload
import com.alibaba.koastline.multiclusters.schedule.model.ResourceStrategy
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyReqDto
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyResult
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class ScheduleStrategyService {
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService

    @Autowired
    lateinit var apREService: ApREService

    @Autowired
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService

    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    fun computeScheduleStrategy(scheduleStrategyReqDto: ScheduleStrategyReqDto): ScheduleStrategyResult {
        checkReqParams(scheduleStrategyReqDto)
        val apREDeedResult = apREService.queryClustersByApREDeedContent(
            buildApREDeed(scheduleStrategyReqDto)
        )
        val apREStrategyList = mutableListOf<ApREStrategy>()
        apREDeedResult.matchDeclarations.forEach { matchDeclaration ->
            matchDeclaration.checkAfterPatchDeclaration()
            //基于四元组声明合并集群
            val resources = matchDeclaration.apres.flatMap { apRE ->
                apRE.resources
            }.run {
                resourcePoolService.mergeResourceByCluster(this)
            }
            //基于站定多集群均衡
            val declaration = checkNotNull(matchDeclaration.declaration) { "missing declaration of matchDeclaration" }
            val clusterBalancePatchData = apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                appName = scheduleStrategyReqDto.appName,
                resourceGroup = scheduleStrategyReqDto.resourceGroup,
                stage = checkNotNull(declaration.stage) { "missing stage in declaration" },
                unit = checkNotNull(declaration.unit) { "missing unit in declaration" },
                site = checkNotNull(declaration.az) { "missing site/az in declaration" })
            if (clusterBalancePatchData == null) {
                apREStrategyList.add(
                    buildCommonApREStrategyList(
                        scheduleStrategyEnvType = scheduleStrategyReqDto.scheduleStrategyEnvType,
                        serverlessBaseAppName = scheduleStrategyReqDto.serverlessBaseAppName,
                        declaration = matchDeclaration.declaration,
                        resources = resources
                    )
                )
            } else {
                apREStrategyList.add(
                    buildBalanceApREStrategyList(
                        scheduleStrategyEnvType = scheduleStrategyReqDto.scheduleStrategyEnvType,
                        serverlessBaseAppName = scheduleStrategyReqDto.serverlessBaseAppName,
                        declaration = matchDeclaration.declaration,
                        resources = resources,
                        apREDeclarationPatchDataDO = clusterBalancePatchData
                    )
                )
            }
        }
        return ScheduleStrategyResult(
            apres = apREStrategyList
        ).filterEmptyInvalidResourceStrategy()
    }

    /**
     * 存在单站点多集群均衡策略场景
     * 计算集群策略
     */
    private fun buildBalanceApREStrategyList(
        resources: List<ResourceDO>,
        scheduleStrategyEnvType: ScheduleStrategyEnvType,
        serverlessBaseAppName: String? = null,
        declaration: Declaration,
        apREDeclarationPatchDataDO: ApREDeclarationPatchDataDO
    ): ApREStrategy {
        val balancedPatchItemMap = apREDeclarationPatchDataDO.declarationPatch.patchItems.associate { patchItem ->
            checkNotNull(patchItem.clusterSelector) { "集群均衡策略，clusterSelector不能为空。" }.identity() to Pair(
                first = patchItem,
                second = mutableListOf<ResourceDO>()
            )
        }
        resources.forEach { resource ->
            matchResource(resource, apREDeclarationPatchDataDO).forEach { patchItem ->
                balancedPatchItemMap[patchItem.clusterSelector!!.identity()]!!.second.add(resource)
            }
        }
        return ApREStrategy(
            site = declaration.az!!,
            stage = declaration.stage!!,
            unit = declaration.unit!!,
            weight = declaration.weight,
            resources = balancedPatchItemMap.values.map { patchItemResourcePair ->
                if (patchItemResourcePair.second.isEmpty()) {
                    throw ScheduleStrategyException("命中集群均衡策略[ID=${apREDeclarationPatchDataDO.id}],不满足条件[${patchItemResourcePair.first.clusterSelector!!.identity()}],请联系SRE添加授权。")
                }
                ResourceStrategy(
                    weight = patchItemResourcePair.first.weight,
                    clusters = getClusterWorkloadList(
                        scheduleStrategyEnvType = scheduleStrategyEnvType,
                        serverlessBaseAppName = serverlessBaseAppName,
                        resources = patchItemResourcePair.second
                    )
                )
            }
        )
    }

    /**
     * 基于集群均衡标签匹配资源
     */
    fun matchResource(resourceDO: ResourceDO, apREDeclarationPatchDataDO: ApREDeclarationPatchDataDO): List<PatchItem> {
        return apREDeclarationPatchDataDO.declarationPatch.patchItems.filter { patchItem ->
            val clusterSelector = checkNotNull(patchItem.clusterSelector) { "集群均衡策略，clusterSelector不能为空。" }
            when (clusterSelector.selectType) {
                ClusterSelectorType.CLUSTER -> clusterSelector.clusterIds.contains(resourceDO.clusterProfileNew!!.clusterId)
                ClusterSelectorType.CLUSTER_LABEL -> isMatchResourceLabel(resourceDO.apRELabels, clusterSelector)
            }
        }
    }

    /**
     * 集群标签匹配
     */
    private fun isMatchResourceLabel(resourceLabelList: List<ApRELabelDO>, clusterSelector: ClusterSelector): Boolean {
        if (clusterSelector.labels.isEmpty()) {
            throw ApREDeclarationPatchException("集群均衡策略，clusterSelector.labels不能为空。")
        }
        return clusterSelector.labels.all { selectorLabel ->
            resourceLabelList.any { selectorLabel.name == it.name && selectorLabel.value == it.value }
        }
    }

    /**
     * 不存在单站点多集群均衡策略场景
     * 计算集群策略
     */
    private fun buildCommonApREStrategyList(
        scheduleStrategyEnvType: ScheduleStrategyEnvType,
        serverlessBaseAppName: String?,
        declaration: Declaration,
        resources: List<ResourceDO>
    ): ApREStrategy {
        return ApREStrategy(
            site = checkNotNull(declaration.az) { "missing az/site in declaration" },
            stage = checkNotNull(declaration.stage) { "missing stage in declaration" },
            unit = checkNotNull(declaration.unit) { "missing unit in declaration" },
            weight = declaration.weight,
            resources = listOf(
                ResourceStrategy(
                    weight = 1,
                    clusters = getClusterWorkloadList(
                        scheduleStrategyEnvType = scheduleStrategyEnvType,
                        serverlessBaseAppName = serverlessBaseAppName,
                        resources = resources
                    )
                )
            )
        )
    }

    private fun getClusterWorkloadList(
        scheduleStrategyEnvType: ScheduleStrategyEnvType,
        serverlessBaseAppName: String?,
        resources: List<ResourceDO>
    ): List<ClusterWorkload> {
        if (scheduleStrategyEnvType == SERVERLESS_APP) {
            return resources.flatMap { resource ->
                resource.apRELabels.filter {
                    it.name == APRE_LABEL_FEATURE_NAME && it.value == "$RUNTIME_TEMPLATE_PREFIX$serverlessBaseAppName"
                }.flatMap { apRELabel ->
                    apRELabel.apREFeatureSpecs?.map { apREFeatureSpec ->
                        ClusterWorkload(
                            clusterId = resource.clusterProfileNew!!.clusterId,
                            clusterName = resource.clusterProfileNew.clusterName,
                            runtimeId = apREFeatureSpec.specCode
                        )
                    } ?: emptyList()
                }
            }
        }
        return resources.map {
            ClusterWorkload(
                clusterId = it.clusterProfileNew!!.clusterId,
                clusterName = it.clusterProfileNew.clusterName
            )
        }
    }

    private fun checkReqParams(scheduleStrategyReqDto: ScheduleStrategyReqDto) {
        if (scheduleStrategyReqDto.declarative) {
            requireNotNull(scheduleStrategyReqDto.resourceGroup) { "declarative need resourceGroup" }
        }
        if (scheduleStrategyReqDto.scheduleStrategyEnvType == SERVERLESS_APP) {
            checkNotNull(scheduleStrategyReqDto.serverlessBaseAppName) { "ServerlessApp场景，serverlessBaseAppName参数不能为空" }
        }
    }

    private fun buildApREDeed(scheduleStrategyReqDto: ScheduleStrategyReqDto): ApREDeedDO {
        if (scheduleStrategyReqDto.declarative) {
            //声明式构建ApREDeed
            return scheduleStandardService.getAssembledApREDeedByDeclarative(
                apREDeedKey = null,
                resourceGroup = checkNotNull(scheduleStrategyReqDto.resourceGroup) { "missing resourceGroup in scheduleStrategyReqDto in declarative req" },
                serverless = (scheduleStrategyReqDto.scheduleStrategyEnvType == SERVERLESS_APP),
                serverlessRuntimeTemplate = scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(
                    appName = scheduleStrategyReqDto.appName,
                    resourceGroup = scheduleStrategyReqDto.resourceGroup,
                    serverlessBaseAppName = checkNotNull(scheduleStrategyReqDto.serverlessBaseAppName){
                        "missing serverlessBaseAppName"
                    }
                )
            )
        }
        val declaration = checkNotNull(scheduleStrategyReqDto.declaration) { "missing declaration in scheduleStrategyReqDto" }
        //非声明式构建ApREDeed
        return ApREDeedDO(
            identityInfo = IdentityInfo(
                envLevel = declaration.stage,
                appName = scheduleStrategyReqDto.appName,
                nodeGroup = scheduleStrategyReqDto.resourceGroup
            ),
            declarations = mutableListOf(
                Declaration(
                    az = declaration.site,
                    unit = declaration.unit,
                    stage = declaration.stage,
                    matchApRELabels = buildMatchApRELabels(scheduleStrategyReqDto)
                )
            )
        )
    }

    private fun buildMatchApRELabels(scheduleStrategyReqDto: ScheduleStrategyReqDto): List<MatchApRELabel> {
        if (scheduleStrategyReqDto.scheduleStrategyEnvType == SERVERLESS_APP) {
            return scheduleStandardService.getServerlessMatchApRELabelList(
                serverlessRuntimeTemplate = scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(
                    appName = scheduleStrategyReqDto.appName,
                    resourceGroup = checkNotNull(scheduleStrategyReqDto.resourceGroup) { "missing resourceGroup in req of serverless scheduleStrategyEnvType" },
                    serverlessBaseAppName = scheduleStrategyReqDto.serverlessBaseAppName!!
                ),
                resourceGroup = scheduleStrategyReqDto.resourceGroup
            )
        }
        return scheduleStrategyReqDto.declaration!!.matchApRELabels.map {
            MatchApRELabel(name = it.name, value = it.value)
        }
    }
}

fun ScheduleStrategyResult.filterEmptyInvalidResourceStrategy(): ScheduleStrategyResult {
    return this.let {
        it.copy(
            apres = it.apres.map { apREStrategy ->
                apREStrategy.copy(
                    resources = apREStrategy.resources.filter { resourceStrategy -> resourceStrategy.clusters.isNotEmpty() }
                )
            }
        )
    }
}