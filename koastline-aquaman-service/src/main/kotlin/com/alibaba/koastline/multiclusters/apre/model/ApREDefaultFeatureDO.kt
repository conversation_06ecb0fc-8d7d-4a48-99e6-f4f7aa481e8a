package com.alibaba.koastline.multiclusters.apre.model

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant
import java.util.*

data class ApREDefaultFeatureDO (
    val id: Long?,
    val featureKey: String?,
    val code: String,
    /**
     * 使用方式，默认导入/指定导入
     */
    val featureUsage: String,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = "N",
    val defaultFeatureSpecs: List<ApREDefaultFeatureSpecDO>?
)

data class ApREDefaultFeatureSpecDO (
    val id: Long?,
    val featureKey: String?,
    val title: String,
    val specType: String?,
    val specCode: String,
    val scope: String?,
    val status: String?,
    val annotations: String?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = "N"
)