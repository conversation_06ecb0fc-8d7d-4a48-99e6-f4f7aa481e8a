package com.alibaba.koastline.multiclusters.apre.model.req

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.RuntimeProperties
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.fasterxml.jackson.annotation.JsonAlias
import io.swagger.annotations.ApiModelProperty

data class ApRECreateReqDto(
    @ApiModelProperty("名称", required = true)
    val name: String?,
    @ApiModelProperty("创建人", required = true)
    val creator: String,
    @ApiModelProperty("管控面KEY", required = true)
    val managedClusterKey: String,
    @ApiModelProperty("区域", required = true)
    val region: String,
    @ApiModelProperty("可用区（站点）", required = true)
    val az: String,
    @ApiModelProperty("用途", required = true)
    val stage: MetadataStageEnum,
    @ApiModelProperty("单元", required = true)
    val unit: String,
    @ApiModelProperty("状态", required = true)
    val status: ApREStatusEnum = ApREStatusEnum.ONLINE,
    @ApiModelProperty("注释信息", required = true)
    val metaData: Map<String, String> = mapOf(),
    @ApiModelProperty("标签列表", required = true)
    val apRELabelList: List<ApRELabelCreateReqDto> = emptyList()
)

data class ApRELabelCreateReqDto(
    @ApiModelProperty("关联目标key, 级联创建时候 无需输入", required = false)
    @JsonAlias(value = ["targetKey", "runtimeEnvKey"])
    val targetKey: String? = null,
    @ApiModelProperty("标签名称", required = true)
    val name: String,
    @ApiModelProperty("标签数值", required = true)
    val value: String,
    @ApiModelProperty("规格列表", required = true)
    val apREFeatureSpecList: List<ApREFeatureSpecCreateReqDto> = emptyList(),
    @ApiModelProperty("目标类型", required = true)
    val targetType: String,
    @ApiModelProperty("label类型", required = true)
    val type: ApRELabelType
) {
    fun convertToApRELabel(): ApRELabel {
        return ApRELabel(
            targetKey = checkNotNull(this.targetKey) { "targetKey cannot be null in convertToApRELabel" },
            name = this.name.lowercase(),
            value = this.value.lowercase(),
            apRELabelKey = KeyGenerator.generateAlphanumericKey(ApRELabelExt.APRE_LABEL_KEY_LENGTH),
            targetType = this.targetType,
            type = type
        )
    }
}

data class ApRELabelUpdateReqDto(
    @ApiModelProperty("ApRELabel Key", required = true)
    val apRELabelKey: String,
    @ApiModelProperty("关联目标key", required = true)
    @JsonAlias(value = ["targetKey", "runtimeEnvKey"])
    val targetKey: String,
    @ApiModelProperty("修改后标签名称", required = true)
    val name: String,
    @ApiModelProperty("修改好后标签数值", required = true)
    val value: String,
    @ApiModelProperty("修改后规格列表", required = true)
    val apREFeatureSpecList: List<ApREFeatureSpecCreateReqDto>,
    @ApiModelProperty("目标类型", required = true)
    val targetType: String,
    @ApiModelProperty("label类型", required = true)
    val type: ApRELabelType
)

data class ApREFeatureSpecCreateReqDto(
    @ApiModelProperty("标签KEY", required = false)
    val apRELabelKey: String? = null,
    @ApiModelProperty("标题", required = true)
    val title: String,
    @ApiModelProperty("规格类型", required = true)
    val specType: String = "common",
    @ApiModelProperty("规格代码", required = true)
    val specCode: String,
    @ApiModelProperty("使用范围", required = true)
    val scope: ApREFeatureSpecScopeEnum = ApREFeatureSpecScopeEnum.publish,
    @ApiModelProperty("状态", required = true)
    val status: ApREFeatureSpecStatusEnum = ApREFeatureSpecStatusEnum.online,
    @ApiModelProperty("来源类型", required = false)
    val sourceType: String? = null,
    @ApiModelProperty("来源ID", required = false)
    val sourceId: String? = null,
    @ApiModelProperty("版本类型", required = false)
    val versionType: String? = null,
    @ApiModelProperty("版本ID", required = false)
    val versionId: String? = null,
    @ApiModelProperty("注释信息", required = true)
    val annotations: Map<String, String> = emptyMap(),
    @ApiModelProperty("标签信息", required = false)
    val labels: Map<String, String> = emptyMap()
)

data class ResourcePoolCreateOrUpdateReqDto(
    @ApiModelProperty("绑定集群id", required = true)
    val clusterId: String,
    @ApiModelProperty("绑定管控集群切面id", required = true)
    val managedClusterKey: String,
    @ApiModelProperty("创建人", required = true)
    val creator: String,
    @ApiModelProperty("标签列表", required = true)
    val apRELabelList: List<ApRELabelCreateReqDto> = emptyList()
)


/**
 * 将对应集群对齐终态
 */
data class ClusterWithFeatureSelectorDto(
    @ApiModelProperty("选定的集群", required = true)
    val clusterId: String,
    @ApiModelProperty("集群特性", required = true)
    val clusterFeatureLabel: List<ApRELabelCreateReqDto>,
) {
    fun mapToResourcePoolCreateReqDto(managedClusterKey: String, creator: String): ResourcePoolCreateOrUpdateReqDto {
        return ResourcePoolCreateOrUpdateReqDto(
            clusterId = clusterId,
            managedClusterKey = managedClusterKey,
            creator = creator,
            apRELabelList = clusterFeatureLabel
        )
    }
}

data class ApREAndRelativeCreateReqDto(
    @ApiModelProperty("创建ApRE环境参数", required = true)
    val apREBaseCreateReqDto: ApREBaseDetailsCreateReqDto,
    @ApiModelProperty("选定集群及其特性", required = true)
    val clusterSelector: List<ClusterWithFeatureSelectorDto>
)

data class ApREBaseDetailsCreateReqDto(
    @ApiModelProperty("名称", required = true)
    val name: String?,
    @ApiModelProperty("创建人", required = true)
    val creator: String,
    @ApiModelProperty("可用区（站点）", required = true)
    val az: String,
    @ApiModelProperty("用途", required = true)
    val stage: MetadataStageEnum,
    @ApiModelProperty("单元", required = true)
    val unit: String,
    @ApiModelProperty("状态", required = true)
    val status: ApREStatusEnum = ApREStatusEnum.ONLINE,
    @ApiModelProperty("标签列表", required = true)
    val apRELabelList: List<ApRELabelCreateReqDto> = emptyList()
) {
    fun mapToApRECreateReqDto(
        kManageClusterKey: String,
        metaData: Map<String, String>,
        region: String
    ): ApRECreateReqDto {
        return ApRECreateReqDto(
            name = name,
            creator = creator,
            region = region,
            az = az,
            status = status,
            stage = stage,
            unit = unit,
            apRELabelList = apRELabelList,
            managedClusterKey = kManageClusterKey,
            metaData = metaData
        )
    }
}
data class ApREBaseDetailsUpdateReqDto(
    @ApiModelProperty("ApREKey", required = true)
    val runtimeEnvKey: String,
    @ApiModelProperty("名称", required = false)
    val name: String?,
    @ApiModelProperty("状态", required = true)
    val status: ApREStatusEnum,
    @ApiModelProperty("修改人", required = true)
    val modifier: String,
    @ApiModelProperty("修改后的ApRE Label 列表", required = true)
    val apRELabelList: List<ApRELabelCreateReqDto>
)

data class ApREPropertiesUpdateReqDto(
    @ApiModelProperty("ApREKey", required = true)
    val runtimeEnvKey: String,
    @ApiModelProperty("名称", required = false)
    val name: String?,
    @ApiModelProperty("状态", required = true)
    val status: ApREStatusEnum,
    @ApiModelProperty("修改人", required = true)
    val modifier: String,
)

data class ApREAndRelativeUpdateReqDto(
    @ApiModelProperty("更新ApRE环境参数", required = true)
    val apREBaseUpdateReqDto: ApREBaseDetailsUpdateReqDto,
    @ApiModelProperty("选定集群及其特性", required = true)
    val clusterSelector: List<ClusterWithFeatureSelectorDto>,
)

/**
 * 创建授权
 */
data class AttorneyCreateDto(
    @ApiModelProperty("绑定的ApREKey", required = true)
    val runtimeEnvKey: String,
    @ApiModelProperty("绑定的ApREKey, 授权选定集群列表, 授权特性列表", required = true)
    val selector: ApREBindingTerm,
    @ApiModelProperty("修改后授权特性范围", required = true)
    val matchScopeData: MatchScopeDataReqDto, // ApRE BindingData : MatchScopeData = 1 : 1 保证 MatchScope 享有唯一 ApRE BindingData
    val creator: String,
) {
    fun convertToAttorney(): Attorney {
        return Attorney(
            apREBindingData = ApREBindingData(
                runtimeEnvKey = runtimeEnvKey,
                apREBindingTerm = selector,
                id = null
            ),
            matchScopeData = matchScopeData.buildMatchScopeDataDO(
                creator = creator, targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
            )
        )
    }
}


data class CreateServerlessAttorneyDto(
    val serverlessBaseApp: String,
    val externalId: String,
    val externalType: MatchScopeExternalTypeEnum,
    val properties: RuntimeProperties,
    val description: String = "",
    val creator: String
) {
    fun validate() {
        require(serverlessBaseApp.isNotBlank()) { "serverlessBaseApp cannot be blank" }
        require(externalId.isNotBlank()) { "externalId cannot be blank" }
        require(creator.isNotBlank()) { "creator cannot be blank" }
    }
}

data class UpdateServerlessAttorneyDto(
    val serverlessBaseApp: String,
    val externalId: String,
    val externalType: MatchScopeExternalTypeEnum,
    val properties: RuntimeProperties,
    val description: String = "",
    val modifier: String
) {
    fun validate() {
        require(serverlessBaseApp.isNotBlank()) { "serverlessBaseApp cannot be blank" }
        require(externalId.isNotBlank()) { "externalId cannot be blank" }
        require(modifier.isNotBlank()) { "modifier cannot be blank" }
    }
}
