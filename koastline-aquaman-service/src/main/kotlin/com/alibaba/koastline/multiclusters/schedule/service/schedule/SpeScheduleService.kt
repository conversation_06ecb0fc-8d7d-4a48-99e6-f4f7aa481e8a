package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.md5
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.BendiSpeApi
import com.alibaba.koastline.multiclusters.external.DiamondApi.Companion.DIAMOND_DNS
import com.alibaba.koastline.multiclusters.external.EnvCenterApi
import com.alibaba.koastline.multiclusters.external.KaolaSpeApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.ConfigResult
import com.alibaba.koastline.multiclusters.schedule.model.SpeResult
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * @author:    <EMAIL>
 * @description:  spe调度服务
 * @date:    2024/2/25 9:15 AM
 */
@Component
class SpeScheduleService(
    val skylineApi: SkylineApi,
    val appCenterApi: AppCenterApi,
    val runtimeWorkloadMetaService: RuntimeWorkloadMetaService,
    val kaolaSpeApi: KaolaSpeApi,
    val bendiSpeApi: BendiSpeApi,
    val commonProperties: CommonProperties,
    val envCenterApi: EnvCenterApi
) {
    val log by logger()
    @Value("\${spe.host}")
    lateinit var speHost: String
    @Value("\${spe.account}")
    lateinit var speAccount: String
    @Value("\${spe.access.key}")
    lateinit var speAccessKey: String
    fun querySpeAppIps(appName: String, paramUrl: String? = null): List<String> {
        // 基座应用
        if (appCenterApi.isRuntimeApp(appName)) {
            val unitList = paramUrl ?.run {
                parseUnitFromParamUrl(paramUrl)
            } ?: emptyList()
            return querySpeAppIpsForRuntime(appName, unitList)
        }
        return querySpeAppIpsForCommonApp(appName = appName, paramUrl = paramUrl)
    }

    private fun querySpeAppIpsForRuntime(appName: String, unitList: List<String>): List<String> {
        val runtimeWorkloadIdList = runtimeWorkloadMetaService.listRunningWorkloadListByApp(appName = appName).filter {
            (unitList.isEmpty() || unitList.contains(it.unit))
                    && it.stage == MetadataStageEnum.SMALLFLOW.name
        }.map { it.runtimeWorkloadId }.ifEmpty { return emptyList() }
        return skylineApi.listServerByAppAndRuntimeWorkloadId(
            appName = appName,
            runtimeWorkloadIdList = runtimeWorkloadIdList
        ).map { it.ip!! }
    }

    private fun querySpeAppIpsForCommonApp(appName: String, paramUrl: String? = null): List<String> {
        if (enableMultiAppQuery(appName, paramUrl)) {
            // 临时依赖 env center 查询 Diamond 发布 ip, 后续将全部 SPE ip 查询逻辑迁移到 env center
            val multiAppIps = envCenterApi.queryDiamondSpeIps(appName, paramUrl!!)
            if (multiAppIps.isNotEmpty()) {
                return multiAppIps
            }
        }

        //暂时还是从spe获取
        return forwardToSpEnv(appName, paramUrl)
    }

    private fun forwardToSpEnv(appName: String, paramUrl: String? = null): List<String> {
        //暂时还是从spe获取
        val speResult = HttpClientUtils.httpGet(
            url = speHost + URL_QUERY_APP_IPS,
            params = mapOf(
                "appName" to appName,
                "paramUrl" to (paramUrl ?:""),
                "account" to speAccount,
                "token" to getToken()
            )
        ).run {
            JsonUtils.readValue(this, SpeResult::class.java)
        }
        if (!speResult.success) {
            throw ScheduleException("SPE查询失败")
        }
        return speResult.result ?.ips ?: emptyList()
    }

    private fun parseUnitFromParamUrl(paramUrl: String): List<String> {
        log.info("parseUnitFromParamUrl, paramUrl: $paramUrl")
        val configResult = HttpClientUtils.httpGet(url = paramUrl, params = emptyMap()).run {
            JsonUtils.readValue(this, ConfigResult::class.java)
        }
        if (!configResult.successful) {
            throw ScheduleException("call error,  paramUrl:$paramUrl")
        }
        return configResult.data!!.publishParams.firstOrNull {
            // unitEnvs: switch 专用
            // speUnit: spe 通用
            "unitEnvs" == it.paramName || "speUnit" == it.paramName
        } ?.newValue ?.split(",") ?.map {unit ->
            if (unit.startsWith(UNIT_PREFIX)){
                unit
            } else {
                UNIT_PREFIX + unit
            }
        } ?: emptyList()
    }

    private fun enableMultiAppQuery(appName: String, paramUrl: String? = null): Boolean {
        return !paramUrl.isNullOrBlank()
                && paramUrl.contains(DIAMOND_DNS)
                && !commonProperties.contains(CommonProperties.SPE_EXTRA_APP_LIST, appName)
                && !kaolaSpeApi.isKaolaApp(appName)
                && try {
                    !bendiSpeApi.isBendiApp(appName)
                } catch (e: Exception) {
                    log.error(e.message, e)
                    true
                }
                // 灰度判断
                && (commonProperties.firstOrNull(CommonProperties.SPE_DIAMOND_MULTI_APP_PUBLISH) ?: "true") == "true"
    }

    private fun getToken(): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH")
        val formattedString = LocalDateTime.now().format(formatter)
        return (speAccessKey + formattedString).md5()
    }

    companion object {
        private const val URL_QUERY_APP_IPS = "/api/app/queryAppIps"
        private const val UNIT_PREFIX = "CENTER_UNIT."
    }
}