package com.alibaba.koastline.multiclusters.event.consumer.app

import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently
import com.alibaba.rocketmq.common.message.MessageExt
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class AoneAppMessageListener: MessageListenerConcurrently {
    val log by logger()
    @Autowired
    lateinit var userLabelService: UserLabelService

    @Autowired
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService

    override fun consumeMessage(
        msgList: MutableList<MessageExt>,
        consumeConcurrentlyContext: ConsumeConcurrentlyContext
    ): ConsumeConcurrentlyStatus {
        log.info(" Receive aone app new messages: ${msgList}")
        msgList.forEach { messageExt ->
            processMessageExt(messageExt)
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS
    }

    private fun processMessageExt(messageExt: MessageExt) {
        try {
            val aoneAppMetaQMessage = JsonUtils.readValue(String(messageExt.body), AoneAppMetaQMessage::class.java)
            userLabelService.initAppLabels(aoneAppMetaQMessage.appInfo.name)
            resourceObjectFeatureService.initLightContainerForNewApp(aoneAppMetaQMessage.appInfo.name)
        }catch (e: Exception) {
            //metaq不展示异常信息，这里捕捉打印下异常日志
            log.error("AoneAppMessage - ${e.message}", e)
            throw e
        }
    }
}