package com.alibaba.koastline.multiclusters.event.consumer.app

import com.alibaba.ais.skyline.common.message.ItemOperateMessageDO
import com.alibaba.ais.skyline.common.message.OperateType
import com.alibaba.fastjson.JSON
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently
import com.alibaba.rocketmq.common.message.MessageExt
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.nio.charset.Charset

@Component
class SkylineMessageListener: MessageListenerConcurrently {

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    override fun consumeMessage(
        msgs: MutableList<MessageExt>,
        context: ConsumeConcurrentlyContext
    ): ConsumeConcurrentlyStatus {

        try {
            msgs.forEach { msg ->
                val message = JSON.parseObject(String(msg.body, UTF_8), ItemOperateMessageDO::class.java)

                if (message.operateType !in listOf(OperateType.DELETE_ITEM)) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS
                }

                logger.info("process skyline item operation: ${JSON.toJSONString(message)}")

                consumeMessage(message)
            }
        } catch (e: Exception) {
            logger.error("failed to consume skyline message: ${e.message}", e)
            return ConsumeConcurrentlyStatus.RECONSUME_LATER
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS
    }

    private fun consumeMessage(
        messageDO: ItemOperateMessageDO
    ) = when(messageDO.categoryName) {
        CATEGORY_APP_GROUP -> consumeAppGroupMessage(messageDO)
        else -> Unit
    }

    private fun consumeAppGroupMessage(messageDO: ItemOperateMessageDO) {
        messageDO.messageDetailDOList.forEach {
            val appName = it.itemLogDO.rawData.get("real_app_name") as? String
            val groupName = it.itemLogDO.rawData.get("name") as? String
            if (appName != null && groupName != null) {
                matchScopeService.deleteAppNameToGroupName(
                    groupName = groupName,
                    appName = appName,
                )
            }
        }

    }

    companion object {
        private val UTF_8: Charset = Charset.forName("UTF-8")
        private val logger = LoggerFactory.getLogger(SkylineMessageListener::class.java)
        private const val CATEGORY_APP_GROUP = "app_group"

    }
}