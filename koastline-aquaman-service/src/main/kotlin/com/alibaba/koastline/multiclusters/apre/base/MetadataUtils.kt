package com.alibaba.koastline.multiclusters.apre.base

object MetadataUtils {

    /**
     * 声明元数据匹配
     * @param declarationValue 声明字段，空代表该项任意值均可
     * @param metadataValue 元数据字段
     */
    fun matchDeclarationMetadata(declarationValue: Any?, metadataValue: Any?): Boolean {
        return declarationValue ?.run {
            if (declarationValue is String && declarationValue.trim().isBlank()) {
                true
            } else {
                declarationValue == metadataValue
            }
        } ?:run {
            true
        }
    }

    fun formatSite(site: String): String {
        return site.lowercase()
    }

    fun formatUnit(unit: String?): String? {
        if (unit.isNullOrBlank()) {
            return unit
        }
        if (unit.startsWith(UNIT_PREFIX)) {
            return unit
        }
        return "$UNIT_PREFIX$unit"
    }

    fun formatShortUnit(unit: String): String {
        return unit.removePrefix(UNIT_PREFIX)
    }

    private const val UNIT_PREFIX = "CENTER_UNIT."
}