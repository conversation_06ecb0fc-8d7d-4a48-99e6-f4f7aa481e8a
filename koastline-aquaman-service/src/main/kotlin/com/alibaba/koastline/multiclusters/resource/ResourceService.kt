package com.alibaba.koastline.multiclusters.resource

import com.alibaba.env.orchestration.protocol.common.V2ResourceStatus
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager.Companion.LOGIN_ACCOUNT_UNKNOWN
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceBoxRepo
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceOwnerReferenceRepo
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceRepo
import com.alibaba.koastline.multiclusters.data.vo.resource.OwnerReference
import com.alibaba.koastline.multiclusters.data.vo.resource.Resource
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceBox
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceDesc
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceOwnerReference
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceCreateReq
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceDelReq
import com.fasterxml.jackson.databind.node.ObjectNode
import org.springframework.stereotype.Component
import javax.transaction.Transactional

/**
 * @author:    <EMAIL>
 * @date:    2024/5/15 1:57 PM
 */
@Component
class ResourceService(
    val resourceRepo: ResourceRepo,
    val resourceBoxRepo: ResourceBoxRepo,
    val resourceOwnerReferenceRepo: ResourceOwnerReferenceRepo,
) {
    val log by logger()

    /**
     * 提交资源协议版本
     */
    @Transactional
    fun applyResourceSpec(resourceSpec: String) {
        val resourceProtocol = JsonUtils.readValue(resourceSpec, ResourceProtocol::class.java)
        val resource = resourceRepo.findByKindAndName(
            kind = resourceProtocol.kind,
            name = resourceProtocol.metadata.name!!,
        ) ?: run {
            createResourceFromProtocol(resourceProtocol)
        }
        resourceBoxRepo.findByResourceIdAndVersion(
            resourceId = resource.id!!,
            version = resourceProtocol.getVersion(),
        ) ?: let {
            resourceBoxRepo.insert(
                ResourceBox(
                    resourceId = resource.id!!,
                    spec = resourceSpec,
                    version = resourceProtocol.getVersion(),
                    creator = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
                    modifier = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
                )
            )
            if (resourceProtocol.getVersion() != 1) {
                return
            }
            /**
             * 暂时只针对首次版本做REF注册
             */
            resourceProtocol.metadata.ownerReferences?.forEach { ownerReference ->
                resourceOwnerReferenceRepo.insert(
                    ResourceOwnerReference(
                        subResourceId = resource.id!!,
                        ownerRefKind = ownerReference.kind,
                        ownerRefName = ownerReference.name,
                        blockOwnerDeletion = ownerReference.blockOwnerDeletion ?: true,
                        creator = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
                        modifier = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
                    )
                )
            }
        }
    }

    fun createResourceOwnerReference(resourceOwnerReferenceCreateReq: ResourceOwnerReferenceCreateReq) {
        val subResource = resourceRepo.findByKindAndName(kind = resourceOwnerReferenceCreateReq.kind,name = resourceOwnerReferenceCreateReq.name)
            ?:throw ResourceException("未找到符合的Resource,[kind:${resourceOwnerReferenceCreateReq.kind},name:${resourceOwnerReferenceCreateReq.name}]")
        resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(subResource.id!!).firstOrNull {
            it.ownerRefKind == resourceOwnerReferenceCreateReq.ownerKind && it.ownerRefName == resourceOwnerReferenceCreateReq.ownerName
        }?.let {
            throw ResourceException("已存在符合的OwnerReference,subResource[kind:${resourceOwnerReferenceCreateReq.kind},name:${resourceOwnerReferenceCreateReq.name}],ownerRef[Kind:${resourceOwnerReferenceCreateReq.ownerKind},name:${resourceOwnerReferenceCreateReq.ownerName}]")
        }
        resourceOwnerReferenceRepo.insert(
            ResourceOwnerReference(
                subResourceId = subResource.id!!,
                ownerRefKind = resourceOwnerReferenceCreateReq.ownerKind,
                ownerRefName = resourceOwnerReferenceCreateReq.ownerName,
                blockOwnerDeletion = resourceOwnerReferenceCreateReq.blockOwnerDeletion ?: true,
                creator = resourceOwnerReferenceCreateReq.creator,
                modifier = resourceOwnerReferenceCreateReq.creator,
            )
        )
    }

    fun delResourceOwnerReference(resourceOwnerReferenceDelReq: ResourceOwnerReferenceDelReq) {
        val subResource = resourceRepo.findByKindAndName(kind = resourceOwnerReferenceDelReq.kind,name = resourceOwnerReferenceDelReq.name)
            ?:throw ResourceException("未找到符合的Resource,[kind:${resourceOwnerReferenceDelReq.kind},name:${resourceOwnerReferenceDelReq.name}]")
        resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(subResource.id!!).firstOrNull {
            it.ownerRefKind == resourceOwnerReferenceDelReq.ownerKind && it.ownerRefName == resourceOwnerReferenceDelReq.ownerName
        }?.let {
            resourceOwnerReferenceRepo.deleteById(it.id!!)
        }
    }

    /**
     * 设置资源版本基线
     */
    @Transactional
    fun setBaselineOfResourceBox(resourceSpec: String) {
        val resourceProtocol = JsonUtils.readValue(resourceSpec, ResourceProtocol::class.java)
        val resource = resourceRepo.findByKindAndName(
            kind = resourceProtocol.kind,
            name = resourceProtocol.metadata.name!!,
        )
            ?: throw ResourceException("未找到符合的Resource,[kind:${resourceProtocol.kind},name:${resourceProtocol.metadata.name}]")
        val resourceBox = resourceBoxRepo.findByResourceIdAndVersion(
            resourceId = resource.id!!,
            version = resourceProtocol.getVersion(),
        )
            ?: throw ResourceException("未找到符合的ResourceBox,[resourceId:${resource.id},version:${resourceProtocol.getVersion()}]")
        if (resource.baselineBoxId > resourceBox.id!!) {
            throw ResourceException("Resource当前版本大于待设置版本,[currentBaselineBoxId:${resource.baselineBoxId},toSetBaselineBoxId:${resourceBox.id}]")
        }
        resourceRepo.updateBaselineBoxIdById(
            id = resource.id!!,
            baseLineBoxId = resourceBox.id!!
        )

        // 将 status 更新到 spec 中
        if (resourceProtocol.status != null) {
            val specNode = JsonUtils.readTree(resourceBox.spec) as ObjectNode
            specNode.set("status", JsonUtils.value2Tree(resourceProtocol.status))
            resourceBoxRepo.updateSpecById(resourceBox.id!!, JsonUtils.writeValueAsString(specNode))
        }
    }

    @Transactional
    fun updateResourceStatus(kind: String, name: String, version: Int, resourceStatus: V2ResourceStatus) {
        val resource = resourceRepo.findByKindAndName(
            kind = kind,
            name = name,
        ) ?: throw ResourceException("未找到符合的Resource,[kind:${kind},name:${name}]")
        val resourceBox = resourceBoxRepo.findByResourceIdAndVersion(
            resourceId = resource.id!!,
            version = version,
        ) ?: throw ResourceException("未找到符合的ResourceBox,[resourceId:${resource.id},version:${version}]")

        // 将 status 更新到 spec 中
        val specNode = JsonUtils.readTree(resourceBox.spec) as ObjectNode
        specNode.set("status", JsonUtils.value2Tree(resourceStatus))
        resourceBoxRepo.updateSpecById(resourceBox.id!!, JsonUtils.writeValueAsString(specNode))
    }

    @Transactional
    fun createResourceFromProtocol(resourceProtocol: ResourceProtocol): Resource {
        val resource = Resource(
            kind = resourceProtocol.kind,
            name = resourceProtocol.metadata.name!!,
            serviceProvider = resourceProtocol.metadata.serviceProvider!!,
            controller = resourceProtocol.metadata.deleteOptions?.controller ?: true,
            needRecycling = resourceProtocol.metadata.deleteOptions?.needRecycling ?: true,
            creator = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
            modifier = resourceProtocol.metadata.audit?.submitterEmpId ?: LOGIN_ACCOUNT_UNKNOWN,
        )
        resourceRepo.insert(resource)
        return resource
    }

    /**
     * 级联查询当前资源&子资源
     */
    fun queryCascadedResourceDescList(kind: String, name: String, expectSubResourceLevel: Int? = 1): List<ResourceDesc> {
        val resourceDescList = mutableListOf<ResourceDesc>()
        resourceDescList.add(queryResourceDesc(kind = kind, name = name, sub = false))
        resourceDescList.addAll(querySubResourceDescList(ownerRefKind = kind, ownerRefName = name, currentSubResourceLevel = 0, expectSubResourceLevel = expectSubResourceLevel?:1))
        return resourceDescList
    }

    /**
     * 查询资源&版本明细
     */
    fun queryResourceDesc(kind: String, name: String, sub: Boolean): ResourceDesc {
        val resource = resourceRepo.findByKindAndName(kind = kind, name = name)
            ?: throw ResourceException("未找到符合的Resource,[kind:$kind,name:$name]")

        val resourceBox = if (resource.baselineBoxId == -1L) {
            log.warn("Resource当前版本未设置基线,[resourceId:${resource.id}],查看最近一次版本")
            resourceBoxRepo.findLatestByResourceId(resource.id!!)
        }else {
            resourceBoxRepo.findById(resource.baselineBoxId)
        }?: throw ResourceException("未找到符合的ResourceBox,[resourceId:${resource.id},version:${resource.baselineBoxId}]")
        return toResourceDesc(resource, resourceBox.spec, sub)
    }

    @Transactional
    fun deleteResource(kind: String, name: String) {
        resourceOwnerReferenceRepo.querySubResourceByOwnerReference(ownerRefKind = kind, ownerRefName = name).map {
            resourceRepo.findById(it.subResourceId)
        }.filter{subResource ->
            subResource != null && subResource.isDeleted == "N" && subResource.needRecycling
        }.let {subResourceList ->
            if (subResourceList.isNotEmpty()) {
                throw ResourceException("存在子资源未删除，无法删除当前资源,当前资源[kind:${kind},name:${name}],待清理子资源列表:${JsonUtils.writeValueAsString(subResourceList)})")
            }
        }
        val resource = resourceRepo.findByKindAndName(kind = kind, name = name)?:return
        //清理父级关联关系
        resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(resource.id!!).forEach {
            resourceOwnerReferenceRepo.deleteById(it.id!!)
        }
        resourceRepo.deleteById(resource.id!!)
    }

    /**
     * 查询子资源
     * @param ownerRefKind 父级资源类型
     * @param ownerRefName 父级资源名
     * @param currentSubResourceLevel 当前子资源层级
     * @param expectSubResourceLevel 预期子资源层级（-1代表全部）
     */
    private fun querySubResourceDescList(ownerRefKind: String, ownerRefName: String, currentSubResourceLevel: Int, expectSubResourceLevel: Int): List<ResourceDesc> {
        if (expectSubResourceLevel != RESOURCE_ALL_SUB_LEVEL && currentSubResourceLevel == expectSubResourceLevel) {
            return emptyList()
        }
        val resourceDescList = mutableListOf<ResourceDesc>()
        resourceOwnerReferenceRepo.querySubResourceByOwnerReference(
            ownerRefKind = ownerRefKind,
            ownerRefName = ownerRefName
        ).forEach {
            val resource = resourceRepo.findById(it.subResourceId)
                ?: throw ResourceException("未找到符合的Resource,[resourceId:${it.subResourceId}]")
            resourceDescList.add(queryResourceDesc(kind = resource.kind, name = resource.name, sub = true))
            resourceDescList.addAll(querySubResourceDescList(ownerRefKind = resource.kind, ownerRefName = resource.name, currentSubResourceLevel + 1, expectSubResourceLevel))
        }
        return resourceDescList.distinctBy { "${it.name}-${it.kind}" }
    }

    private fun toResourceDesc(resource: Resource, spec: String, sub: Boolean): ResourceDesc {
        return ResourceDesc(
            kind = resource.kind,
            name = resource.name,
            serviceProvider = resource.serviceProvider,
            controller = resource.controller,
            needRecycling = resource.needRecycling,
            spec = spec,
            sub= sub,
            ownerReferences = resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(resource.id!!).map {
                OwnerReference(
                    kind = it.ownerRefKind,
                    name = it.ownerRefName,
                    blockOwnerDeletion = it.blockOwnerDeletion,
                )
            }
        )
    }
    companion object{
        const val RESOURCE_ALL_SUB_LEVEL = -1
    }
}