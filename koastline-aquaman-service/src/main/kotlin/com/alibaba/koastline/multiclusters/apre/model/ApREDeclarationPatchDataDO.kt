package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.*

data class ApREDeclarationPatchDataDO (
    val id: Long? = null,
    @ApiModelProperty("类型")
    val balanceType: String  = ApREDeclarationPatchType.BALANCE_SITE.name,
    @ApiModelProperty("区域")
    val region: String? = null,
    @ApiModelProperty("单元")
    val unit: String,
    @ApiModelProperty("用途标")
    val stage: String,
    @ApiModelProperty("站点")
    val site: String? = null,
    val declarationPatch: DeclarationPatch,
    @ApiModelProperty("创建人工号")
    val creator: String,
    @ApiModelProperty("修改人工号")
    val modifier: String,
    @Deprecated("使用MatchScope优先级替代")
    @ApiModelProperty("优先级")
    val priority: Int = 1,
    val gmtCreate: Date? = null,
    val gmtModified: Date? = null,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = null,
    val matchScopeDataDOs: List<MatchScopeDataDO>? = null
)

@ApiModel("附加声明限定条款")
data class DeclarationPatch (
    val patchItems: List<PatchItem> = emptyList(),
){
    fun validate(){
        patchItems.forEach {
            it.validate()
        }
        // 站点均衡策略参数重复校验
        val sitePolices = patchItems.filter { it.site != null }
        val allSite = sitePolices.mapNotNull { it.site }.distinct()
        require(sitePolices.size == allSite.size) {
            "duplicated site policy!"
        }
        // 集群&特性均衡策略参数重复校验
        val clusterSelectorPolices = patchItems.filter { it.clusterSelector != null }
        val clusterSelectorIdentities = clusterSelectorPolices.mapNotNull { it.clusterSelector?.identity() }
        require(clusterSelectorIdentities.size == clusterSelectorIdentities.distinct().size) {
            "duplicated clusterSelector policy!"
        }
        val clusterParams = clusterSelectorPolices.mapNotNull { it.clusterSelector?.clusterIds }.flatten()
        require(clusterParams.size == clusterParams.distinct().size) {
            "duplicated cluster param policy!"
        }
        val featParams = clusterSelectorPolices.mapNotNull { it.clusterSelector?.labels }.flatten()
        require(featParams.size == featParams.distinct().size) {
            "duplicated feat param policy!"
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PatchItem (
    /**
     * 站点均衡模式，必填,已开放对外集成，暂保留该结构
     */
    val region: String? = null,
    val site: String? = null,
    /**
     * 集群均衡模式，必填
     */
    val clusterSelector: ClusterSelector? = null,
    /**
     * 权重
     */
    val weight: Int = 1
){
    fun validate(){
        require(weight >= 1 ){
            "weight of patchItem cannot must be positive integer"
        }
        require(site!= null && clusterSelector == null || site == null && clusterSelector != null){
            "patchItem only can be set with one policy!"
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ClusterSelector (
    val selectType: ClusterSelectorType = ClusterSelectorType.CLUSTER_LABEL,
    val labels: List<ClusterLabel> = emptyList(),
    val clusterIds: List<String> = emptyList()
) {
    fun identity() = when (selectType) {
        ClusterSelectorType.CLUSTER -> ClusterSelectorType.CLUSTER.name + "$" + clusterIds.joinToString("$")
        ClusterSelectorType.CLUSTER_LABEL -> ClusterSelectorType.CLUSTER_LABEL.name + "$" +  labels.joinToString("$")
    }
}

data class ClusterLabel (
    val name: String,
    val value: String
) {
    override fun toString() = "$name=$value"
}

enum class ClusterSelectorType {
    /**
     * 指定集群
     */
    CLUSTER,
    /**
     * 指定集群标签
     */
    CLUSTER_LABEL
}