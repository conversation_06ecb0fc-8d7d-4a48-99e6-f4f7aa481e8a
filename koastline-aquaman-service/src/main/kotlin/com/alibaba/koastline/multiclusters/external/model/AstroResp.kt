package com.alibaba.koastline.multiclusters.external.model

data class AstroResult<T>(
    /**
     * 是否执行成功(不可作为是否调用成功标示)
     */
    var success: Boolean,
    /**
     * 错误信息
     */
    val message: String? = null,
    /**
     * 返回值内容
     */
    val data: T? = null,
    /**
     * 错误码
     */
    val code: String? = null,
    /**
     * 执行请求机器ip，以便排查问题
     */
    val ip: String? = null
)

data class CommonQueryResult<T> (
    val start: Int = 0,
    val total: Int = 0,
    val result: List<T>,
)

data class AppUserLabel(
    val appName: String,
    val labelValue: String,
    val labelName: String
)
