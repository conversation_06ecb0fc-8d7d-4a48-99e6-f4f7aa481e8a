package com.alibaba.koastline.multiclusters.common.utils

import org.yaml.snakeyaml.DumperOptions
import org.yaml.snakeyaml.Yaml
import org.yaml.snakeyaml.nodes.Tag

object YamlUtils {
    val dumperOptions = DumperOptions().apply {
        this.setDereferenceAliases(true)
    }
    
    fun dump(paramMap: Map<String, Any>): String {
        return newYaml().dumpAs(paramMap, Tag.MAP, DumperOptions.FlowStyle.BLOCK)
    }

    fun json2yaml(json: String): String {
        val jsonMap = JsonUtils.readValue(json, Map::class.java)
        return YamlUtils.dump(jsonMap as Map<String, Any>)
    }

    fun yaml2json(yaml: String): String {
        val yamlMap = YamlUtils.load(yaml)
        return JsonUtils.writeValueAsString(yamlMap)
    }

    fun load(yamlStr: String) : Map<String, Any> {
        return newYaml().load(yamlStr)
    }

    /*
    snakeYaml is not thread-safe, so we need to create a new instance every time
     */
    fun newYaml(): Yaml {
        return Yaml(dumperOptions)
    }
}