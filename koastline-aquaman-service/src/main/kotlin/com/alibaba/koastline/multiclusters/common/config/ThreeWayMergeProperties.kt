package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration


@Configuration
@RefreshScope
class ThreeWayMergeProperties {
    @Autowired
    lateinit var appCenterClient: AppCenterApi

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    var properties = ThreeWayMergeDiamondConfig.buildProperties()

    fun whetherInThreeWayMerge(
        appName: String,
        stage: String,
        resourceObjectProtocol: ResourceObjectProtocolEnum,
        operationScene: ResourceObjectSceneEnum,
    ): Boolean {
        if (whetherInThreeWayMergeBlackList(appName, stage)) {
            return false
        }
        if (resourceObjectProtocol == ResourceObjectProtocolEnum.RollingSet) {
            return true
        }
        if (resourceObjectProtocol == ResourceObjectProtocolEnum.ServerlessApp) {
            return false
        }
        if (whetherInThreeWayMergeWhiteList(appName, stage)) {
            return true
        }
        if (whetherInThreeWayMergeAppSuffixWhiteList(appName, stage)) {
            return true
        }
        if (whetherServeProductionTraffic(stage)) {
            return whetherInThreeWayMergeForProductionTraffic(appName, operationScene, resourceObjectProtocol)
        }
        return false
    }

    fun whetherServeProductionTraffic(
        stage: String,
    ): Boolean {
        return stage in listOf(
            MetadataStageEnum.PUBLISH.name,
            MetadataStageEnum.GRAY.name,
            MetadataStageEnum.SMALLFLOW.name,
        )
    }

    fun whetherInThreeWayMergeBlackList(
        appName: String,
        stage: String
    ): Boolean {
        return properties[THREE_WAY_MERGE_APP_STAGE_BLACK_LIST]?.get(appName)?.contains(stage) ?: false
    }

    fun whetherInThreeWayMergeWhiteList(
        appName: String,
        stage: String
    ): Boolean {
        return properties[THREE_WAY_MERGE_APP_STAGE_WHITE_LIST]?.get(appName)?.contains(stage) ?: false
    }

    fun whetherInThreeWayMergeAppSuffixWhiteList(
        appName: String,
        stage: String
    ): Boolean {
        val appId = appCenterClient.getAppInfoByName(appName).id
        return properties[THREE_WAY_MERGE_APPID_SUFFIX_WHITE_LIST]?.get(appId.mod(10).toString())?.contains(stage)
            ?: false
    }

    fun whetherInThreeWayMergeProductlineAppIdSuffix(
        productLineId: String,
        appId: Long,
    ): Boolean {
        return properties[THREE_WAY_MERGE_PROD_PRODUCTLINE_APPID_SUFFIX_WHITE_LIST]?.get(productLineId)
            ?.contains(appId.mod(10).toString())
            ?: false
    }

    fun whetherInThreeWayMergeProductlineAppgrade(
        productLineId: String,
        appGrade: String,
    ): Boolean {
        return properties[THREE_WAY_MERGE_PROD_PRODUCTLINE_APPGRADE_WHITE_LIST]?.get(productLineId)?.contains(appGrade)
            ?: false
    }

    fun whetherInThreeWayMergeOpsProtocol(
        operationScene: String,
        protocol: String,
    ): Boolean {
        return properties[THREE_WAY_MERGE_PROD_OPS_PROTOCOL_WHITE_LIST]?.get(operationScene)?.contains(protocol)
            ?: false
    }

    fun whetherInThreeWayMergeForProductionTraffic(
        appName: String,
        operationScene: ResourceObjectSceneEnum,
        protocol: ResourceObjectProtocolEnum,
    ): Boolean {
        if (!whetherInThreeWayMergeOpsProtocol(operationScene.name, protocol.name)) {
            return false
        }
        val appInfo = appCenterClient.getAppInfoByName(appName)
        return matchScopeService.buildProductLineTransformedIds(appInfo.buId, appInfo.productFullLineIdPath)
            .firstOrNull {
                whetherInThreeWayMergeProductlineAppgrade(it, appInfo.level?.name ?: AppLevelEnum.UNDEFINED.name)
                        ||
                        whetherInThreeWayMergeProductlineAppIdSuffix(it, appInfo.id)
            } != null
    }

    companion object {
        const val THREE_WAY_MERGE_APP_STAGE_WHITE_LIST = "THREE_WAY_MERGE_APP_STAGE_WHITE_LIST"

        /*
        following 2 configs are effective for PUBLISH/GRAY/SMALLFLOW stage which serve online traffic
         */
        const val THREE_WAY_MERGE_PROD_PRODUCTLINE_APPGRADE_WHITE_LIST =
            "THREE_WAY_MERGE_PROD_PRODUCTLINE_APPGRADE_WHITE_LIST"
        const val THREE_WAY_MERGE_PROD_OPS_PROTOCOL_WHITE_LIST = "THREE_WAY_MERGE_PROD_OPS_PROTOCOL_WHITE_LIST"

        const val THREE_WAY_MERGE_APP_STAGE_BLACK_LIST = "THREE_WAY_MERGE_APP_STAGE_BLACK_LIST"
        const val THREE_WAY_MERGE_APPID_SUFFIX_WHITE_LIST = "THREE_WAY_MERGE_APPID_SUFFIX_WHITE_LIST"
        const val THREE_WAY_MERGE_PROD_PRODUCTLINE_APPID_SUFFIX_WHITE_LIST =
            "THREE_WAY_MERGE_PROD_PRODUCTLINE_APPID_SUFFIX_WHITE_LIST"
    }
}