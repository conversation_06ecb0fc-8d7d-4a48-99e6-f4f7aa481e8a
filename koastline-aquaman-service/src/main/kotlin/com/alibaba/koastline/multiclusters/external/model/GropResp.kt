package com.alibaba.koastline.multiclusters.external.model

import com.fasterxml.jackson.annotation.JsonProperty

data class GropResp<T>(

    val success: Boolean,
    val data: T?,
    val message: String?,

)

data class CpuShareRespData(
    val data: CpuShareData?
)

data class CpuShareData(
    val allocSpec: String?
)

data class CpuShareStatus(
    @JsonProperty(value = "CpuSetMode")
    var cpuSetMode: String?,
)
