package com.alibaba.koastline.multiclusters.apre.common

import com.alibaba.koastline.multiclusters.apre.ApRELabelDefineService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.matchDeclarationMetadata
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSourceEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.checkApRELabelTargetType
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelNotFoundException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelSpecNotFoundException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.ApREFeatureSpecRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ApRELabelRepo
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApREFeatureSpec
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible


/**
 * refactor by boche in 2023.2.24
 */
@Component
class ApRELabelService {
    val log by logger()

    @Autowired
    lateinit var apRELabelRepo: ApRELabelRepo

    @Autowired
    lateinit var apRELabelSpecRepo: ApREFeatureSpecRepo

    @Autowired
    lateinit var apREDefaultFeatureService: ApREDefaultFeatureService

    @Autowired
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo

    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    @Autowired
    lateinit var apRELabelDefinitionService: ApRELabelDefineService

    @Autowired
    lateinit var apREFeatureSpecService: ApREFeatureSpecService

    /**
     * 参照 TargetKey & TargetId 获取 label 并级联获取对应的特性规格
     * @param targetKey label 关联对象键
     * @param targetType label 关联对象类型
     */
    fun findApRELabelByTarget(targetKey: String, targetType: String): List<ApRELabelDO> {
        val apRELabelList = apRELabelRepo.findByTarget(targetKey, targetType)
        val apREFeatureSpecList = apREFeatureSpecService.batchQueryOnlineApREFeatureSpecByLabel(
            apRELabelKeyList = apRELabelList.map { it.apRELabelKey }
        )
        val apRELabelDOs = apRELabelList.map { apRELabel ->
            val matchApREFeatureSpecList = apREFeatureSpecList.filter { apREFeatureSpecDO ->
                apRELabel.apRELabelKey == apREFeatureSpecDO.apRELabelKey
            }
            transformToCustomizedFeatureApRELabelDO(
                apRELabel = apRELabel, apREFeatureSpecs = if (apRELabel.name == APRE_LABEL_FEATURE_NAME) {
                    matchApREFeatureSpecList.ifEmpty {
                        apREFeatureSpecService.findDefaultApREFeatureSpecByLabelValue(apRELabel.value)
                    }
                } else {
                    matchApREFeatureSpecList
                }
            )
        }
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    /**
     * 返回所有的特性规格
     */
    fun findAllApRELabelByTarget(targetKey: String, targetType: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        apRELabelRepo.findByTarget(targetKey, targetType).mapTo(apRELabelDOs) {
            transformToCustomizedFeatureApRELabelDO(
                apRELabel = it,
                apREFeatureSpecs = apREFeatureSpecService.findAllApREFeatureSpecByLabel(it.apRELabelKey)
            )
        }
        // 注入title
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    fun findApRELabelByLabelKey(apRELabelKey: String): ApRELabelDO? {
        return apRELabelRepo.findByApRELabelKey(apRELabelKey)?.let {
            transformToCustomizedFeatureApRELabelDO(
                apRELabel = it, apREFeatureSpecs = if (it.name == APRE_LABEL_FEATURE_NAME) {
                    apREFeatureSpecService.findOnlineApREFeatureSpecByLabelWithDefault(it)
                } else {
                    apREFeatureSpecService.findOnlineApREFeatureSpecByLabel(it.apRELabelKey)
                }
            )
        } ?.let {
            it.title =  apRELabelDefinitionService.getApRELabelDefinition(it.name, it.value)?.title ?: "${it.name}=${it.value}"
            it
        }
    }


    fun findApRELabelByTargetWithDefaultLabel(targetKey: String, targetType: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        // 查询ApRELabel 按照类别
        apRELabelDOs.addAll(findApRELabelByTarget(targetKey, targetType))
        val apRELabelValues = apRELabelDOs.map { it.value }
        // 添加默认导入的特性&规格
        apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage().filter {
            !apRELabelValues.contains(it.code)
        }.forEach {
            apRELabelDOs.add(
                ApRELabelDO(
                    id = it.id,
                    targetKey = targetKey,
                    name = APRE_LABEL_FEATURE_NAME,
                    value = it.code,
                    gmtCreate = it.gmtCreate,
                    gmtModified = it.gmtModified,
                    isDeleted = it.isDeleted,
                    apRELabelKey = it.featureKey,
                    apREFeatureSpecs = apREFeatureSpecService.convertDefaultFeatureSpec(it.defaultFeatureSpecs!!),
                    source = ApREFeatureSourceEnum.DEFAULT.name,
                    targetType = ApRELabelTargetTypeEnum.valueOf(targetType),
                    type = ApRELabelType.DEFAULT,
                    title = "$APRE_LABEL_FEATURE_NAME/${it.code}"
                )
            )
        }
        // 注入title
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    /**
     * 创建运行时环境标签，如果存在则忽略;同时级联创建特性规格
     */
    @Transactional
    fun createApRELabelIgnoreWhileExistWithFeatureSpec(reqDto: ApRELabelCreateReqDto): ApRELabelDO {
        requireNotNull(reqDto.targetType) { "targetType cannot be blank in createApRELabelIgnoreWhileExistWithFeatureSpec " }
        requireNotNull(reqDto.targetKey) { "targetKey cannot be blank in createApRELabelIgnoreWhileExistWithFeatureSpec" }
        checkApRELabelTargetType(reqDto.targetType)

        val apRELabel = apRELabelRepo.findByTarget(reqDto.targetKey, reqDto.targetType).firstOrNull {
            it.name == reqDto.name.lowercase() && it.value == reqDto.value.lowercase() && it.targetType == reqDto.targetType
        } ?: let {
            reqDto.convertToApRELabel().let { label ->
                if (apRELabelRepo.insert(label) == 0) throw ApREException("create runtime environment label causes exception")
                label
            }
        }

        val apREFeatureSpecs = mutableListOf<ApREFeatureSpecDO>()
        reqDto.apREFeatureSpecList.forEach {
            apREFeatureSpecs.add(
                apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(
                    it.copy(apRELabelKey = apRELabel.apRELabelKey)
                )
            )
        }
        return transformToCustomizedFeatureApRELabelDO(apRELabel = apRELabel, apREFeatureSpecs = apREFeatureSpecs)
    }

    /**
     * 修改 ApRE Label 并级联修改 ApRE Feature Spec
     * 若 不存在 ApRE label 则级联创建
     * 注: 传入的 ApRE Feature Spec 为终态 会删除没有在终态特性规格中数据库数据
     */
    @Transactional
    fun updateApRELabelWithFeatureSpec(reqDto: ApRELabelUpdateReqDto) {
        log.info("updateApRELabelWithFeatureSpec:$reqDto")
        checkApRELabelTargetType(reqDto.targetType)
        val apRELabelKey = checkNotNull(reqDto.apRELabelKey)
        val originalApRELabel = apRELabelRepo.findByApRELabelKey(apRELabelKey) ?: let {
            throw ApRELabelException("cannot find ApRE label with $apRELabelKey")
        }

        val originalFeatureSpecs = apRELabelSpecRepo.queryByApRELabelKey(apRELabelKey)
        val finalFeatureSpecs = reqDto.apREFeatureSpecList
        // Feature spec 存量在终态未出现的进行删除
        val toDeleteSpecs = mutableListOf<ApREFeatureSpec>()
        // 不删除写入的serverless
        originalFeatureSpecs.forEach { originalFeatureSpec ->
            val intersectionalFeatureSpec = finalFeatureSpecs.firstOrNull { finalFeatureSpec ->
                finalFeatureSpec.specCode == originalFeatureSpec.specCode
                        && finalFeatureSpec.sourceType == originalFeatureSpec.sourceType
                        && finalFeatureSpec.sourceId == originalFeatureSpec.sourceId
            }
            intersectionalFeatureSpec ?: let {
                toDeleteSpecs.add(originalFeatureSpec)
            }
        }
        toDeleteSpecs.forEach {
            val id = checkNotNull(it.id) { "to Delete Spec id cannot be null" }
            apRELabelSpecRepo.deleteById(id)
        }
        finalFeatureSpecs.forEach {
            apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(it)
        }
        // 修改 ApRE Label 属性
        val now = Date(Instant.now().toEpochMilli())
        val apRELabel = ApRELabel(
            name = reqDto.name,
            value = reqDto.value,
            targetType = reqDto.targetType,
            targetKey = reqDto.targetKey,
            gmtModified = now,
            apRELabelKey = apRELabelKey,
            gmtCreate = originalApRELabel.gmtCreate,
            type = originalApRELabel.type
        )
        apRELabelRepo.updateByApRELabelKey(apRELabel)
    }

    /**
     * 查询特性标签详情，附带特性规格
     * 按照 targetKey 和 targetType 删除 ApRE label
     * 批量删除接口 使用请谨慎
     * @param targetKey label 关联对象键
     * @param targetType label 关联对象类型
     */
    fun deleteApRELabelByTargetAndType(targetKey: String, targetType: String, type: ApRELabelType) {
        apRELabelRepo.deleteByTargetAndType(targetKey, targetType, type.name)
    }

    fun deleteApRELabelByTarget(targetKey: String, targetType: String) {
        apRELabelRepo.deleteByTarget(targetKey, targetType)
    }
    /**
     * 按照 ApRELabelKey 进行指定删除
     */
    fun deleteApRELabelByApRELabelKey(apRELabelKey: String) {
        apRELabelRepo.deleteByApRELabelKey(apRELabelKey = apRELabelKey)
    }

    /**
     * 更加特性规格状态 级联更新 ApRE feature spec 的特性规格
     */
    fun modifyApREFeatureSpecStatus(apRELabelDO: ApRELabelDO) {
        if (apRELabelDO.apREFeatureSpecs.isNullOrEmpty()) {
            return
        }
        apRELabelRepo.findByTarget(apRELabelDO.targetKey!!, apRELabelDO.targetType!!.name).firstOrNull {
            (apRELabelDO.id == it.id) || (apRELabelDO.name == it.name && apRELabelDO.value == it.value)
        }?.let { apRELabel ->
            apRELabelDO.apREFeatureSpecs.forEach { apREFeatureSpecDTO ->
                apRELabelSpecRepo.queryByApRELabelKey(apRELabel.apRELabelKey).firstOrNull { apRELabelSpec ->
                    (apREFeatureSpecDTO.id == apRELabelSpec.id) ||
                            (apREFeatureSpecDTO.sourceType!! == apRELabelSpec.sourceType && apREFeatureSpecDTO.sourceId!! == apRELabelSpec.sourceId)
                }?.let { apRELabelSpec ->
                    apRELabelSpecRepo.updateStatusById(
                        apRELabelSpec.id!!,
                        ApREFeatureSpecStatusEnum.valueOf(apREFeatureSpecDTO.status!!).name
                    )
                } ?: throw ApRELabelSpecNotFoundException()
            }
        } ?: throw ApRELabelNotFoundException()
    }


    /**
     * 根据 ApREBindingTerm 定义的权限范围约束条款，过滤出符合的特性规格返回
     * 注意: 对于 apREFeatureSpecSelectorTerm matchExpressions 中的匹配都需要命中
     */
    fun filterByLabelSelectorTerm(
        apRELabel: ApRELabelDO,
        apRELabelSelectorTerms: List<ApRELabelSelectorTerm>
    ): ApRELabelDO? {
        apRELabelSelectorTerms.firstOrNull { apRELabelSelectorTerm ->
            matchDeclarationMetadata(apRELabelSelectorTerm.name, apRELabel.name) && matchDeclarationMetadata(
                apRELabelSelectorTerm.value,
                apRELabel.value
            )
        }?.let { apRELabelSelectorTerm ->
            //存在匹配的label,进一步匹配feature spec
            if (apRELabel.apREFeatureSpecs.isNullOrEmpty() || apRELabelSelectorTerm.apREFeatureSpecSelectorTerms.isNullOrEmpty()) {
                return apRELabel
            }
            val filterApREFeatureSpecs = mutableListOf<ApREFeatureSpecDO>()
            apRELabel.apREFeatureSpecs.forEach { apREFeatureSpec ->
                apRELabelSelectorTerm.apREFeatureSpecSelectorTerms.firstOrNull { apREFeatureSpecSelectorTerm ->
                    apREFeatureSpecSelectorTerm.matchExpressions.all { matchExpression ->
                        matchExpression.values.contains(getDataPropertyValue(apREFeatureSpec, matchExpression.key))
                    }
                }?.let {
                    filterApREFeatureSpecs.add(apREFeatureSpec)
                }
            }
            return apRELabel.copy(apREFeatureSpecs = filterApREFeatureSpecs)
        }
        return null
    }

    /**
     * 过滤 ApRE 捎带的全部特性
     * 将 resourcePool 和 ApRE 自身携带的所有特性标签 合并过滤
     */
    fun getApREFilterLabels(
        apREDO: ApREDO,
        apRELabelSelectorTerms: List<ApRELabelSelectorTerm>?,
        isAllLabelSupported: Boolean? = false
    ): List<ApRELabelDO> {
        val apRELabels = apREDO.resources.flatMap { resource -> resource.apRELabels }.toMutableList()
        apREDO.apRELabels?.let { labels ->
            apRELabels.addAll(labels)
        }
        return if (isAllLabelSupported == true) {
            apRELabels
        } else if (apRELabelSelectorTerms.isNullOrEmpty()) {
            emptyList()
        } else {
            val filterLabels = mutableListOf<ApRELabelDO>()
            apRELabels.forEach { label ->
                filterByLabelSelectorTerm(label, apRELabelSelectorTerms)?.let { filterLabel ->
                    filterLabels.add(filterLabel)
                }
            }
            filterLabels
        }
    }

    /**
     * 查询APRE特性标签详情，附带特性规格；如果特性规格不存在，则加载缺省特性规格
     */
    fun findApRELabelByRuntimeEnvKeyWithResourcePoolAndDefault(runtimeEnvKey: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        val apRELabelValues = mutableListOf<String>()
        // 添加ApRE Label
        findApRELabelByTarget(runtimeEnvKey, ApRELabelTargetTypeEnum.APRE.name).forEach {
            apRELabelDOs.add(it)
            apRELabelValues.add(it.value)
        }
        // 添加ApRE Resource Pool Label
        findApREResourcePoolLabelByApRERuntimeEnvKey(runtimeEnvKey).forEach {
            apRELabelDOs.add(it)
            apRELabelValues.add(it.value)
        }
        // 添加默认导入的特性&规格
        apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage().filter {
            !apRELabelValues.contains(it.code)
        }.forEach {
            apRELabelDOs.add(
                ApRELabelDO(
                    id = it.id,
                    targetKey = runtimeEnvKey,
                    name = APRE_LABEL_FEATURE_NAME,
                    value = it.code,
                    gmtCreate = it.gmtCreate,
                    gmtModified = it.gmtModified,
                    isDeleted = it.isDeleted,
                    apRELabelKey = it.featureKey,
                    apREFeatureSpecs = convertDefaultFeatureSpec(it.defaultFeatureSpecs!!),
                    source = ApREFeatureSourceEnum.DEFAULT.name,
                    targetType = ApRELabelTargetTypeEnum.APRE,
                    type = ApRELabelType.DEFAULT,
                    title = "$APRE_LABEL_FEATURE_NAME/${it.code}"
                )
            )
        }
        return apRELabelDOs
    }

    fun findServerlessBaseApp(pageSize: Int, pageNumber: Int, target: String? = null): PageData<String> {
        // 默认的前缀
        val prefix = ApRELabelExt.serverlessNameFormat(target)
        var page: Page<String> = PageHelper.startPage<String>(pageNumber, pageSize).doSelectPage {
                apRELabelRepo.findValuesByNameAndValuePrefixAndType(name = APRE_LABEL_FEATURE_NAME, valuePrefix = prefix, type = ApRELabelType.SERVERLESS.name)
            }
        return PageData.transformFrom(page)
    }

    /**
     *根据ApREBindingTerm定义的权限范围约束条款，过滤出符合的特性规格返回
     */
    fun filterByApREBindingTerm(apRELabel: ApRELabelDO, apRELabelSelectorTerms: List<ApRELabelSelectorTerm>): ApRELabelDO? {
        apRELabelSelectorTerms.firstOrNull { apRELabelSelectorTerm ->
            matchDeclarationMetadata(apRELabelSelectorTerm.name, apRELabel.name) && matchDeclarationMetadata(apRELabelSelectorTerm.value, apRELabel.value)
        } ?.let { apRELabelSelectorTerm ->
            //存在匹配的label,进一步匹配feature spec
            if (apRELabel.apREFeatureSpecs.isNullOrEmpty() || apRELabelSelectorTerm.apREFeatureSpecSelectorTerms.isNullOrEmpty()) {
                return apRELabel
            }
            val filterApREFeatureSpecs = mutableListOf<ApREFeatureSpecDO>()
            apRELabel.apREFeatureSpecs.forEach { apREFeatureSpec ->
                apRELabelSelectorTerm.apREFeatureSpecSelectorTerms.firstOrNull { apREFeatureSpecSelectorTerm ->
                    apREFeatureSpecSelectorTerm.matchExpressions.none { matchExpression ->
                        !matchExpression.values.contains(getDataPropertyValue(apREFeatureSpec, matchExpression.key))
                    }
                } ?.let {
                    filterApREFeatureSpecs.add(apREFeatureSpec)
                }
            }
            return apRELabel.copy(apREFeatureSpecs = filterApREFeatureSpecs)
        }
        return null
    }



    /**
     * 用于校验是否是 Api 创建的 ApRELabel 不符合白屏化管辖范围内的 Label 存在相关资源也不能删除
     */
    fun isApiCreatedApRELabel(apRELabelDO: ApRELabelDO): Boolean {
        if (apRELabelDO.type == ApRELabelType.CONSOLE) {
            return false
        }
        return true
    }

    /**
     * 用于检查是否存在非白屏化接管范围内的资源
     * 现只支持 ApRELabels 未来更多检测对象请在此函数中填充
     * 请使用 Kotlin 显示赋值调用函数
     */
    fun isHasApiRelativeObjectResource(
        resourcePoolKey: String? = null,
        apREKey: String? = null
    ): Boolean {
        if (resourcePoolKey.isNullOrBlank() && apREKey.isNullOrBlank()) {
            throw IllegalArgumentException("all resource for check cannot be null, check fun must have one resource for checking at least!")
        }
        // 1.检查是否存在非白屏化管控范围外的ApRELabel
        val apRELabels = mutableListOf<ApRELabelDO>()
        if (!resourcePoolKey.isNullOrBlank()) {
            apRELabels.addAll(
                findApRELabelByTarget(
                    targetKey = resourcePoolKey,
                    targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name
                )
            )
        }
        if (!apREKey.isNullOrBlank()) {
            apRELabels.addAll(
                findApRELabelByTarget(
                    targetType = apREKey,
                    targetKey = ApRELabelTargetTypeEnum.APRE.name
                )
            )
        }
        apRELabels.forEach {
            if (isApiCreatedApRELabel(it)) {
                return true
            }
        }
        return false
    }

    private fun convertDefaultFeatureSpec(apREDefaultFeatureSpecDOs: List<ApREDefaultFeatureSpecDO>): List<ApREFeatureSpecDO> {
        val apREFeatureSpecDOs = mutableListOf<ApREFeatureSpecDO>()
        apREDefaultFeatureSpecDOs.forEach {
            apREFeatureSpecDOs.add(
                ApREFeatureSpecDO(
                    it.id,
                    it.featureKey,
                    it.title,
                    it.specType,
                    it.specCode,
                    it.scope,
                    it.status,
                    it.annotations,
                    null,
                    it.gmtCreate,
                    it.gmtModified,
                    it.isDeleted,
                    ApREFeatureSourceEnum.DEFAULT.name
                )
            )
        }
        return apREFeatureSpecDOs
    }

    /**
     * 查询资源池标签
     */
    private fun findApREResourcePoolLabelByApRERuntimeEnvKey(runtimeEnvKey: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey) ?.run {
            resourcePoolService.listByManagedClusterKey(this.managedClusterKey).forEach {
                apRELabelDOs.addAll(findApRELabelByTarget(it.resourcePoolKey, ApRELabelTargetTypeEnum.RESOURCE_POOL.name))
            }
        }
        return apRELabelDOs
    }

    /**
     * 反射获取数据对象属性值
     */
    private fun getDataPropertyValue(any: Any, fieldName: String): String? {
        any::class.memberProperties.forEach {
            it.isAccessible = true
            if (it.name == fieldName) {
                return it.call(any).toString()
            }
        }
        return null
    }

    /**
     * ApRELabel  transFrom -> ApRELabelDO
     * 使用 ApREFeatureSourceEnum.CUSTOMIZED
     */
    fun transformToCustomizedFeatureApRELabelDO(
        apRELabel: ApRELabel,
        apREFeatureSpecs: List<ApREFeatureSpecDO>?
    ): ApRELabelDO {
        return ApRELabelDO(
            id = apRELabel.id,
            targetKey = apRELabel.targetKey,
            name = apRELabel.name,
            value = apRELabel.value,
            gmtCreate = apRELabel.gmtCreate,
            gmtModified = apRELabel.gmtModified,
            isDeleted = apRELabel.isDeleted,
            apRELabelKey = apRELabel.apRELabelKey,
            // 注入特性规格
            apREFeatureSpecs = apREFeatureSpecs,
            // 默认为自定义
            source = ApREFeatureSourceEnum.CUSTOMIZED.name,
            targetType = ApRELabelTargetTypeEnum.valueOf(apRELabel.targetType),
            type = apRELabel.type,
            title = "${apRELabel.name}/${apRELabel.value}"
        )
    }
}