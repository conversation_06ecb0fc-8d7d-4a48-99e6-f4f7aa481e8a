package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_DELIMITER
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_PREFIX
import com.alibaba.koastline.multiclusters.apre.StackServerlessBaseAppBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchApREFeatureSpec
import com.alibaba.koastline.multiclusters.apre.model.MatchApRELabel
import com.alibaba.koastline.multiclusters.apre.model.StackServerlessBaseAppBindingDataDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedResourceGroupBindingNotFoundException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.WorkloadUtils
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceRequirementRequest
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException.Companion.SCHEDULE_COMPUTE_DECLARATION_EXPECTED_REPLICAS_IS_WRONG
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum.LIST_POD
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum.NON_DECLARATIVE
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleWeight
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractScaleOutScheduleFacade
import kotlin.math.roundToInt
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class ScheduleStandardService {
    val log by logger()

    @Autowired
    lateinit var apREService: ApREService
    @Autowired
    lateinit var skylineApi: SkylineApi
    @Autowired
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService
    @Autowired
    lateinit var apREDeedService: ApREDeedService
    @Autowired
    lateinit var apreDeedPatchService: ApREDeclarationPatchService
    @Autowired
    lateinit var stackServerlessBaseAppBindingService: StackServerlessBaseAppBindingService
    @Autowired
    lateinit var resourceObjectService: ResourceObjectService
    @Autowired
    lateinit var appCenterApi: AppCenterApi
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi

    /**
     * 生成namespace
     * 同五元组（应用、分组、站点、单元、用途）保持namespace相同
     * 兼容CloneSet namespace,pattern = ${appName} + "--nc"
     */
    fun generateNameSpace(workloadMetadataConstraint: WorkloadMetadataConstraint): String {
        val scheduleRequestContent = ScheduleRequestContent(
            resourceScope = ResourceScope(
                appName = workloadMetadataConstraint.appName,
                resourceGroup = workloadMetadataConstraint.resourceGroup
            ),
            scheduleType = ScheduleType(NON_DECLARATIVE, LIST_POD),
            includeClusterInfoToResult = false
        )
        val scheduleResult = scheduleServiceFactory.getScheduleService(scheduleRequestContent.scheduleType)
            .doSchedule(scheduleRequestContent)
        return scheduleResult.workloadExpectedStates.firstOrNull { current ->
            current.workloadMetadataConstraint.unit == workloadMetadataConstraint.unit
                    && current.workloadMetadataConstraint.site == workloadMetadataConstraint.site
                    && current.workloadMetadataConstraint.stage == workloadMetadataConstraint.stage
                    && current.workloadMetadataConstraint.runtimeId.isNullOrBlank()
        }?.run { checkNotNull(this.workloadMetadataConstraint.namespace) { "namespace不能为空." } }
            ?: run {
                val appGroup = skylineApi.getAppGroup(workloadMetadataConstraint.resourceGroup)
                if (appGroup.tags.contains("resource_res_type.native_cloud")) {
//                    WorkloadUtils.buildNamespace(workloadMetadataConstraint.appName) + "--nc"
                    /**
                     * 暂时云原生环境默认返回cse-default
                     */
                    CloneSetSpecService.DEFAULT_NAME_SPACE
                } else {
                    WorkloadUtils.buildNamespace(workloadMetadataConstraint.appName)
                }
            }
    }

    /**
     * 基于声明权重计算多声明变更副本数
     * @param scheduleWeightList 权重列表信息
     * @param expectedReplicas 期待的终态副本数量
     * @return 基于多声明权重计算期望的终态副本数量分布, map of(key:declarationId,value:副本数)
     */
    fun doComputeDeclarationExpectedReplicas(scheduleWeightList: List<ScheduleWeight>, expectedReplicas: Int): Map<String, Int> {
        log.info("doComputeDeclarationExpectedReplicas,scheduleWeightList:{},expectedReplicas:{}", JsonUtils.writeValueAsString(scheduleWeightList), expectedReplicas)
        val reBuildScheduleWeightList = reBuildAllZeroOfWeight(scheduleWeightList)
        val totalWeight = reBuildScheduleWeightList.sumOf { it.weight }
        val scheduleExpectedReplicasMap = mutableMapOf<String, Int>()
        reBuildScheduleWeightList.forEachIndexed { index, scheduleWeight ->
            val remainder = expectedReplicas - scheduleExpectedReplicasMap.values.sum()
            if (remainder <= 0) {
                scheduleExpectedReplicasMap[scheduleWeight.uniqueKey] = 0
                return@forEachIndexed
            }
            if (index == (reBuildScheduleWeightList.size -1)) {
                scheduleExpectedReplicasMap[scheduleWeight.uniqueKey] = remainder
                return@forEachIndexed
            }
            (expectedReplicas.toDouble() * scheduleWeight.weight / totalWeight).roundToInt().let { num ->
                if (num >= remainder) {
                    scheduleExpectedReplicasMap[scheduleWeight.uniqueKey] = remainder
                    return@forEachIndexed
                }
                scheduleExpectedReplicasMap[scheduleWeight.uniqueKey] = num
            }
        }
        //校验计算结果
        if (expectedReplicas != scheduleExpectedReplicasMap.values.sum()) {
            throw ScheduleException("调度计算终态副本数错误，期望副本:$expectedReplicas，声明权重:${scheduleWeightList.associate { it.uniqueKey to it.weight }},计算结果:$scheduleExpectedReplicasMap", SCHEDULE_COMPUTE_DECLARATION_EXPECTED_REPLICAS_IS_WRONG)
        }
        return scheduleExpectedReplicasMap
    }

    /**
     * 针对所有权重为0的场景，修改权重全部为1参与计算
     */
    private fun reBuildAllZeroOfWeight(scheduleWeightList: List<ScheduleWeight>): List<ScheduleWeight> {
        val totalWeight = scheduleWeightList.sumOf { it.weight }
        if (totalWeight != 0) {
            return scheduleWeightList
        }
        return scheduleWeightList.map {
            it.copy(weight = 1)
        }
    }

    /**
     * 基于运行态资源现状，按权重值面向终态计算增减副本数的分布
     * @param scheduleWeightList 调度权重
     * @param modifiedReplicas 变更副本数 positive val -> increase total | negative val -> decrease total
     * @param computeModifiedReplicas 高阶函数，用于针对具体场景（如扩缩）计算变更副本数
     * @return map of(key:对应调度权重uniqueKey,value:变更资源数量)
     */
    fun doComputeExpectedReplicasByRunningState(scheduleWeightList: List<ScheduleWeight>, modifiedReplicas: Int,
                                                        computeModifiedReplicas:(runningResourceNum: Int, expectedReplicas: Int)->Int): Map<String, Int> {
        //计算基于资源总量（运行态资源量+增量）期望的终态资源分布状态
        //当存在例如非缩容部分但是实际上分布比期待中最终数量还要少 就需要将相关选项的给剔除
        val filterExpectedReplicasState = doComputeDeclarationExpectedReplicas(
            scheduleWeightList = scheduleWeightList,
            expectedReplicas = scheduleWeightList.sumOf { it.runningResourceNum } + modifiedReplicas
        ).filter { (uniqueKey, expectedReplicas) ->
            //过滤超出期望边界的调度项
            computeModifiedReplicas(scheduleWeightList.first { it.uniqueKey == uniqueKey }.runningResourceNum, expectedReplicas) > 0
        }
        if (filterExpectedReplicasState.size == scheduleWeightList.size) {
            return filterExpectedReplicasState.mapValues { (uniqueKey, expectedReplicas) ->
                computeModifiedReplicas(scheduleWeightList.first { it.uniqueKey == uniqueKey }.runningResourceNum, expectedReplicas)
            }
        }
        //存在并过滤超出期望边界的调度项后，重新调度计算
        return doComputeExpectedReplicasByRunningState(
            scheduleWeightList = scheduleWeightList.filter { filterExpectedReplicasState.containsKey(it.uniqueKey) },
            modifiedReplicas = modifiedReplicas,
            computeModifiedReplicas
        )
    }

    fun plusValueToMap(values:MutableMap<String, Int>, key: String, value: Int) {
        values[key] ?.let {
            values[key] = values[key]!!.plus(value)
        } ?:let {
            values[key] = value
        }
    }

    fun checkServerlessParams(content: ScheduleRequestContent) {
        checkNotNull(content.scheduleRequestParam).let { scheduleRequestParam ->
            if (scheduleRequestParam.serverless) {
                scheduleRequestParam.serverlessRuntimeTemplate?.toNullIfBlank()?.let { rt ->
                    /**
                     * RT格式为：server/基座应用名$$软件包基线版本$$SPEC:资源规格
                     */
                    if (rt.split(RUNTIME_TEMPLATE_DELIMITER).size != 3) {
                        throw ScheduleException(
                            "serverless扩容调度场景下，serverlessRuntimeTemplate格式非法，content:${content}",
                            ScheduleException.INVALID_PARAM
                        )
                    }
                } ?: let {
                    throw ScheduleException(
                        "serverless扩容调度场景下，serverlessRuntimeTemplate不能为空，content:${content}",
                        ScheduleException.INVALID_PARAM
                    )
                }
            }
        }
    }

    fun getServerlessRuntimeTemplate(
        serverlessBaseAppName: String? = null,
        appName: String,
        resourceGroup: String,
        envStackId: String? = null
    ): String {
        if (serverlessBaseAppName != null) {
            return getServerlessRuntimeTemplateByBaseApp(
                appName = appName,
                resourceGroup = resourceGroup,
                serverlessBaseAppName = serverlessBaseAppName
            )
        }
        return getServerlessRuntimeTemplateByEnv(
            appName = appName,
            resourceGroup = resourceGroup,
            envStackId = checkNotNull(envStackId){"ServerlessApp模式下，需要指定环境StackId以获取基座应用."}
        )
    }

    fun getServerlessRuntimeTemplateByEnv(appName: String, resourceGroup: String, envStackId: String): String {
        //优先从环境取基座，不存在的情况下从应用获取
        val serverlessBaseAppName = stackServerlessBaseAppBindingService.getServerlessBaseAppName(envStackId) ?:let {
            appCenterApi.getAppInfoByName(appName).runtimeAppName ?:let {
                throw ScheduleException("环境[stackId:${envStackId}]以及应用[$appName]未配置基座应用。", ScheduleException.INVALID_PARAM)
            }
        }
        val resourceSpec = resourceObjectService.getResourceBaselineSpec(ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)).first
        return apREService.generateServerlessRuntimeTemplateName(serverlessBaseAppName, null, resourceSpec.cpu, resourceSpec.memory)
    }
    fun getServerlessRuntimeTemplateByBaseApp(appName: String, resourceGroup: String, serverlessBaseAppName: String): String {
        val resourceSpec = resourceObjectService.getResourceBaselineSpec(ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)).first
        return apREService.generateServerlessRuntimeTemplateName(serverlessBaseAppName, null, resourceSpec.cpu, resourceSpec.memory)
    }

    /**
     * serverless场景下，基于RT来构建match label&spec
     * serverlessRuntimeTemplate前置已做参数校验
     * @see AbstractScaleOutScheduleFacade.preCheck
     */
    fun getServerlessMatchApRELabelList(
        serverlessRuntimeTemplate: String,
        envStackId: String? = null,
        resourceGroup: String? = null,
        envStackPkId: String? = null
    ): List<MatchApRELabel>{
        val rtItems = serverlessRuntimeTemplate.split(RUNTIME_TEMPLATE_DELIMITER)
        val stackServerlessBaseAppBindingData = getStackServerlessBaseAppBindingData(
            envStackId = envStackId, resourceGroup = resourceGroup
        )
        val serverlessBaseAppOfStackExtraParams = stackServerlessBaseAppBindingData.extraParams
        // 添加kunLun的环境级别查询查询runtimeId等相关信息
        val serverlessBaseAppInfo = envStackPkId ?.run {
            cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(this)
        }
        return listOf(
            MatchApRELabel(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = rtItems[0],
                matchApREFeatureSpecs = listOf(
                    serverlessBaseAppInfo ?.run {
                        MatchApREFeatureSpec(
                            specType = serverlessRuntimeTemplate,
                            matchFeatureSpecLabels = mapOf(
                                "envStackId" to this.baseEnvStackId
                            )
                        )
                    } ?:run {
                        MatchApREFeatureSpec(
                            specType = if (serverlessBaseAppOfStackExtraParams ?.runtimeId == null) serverlessRuntimeTemplate else null,
                            specCode = serverlessBaseAppOfStackExtraParams ?.runtimeId,
                            matchFeatureSpecLabels = serverlessBaseAppOfStackExtraParams ?.runtimeBaseEnvStackId ?.run { mapOf(
                                "envStackId" to this
                            ) } ?:run { null }
                        )
                    }
                )
            )
        )
    }

    fun getStackServerlessBaseAppBindingData(envStackId: String? = null,
                                                     resourceGroup: String? = null): StackServerlessBaseAppBindingDataDO {
        envStackId ?.run {
            return stackServerlessBaseAppBindingService.getStackServerlessBaseAppBindingData(envStackId) ?:run {
                throw ScheduleException("Serverless调度,缺少环境[envStackId:${envStackId}]声明信息.")
            }
        }
        resourceGroup ?.run {
            val envStackIds = skylineApi.listBindingEnvStackIdByResourceGroup(this)
            if (envStackIds.isEmpty()) {
                throw ScheduleException("Serverless调度,分组[${resourceGroup}]未绑定环境.")
            }
            return stackServerlessBaseAppBindingService.getUniqueStackServerlessBaseAppBindingData(envStackIds) ?:run {
                throw ScheduleException("Serverless调度,分组[${resourceGroup}]绑定环境缺少声明信息.")
            }
        }
        throw ScheduleException("Serverless调度,环境和分组不能同时为空.")
    }

    fun getAssembledApREDeedByDeclarative(content: ScheduleRequestContent): ApREDeedDO {
        return getAssembledApREDeedByDeclarative(
            apREDeedKey = content.declarationData?.apREDeedKey,
            resourceGroup = checkNotNull(content.resourceScope.resourceGroup){"参数非法：resourceGroup不能为空"},
            serverless = content.scheduleRequestParam ?.serverless ?: false,
            serverlessRuntimeTemplate = content.scheduleRequestParam ?.serverlessRuntimeTemplate,
            envStackId = content.resourceScope.envStackId,
            envStackPkId = content.scheduleRequestParam ?.envStackPkId
        )
    }

    /**
     * 获取ApRE资源声明契约，并：
     * 1.补充均衡策略
     * 2.Serverless场景下补偿RT声明
     */
    fun getAssembledApREDeedByDeclarative(
        apREDeedKey: String?,
        resourceGroup: String,
        serverless: Boolean,
        serverlessRuntimeTemplate: String?,
        envStackId: String? = null,
        envStackPkId: String? = null
    ): ApREDeedDO {
        val apREDeedKey = apREDeedKey ?: run {
            //如果没有传递apREDeedKey，则通过分组查询是否存在绑定的apREDeedKey
            apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup) ?.run {
                this.apREDeedKey
            } ?:run {
                // 判断是否测试分组
                val appGroup = skylineApi.getAppGroup(resourceGroup).apply {
                    if (!this.isTestingAppGroup()) {
                        throw ApREDeedResourceGroupBindingNotFoundException(resourceGroup)
                    }
                }
                //为测试分组生成默认声明
                apREDeedService.createApREDeedWhileNotExist(
                    ApREDeedDO(
                        identityInfo = IdentityInfo(
                            appName = appGroup.appName,
                            envLevel = DeclarativeScaleOutScheduleService.TEST_ENV_LEVEL,
                            nodeGroup = appGroup.name
                        ),
                        declarations = mutableListOf(
                            Declaration(
                                unit = DeclarativeScaleOutScheduleService.TEST_DEFAULT_UNIT,
                                stage = DeclarativeScaleOutScheduleService.TEST_STAGE
                            )
                        )
                    )
                ).key!!.apply {
                    apREDeedResourceGroupBindingService.createIgnoreWhileExist(appGroup.appName, appGroup.name, this)
                }
            }
        }
        /**
         * 补偿SRE切面策略
         * 只针对弱声明类型分组做SRE声明补偿，如果未发现补偿策略则抛异常
         */
        return apreDeedPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(
            apREDeedService.findApREDeedByKey(apREDeedKey)
        ).apply {
            if (serverless) {
                // 补偿Serverless场景RT声明
                this.declarations = this.declarations ?.map { declaration ->
                    if (hasServerlessDeclaration(declaration)) {
                        declaration
                    } else {
                        declaration.copy(
                            matchApRELabels = getServerlessMatchApRELabelList(
                                serverlessRuntimeTemplate = serverlessRuntimeTemplate!!,
                                envStackId = envStackId,
                                resourceGroup = resourceGroup,
                                envStackPkId = envStackPkId
                            )
                        )
                    }
                } ?.toMutableList()
            }
        }
    }

    /**
     * 是否已经存在Serverless Runtime声明
     */
    private fun hasServerlessDeclaration(declaration: Declaration): Boolean {
        if (declaration.matchApRELabels.isNullOrEmpty()) {
            return false
        }
        declaration.matchApRELabels.first().let { matchApRELabel ->
            if (null == matchApRELabel.value) {
                return false
            }
            if (!matchApRELabel.value.startsWith(RUNTIME_TEMPLATE_PREFIX)) {
                return false
            }
            if (matchApRELabel.matchApREFeatureSpecs.isNullOrEmpty()) {
                return false
            }
        }
        return true
    }

    /**
     * 调度校验，不允许STS&CloneSet协议同分组混合
     */
    fun checkMultiProtocol(resourceObjectProtocol: String, appName: String, resourceGroup: String){
        val resourceObjectProtocol = resourceObjectProtocol
        if (resourceObjectProtocol != ResourceObjectProtocolEnum.StatefulSet.name
            && resourceObjectProtocol != ResourceObjectProtocolEnum.CloneSet.name) {
            return
        }
        val workloadMetadataConstraintAssembleList = skylineApi.listWorkloadMetadataConstraintThroughServerList(
            appName = appName,
            resourceGroup = resourceGroup
        ).ifEmpty { return }
        if (resourceObjectProtocol == ResourceObjectProtocolEnum.StatefulSet.name) {
            workloadMetadataConstraintAssembleList.firstOrNull{
                it.workloadMetadataConstraint.namespace == CloneSetSpecService.DEFAULT_NAME_SPACE
                        || it.workloadMetadataConstraint.namespace == CloneSetSpecService.DEFAULT_NAME_SPACE_PRE
            } ?.let {
                throw ScheduleException("当前分组:${resourceGroup}存在云原生资源，无法同时扩容ASI资源.")
            }
        } else {
            workloadMetadataConstraintAssembleList.firstOrNull{
                it.workloadMetadataConstraint.namespace != CloneSetSpecService.DEFAULT_NAME_SPACE
                        && it.workloadMetadataConstraint.namespace != CloneSetSpecService.DEFAULT_NAME_SPACE_PRE
            } ?.let {
                throw ScheduleException("当前分组:${resourceGroup}存在ASI资源，无法同时扩容云原生资源.")
            }
        }
    }
}