package com.alibaba.koastline.multiclusters.appenv.params

/**
 * <AUTHOR>
 */
data class ClusterEnvironmentSpec (
        val clusterEnvironmentKey: String?,
        val clusterEnvironmentName: String?, // to be removed
        val clusterId: String?, // cluster id which is used for creating cluster env
        var gatewayNamespace: String?, // gateway id to locate system components
        val kClusterKey: String?,
        var envNamespace: String?,
        val source: String, // eg. aone
        val stage: String,
        val region: String,
        val az: String,
        val unit: String?,
        val envMeta: MutableMap<String, String>
) {
    constructor(clusterId: String, sysNamespace: String, gatewayNamespace: String, source: String, stage: String, region: String, az: String, unit: String, envMeta: MutableMap<String, String>) : this(
            clusterEnvironmentKey = null,
            clusterEnvironmentName = null,
            clusterId = clusterId,
            gatewayNamespace = gatewayNamespace,
            kClusterKey = null,
            envNamespace = sysNamespace,
            source = source,
            stage = stage,
            region = region,
            az = az,
            unit = unit,
            envMeta = envMeta
    )
    constructor(clusterId: String, source: String, stage: String, region: String, az: String, unit: String?, envMeta: MutableMap<String, String>) : this(
            clusterEnvironmentKey = null,
            clusterEnvironmentName = null,
            clusterId = clusterId,
            gatewayNamespace = null,
            kClusterKey = null,
            envNamespace = null,
            source = source,
            stage = stage,
            region = region,
            az = az,
            unit = unit,
            envMeta = envMeta
    ) {
        if (stage.lowercase() == "pre_publish") {
            this.gatewayNamespace = "cse-system-pre"
            this.envNamespace = "cse-default-pre"
        }
        else {
            this.gatewayNamespace = "cse-system"
            this.envNamespace = "cse-default"
        }
    }
}