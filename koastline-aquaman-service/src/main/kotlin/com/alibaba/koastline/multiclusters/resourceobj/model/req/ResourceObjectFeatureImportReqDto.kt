package com.alibaba.koastline.multiclusters.resourceobj.model.req

import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureEffectiveStageEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureImportStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.env.DEFAULT_VERSION
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("资源对象特性导入创建")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportCreateReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val resourceObjectFeatureKey: String,
    /**
     * @see ResourceObjectFeatureImportStatusEnum
     */
    @ApiModelProperty("状态，可选项:ENABLED|DISABLED -> 启用|停用",required = true)
    val status: ResourceObjectFeatureImportStatusEnum = ResourceObjectFeatureImportStatusEnum.ENABLED,
    @ApiModelProperty("预置参数映射",required = true)
    val paramMap: Map<String, Any>,
    @ApiModelProperty("创建人",required = true)
    val creator: String,
    @ApiModelProperty("匹配范围",required = true)
    val matchScopeDataList: List<MatchScopeDataReqDto>,
    @ApiModelProperty("版本号", required = false)
    val version: String = DEFAULT_VERSION,
) {
    fun validate(): ResourceObjectFeatureImportCreateReqDto {
        require(resourceObjectFeatureKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        require(creator.isNotBlank()) { "creator must be non-blank" }
        require(matchScopeDataList.isNotEmpty()) { "matchScopeDataList must be non-empty" }
        matchScopeDataList.forEach { it.validate() }
        return this
    }
}

@ApiModel("批量创建或者更新资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportCreateOrUpdateReqDto(
    @ApiModelProperty("匹配范围",required = true)
    val matchScopes: List<MatchScopeDataReqDto>,
    @ApiModelProperty("特性增加或修改",required = true)
    val createOrUpdateTraitList: List<TraitCreateReqDto>,
    @ApiModelProperty("修改人",required = true)
    val modifier: String,
) {
    fun validate(): ResourceObjectFeatureImportCreateOrUpdateReqDto {
        require(modifier.isNotBlank()) { "modifier must be non-blank" }
        matchScopes.forEach { it.validate() }
        createOrUpdateTraitList.forEach { it.validate() }
        return this
    }
}

@ApiModel("特性创建")
@JsonIgnoreProperties(ignoreUnknown = true)
data class TraitCreateReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val traitKey: String,
    /**
     * @see ResourceObjectFeatureImportStatusEnum
     */
    @ApiModelProperty("状态，可选项:ENABLED|DISABLED -> 启用|停用",required = true)
    val status: ResourceObjectFeatureImportStatusEnum = ResourceObjectFeatureImportStatusEnum.ENABLED,
    @ApiModelProperty("预置参数映射",required = true)
    val formData: Map<String, Any>,
    @ApiModelProperty("版本号", required = false)
    val version: String = DEFAULT_VERSION,
) {
    fun validate(): TraitCreateReqDto {
        require(traitKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        return this
    }
}

@ApiModel("资源对象特性导入更新")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportUpdateReqDto(
    val id: Long,
    /**
     * @see ResourceObjectFeatureImportStatusEnum
     */
    @ApiModelProperty("状态，可选项:ENABLED|DISABLED -> 启用|停用",required = true)
    val status: ResourceObjectFeatureImportStatusEnum,
    @ApiModelProperty("预置参数映射",required = true)
    val paramMap: Map<String, Any>,
    @ApiModelProperty("修改人",required = true)
    val modifier: String,
    @ApiModelProperty("匹配范围",required = true)
    val matchScopeDataList: List<MatchScopeDataReqDto>,
    @ApiModelProperty("版本号", required = false)
    val version: String? = null,
)

@ApiModel("特性更新")
@JsonIgnoreProperties(ignoreUnknown = true)
data class TraitUpdateReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val traitKey: String,
    /**
     * @see ResourceObjectFeatureImportStatusEnum
     */
    @ApiModelProperty("状态，可选项:ENABLED|DISABLED -> 启用|停用")
    val status: ResourceObjectFeatureImportStatusEnum? = null,
    @ApiModelProperty("预置参数映射")
    val formData: Map<String, Any>? = null,
    @ApiModelProperty("版本号", required = false)
    val version: String? = null,

    ) {
    fun validate(): TraitUpdateReqDto {
        require(traitKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        return this
    }
}

@ApiModel("特性增加/修改/删除")
@JsonIgnoreProperties(ignoreUnknown = true)
data class TraitModifyReq(

    @ApiModelProperty("匹配范围",required = true)
    val matchScope: MatchScopeDataReqDto,

    @ApiModelProperty("特性增加")
    val addingTraitList: List<TraitCreateReqDto>?,

    @ApiModelProperty("特性增加或修改")
    val createOrUpdateTraitList: List<TraitCreateReqDto>?,

    @ApiModelProperty("特性修改")
    val updatingTraitList: List<TraitUpdateReqDto>?,

    @ApiModelProperty("特性删除")
    val deletingTraitList: List<TraitDeleteReqDto>?,

    @ApiModelProperty("修改人",required = true)
    val modifier: String,

) {
    fun validate(): TraitModifyReq {

        matchScope.validate()
        addingTraitList?.forEach { it.validate() }
        updatingTraitList?.forEach { it.validate() }
        deletingTraitList?.forEach { it.validate() }
        require(modifier.isNotBlank()) { "modifier must be non-blank" }
        return this
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TraitModifyReqDto(

    val matchScope: MatchScopeDataReqDto,

    val addingTraitList: List<TraitCreateReqDto>?,

    val createOrUpdateTraitList: List<TraitCreateReqDto>?,

    val updatingTraitList: List<TraitUpdateReqDto>?,

    val deletingTraitList: List<TraitDeleteReqDto>?,

    val modifier: String,

    val submitters: String,
)

@ApiModel("根据生效范围更新资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportUpdateByMatchScopeReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val resourceObjectFeatureKey: String,
    /**
     * @see ResourceObjectFeatureImportStatusEnum
     */
    @ApiModelProperty("状态，可选项:ENABLED|DISABLED -> 启用|停用")
    val status: ResourceObjectFeatureImportStatusEnum? = null,
    @ApiModelProperty("预置参数映射")
    val paramMap: Map<String, Any>? = null,
    @ApiModelProperty("修改人",required = true)
    val modifier: String,
    @ApiModelProperty("匹配范围",required = true)
    val matchScope: MatchScopeDataReqDto,
    @ApiModelProperty("版本号", required = false)
    val version: String? = null,
) {
    fun validate(): ResourceObjectFeatureImportUpdateByMatchScopeReqDto {
        require(resourceObjectFeatureKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        require(modifier.isNotBlank()) { "modifier must be non-blank" }
        matchScope.validate()
        return this
    }
}

@ApiModel("根据生效范围删除资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportDeleteByMatchScopeReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val resourceObjectFeatureKey: String,
    @ApiModelProperty("修改人",required = true)
    val modifier: String,
    @ApiModelProperty("匹配范围",required = false)
    val matchScope: MatchScopeDataReqDto? = null,
    @ApiModelProperty("匹配范围列表",required = false)
    val matchScopes: List<MatchScopeDataReqDto> = emptyList(),
) {
    fun validate(): ResourceObjectFeatureImportDeleteByMatchScopeReqDto {
        require(matchScopes.isNotEmpty() || matchScope != null)
        require(resourceObjectFeatureKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        require(modifier.isNotBlank()) { "modifier must be non-blank" }
        matchScope?.validate()
        matchScopes.forEach { it.validate() }
        return this
    }
}

@ApiModel("根据生效范围批量删除资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class BatchResourceObjectFeatureImportDeleteByMatchScopeReqDto(
    @ApiModelProperty("特性删除",required = true)
    val deletingTraitList: List<TraitDeleteReqDto>,
    @ApiModelProperty("匹配范围列表",required = true)
    val matchScopes: List<MatchScopeDataReqDto>,
    @ApiModelProperty("修改人",required = true)
    val modifier: String,
) {
    fun validate(): BatchResourceObjectFeatureImportDeleteByMatchScopeReqDto {
        require(modifier.isNotBlank()) { "modifier must be non-blank" }
        matchScopes.forEach { it.validate() }
        deletingTraitList.forEach { it.validate() }
        return this
    }
}

@ApiModel("特性删除")
@JsonIgnoreProperties(ignoreUnknown = true)
data class TraitDeleteReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val traitKey: String,
) {
    fun validate(): TraitDeleteReqDto {
        require(traitKey.isNotBlank()) { "resourceObjectFeatureKey must be non-blank" }
        return this
    }
}

@ApiModel("根据生效范围获取资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportGetByMatchScopeReqDto(
    @ApiModelProperty("资源对象特性Key",required = true)
    val keyList: List<String>?,
    @ApiModelProperty("匹配范围",required = true)
    val matchScopes: List<MatchScopeDataReqDto>,
    @ApiModelProperty("特性作用阶段",required = false)
    val traitEffectiveStageListFilter: List<ResourceObjectFeatureEffectiveStageEnum>?,
    @ApiModelProperty("特性类型",required = false)
    val traitTypeFilter: ResourceObjectFeatureTypeEnum?,
    @ApiModelProperty("租户名称", required = false)
    val submitters: String = "SYSTEM",
) {
    fun validate(): ResourceObjectFeatureImportGetByMatchScopeReqDto {
        matchScopes.forEach {
            it.validate()
        }
        return this
    }
}

@ApiModel("根据应用名获取资源对象特性导入")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportGetByAppNameReqDto(
    @ApiModelProperty("应用名称",required = true)
    val appName: String,
    @ApiModelProperty("特性作用阶段",required = false)
    val traitEffectiveStageFilter: ResourceObjectFeatureEffectiveStageEnum = ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT,
    @ApiModelProperty("是否包含老特性",required = false)
    val includingLegacy: Boolean = false,
    @ApiModelProperty("是否包含系统特性",required = false)
    val includingSystem: Boolean = false,
) {
    fun validate(): ResourceObjectFeatureImportGetByAppNameReqDto {
        require(appName.isNotBlank()) { "appName must be non-blank" }
        return this
    }
}

@ApiModel("根据范围和特性Key获取全部资源对象特性导入（包括运维特性和环境特性）")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportGetByMatchScopeAndKeyReqDto(
    @ApiModelProperty("资源对象特性Key-新", required = false)
    val traitKey: String?,
    @ApiModelProperty("资源对象特性Key", required = false)
    val resourceObjectFeatureKey: String?,
    @ApiModelProperty("匹配范围", required = true)
    val matchScope: MatchScopeDataReqDto,

    ) {
    fun validate(): ResourceObjectFeatureImportGetByMatchScopeAndKeyReqDto {
        matchScope.validate()
        require(traitKey?.toNullIfBlank() != null || resourceObjectFeatureKey?.toNullIfBlank() != null) { "traitKey or resourceObjectFeatureKey must be non-blank" }
        return this.copy(
            resourceObjectFeatureKey = if (traitKey?.toNullIfBlank() == null) { resourceObjectFeatureKey } else { traitKey }
        )
    }
}

@ApiModel("根据特性Key获取对象特性导入，此接口仅支持asi")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResourceObjectFeatureImportAsiAwareReqDto(
    @ApiModelProperty("资源对象特性keys", required = true)
    val traitKeyList: List<String>,

    @ApiModelProperty("nextToken", required = false)
    val nextToken: String? = null,

    @ApiModelProperty("页面大小", required = false)
    val pageSize: Int,
)
