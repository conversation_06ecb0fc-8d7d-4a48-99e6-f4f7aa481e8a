package com.alibaba.koastline.multiclusters.common.exceptions
/**
 * <AUTHOR>
 */

open class BizException(
    message: String? = null,
    val errorCode: Int? = null
) : RuntimeException(message)

// Common Related exceptions
class ParamMustNotBeNullException(fieldName: String): RuntimeException("field $fieldName must not be null")
class CreateDataException(data: Any): RuntimeException("create data causes exception,data: $data")
class ModifyDataException(conditions: String, data: Any): RuntimeException("update data causes exception,conditions: $conditions, modified data: $data")

// ClusterEnvironment Related exceptions
class EnvironmentParamsNotValidException(param: String) : RuntimeException("environment params $param is not valid")
class ClusterEnvironmentException(message: String): RuntimeException(message)

// Cluster profile related exceptions
class ClusterProfileUniqueException(clusterId: String): RuntimeException("cluster info has been record in database, conflict with clusterId!")
class ClusterProfileNotFoundException(clusterId: Any?) : BizException("cluster profile $clusterId is not found")
class ManagedClusterNotExistException(managedClusterKey: String? ="") : RuntimeException("managed profile $managedClusterKey not exist")
class ClusterBindingInfoNotFoundException(): RuntimeException("there is no cluster binding info found")
class ClusterBindingCreationException(): RuntimeException("cannot create cluster binding info")
class ClusterBindingDeleteException(): RuntimeException("cannot delete the cluster env binding information")
class ManagedClusterCreationException(): RuntimeException("managed cluster cannot be created")
class ManagedClusterUpdateException(): RuntimeException("managed cluster cannot be updated")
class SystemComponentsDeleteException(): RuntimeException("system components cannot be deleted")

// Gateway related exception
class GatewayCreationException(message: String): RuntimeException("gateway $message create failed ")
class GatewayParamsNotValidException(): RuntimeException("gateway params occurs exception")
class SystemComponentsCreationException(): RuntimeException("system components data creation failed")
class SystemComponentsUpdateException(): RuntimeException("system components cannot be updated")
// User authentication/authorization
class UserHasNoRightException(): RuntimeException("user has no right to access the resource")

// AppRuntimeEnvironment Related exceptions
class ApREUniqueExistException(runtimeEnvKey: String? ="") : BizException("runtime env $runtimeEnvKey exist")
class ApREUniqueExistMetadataConstraintException(region: String,az: String, stage: String, unit: String) : BizException("runtime env metadata constraint {$region,$az,$stage,$unit} exist")
class EnvLevelStageMappingDataNotFoundException(): BizException("there is no metaData found")
class ApREException(message: String): BizException(message)
class ApREParamsException(message: String): BizException(message)
class ApRENotFoundException(): BizException("there is no app runtime environment found")

class ApREMultipleDiscernException(): BizException("cannot discern multiple app runtime environment")
class ApREMergeBindingException(message: String?): BizException(message)
class ApREConsoleResolveException(message: String?): BizException(message)

// ApREBindingData Related exceptions
class ApREBindingDataException(message: String) : BizException(message)

class ApREAttorneyException(message: String):BizException(message)

// AppRuntimeEnvironment Label Related exceptions
class ApRELabelNotFoundException(): BizException("there is no app runtime environment label found")

// ApRE Label Definition Related Exception
class ApRELabelDefinitionUniqueExistException(message: String): BizException(message)
class FeatureSpecDefinitionException(message: String): BizException(message)

// AppRuntimeEnvironment Label Spec Related exceptions
class ApRELabelException(message: String): BizException(message)
class ApRELabelSpecException(message: String): BizException(message)
class ApRELabelSpecNotFoundException(): BizException("there is no app runtime environment label spec found")
class ApREFeatureSpecStatusInvalidException(): BizException("the spec status is invalid")
class ApREFeatureSpecScopeInvalidException(): BizException("the spec scope is invalid")
class ApREMergeFeatureSpecException(message: String?): BizException(message)

// ApRE Deed Related exceptions
class ApREDeedException(message: String?): BizException(message)
class ApREDeedNotFoundException(): BizException("there is no apre deed found")
class ApREDeedMultiDeclarationException(): BizException("only one declaration allowed")

// ApRE Deed Binding exceptions
class ApREDeedResourceGroupMultiBindingException(resourceGroup: String): BizException("不允许分组：$resourceGroup 绑定多声明")
class ApREDeedResourceGroupBindingNotFoundException(resourceGroup: String): BizException("分组：$resourceGroup 未找到资源声明")

// EnvLevel Mapping Related exceptions
class EnvLevelStageMappingFoundException(): BizException("there is no env level stage mapping found")

// Metadata Mapping Related exceptions
class MetadataException(message: String?) : BizException(message)
class MetadataConstraintUniqueExistException(site: String, unit: String, stage: String) : BizException("metadata constrain {$site,$unit,$stage} exist")
class MetadataConstraintNotFoundException(site: String, unit: String, stage: String) : BizException("there is no metadata constrain {$site,$unit,$stage} found")
class MetadataOfSiteUniqueExistException(site: String) : BizException("metadata of site {$site} exist")
class MetadataOfSiteNotFoundException(site: String) : BizException("there is no metadata of site {$site} found")

// ApREDeclarationPatch Related exceptions
class ApREDeclarationPatchException(msg: String?) : BizException(msg)
class ApREDeclarationPatchNotExistException(apREDeclarationPatchId: Long) : BizException("ApREDeclarationPatch(id:$apREDeclarationPatchId) not exist")

// ScheduleService Related exceptions
class ScheduleServiceNotFoundException(scheduleType: String): BizException("there is no schedule service found for $scheduleType")

// External Service Related exceptions
class AstroException(message: String?): RuntimeException("call astro error,message:${message}")

// External Service Related exceptions
class SkylineException(message: String?): RuntimeException("call skyline error,message:${message}")

class SkylineBizException(message: String?): BizException("call skyline biz error,message:${message}")

// External Service Related exceptions
class CapacityException(message: String? = "", e: Throwable? = null): RuntimeException("call capacity error,message:${message}", e)

// Compnent data Related exceptions
class ComponentDataUniqueExistException(code: String, refObjectType: String, refObjectId: String) : BizException("metadata constrain {$code,$refObjectType,$refObjectId} exist")

// Resource pool data Related exceptions
class ResourcePoolUniqueExistException(managedClusterKey: String, clusterId: String) : BizException("resource pool {$managedClusterKey,$clusterId} exist")
class ResourcePoolNotFoundException(managedClusterKey: String, clusterId: String) : BizException("resource pool {$managedClusterKey,$clusterId} not found")

// Call external sys  Related exceptions
class CallExternalSysException(sys: String, errorMessage: String? = null, errorCode: Int? = null): RuntimeException("call external sys error,sys:$sys,errorMessage:$errorMessage,errorCode:$errorCode")

// Call cmdb biz Related exceptions
class CmdbException(message: String?):BizException(message)

// Call Fed config Related exceptions
class FedConfigException(message: String?) : RuntimeException(message)

// sFed cluster Related exceptions
class FedClusterException(message: String?) : BizException(message)

class FedPolicyException(message: String?) : BizException(message)

// Resource Object Related exceptions
class ResourceObjectException(message: String?):BizException(message)
class ResourceObjectProtocolNotFoundException(message: String?):RuntimeException(message)
class DispatchLabelException(message: String?) : RuntimeException(message)

class ResourceObjectPostCheckException(message: String?):RuntimeException(message)
class ResourceObjectPreCheckException(message: String?):BizException(message)
class StatefulSetException(message: String?):BizException(message)

// Match Scope Data Related exceptions
class MatchScopeDataException(message: String?):BizException(message)
class MatchScopeDataUniqueExistException(externalType: String, externalId: String, targetType: String, targetId: Long) : BizException("match scope data {$externalType,$externalId,$targetType,$targetId} exist")

// ApRE Resource Group Binding exceptions
class ApREResourceGroupBindingException(message: String?) : BizException(message)
class ApREResourceGroupBindingUniqueExistException(appName: String, resourceGroup: String, runtimeEnvKey: String) : BizException("ApREResourceGroupBinding{$appName,$resourceGroup,$runtimeEnvKey} exist")

//外部调用异常
class AppCenterException(message: String?) : RuntimeException(message)
class PodAggregatorException(message: String?) : RuntimeException(message)
class AcniHomeException(message: String?) : RuntimeException(message)
class VipCrException(message: String?) : RuntimeException(message)
class GropException(message: String?) : RuntimeException(message)
class NormandyException(message: String?) : RuntimeException(message)
class NormandyGropException(message: String?) : RuntimeException(message)
class EventSendException(message: String?) : RuntimeException(message)
class HcrmException(message: String?) : RuntimeException(message)
class AoneGreyException(message: String?) : RuntimeException(message)
class EnvCenterException(message: String?) : RuntimeException(message)

//方法废弃异常
class FunctionDeprecatedException() : RuntimeException("this function is deprecated!")

//RuntimeData相关异常
class RuntimeDataException(message: String?): RuntimeException(message)
//集群策略异常
class ScheduleStrategyException(message: String?): RuntimeException(message)
class AppStackOriginalAppException(message: String?) : RuntimeException(message)
class AppNotInAppstackAoneInfusionGrayException(message: String?) : RuntimeException(message)
class AddAppToDeployGrayException(message: String?) : RuntimeException(message)
class ThreeWayMergeDiffException(message: String?) : RuntimeException(message)
class RuntimeWorkloadMetaException(message: String?) :RuntimeException(message)

/**
 * 未找到基座轮转匹配的Runtime
 */
class RuntimeWorkloadMetaMigrationMatchException(message: String?) :BizException(message)

class ResourceException(message: String?):BizException(message)

class BatchSizeException(message: String?) : BizException(message)