package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.atomcore.facade.param.StrategyParam
import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager.Companion.LOGIN_ACCOUNT_UNKNOWN
import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.constants.CachePrefixConstants
import com.alibaba.koastline.multiclusters.common.exceptions.MetadataException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.UserLabelRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.resourceobj.UserLabel
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.AtomApi
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelType
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component


@Component
class UserLabelBaseService {
    @Autowired
    lateinit var userLabelRepo: UserLabelRepo

    @Autowired
    lateinit var authenticationManager: AuthenticationManager

    @Autowired
    lateinit var commonProperties: CommonProperties

    @Autowired
    lateinit var redisService: RedisService
    @Autowired
    lateinit var atomApi: AtomApi
    @Autowired
    lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var checkService: CheckService

    fun createWithCache(userLabelCreateReqDto: UserLabelCreateReqDto): UserLabel {
        userLabelRepo.findByExternalAndLabel(
            externalId = userLabelCreateReqDto.externalId,
            externalType = userLabelCreateReqDto.externalType.name,
            labelName = userLabelCreateReqDto.labelName
        ) ?.let {
            throw MetadataException("UserLabel[externalId = ${userLabelCreateReqDto.externalId},externalType = ${userLabelCreateReqDto.externalType}, labelName = ${userLabelCreateReqDto.labelName}]已存在")
        }
        require(userLabelCreateReqDto.labelValue != null) {"labelValue must not be null"}
        return UserLabel(
            externalType = userLabelCreateReqDto.externalType.name,
            externalId = userLabelCreateReqDto.externalId,
            labelName = userLabelCreateReqDto.labelName,
            labelValue = userLabelCreateReqDto.labelValue,
            creator = userLabelCreateReqDto.creator,
            modifier = userLabelCreateReqDto.creator,
            submitter = authenticationManager.getLoginAccount() ?.authentication ?.userName ?: LOGIN_ACCOUNT_UNKNOWN
        ).apply {
            userLabelRepo.insert(this)
            redisService.setValue(buildUserLabelKey(
                externalType = this.externalType,
                externalId = this.externalId,
                labelName = this.labelName
            ), JsonUtils.writeValueAsString(this))
        }
    }

    fun findByExternalAndLabelWithCache(externalId: String, externalType: String, labelName: String): UserLabel? {
        redisService.getValue(buildUserLabelKey(
            externalType = externalType,
            externalId = externalId,
            labelName = labelName
        )) ?.let {
            return JsonUtils.readValue(it, UserLabel::class.java)
        }
        return userLabelRepo.findByExternalAndLabel(externalId, externalType, labelName) ?.apply {
            redisService.setValue(buildUserLabelKey(
                externalType = this.externalType,
                externalId = this.externalId,
                labelName = this.labelName
            ), JsonUtils.writeValueAsString(this))
        }
    }

    fun batchQueryByMultiExternalAndLabel(externalIdList: List<String>, externalType: String, labelName: String, labelValue: String? = null): List<UserLabel> {
        return userLabelRepo.batchQueryByMultiExternalAndLabel(externalIdList, externalType, labelName, labelValue)
    }

    fun deleteByExternalAndLabelWithCache(externalId: String, externalType: String, labelName: String, modifier: String) {
        userLabelRepo.findByExternalAndLabel(
            externalId = externalId,
            externalType = externalType,
            labelName = labelName
        ) ?.let {
            userLabelRepo.deleteById(it.id!!, modifier)
            redisService.deleteValue(buildUserLabelKey(
                externalType = externalType,
                externalId = externalId,
                labelName = labelName
            ))
        }
    }

    fun findByExternalAndLabel(externalId: String, externalType: String, labelName: String): UserLabel? {
        return userLabelRepo.findByExternalAndLabel(externalId, externalType, labelName)
    }

    fun findByExternal(externalId: String, externalType: String): List<UserLabel> {
        return userLabelRepo.findByExternal(externalId, externalType)
    }

    fun queryAppLabel(appName: String): List<Map<String, Any>> {
        if (
            whetherFallback(appName)
        ) {
            return atomApi.queryAppLabel(appName)

        }
        return findByExternal(appName, UserLabelExternalType.APPLICATION.name).map { it ->
            mapOf(
                "labelName" to it.labelName,
                "labelValue" to it.labelValue
            )
        }
    }

    fun queryStrategy(strategyParam: String): StrategiesResultVO {
        val req = JsonUtils.readValue(strategyParam, StrategyParam::class.java)
        if (
            whetherFallback(req.app)
        ) {
            return atomApi.queryStrategy(req)
        }
        if (appCenterApi.getAppInfoByName(req.app).status == AppStatusEnum.OFFLINE) {
            throw MetadataException("${req.app}应用不存在!")
        }
        val resourcePool = getResourcePool(req)
        val cpuRatio = getCpuRatio(req, resourcePool)
        val aquamanRet = StrategiesResultVO().apply {
            this.cpuRatio = cpuRatio
            this.resourcePool = resourcePool

        }
        if (commonProperties.contains(
                CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH,
                "false"
            ) ||
            commonProperties.contains(
                CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, req.app
            )
        ) {
            return aquamanRet
        }
        val atomRet = try {
            atomApi.queryStrategy(req)
        } catch (e: Exception) {
            return aquamanRet
        }
        var ret = aquamanRet
        try {
            checkService.checkQueryStrategy(req, aquamanRet, atomRet)
        } catch (e: Exception) {
            ret = atomRet
        }
        return ret
    }


    fun getResourcePool(strategyParam: StrategyParam): String {
        if (strategyParam.app != null && commonProperties.contains(
                CommonProperties.VIDEO_CLOUD_APP_LIST, strategyParam.app
            )
        ) {
            return RESOURCE_POOL.video_cloud.name
        }
        val buId = appCenterApi.getAppInfoByName(strategyParam.app).buId
        if (buId != null && buId == UC_BU) {
            return RESOURCE_POOL.sigma_public.name
        }
        if (buId != null && buId == ANQUAN_BU) {
            if (strategyParam.cell == null) {
                return RESOURCE_POOL.anquan.name
            }
            if (strategyParam.cell == ANQUAN_UNIT && (strategyParam.site != null && strategyParam.site.toLowerCase() in ANQUAN_SITE_LIST)
            ) {
                return RESOURCE_POOL.anquan.name
            } else {
                return RESOURCE_POOL.sigma_public.name
            }
        }
        if (commonProperties.contains(
                CommonProperties.SERVER_OWNER_APP_LIST,
                strategyParam.app
            )
        ) {
            return RESOURCE_POOL.standalone.name;
        }
        return RESOURCE_POOL.sigma_public.name
    }

    fun findDistinctValueByExternalTypeAndName(externalType: String, labelName: String): List<String> {
        return userLabelRepo.findDistinctValueByExternalTypeAndName(externalType, labelName)
    }

    fun findByExternalTypeAndName(externalType: String, labelName: String, pageNumber: Int, pageSize: Int, nextToken: String? = null, externalId: String? = null, labelValue: String? = null): PageData<UserLabel> {
        val idToken = nextToken?.toLongOrNull()
        val page: Page<UserLabel> =
            PageHelper.startPage<UserLabel>(pageNumber, pageSize, "id DESC")
                .doSelectPage {
                    userLabelRepo.findByExternalTypeAndName(
                        externalType = externalType,
                        labelName = labelName,
                        idToken = idToken,
                        externalId = externalId,
                        labelValue = labelValue,
                    )
                }
        val nextTokenResult = page.result.lastOrNull()?.id?.toString()
        return PageData.transformFrom(page, nextTokenResult)
    }

    private fun buildUserLabelKey(externalType: String, externalId: String, labelName: String): String {
        return "${CachePrefixConstants.USER_LABEL}-${externalType}-${externalId}-${labelName}"
    }

    fun whetherFallback(appName: String) = !commonProperties.contains(
        CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, appName
    ) && !commonProperties.contains(
        CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*"
    )

    enum class RESOURCE_POOL {
        sigma_public,
        anquan,
        cainiao,
        standalone,
        video_cloud
    }

    companion object {

        /*
        UC BU
         */
        private const val UC_BU = 37L

        /*
       安全BU
        */
        private const val ANQUAN_BU = 47L

        private val ANQUAN_SITE_LIST = listOf(
            "na61", "na62", "na610", "na620"
        )
        private const val ANQUAN_UNIT = "CENTER_UNIT.center"



        fun getCpuRatio(strategyParam: StrategyParam, resourcePool: String): String {
            if (resourcePool == RESOURCE_POOL.cainiao.name) {
                return "2"
            }
            if (strategyParam.env != null && strategyParam.env == MetadataStageEnum.DAILY.name) {
                return "2"
            }
            return "1"
        }
    }
}