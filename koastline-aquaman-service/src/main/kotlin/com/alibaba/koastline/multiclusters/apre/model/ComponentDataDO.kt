package com.alibaba.koastline.multiclusters.apre.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.*

@ApiModel("组件数据")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ComponentDataDO (
    val id: Long?,
    @ApiModelProperty("代码")
    val code: String,
    @ApiModelProperty("关联对象ID")
    val refObjectId: String,
    @ApiModelProperty("关联对象类型")
    val refObjectType: String,
    @ApiModelProperty("组件注释,key->value格式")
    val annotations: Map<String, String>,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String?
)