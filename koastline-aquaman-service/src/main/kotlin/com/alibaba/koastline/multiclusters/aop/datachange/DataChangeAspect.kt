package com.alibaba.koastline.multiclusters.aop.datachange

import com.alibaba.koastline.multiclusters.aop.datachange.DataChangeManager.Companion.OPS_FALSE
import com.alibaba.koastline.multiclusters.aop.datachange.DataChangeManager.Companion.OPS_SUCCESS
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.data.dao.env.DataChangeOrderRepo
import com.alibaba.koastline.multiclusters.data.vo.env.DataChangeOrder
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.LocalVariableTableParameterNameDiscoverer
import org.springframework.expression.EvaluationContext
import org.springframework.expression.Expression
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Component
import java.lang.reflect.Method
import java.time.Instant
import java.util.*


@Aspect
@Component
class DataChangeAspect @Autowired constructor(
    val dataChangeManager: DataChangeManager
) {

    val log by logger()
    private val discoverer = LocalVariableTableParameterNameDiscoverer()
    private val parser: ExpressionParser = SpelExpressionParser()

    /**
     * 变更事件 相关事件采集 并落库
     * 1.变更人员
     * 2.变更时间
     * 3.变更传入参数
     * 4.耗时时间
     * 5.变更是否成功
     * 6.变更报错信息
     * 7.变更结果
     * 8.变更事件
     * 9.traceId
     */
    @Around("@annotation(com.alibaba.koastline.multiclusters.aop.datachange.DataChange)")
    @Throws(Throwable::class)
    fun logDataChangeExecution(joinPoint: ProceedingJoinPoint): Any? {
        val changeStartTime = Date(Instant.now().toEpochMilli())
        val signature: MethodSignature = joinPoint.signature as MethodSignature
        val method = signature.method
        val dataChange =
            checkNotNull(method.getAnnotation(DataChange::class.java)) { "missing annotation of dataChange in method:${method.name}" }
        val operator = findOperator(joinPoint, dataChange)
        var rs: Any? = null
        var isSuccess = false
        try {
            rs = joinPoint.proceed()
            isSuccess = true
        } catch (e: Exception) {
            log.error("change data with error, changeEvent:${signature.method}, exceptionMsg: ${e.message}", e)
            throw e
        } finally {
            dataChangeManager.noteEvent(
                DataChangeOrder(
                    changeEvent = method.name,
                    startTime = changeStartTime,
                    finishTime = Date(Instant.now().toEpochMilli()),
                    changeDataInput = Arrays.toString(joinPoint.args),
                    operator = operator,
                    success = if (isSuccess) OPS_SUCCESS else OPS_FALSE,
                    result = rs.toString(),
                    changeErrorMsg = null,
                    traceId = TraceUtils.getTraceId()
                )
            )
        }
        return rs
    }

    fun findOperator(joinPoint: ProceedingJoinPoint, dataChange: DataChange): String {
        if (dataChange.operator.isNotBlank()) {
            return dataChange.operator
        }
        val args: Array<Any> = joinPoint.args
        val method: Method = (joinPoint.signature as MethodSignature).method
        val paramNames = checkNotNull(discoverer.getParameterNames(method)) {
            "method params is empty, cannot analysis operator name"
        }
        val context: EvaluationContext = StandardEvaluationContext()
        paramNames.forEachIndexed { index, paramName ->
            context.setVariable(paramName, args[index])
        }
        val operatorKey = dataChange.operatorKey
        val keyExpression: Expression = parser.parseExpression(operatorKey)
        val operator = keyExpression.getValue(context, String::class.java)
            ?: throw IllegalStateException("cannot find operator name!")
        check(operator.isNotBlank()) { "operator cannot be blank!" }
        return operator
    }
}


fun isSucces(isSucces: Boolean) = if (isSucces) OPS_SUCCESS else OPS_FALSE