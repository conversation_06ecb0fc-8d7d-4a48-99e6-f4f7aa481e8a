package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.cse.models.v1alpha1.cloneset.CloneSet
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.SCALE_OUT_POST_CHECK
import com.alibaba.koastline.multiclusters.common.config.ThreeWayMergeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.BatchSizeException
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.exceptions.ThreeWayMergeDiffException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toBlankIfNull
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureRepo
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.CapacityApi
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.resourceobj.base.*
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService.Companion.CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME
import com.alibaba.koastline.multiclusters.resourceobj.base.RollingSetSpecService.Companion.ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME
import com.alibaba.koastline.multiclusters.resourceobj.base.StatefulSetSpecService.Companion.STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.WorkloadSpecFacade
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum.DEPLOY
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum.SCALE_OUT
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.GPU_AFFINITY_SPEC_FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureEffectiveStageEnum
import com.alibaba.koastline.multiclusters.resourceobj.specific.ResourceSpecFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.utils.ThreeWayMerge
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeclarativeScaleOutScheduleService.Companion.TEST_STAGE
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.io.StringReader
import javax.json.Json
import javax.json.JsonValue

@Component
class ResourceObjectService {
    val log by logger()
    @Autowired
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService
    @Autowired
    lateinit var resourceSpecFeatureService: ResourceSpecFeatureService
    @Autowired
    lateinit var resourceObjectPatchService: ResourceObjectPatchService
    @Autowired
    lateinit var serverlessSpecService: ServerlessSpecService
    @Autowired
    lateinit var workloadSpecFactory: WorkloadSpecFactory
    @Autowired
    lateinit var capacityApi: CapacityApi
    @Autowired
    lateinit var crSpecService: CRSpecService
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var userLabelService: UserLabelService
    @Autowired
    lateinit var threeWayMergeProperties: ThreeWayMergeProperties

    @Autowired
    lateinit var commonProperties: CommonProperties
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService
    @Autowired
    lateinit var appCenterApi: AppCenterApi
    @Autowired
    lateinit var resourceObjectFeatureRepo: ResourceObjectFeatureRepo

    fun getAssembledResourceObjectToScaleOut(assembledResourceObjectRequest: ScaleOutAssembledResourceObjectRequest): AssembledResourceObjectResult {
        scheduleStandardService.checkMultiProtocol(
            resourceObjectProtocol = assembledResourceObjectRequest.resourceObjectProtocol,
            appName = assembledResourceObjectRequest.workloadMetadataConstraint.appName,
            resourceGroup = assembledResourceObjectRequest.workloadMetadataConstraint.resourceGroup
        )
        //依据请求决策资源对象协议
        val protocol = getResourceObjectProtocol(assembledResourceObjectRequest.resourceObjectProtocol)
        val version = assembledResourceObjectRequest.resourceObjectProtocolVersion
        if (protocol == ResourceObjectProtocolEnum.RollingSet) {
            checkNotNull(assembledResourceObjectRequest.workloadMetadataConstraint.runtimeId)
        }
        val resourceObjectBuildTypeEnum = assembledResourceObjectRequest.currentResourceObject ?.toNullIfBlank() ?.run {
            ResourceObjectBuildTypeEnum.PATCH } ?:ResourceObjectBuildTypeEnum.CREATE
        val resourceObjectFormatEnum = checkNotNull(assembledResourceObjectRequest.resourceObjectFormatEnum)
        val workloadSpecFacade = workloadSpecFactory.getWorkloadSpecFacade(getResourceObjectProtocol(assembledResourceObjectRequest.resourceObjectProtocol))
        val context = WorkloadSpecContext(
            envStackId = assembledResourceObjectRequest.envStackId,
            workloadMetadataConstraint = assembledResourceObjectRequest.workloadMetadataConstraint,
            resourceObjectProtocolEnum = protocol
        )
        //如果当前不存在workload，则从基线获取
        val originalResourceObjectSpec = assembledResourceObjectRequest.currentResourceObject ?.toNullIfBlank() ?.run {
            //前置校验输入spec合法
            workloadSpecFacade.preCheck(this, resourceObjectFormatEnum)
            YamlUtils.load(this.run {
                //临时紧急处理
                if(protocol == ResourceObjectProtocolEnum.CloneSet) {
                    processCloneSetObjectIdLabelToScaleOut(this)
                } else this
            })
        } ?:run {
            //如果当前不存在workload，则从基线获取
            workloadSpecFacade.getBaseSpec(context)
        }
        return AssembledResourceObjectResult(
            workloadMetadataConstraint = assembledResourceObjectRequest.workloadMetadataConstraint,
            resourceObject = getResourceObjectStr(protocol.name, version, originalResourceObjectSpec, assembledResourceObjectRequest, resourceObjectBuildTypeEnum, workloadSpecFacade, context),
            resourceObjectProtocol =  protocol.name,
            resourceObjectProtocolVersion = version,
            resourceObjectBuildTypeEnum = resourceObjectBuildTypeEnum,
            resourceObjectFormatEnum = assembledResourceObjectRequest.resourceObjectFormatEnum
        ).run {
            if (threeWayMergeProperties.whetherInThreeWayMerge(
                    assembledResourceObjectRequest.workloadMetadataConstraint.appName,
                    assembledResourceObjectRequest.workloadMetadataConstraint.stage,
                    ResourceObjectProtocolEnum.valueOf(assembledResourceObjectRequest.resourceObjectProtocol),
                    ResourceObjectSceneEnum.SCALE_OUT
                )
            ) {
                if (resourceObjectBuildTypeEnum == ResourceObjectBuildTypeEnum.CREATE) {
                    /*
                    SCALE CREATE: no need to do three way merge, just return the original spec
                     */
                    this
                } else {
                    this.copy(
                        resourceObject = threeWayMerge(
                            /*
                            only record injected features in current config, not the whole workload from cluster
                             */
                            getResourceObjectStr(
                                protocol.name,
                                version,
                                emptyMap(),
                                assembledResourceObjectRequest,
                                resourceObjectBuildTypeEnum,
                                workloadSpecFacade,
                                context
                            ),
                            JsonUtils.writeValueAsString(originalResourceObjectSpec),
                            protocol,
                            ResourceObjectSceneEnum.SCALE_OUT,
                            resourceObjectBuildTypeEnum,
                            assembledResourceObjectRequest.resourceObjectFormatEnum
                        ),
                        resourceObjectMergeType = ResourceObjectMergeTypeEnum.THREE_WAY_MERGE
                    )
                }

            } else {
                this.copy(
                    resourceObject = filterLastAppliedConf(
                        this.resourceObject,
                        assembledResourceObjectRequest.resourceObjectFormatEnum,
                        ResourceObjectProtocolEnum.valueOf(assembledResourceObjectRequest.resourceObjectProtocol)
                    ),
                )
            }
        }.apply {
            if (commonProperties.firstOrNull(SCALE_OUT_POST_CHECK) == "true") {
                workloadSpecFacade.postCheckForScaleOut(
                    assembledResourceObjectRequest.currentResourceObject,
                    this.resourceObject,
                    assembledResourceObjectRequest.resourceObjectFormatEnum
                )
            }
        }


    }

    /**
     * 先紧急修复，回头重构一把
     */
    private fun processCloneSetObjectIdLabelToScaleOut(resourceObjectSpecStr: String): String {
        val cloneSet = JsonUtils.gsonReadValue(resourceObjectSpecStr, CloneSet::class.java)
        cloneSet.spec.selector ?.matchLabels ?.let {
            if (!it.containsKey(CloneSetSpecService.TEMPLATE_METADATA_LABEL_OBJECT_ID)) {
                cloneSet.spec.template.metadata ?.labels ?.let { labels ->
                    labels.remove(CloneSetSpecService.TEMPLATE_METADATA_LABEL_OBJECT_ID)
                }
            }
        }
        return JsonUtils.gsonWriteValueAsString(cloneSet)
    }

    fun getPatchStrategyDefinition(protocol: ResourceObjectProtocolEnum) =
        ResourceObjectPatchService.patchStrategyDefinitionList.firstOrNull { patchStrategyDefinition ->
            patchStrategyDefinition.protocols.firstOrNull {
                it.kind == protocol.name
            } != null
        } ?: let {
            throw ResourceObjectException("未找到Patch策略，protocol:${protocol}")
        }

    fun getAssembledResourceObjectToDeploy(assembledResourceObjectRequest: DeployAssembledResourceObjectRequest): AssembledResourceObjectResult {
        //依据请求决策资源对象协议
        val protocol = getResourceObjectProtocol(assembledResourceObjectRequest.resourceObjectProtocol)
        val version = assembledResourceObjectRequest.resourceObjectProtocolVersion
        val resourceObjectFormatEnum = checkNotNull(assembledResourceObjectRequest.resourceObjectFormatEnum)

        //计算基础workload spec
        val workloadSpecFacade = workloadSpecFactory.getWorkloadSpecFacade(protocol)
        val originalResourceObjectSpec = assembledResourceObjectRequest.currentResourceObject.apply {
            //前置校验输入spec合法
            workloadSpecFacade.preCheck(this, resourceObjectFormatEnum)
        } .run {
            workloadSpecFacade.getDeploySpec(assembledResourceObjectRequest.stackPkId, assembledResourceObjectRequest.currentResourceObject,
                resourceObjectFormatEnum, assembledResourceObjectRequest.workloadMetadataConstraint)
        }
        val ret = AssembledResourceObjectResult(
            workloadMetadataConstraint = assembledResourceObjectRequest.workloadMetadataConstraint,
            resourceObject = fillFeatureToWorkloadSpec(
                assembledResourceObjectRequest.workloadMetadataConstraint,
                resourceObjectFormatEnum,
                ResourceObjectSceneEnum.DEPLOY,
                ResourceObjectBuildTypeEnum.PATCH,
                protocol, version, assembledResourceObjectRequest.inputParams ?: emptyMap(),
                originalResourceObjectSpec,
                workloadSpecFacade,
                assembledResourceObjectRequest.envStackId
            ),
            resourceObjectProtocol = protocol.name,
            resourceObjectProtocolVersion = version,
            resourceObjectBuildTypeEnum = ResourceObjectBuildTypeEnum.PATCH,
            resourceObjectFormatEnum = assembledResourceObjectRequest.resourceObjectFormatEnum
        )
        if (threeWayMergeProperties.whetherInThreeWayMerge(
                assembledResourceObjectRequest.workloadMetadataConstraint.appName,
                assembledResourceObjectRequest.workloadMetadataConstraint.stage,
                ResourceObjectProtocolEnum.valueOf(assembledResourceObjectRequest.resourceObjectProtocol),
                ResourceObjectSceneEnum.DEPLOY
            )
        ) {
            if (ResourceObjectProtocolEnum.CloneSet.name == assembledResourceObjectRequest.resourceObjectProtocol && !CloneSetSpecService.whetherThreeWayMergeInitialized(ret.resourceObject)) {
                return ret.copy(
                    resourceObject = CloneSetSpecService.lastAppliedConfigInit(ret.resourceObject, assembledResourceObjectRequest.resourceObjectFormatEnum),
                    resourceObjectMergeType = ResourceObjectMergeTypeEnum.THREE_WAY_MERGE
                )
            } else {
                return ret.copy(
                    resourceObject = threeWayMerge(
                        ret.resourceObject,
                        assembledResourceObjectRequest.currentResourceObject!!,
                        protocol,
                        ResourceObjectSceneEnum.DEPLOY,
                        ResourceObjectBuildTypeEnum.PATCH,
                        assembledResourceObjectRequest.resourceObjectFormatEnum
                    ),
                    resourceObjectMergeType = ResourceObjectMergeTypeEnum.THREE_WAY_MERGE
                )
            }

        } else {
            return ret.copy(
                resourceObject = filterLastAppliedConf(
                    ret.resourceObject,
                    assembledResourceObjectRequest.resourceObjectFormatEnum,
                    ResourceObjectProtocolEnum.valueOf(assembledResourceObjectRequest.resourceObjectProtocol)
                ),
            )
        }
    }

    fun getAssembledVersionToDeploy(assembledVersionRequest: DeployAssembledVersionRequest): AssembledVersionResult {
        val protocol = getResourceObjectProtocol(assembledVersionRequest.versionProtocol)
        val resourceObjectFormatEnum = ResourceObjectFormatEnum.YAML

        val workloadSpecFacade = workloadSpecFactory.getWorkloadSpecFacade(protocol)
        val (version, traitList) = assembleVersionSpec(
            resourceObjectFormatEnum = resourceObjectFormatEnum,
            resourceObjectProtocol = protocol,
            version = null,
            userTraits = assembledVersionRequest.userTraits,
            inputParams = assembledVersionRequest.extendedParams?.run {
                YamlUtils.load(JsonUtils.writeValueAsString(this))
            } ?: emptyMap(),
            originalResourceObjectSpec = assembledVersionRequest.workload?.toNullIfBlank()?.run { YamlUtils.load(this) } ?: emptyMap(),
            envStackId = assembledVersionRequest.envStackId,
            appName = assembledVersionRequest.appName,
            workloadSpecFacade = workloadSpecFacade,
        )
        return AssembledVersionResult(
            appName = assembledVersionRequest.appName,
            envStackId = assembledVersionRequest.envStackId,
            versionProtocol = assembledVersionRequest.versionProtocol,
            version = version,
            traitList = traitList,
        )


    }

    fun getAssembledCRListToScaleOut(assembledCRListRequest: ScaleOutAssembledCRListRequest): AssembledCRListResult {
        return crSpecService.getScaleCRSpecList(
            envStackId = assembledCRListRequest.envStackId,
            workloadMetadataConstraint = assembledCRListRequest.workloadMetadataConstraint,
            resourceObjectFormatEnum = assembledCRListRequest.resourceObjectFormatEnum!!,
            acceptableCRListResult = assembledCRListRequest.acceptableCrTypeList
        )
    }

    fun getAssembledCRListToDeploy(assembledCRListRequest: DeployAssembledCRListRequest): AssembledCRListResult {
        return crSpecService.getDeployCRSpecList(
            envStackId = assembledCRListRequest.envStackId,
            stackPkId = assembledCRListRequest.stackPkId,
            workloadMetadataConstraint = assembledCRListRequest.workloadMetadataConstraint,
            resourceObjectFormatEnum = assembledCRListRequest.resourceObjectFormatEnum!!
        )
    }

    /**
     * 向原始Workload Spec中注入特性
     * @param workloadMetadataConstraint workload约束元数据
     * @param resourceObjectBuildTypeEnum 构建类型
     * @param resourceObjectFormatEnum 格式类型
     * @param resourceObjectProtocol workload协议
     * @param version 协议版本
     * @param inputParams 输入参数,value可以为结构体
     * @param originalResourceObjectSpec 原始workload spec
     * @param workloadSpecFacade workload服务
     * @return workload spec
     */
    private fun fillFeatureToWorkloadSpec(workloadMetadataConstraint: WorkloadMetadataConstraint, resourceObjectFormatEnum: ResourceObjectFormatEnum,
                                          resourceObjectSceneEnum: ResourceObjectSceneEnum,
                                          resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
                                          resourceObjectProtocol: ResourceObjectProtocolEnum, version: String?,
                                          inputParams: Map<String, Any>,
                                          originalResourceObjectSpec: Map<String, Any>,
                                          workloadSpecFacade: WorkloadSpecFacade,
                                          envStackId: String
    ): String {
        //特性注入
        val featureImportSpecList = resourceObjectFeatureService.listResourceObjectFeatureImportSpec(workloadMetadataConstraint,
            resourceObjectSceneEnum, resourceObjectBuildTypeEnum, resourceObjectProtocol.name, version, buildSystemParams(
                inputParams, workloadMetadataConstraint, originalResourceObjectSpec
            ), envStackId
        )
        log.info("fillFeatureToWorkloadSpec,workloadMetadataConstraint:${workloadMetadataConstraint}, " +
                "resourceObjectSceneEnum:${resourceObjectSceneEnum}, resourceObjectBuildTypeEnum:${resourceObjectBuildTypeEnum}, " +
                "resourceObjectProtocol:${resourceObjectProtocol}, envStackId:${envStackId}, featureImportSpecList:${featureImportSpecList}")
        return patchFeatureImportToResourceObjectSpec(
            protocol = resourceObjectProtocol.name,
            version = version,
            resourceObjectSpec = originalResourceObjectSpec,
            featureImportList = featureImportSpecList,
            appName = workloadMetadataConstraint.appName,
            resourceObjectSceneEnum = resourceObjectSceneEnum
        ).run {
            when(resourceObjectFormatEnum) {
                ResourceObjectFormatEnum.YAML -> YamlUtils.dump(this)
                else -> JsonUtils.writeValueAsString(this)
            }
        }.run {
            workloadSpecFacade.postApply(this, resourceObjectFormatEnum)
        }.apply {
            //后置校验输入spec合法
            workloadSpecFacade.postCheck(this, resourceObjectFormatEnum)
        }
    }

    private fun assembleVersionSpec(
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
        resourceObjectProtocol: ResourceObjectProtocolEnum,
        version: String?,
        userTraits: List<UserTrait>,
        inputParams: Map<String, Any>,
        originalResourceObjectSpec: Map<String, Any>,
        workloadSpecFacade: WorkloadSpecFacade,
        envStackId: String,
        appName: String,
    ): Pair<String, String> {
        //特性注入
        val featureImportSpecList = resourceObjectFeatureService.listVersionFeatureImportSpec(
            appName,
            ResourceObjectSceneEnum.DEPLOY,
            ResourceObjectBuildTypeEnum.PATCH,
            resourceObjectProtocol.name,
            version,
            userTraits,
            inputParams,
            envStackId
        )
        val ResourceObjectGetFeatureImportDOList = mutableListOf<ResourceObjectGetFeatureImportDO>()
        featureImportSpecList.forEach {
            ResourceObjectGetFeatureImportDOList.add(
                ResourceObjectGetFeatureImportDO(
                    resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                    formData = it.paramMap?.let {
                        YamlUtils.load(it)
                    },
                    creator = it.creator,
                    modifier = it.modifier,
                    gmtCreate = it.gmtCreate,
                    gmtModified = it.gmtModified,
                    version = it.version,
                )
            )
        }
        return Pair(
            patchFeatureImportToResourceObjectSpec(
                protocol = resourceObjectProtocol.name,
                version = version,
                resourceObjectSpec = originalResourceObjectSpec,
                featureImportList = featureImportSpecList.filter { it ->
                    resourceObjectFeatureRepo.findByResourceObjectFeatureKeyAndVersion(
                        it.resourceObjectFeatureKey,
                        it.version
                    )!!.effectiveStage == ResourceObjectFeatureEffectiveStageEnum.DURING_VERSIONOUT.name
                },
                appName = appName,
                resourceObjectSceneEnum = ResourceObjectSceneEnum.DEPLOY
            ).run {
                when (resourceObjectFormatEnum) {
                    ResourceObjectFormatEnum.YAML -> YamlUtils.dump(this)
                    else -> JsonUtils.writeValueAsString(this)
                }
            }.apply {
                //后置校验输入spec合法
                workloadSpecFacade.postCheck(this, resourceObjectFormatEnum)
            },

            JsonUtils.writeValueAsString(ResourceObjectGetFeatureImportDOList)
        )
    }

    /**
     * 获取终态资源对象Spec
     * @param protocol 资源对象协议 @see ResourceObjectProtocolEnum
     * @param version 资源对象协议版本，null代表不限定
     * @param originalResourceObjectSpec 运行态原始Workload spec
     * @param assembledResourceObjectRequest 扩容聚合Spec请求参数
     * @param resourceObjectBuildTypeEnum 资源对象构建类型 @see ResourceObjectBuildTypeEnum
     * @param workloadSpecFacade 资源对象Spec编排服务
     * @return 聚合完整的资源对象Spec
     */
    private fun getResourceObjectStr(protocol: String, version: String?,
                                     originalResourceObjectSpec: Map<String, Any>,
                                     assembledResourceObjectRequest: ScaleOutAssembledResourceObjectRequest,
                                     resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
                                     workloadSpecFacade: WorkloadSpecFacade,
                                     context: WorkloadSpecContext): String {
        val resourceObject = run {
                val featureImportSpecList = resourceObjectFeatureService.listResourceObjectFeatureImportSpec(assembledResourceObjectRequest.workloadMetadataConstraint,
                    ResourceObjectSceneEnum.SCALE_OUT, resourceObjectBuildTypeEnum, protocol, version, buildSystemParams(
                        assembledResourceObjectRequest.inputParams,
                        assembledResourceObjectRequest.workloadMetadataConstraint,
                        originalResourceObjectSpec
                    ),
                    assembledResourceObjectRequest.envStackId
                )
                val resourceObjectSpec = patchFeatureImportToResourceObjectSpec(
                    protocol = protocol,
                    version = version,
                    resourceObjectSpec = originalResourceObjectSpec,
                    featureImportList = featureImportSpecList,
                    appName = assembledResourceObjectRequest.workloadMetadataConstraint.appName,
                    resourceObjectSceneEnum = SCALE_OUT
                )
                workloadSpecFacade.postModifyBaseSpec(resourceObjectSpec, context)
            }

        return when(assembledResourceObjectRequest.resourceObjectFormatEnum) {
            null, ResourceObjectFormatEnum.YAML -> YamlUtils.dump(resourceObject)
            else -> JsonUtils.writeValueAsString(resourceObject)
        }.apply {
            workloadSpecFacade.postCheck(this, assembledResourceObjectRequest.resourceObjectFormatEnum!!)
        }
    }

    /**
     * 构建系统参数
     * TODO 待进一步丰富化系统变量
     */
    private fun buildSystemParams(
        inputParams: Map<String, Any>,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        originalResourceObjectSpec: Map<String, Any>
    ): Map<String, Any> {
        return inputParams.toMutableMap().apply {
            this["metadata"] = mapOf(
                ResourceObjectConstants.METADATA_APP_NAME to workloadMetadataConstraint.appName,
                ResourceObjectConstants.METADATA_RESOURCE_GROUP to workloadMetadataConstraint.resourceGroup,
                ResourceObjectConstants.METADATA_SITE to workloadMetadataConstraint.site,
                ResourceObjectConstants.METADATA_UNIT to workloadMetadataConstraint.unit,
                ResourceObjectConstants.METADATA_STAGE to workloadMetadataConstraint.stage,
                ResourceObjectConstants.METADATA_SUBGROUP to workloadMetadataConstraint.subgroup,
                ResourceObjectConstants.METADATA_CLUSTER_ID to workloadMetadataConstraint.clusterId,
                ResourceObjectConstants.METADATA_ENVTYPE to getEnvType(workloadMetadataConstraint.stage),
            )
            this["appInfo"] = appCenterApi.getAppInfoByName(workloadMetadataConstraint.appName)
            this["deployVersion"] =
                getDeployVersionFromOriginalResourceObject(originalResourceObjectSpec).toBlankIfNull()
        }
    }
    fun getEnvType(stage: String): String {
        return if (stage == MetadataStageEnum.DAILY.name) TESTING_ENVTYPE else ONLINE_ENVTYPE
    }

    /**
     * 将特性附加到资源对象Spec Yaml中
     */
    private fun patchFeatureImportToResourceObjectSpec(
        protocol: String,
        version: String?,
        resourceObjectSpec: Map<String, Any>,
        featureImportList: List<FeatureImportPatchDO>,
        appName: String,
        resourceObjectSceneEnum: ResourceObjectSceneEnum
    ): Map<String, Any> {
        if (featureImportList.isEmpty()) {
            return resourceObjectSpec
        }
        var targetResourceObjectSpec = resourceObjectSpec.toMutableMap()
        featureImportList.forEach {
            targetResourceObjectSpec = resourceObjectPatchService.patchFeature(
                protocol = protocol,
                version = version,
                baseResourceObjectSpec = targetResourceObjectSpec,
                featureSpec = it.patch,
                additionalPatchStrategyDefinition = it.strategy,
                resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                appName = appName,
                resourceObjectSceneEnum = resourceObjectSceneEnum
            )
        }
        return targetResourceObjectSpec
    }


    /**
     * 三路合并
     * @param currentResourceObjectConfig 当前下发配置
     * @param originalResourceObjectSpec 原始资源对象
     * @return
     */
    fun threeWayMerge(
        currentResourceObjectConfigStr: String,
        originalResourceObjectSpecStr: String,
        resourceObjectProtocol: ResourceObjectProtocolEnum,
        resourceObjectScene: ResourceObjectSceneEnum,
        resourceObjectBuildType: ResourceObjectBuildTypeEnum,
        resourceObjectFormat: ResourceObjectFormatEnum,
    ): String {
        log.info("currentResourceObjectConfigStr:${currentResourceObjectConfigStr},originalResourceObjectSpecStr:${originalResourceObjectSpecStr},resourceObjectProtocol:${resourceObjectProtocol},resourceObjectScene:${resourceObjectScene},resourceObjectBuildType:${resourceObjectBuildType},resourceObjectFormat:${resourceObjectFormat}")
        if (resourceObjectBuildType == ResourceObjectBuildTypeEnum.CREATE) {
            throw ThreeWayMergeDiffException("when scale create there is no currentResourceObjectConfig in cluster, which do not need three way merge")
        }
        if (currentResourceObjectConfigStr.trimIndent().replace("\n", "") == EMPTY_JSON_OBJECT) {
            return originalResourceObjectSpecStr
        }

        var currentResourceObjectConfig =
            YamlUtils.load(currentResourceObjectConfigStr.toNullIfBlank() ?: EMPTY_JSON_OBJECT)
        preProcessLastAppliedConf(currentResourceObjectConfig)


        /*
        serialize filtered currentResourceObjectConfig
         */
        val lastAppliedConf = JsonUtils.writeValueAsString(currentResourceObjectConfig)

        /*
        pre-load metadata.annotations
         */
        currentResourceObjectConfig = preLoadResourceSpecAnnotations(currentResourceObjectConfig)


        /*
        set last-applied-configuration annotations
         */
        if (resourceObjectScene == ResourceObjectSceneEnum.SCALE_OUT && resourceObjectBuildType == ResourceObjectBuildTypeEnum.PATCH) {

            getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)?.apply {
                this.put(LAST_APPLIED_CONF_SCALE, lastAppliedConf)
                this.put(LAST_APPLIER, LAST_APPLIER_SCALE)
            }

        } else {

            getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)?.apply {
                this.put(LAST_APPLIED_CONF_DEPLOY, lastAppliedConf)
                this.put(LAST_APPLIER, LAST_APPLIER_DEPLOY)
            }
        }

        var originalResourceObjectSpec =
            YamlUtils.load(originalResourceObjectSpecStr)

        /*
        get patch strategy definition
         */
        val patchStrategyDefinition = getPatchStrategyDefinition(resourceObjectProtocol)

        /*
        compare currentResourceObjectConfig and originalResourceObjectSpec,
        get additions and modifications
         */
        val additionAndModifyPatch = ThreeWayMerge.diff(
            Json.createReader(StringReader(JsonUtils.writeValueAsString(originalResourceObjectSpec))).readValue(),
            Json.createReader(StringReader(JsonUtils.writeValueAsString(currentResourceObjectConfig))).readValue(),
            "",
            patchStrategyDefinition,
            null,
            ThreeWayMerge.DiffOptions(
                ignoreChangesAndAdditions = false,
                ignoreDeletions = true
            )
        )

        /*
        get last-applied-configuration annotation from originalResourceObjectSpec,
        Deploy scene: get from kubectl.kubernetes.io/last-applied-configuration
        Scale Out scene: get from kubectl.kubernetes.io/last-applied-configuration-scale
         */
        val lastAppliedConfFromOriginalResourceObjectSpec =

            getLastAppliedConfFromOriginalResourceObjectSpec(originalResourceObjectSpec, resourceObjectScene)

        /*
        compare currentResourceObjectConfig and last-applied-configuration,
        get deletions
        if last-applied-configuaration is empty, deletePatch will return empty, which makes deletions impossible, three-way merge is downgraded to two-way merge
         */
        val deletePatch: JsonValue?
        if (resourceObjectScene == ResourceObjectSceneEnum.SCALE_OUT && ResourceObjectService.LAST_APPLIER_DEPLOY == getLastApplier(originalResourceObjectSpecStr)) {
            deletePatch = null
        } else {
            deletePatch = ThreeWayMerge.diff(
                Json.createReader(
                    StringReader(
                        lastAppliedConfFromOriginalResourceObjectSpec ?: EMPTY_JSON_OBJECT
                    )
                )
                    .readValue(),
                Json.createReader(StringReader(JsonUtils.writeValueAsString(currentResourceObjectConfig))).readValue(),
                "",
                patchStrategyDefinition,
                null,
                ThreeWayMerge.DiffOptions(
                    ignoreChangesAndAdditions = true,
                    ignoreDeletions = false
                )
            )
        }


        /*
        combine two patches (addtions&modifications and deletions), get final patch
         */
        val finalPatch = ThreeWayMerge.merge(
            additionAndModifyPatch ?: JsonValue.EMPTY_JSON_OBJECT,
            deletePatch ?: JsonValue.EMPTY_JSON_OBJECT,
            "",
            patchStrategyDefinition,
            null,
            mergeOptions = ThreeWayMerge.MergeOptions(
                ignoreUnmatchedNulls = false,
            )
        )

        /*
        do final merge, get final total object which is POST to k8s cluster
         */
        var finalPostTotalObject = ThreeWayMerge.merge(
            Json.createReader(StringReader(JsonUtils.writeValueAsString(originalResourceObjectSpec))).readValue(),
            finalPatch,
            "",
            patchStrategyDefinition,
            null,
            mergeOptions = ThreeWayMerge.MergeOptions(
                ignoreUnmatchedNulls = true,
            )
        )

        /*
        compensate for excessive deletions
         */
        if (resourceObjectScene == ResourceObjectSceneEnum.SCALE_OUT && resourceObjectBuildType == ResourceObjectBuildTypeEnum.PATCH && deletePatch != null) {
            finalPostTotalObject = compensateExcessDelete(originalResourceObjectSpec, finalPostTotalObject, deletePatch,patchStrategyDefinition)

        }

        val ret = JsonUtils.format(finalPostTotalObject) ?: EMPTY_JSON_OBJECT
        when (resourceObjectFormat) {
            ResourceObjectFormatEnum.JSON -> {
                return JsonUtils.writeValueAsString(YamlUtils.load(ret))
            }

            else -> {
                return YamlUtils.dump(YamlUtils.load(ret))
            }
        }
    }

    private fun getLastAppliedConfFromOriginalResourceObjectSpec(
        originalResourceObjectSpec: Map<String, Any>,
        resourceObjectScene: ResourceObjectSceneEnum,
    ):String? {
        return getAnnotationsFromWorkloadSpec(originalResourceObjectSpec)?.run {
            when (this.get(LAST_APPLIER)) {
                LAST_APPLIER_SCALE -> this.get(LAST_APPLIED_CONF_SCALE)
                LAST_APPLIER_DEPLOY -> this.get(LAST_APPLIED_CONF_DEPLOY)
                null -> when (resourceObjectScene) {
                    SCALE_OUT -> {
                        EMPTY_JSON_OBJECT
                    }
                    DEPLOY -> {
                        this.get(LAST_APPLIED_CONF_DEPLOY)
                    }
                }
                else -> {
                    throw ThreeWayMergeDiffException("invalid kubectl.kubernetes.io/last-applier")
                }
            }
        } as? String?
    }

    fun compensateExcessDelete(
        originalResourceObjectSpec: Map<String, Any>,
        finalPostTotalObject: JsonValue,
        scaleOutDeletePatch: JsonValue?,
        patchStrategyDefinition: PatchStrategyDefinition
    ): JsonValue {
        if (scaleOutDeletePatch == null) {
            return finalPostTotalObject
        }
        /*
            get deploy last-applied-configuration
             */
        var finalPostTotalObject1 = finalPostTotalObject
        val lastAppliedDeployConfFromOriginalResourceObjectSpec =

            getAnnotationsFromWorkloadSpec(originalResourceObjectSpec)?.run {
                (this.get(LAST_APPLIED_CONF_DEPLOY) as? String?)?.toNullIfBlank()
            } ?: EMPTY_JSON_OBJECT

        var deployLastApplieDeletedByScale = ThreeWayMerge.merge(
            Json.createReader(StringReader(lastAppliedDeployConfFromOriginalResourceObjectSpec)).readValue(),
            scaleOutDeletePatch,
            "",
            patchStrategyDefinition,
            null,
            mergeOptions = ThreeWayMerge.MergeOptions(
                ignoreUnmatchedNulls = true,
            )
        )

        /*
            get deletions from deploy last-applied-configuration
             */
        val deleteFromLastDeployPatch = ThreeWayMerge.diff(
            Json.createReader(
                StringReader(lastAppliedDeployConfFromOriginalResourceObjectSpec)
            )
                .readValue(),
            deployLastApplieDeletedByScale,
            "",
            patchStrategyDefinition,
            null,
            ThreeWayMerge.DiffOptions(
                ignoreChangesAndAdditions = true,
                ignoreDeletions = false
            )
        )

        /*
            get compensating dditions
             */
        val compensateAdditionPatch = ThreeWayMerge.diff(
            ThreeWayMerge.merge(
                Json.createReader(StringReader(lastAppliedDeployConfFromOriginalResourceObjectSpec))
                    .readValue(),
                deleteFromLastDeployPatch ?: JsonValue.EMPTY_JSON_OBJECT,
                "",
                patchStrategyDefinition,
                null,
                mergeOptions = ThreeWayMerge.MergeOptions(
                    ignoreUnmatchedNulls = true,
                )
            ),
            Json.createReader(StringReader(lastAppliedDeployConfFromOriginalResourceObjectSpec))
                .readValue(),
            "",
            patchStrategyDefinition,
            null,
            ThreeWayMerge.DiffOptions(
                ignoreChangesAndAdditions = false,
                ignoreDeletions = true
            )
        )

        /*
            compensate for resource object
             */
        finalPostTotalObject1 = ThreeWayMerge.merge(
            finalPostTotalObject1,
            compensateAdditionPatch ?: JsonValue.EMPTY_JSON_OBJECT,
            "",
            patchStrategyDefinition,
            null,
            mergeOptions = ThreeWayMerge.MergeOptions(
                ignoreUnmatchedNulls = true,
            )

        )

        /*
            compensate for last-applied of resource object
             */
        val finalPostTotalObjectInMap = YamlUtils.load(JsonUtils.format(finalPostTotalObject1)!!)
        val preCompensatedLastApplied =

            getAnnotationsFromWorkloadSpec(finalPostTotalObjectInMap)?.run {
                (this.get(LAST_APPLIED_CONF_SCALE) as? String?)?.toNullIfBlank()
            } ?: EMPTY_JSON_OBJECT
        val afterCompensatedLastApplied = ThreeWayMerge.merge(
            Json.createReader(StringReader(preCompensatedLastApplied)).readValue(),
            compensateAdditionPatch ?: JsonValue.EMPTY_JSON_OBJECT,
            "",
            patchStrategyDefinition,
            null,
            mergeOptions = ThreeWayMerge.MergeOptions(
                ignoreUnmatchedNulls = true,
            )
        )

        getAnnotationsFromWorkloadSpec(finalPostTotalObjectInMap)?.apply {
            this.put(
                LAST_APPLIED_CONF_SCALE,
                JsonUtils.writeValueAsString(
                    YamlUtils.load(
                        JsonUtils.format(afterCompensatedLastApplied) ?: EMPTY_JSON_OBJECT
                    )
                )
            )
        }

        finalPostTotalObject1 =
            Json.createReader(StringReader(JsonUtils.writeValueAsString(finalPostTotalObjectInMap))).readValue()
        return finalPostTotalObject1
    }

    companion object {
        const val BATCH_QUERY_RESOURCE_GROUP_MAX_SIZE = 250
        const val LAST_APPLIED_CONF_DEPLOY = "kubectl.kubernetes.io/last-applied-configuration"
        const val LAST_APPLIED_CONF_SCALE = "kubectl.kubernetes.io/last-applied-configuration-scale"
        const val LAST_APPLIER = "kubectl.kubernetes.io/last-applier"
        const val LAST_APPLIER_SCALE = "scale"
        const val LAST_APPLIER_DEPLOY = "deploy"
        val LAST_APPLIED_CONF_ANNOTATIONS = listOf(
            LAST_APPLIED_CONF_DEPLOY,
            LAST_APPLIED_CONF_SCALE,
            LAST_APPLIER
        )
        val NEW_LAST_APPLIED_CONF_ANNOTATIONS = listOf(
            LAST_APPLIED_CONF_SCALE,
            LAST_APPLIER
        )
        const val ROLLOUT_ID = "apps.kruise.io/rollout-id"
        const val ROLLOUT_BATCH_ID = "apps.kruise.io/rollout-batch-id"
        val PUBLISH_LABELS = listOf(
            ROLLOUT_ID,
            ROLLOUT_BATCH_ID,
        )

        const val PUBLISH_ID = "statefulset.beta1.sigma.ali/publish-id"
        const val UPGRADE_MAC_UNAVAILABLE = "statefulset.beta1.sigma.ali/upgrade-max-unavailable"
        const val PARTITION = "statefulset.beta1.sigma.ali/partition"
        const val POD_UPGRADE_TIMEOUT = "statefulset.beta1.sigma.ali/pod-upgrade-timeout"
        val PUBLISH_ANNOS = listOf(
            PUBLISH_ID,
            UPGRADE_MAC_UNAVAILABLE,
            PARTITION,
            POD_UPGRADE_TIMEOUT
        )

        const val UPDATE_PARTITION = "updateStrategy"
        val PUBLISH_STRATEGY = listOf(
            UPDATE_PARTITION,
        )

        const val KOASTLINE_LIB_CONTAINER = "koastline-lib-injector"
        val PUBLISH_CONTAINERS = listOf(
            KOASTLINE_LIB_CONTAINER,
        )

        val EMPTY_JSON_OBJECT = """{}"""
        val TESTING_ENVTYPE = "testing"
        val ONLINE_ENVTYPE = "online"
        val gson = Gson()
        fun preLoadResourceSpecAnnotations(currentResourceObjectConfig: Map<String, Any>): Map<String, Any> {

            return currentResourceObjectConfig.toMutableMap().apply {
                if (this.get("metadata") == null) {
                    this.put("metadata", mutableMapOf<String, Any>())
                }
                (this.get("metadata") as MutableMap<String, Any>).apply {
                    if (this.get("annotations") == null) {
                        this.put("annotations", mutableMapOf<String, Any>())
                    }
                }
            }
        }

        fun preLoadResourceSpecLabels(currentResourceObjectConfig: Map<String, Any>): Map<String, Any> {

            return currentResourceObjectConfig.toMutableMap().apply {
                if (this.get("metadata") == null) {
                    this.put("metadata", mutableMapOf<String, Any>())
                }
                (this.get("metadata") as MutableMap<String, Any>).apply {
                    if (this.get("labels") == null) {
                        this.put("labels", mutableMapOf<String, Any>())
                    }
                }
            }
        }

        public fun getLastApplier(
            originalResourceObjectSpecStr: String,
        ): String {
            val originalResourceObjectSpec = YamlUtils.load(originalResourceObjectSpecStr.toNullIfBlank() ?: ResourceObjectService.EMPTY_JSON_OBJECT)
            return ResourceObjectService.getAnnotationsFromWorkloadSpec(originalResourceObjectSpec)?.run {
                when (this.get(ResourceObjectService.LAST_APPLIER)) {
                    ResourceObjectService.LAST_APPLIER_SCALE -> ResourceObjectService.LAST_APPLIER_SCALE
                    ResourceObjectService.LAST_APPLIER_DEPLOY -> ResourceObjectService.LAST_APPLIER_DEPLOY
                    null -> ResourceObjectService.LAST_APPLIER_DEPLOY
                    else -> {
                        throw ThreeWayMergeDiffException("invalid kubectl.kubernetes.io/last-applier")
                    }
                }
            } ?: ResourceObjectService.LAST_APPLIER_DEPLOY
        }

        fun getAnnotationsFromWorkloadSpec(workload: Map<String, Any>): MutableMap<String, Any>? {
            return (workload.get("metadata") as? Map<String, Any>?)?.get("annotations") as? MutableMap<String, Any>?
        }

        fun getLabelsFromWorkloadSpec(workload: Map<String, Any>): MutableMap<String, Any>? {
            return (workload.get("metadata") as? Map<String, Any>?)?.get("labels") as? MutableMap<String, Any>?
        }

        fun getSpecFromWorkloadSpec(workload: Map<String, Any>): MutableMap<String, Any>? {
            return workload.get("spec") as? MutableMap<String, Any>?
        }

        fun getContainersFromWorkloadSpec(workload: Map<String, Any>): MutableList<MutableMap<String, Any>?>? {
            return (((workload.get("spec") as? Map<String, Any>?)?.get("template") as? MutableMap<String, Any>?)?.get("spec") as? MutableMap<String, Any>?)?.get("containers") as? MutableList<MutableMap<String, Any>?>?
        }

        fun getPodSpecFromWorkloadSpec(workload: Map<String, Any>): MutableMap<String, Any>? {
            return (((workload.get("spec") as? Map<String, Any>?)?.get("template") as? MutableMap<String, Any>?)?.get("spec") as? MutableMap<String, Any>?)
        }

        fun getPodLabelsFromWorkloadSpec(workload: Map<String, Any>): MutableMap<String, Any>? {
            return (((workload.get("spec") as? Map<String, Any>?)?.get("template") as? MutableMap<String, Any>?)?.get("metadata") as? MutableMap<String, Any>?)?.get(
                "labels"
            ) as? MutableMap<String, Any>?
        }

        fun getDeployVersionFromOriginalResourceObject(
            originalResourceObjectSpec: Map<String, Any>
        ): String? {
            return getPodLabelsFromWorkloadSpec(originalResourceObjectSpec)?.run {
                this.getOrDefault(
                    ResourceObjectConstants.METADATA_DEPLOY_VERSION,
                    this.get(ResourceObjectConstants.METADATA_DEPLOY_VERSION_FOR_ROLLINGSET)
                )
            } as? String?
        }


        fun filterLastAppliedConf(
            resourceObjectSpecStr: String,
            resourceObjectFormat: ResourceObjectFormatEnum,
            resourceObjectProtocol: ResourceObjectProtocolEnum
        ): String {
            var resourceObjectSpec = YamlUtils.load(resourceObjectSpecStr)

            when (resourceObjectProtocol) {
                ResourceObjectProtocolEnum.ServerlessApp -> {
                    return resourceObjectSpecStr
                }

                ResourceObjectProtocolEnum.StatefulSet, ResourceObjectProtocolEnum.RollingSet -> {

                    getAnnotationsFromWorkloadSpec(resourceObjectSpec)?.keys?.removeAll{
                        NEW_LAST_APPLIED_CONF_ANNOTATIONS.contains(it)
                    }

                }

                ResourceObjectProtocolEnum.CloneSet -> {

                    getAnnotationsFromWorkloadSpec(resourceObjectSpec)?.keys?.removeAll{
                        LAST_APPLIED_CONF_ANNOTATIONS.contains(it)
                    }
                }
                else -> {}
            }

            when (resourceObjectFormat) {
                ResourceObjectFormatEnum.YAML -> return YamlUtils.dump(resourceObjectSpec)
                else -> return JsonUtils.writeValueAsString(resourceObjectSpec)
            }
        }

        public fun preProcessLastAppliedConf(currentResourceObjectConfig: Map<String, Any>) {
            /*
            filter last-applied-configuration annotations
            avoid endless recursive in serialization
             */

            getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll {
                LAST_APPLIED_CONF_ANNOTATIONS.contains(it)
            }

            getLabelsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll {
                BaseSpecService.TEMPLATE_LABEL_BLACK_LIST.contains(it)
            }


            /*
            remove publish related config
             */

            getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll {
                PUBLISH_ANNOS.contains(it)
            }

            getLabelsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll {
                PUBLISH_LABELS.contains(it)
            }

            getSpecFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll {
                PUBLISH_STRATEGY.contains(it)
            }

            getContainersFromWorkloadSpec(currentResourceObjectConfig)?.removeAll {
                PUBLISH_CONTAINERS.contains(it?.get("name"))
            }
        }
    }

    /**
     * 判断环境基线是否合法
     */
    fun checkBaseline(envStackId: String, protocol: ResourceObjectProtocolEnum): CheckBaselineResultResp {
        return try {
            when (protocol) {
                ResourceObjectProtocolEnum.StatefulSet -> cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
                ResourceObjectProtocolEnum.CloneSet -> cloudCmdbApi.getEnvBaselineSpec(envStackId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME)
                ResourceObjectProtocolEnum.RollingSet -> cloudCmdbApi.getEnvBaselineSpec(envStackId, ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME)
                ResourceObjectProtocolEnum.ServerlessApp -> serverlessSpecService.getEnvBaselineJarUrl(envStackId)
                else -> {}
            }
            CheckBaselineResultResp(true)
        } catch (e: BizException) {
            log.error("ResourceObjectService,BizException,${e.errorCode}:${e.message}", e)
            CheckBaselineResultResp(false, e.message)
        }
    }

    /**
     * 根据六元组+环境匹配规则获取资源规格
     * 按照优先级匹配: vpa弹性 > 规则配置 > 扩容spec定义 > atom定义（待下线）
     * @return 资源规格
     */
    fun getResourceRequirementSpecWithExtra(resourceRequirementRequest: ResourceRequirementRequest): ResourceRequirementResp {
        val resourceSpecWithOrigin = getResourceBaselineSpec(resourceRequirementRequest)
        var cpuSpecOrigin = CpuSpecOrigin.NON_VPA
        val resourceSpec = resourceSpecWithOrigin.first
        val gpuCardModel = getGpuCardModel(resourceRequirementRequest.resourceGroup)
        return ResourceRequirementResp(
            ResourceRequest(
                resourceSpecFeatureService.convertComputeResourceSpecValue(resourceSpec.cpu)!!,
                resourceSpecFeatureService.convertStoreResourceSpecValue(resourceSpec.memory)!!,
                resourceSpecFeatureService.convertStoreResourceSpecValue(resourceSpec.disk)!!,
                resourceSpecFeatureService.convertComputeResourceSpecValue(resourceSpec.gpu ?.toNullIfBlank())
            ),
            ResourceLimit(
                resourceSpecFeatureService.convertComputeResourceSpecValue(resourceSpec.cpu)!!,
                resourceSpecFeatureService.convertStoreResourceSpecValue(resourceSpec.memory)!!,
                resourceSpecFeatureService.convertStoreResourceSpecValue(resourceSpec.disk)!!,
                resourceSpecFeatureService.convertComputeResourceSpecValue(resourceSpec.gpu ?.toNullIfBlank())
            ),
            resourceRequirementRequest,
            resourceSpecWithOrigin.second,
            cpuSpecOrigin,
            gpuCardModel,
        )
    }

    private fun getGpuCardModel(resourceGroup: String?): String? {
        val gpuCardModel = ((resourceGroup?.toNullIfBlank()?.run {
            getFeatureImportByResourceGroup(
                FeatureImportRequest(
                    resourceGroup = this,
                    featureKey = GPU_AFFINITY_SPEC_FEATURE_KEY,
                )
            )
        }?.formData?.get("gpu") as? Map<String, Any>?)?.get("models") as? List<String>?)?.firstOrNull()
        return gpuCardModel
    }

    fun getResourceBaselineSpec(resourceRequirementRequest: ResourceRequirementRequest): Pair<ResourceSpec, ResourceSpecOrigin> {
        var resourceSpecOrigin = ResourceSpecOrigin.RESOURCE_GROUP
        val resourceSpec = resourceRequirementRequest.resourceGroup ?.toNullIfBlank() ?.run {
            //优先按照分组获取规格配置
            resourceSpecFeatureService.getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, this)
        } ?: run {
            if (resourceRequirementRequest.stage == TEST_STAGE) {
                resourceSpecOrigin = ResourceSpecOrigin.DAILY
                appCenterApi.getTestingResourceSpec(resourceRequirementRequest.appName)

            } else {
                resourceSpecOrigin = ResourceSpecOrigin.APP
                //从迁移的元数据获取应用规格
                userLabelService.getAppResourceSpec(resourceRequirementRequest.appName)

            } ?: userLabelService.getAppResourceSpec(resourceRequirementRequest.appName)
        }
        return Pair(resourceSpec, resourceSpecOrigin)
    }

    /**
     * 批量查询分组规格，如果分组规格不存在返回应用规格
     */
    fun batchQueryResourceGroupBaselineSpec(appName: String, resourceGroupList: List<String>) : Map<String, ResourceSpec>{
        if(resourceGroupList.isEmpty()) {
            return mapOf()
        }
        if (resourceGroupList.size > BATCH_QUERY_RESOURCE_GROUP_MAX_SIZE) {
            throw BizException("批量查询resourceGroupList最大限制${BATCH_QUERY_RESOURCE_GROUP_MAX_SIZE}条")
        }
        val resourceGroupResourceSpecMap = mutableMapOf<String, ResourceSpec>()
        runBlocking {
            val jobs = mutableListOf<Job>()
            resourceGroupList.forEach {resourceGroup ->
                val job = launch(Dispatchers.Default) {
                    try{
                        resourceSpecFeatureService.getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup) ?.let {
                            resourceGroupResourceSpecMap[resourceGroup] = it
                        }
                    } catch (e: Exception) {
                        log.error("ResourceSpecFeatureService,getResourceSpecByMatchScope,error:${e.message},resourceGroup:${resourceGroup}", e)
                    }
                }
                jobs.add(job)
            }
            jobs.joinAll()
        }
        return resourceGroupResourceSpecMap
    }

    fun getResourceRequirementSpecBatchWithExtra(resourceRequirementRequestList: List<ResourceRequirementRequest>): List<ResourceRequirementResp> {
        if (resourceRequirementRequestList.size > commonProperties.firstOrNull(CommonProperties.BATCH_SIZE_LIMIT)
                ?.toInt() ?: CommonProperties.DEFAULT_BATCH_SIZE_LIMIT
        ) {
            throw BatchSizeException("batch size ${resourceRequirementRequestList.size} over limit")
        }
        return resourceRequirementRequestList.map {
            getResourceRequirementSpecWithExtra(it)
        }
    }

    fun getFeatureImportByResourceGroup(featureImport: FeatureImportRequest): ResourceObjectGetFeatureImportDO? {
        val resourceObjectFeatureImport = resourceSpecFeatureService.getFeatureImportByFeatureKeyAndResouceGroup(
            featureImport.featureKey,
            featureImport.resourceGroup
        )
        return resourceObjectFeatureImport?.let {
            ResourceObjectGetFeatureImportDO(
                id = it.id,
                resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                status = it.status,
                formData = it.paramMap?.let {
                    YamlUtils.load(it)
                },
                creator = it.creator,
                modifier = it.modifier,
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                isDeleted = it.isDeleted,
                externalId = featureImport.resourceGroup,
                externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                version = it.version,
            )
        }
    }

    fun getFeatureImportByResourceGroupBatch(featureImportResourceGroupBatchRequest: FeatureImportResourceGroupBatchRequest): List<ResourceObjectGetFeatureImportDO> {
        if (featureImportResourceGroupBatchRequest.resourceGroups.size > commonProperties.firstOrNull(CommonProperties.BATCH_SIZE_LIMIT)
                ?.toInt() ?: CommonProperties.DEFAULT_BATCH_SIZE_LIMIT
        ) {
            throw BatchSizeException("batch size ${featureImportResourceGroupBatchRequest.resourceGroups.size} over limit")
        }
        val resourceObjectFeatureImportList = resourceSpecFeatureService.getFeatureImportByFeatureKeyAndResouceGroups(
            featureImportResourceGroupBatchRequest.featureKey,
            featureImportResourceGroupBatchRequest.resourceGroups
        )
        return resourceObjectFeatureImportList.map {
            ResourceObjectGetFeatureImportDO(
                id = it.id,
                resourceObjectFeatureKey = it.resourceObjectFeatureKey,
                status = it.status,
                formData = it.paramMap?.let {
                    YamlUtils.load(it)
                },
                creator = it.creator,
                modifier = it.modifier,
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                isDeleted = it.isDeleted,
                externalId = it.externalId,
                externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
                version = it.version,
            )
        }
    }
}