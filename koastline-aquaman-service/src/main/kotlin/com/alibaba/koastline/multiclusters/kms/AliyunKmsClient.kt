package com.alibaba.koastline.multiclusters.kms

import com.alibaba.koastline.multiclusters.common.logger
import com.aliyun.kms20160120.Client
import com.aliyun.kms20160120.models.DecryptRequest
import com.aliyun.kms20160120.models.EncryptRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component


/**
 * <AUTHOR>
 */
@Component("aliyunKmsClient")
class AliyunKmsClient {

    @Autowired
    @Qualifier("kmsClient")
    lateinit var kmsClient: Client

    val log by logger()

    init {
        log.info("aliyun kms client starts")
    }

    fun encrypt(plainText: String): String {
        val base64Encoded = java.util.Base64.getEncoder().encodeToString(plainText.toByteArray())
        val encryptRequest = EncryptRequest().setKeyId(keyId).setPlaintext(base64Encoded)
        kmsClient.encrypt(encryptRequest).let {
            return it.getBody().ciphertextBlob
        }
    }

    fun decrypt(cipherText: String): String {
        val decryptRequest = DecryptRequest().setCiphertextBlob(cipherText)
        kmsClient.decrypt(decryptRequest).let {
            return it.body.plaintext
        }
    }

    companion object {
        const val keyId = "cdd4efd1-fd41-4d4e-8e09-db19921c64cf"
    }

}