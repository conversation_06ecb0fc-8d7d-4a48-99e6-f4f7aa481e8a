package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.NormandyException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.CrResourceObjectList
import com.alibaba.koastline.multiclusters.external.model.NormandyResponse
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class NormandyApi() {
    val log by logger()

    @Value("\${normandy.host}")
    lateinit var host: String


    @ExternalCall(SYS_CALLED)
    fun queryDataSetAndSecretCrList(
        appName: String,
        stackId: String,
        stackPKId: String?,
        clusterId: String,
        formatEnum: String,
    ): CrResourceObjectList {

        val url = "${host}/k8sobject/dataset/build-and-mount-cr-list"
        val params = mutableMapOf(
            "tenantId" to "aquaman",
            "appName" to appName,
            "empId" to "264071",
            "stackId" to stackId,
            "clusterId" to clusterId,
            "formatEnum" to formatEnum,
        )
        stackPKId?.let {
            params["stackPKId"] = stackPKId
        }

        val rs = HttpClientUtils.httpPost(url, JsonUtils.writeValueAsString(params), null)
        val normandyResponse =
            JsonUtils.readValue(rs, object : TypeReference<NormandyResponse<CrResourceObjectList>>() {})
        if (!normandyResponse.success) {
            val errMsg =
                "${NormandyApi.SYS_CALLED} queryDataSetAndSecretCrList failed, errMsg:${normandyResponse.message}"
            throw NormandyException(errMsg)
        }
        return normandyResponse.data
    }


    companion object {
        const val SYS_CALLED = "normandy"
    }
}