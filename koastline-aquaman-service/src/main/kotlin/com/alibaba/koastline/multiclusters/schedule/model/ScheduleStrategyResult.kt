package com.alibaba.koastline.multiclusters.schedule.model

import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.DistributionStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.NameSpaceSplitter
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.RootStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.PrefixUniqueKeyDistributionStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.UnionNameSpaceSplitter
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.buildStrategyTree


data class ScheduleStrategyResult(
    val apres: List<ApREStrategy>
) : DistributionStrategy {
    fun validate() {
        this.apres.forEach { it.validate() }
    }

    override fun getNodeWeight(): Int {
        return 1
    }

    override fun getNodeUniqueKey(): String {
        return RootStrategy
    }

    override fun getSubDistributionStrategy(): List<DistributionStrategy> {
        return apres
    }


    fun getStrategyTree(): PrefixUniqueKeyDistributionStrategy {
        return buildStrategyTree(this).also { it.validateUniqueKeys() }
    }
}

data class ApREStrategy(
    val site: String,
    val stage: String,
    val unit: String,
    val weight: Int,
    val resources: List<ResourceStrategy>
) : DistributionStrategy {
    fun uniqueKey(): String {
        return "${site}$NameSpaceSplitter${stage}$NameSpaceSplitter${unit}"
    }

    fun validate() {
        require(site.isNotBlank()) { "site is not blank" }
        require(stage.isNotBlank()) { "stage is not blank" }
        require(unit.isNotBlank()) { "unit is not blank" }
        resources.forEach { it.validate() }
    }

    override fun getNodeWeight(): Int {
        return this.weight
    }

    override fun getNodeUniqueKey(): String {
        return this.uniqueKey()
    }

    override fun getSubDistributionStrategy(): List<DistributionStrategy> {
        return this.resources
    }
}

data class ResourceStrategy(
    val clusters: List<ClusterWorkload>,
    val weight: Int
) : DistributionStrategy {

    fun validate() {
        clusters.forEach { it.validate() }
        require(clusters.map { it.clusterId }.size == clusters.size) {
            "clusterId must be unique"
        }
    }

    override fun getNodeWeight(): Int {
        return this.weight
    }

    override fun getNodeUniqueKey(): String {
        if (clusters.isEmpty()) {
            throw IllegalStateException("wrong resource strategy unique key generate")
        }
        val clustersIds = clusters.map { it.clusterId }.distinct()
        return UnionNameSpaceSplitter + clustersIds.joinToString(UnionNameSpaceSplitter) + UnionNameSpaceSplitter
    }

    override fun getSubDistributionStrategy(): List<DistributionStrategy> {
        return this.clusters
    }

}

data class ClusterWorkload(
    val clusterId: String,
    val clusterName: String,
    val runtimeId: String? = null,
    val weight: Int? = null
) : DistributionStrategy {
    private fun workloadSubUniqueKey(): String {
        return if (runtimeId != null)
            UnionNameSpaceSplitter + this.runtimeId + UnionNameSpaceSplitter + this.clusterId + UnionNameSpaceSplitter
        else
            UnionNameSpaceSplitter + this.clusterId + UnionNameSpaceSplitter
    }


    fun validate() {
        require(clusterId.isNotBlank()) { "clusterId cannot be blank" }
        require(clusterName.isNotBlank()) { "clusterName cannot be blank" }
    }

    override fun getNodeWeight(): Int {
        return weight ?: 1
    }

    override fun getNodeUniqueKey(): String {
        return workloadSubUniqueKey()
    }

    override fun getSubDistributionStrategy(): List<DistributionStrategy> {
        return emptyList()
    }
}