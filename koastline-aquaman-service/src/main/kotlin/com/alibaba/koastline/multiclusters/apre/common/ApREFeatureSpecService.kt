package com.alibaba.koastline.multiclusters.apre.common

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSourceEnum.DEFAULT
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREMergeFeatureSpecException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.env.ApREFeatureSpecRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApREFeatureSpec
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*

@Service
class ApREFeatureSpecService {

    @Autowired
    lateinit var apREDefaultFeatureService: ApREDefaultFeatureService
    @Autowired
    lateinit var apRELabelSpecRepo: ApREFeatureSpecRepo

    /**
     * 创建运行时环境特性规格，如果存在即更新
     */
    @Transactional
    fun createApREFeatureSpecUpdateWhileExist(reqDto: ApREFeatureSpecCreateReqDto): ApREFeatureSpecDO {
        val apREFeatureSpec = apRELabelSpecRepo.queryByApRELabelKey(reqDto.apRELabelKey!!).firstOrNull {
            it.specCode == reqDto.specCode && it.sourceType == reqDto.sourceType && it.sourceId == reqDto.sourceId
        } ?.let { originalApREFeatureSpec ->
            // 存在则更新
            val newApREFeatureSpec = ApREFeatureSpec(
                id = originalApREFeatureSpec.id,
                apRELabelKey = originalApREFeatureSpec.apRELabelKey,
                title = reqDto.title,
                specType = if (reqDto.specType.isBlank()) ApRELabelExt.DEFAULT_APRE_FEATURE_SPEC_TYPE_COMMON else reqDto.specType.trim(),
                specCode = reqDto.specCode,
                scope = reqDto.scope.name,
                status = reqDto.status.name,
                sourceType = reqDto.sourceType,
                sourceId = reqDto.sourceId,
                versionType = reqDto.versionType,
                versionId = reqDto.versionId,
                annotations = JsonUtils.writeValueAsString(reqDto.annotations),
                labels = JsonUtils.writeValueAsString(reqDto.labels),
                gmtCreate = originalApREFeatureSpec.gmtCreate,
                gmtModified = Date(Instant.now().toEpochMilli()),
                isDeleted = originalApREFeatureSpec.isDeleted
            )
            if (apRELabelSpecRepo.updateById(newApREFeatureSpec) == 0) throw ApREException("update runtime environment label spec causes exception,${newApREFeatureSpec}")
            newApREFeatureSpec
        } ?: let {
            // 不存在则创建
            val newApREFeatureSpec = ApREFeatureSpec(
                apRELabelKey = reqDto.apRELabelKey,
                title = reqDto.title,
                specType = if (reqDto.specType.isBlank()) ApRELabelExt.DEFAULT_APRE_FEATURE_SPEC_TYPE_COMMON else reqDto.specType.trim(),
                specCode = reqDto.specCode,
                scope = reqDto.scope.name,
                status = reqDto.status.name,
                sourceType = reqDto.sourceType,
                sourceId = reqDto.sourceId,
                versionType = reqDto.versionType,
                versionId = reqDto.versionId,
                annotations = JsonUtils.writeValueAsString(reqDto.annotations),
                labels = JsonUtils.writeValueAsString(reqDto.labels)
            )
            apRELabelSpecRepo.insert(newApREFeatureSpec).let {
                if (it == 0) throw ApREException("create runtime environment label spec causes exception,${newApREFeatureSpec}")
            }
            newApREFeatureSpec
        }
        return transformToCustomizedApREFeatureSpecDO(apREFeatureSpec)
    }

    /**
     * 根据特性标签查找在线特性规格
     */
    fun findOnlineApREFeatureSpecByLabel(apRELabelKey: String): List<ApREFeatureSpecDO> {
        val apREFeatureSpecDOs = mutableListOf<ApREFeatureSpecDO>()
        apRELabelSpecRepo.queryByApRELabelKeyAndStatus(apRELabelKey, ApREFeatureSpecStatusEnum.online.name).forEach {
            apREFeatureSpecDOs.add(
                transformToCustomizedApREFeatureSpecDO(it)
            )
        }
        return apREFeatureSpecDOs
    }

    fun batchQueryOnlineApREFeatureSpecByLabel(apRELabelKeyList: List<String>): List<ApREFeatureSpecDO> {
        apRELabelKeyList.ifEmpty { return emptyList() }
        return apRELabelSpecRepo.batchQueryByApRELabelKeyAndStatus(apRELabelKeyList, ApREFeatureSpecStatusEnum.online.name).map {
                transformToCustomizedApREFeatureSpecDO(it)
        }
    }

    fun findAllApREFeatureSpecByLabel(apRELabelKey: String): List<ApREFeatureSpecDO> {
        val apREFeatureSpecDOs = mutableListOf<ApREFeatureSpecDO>()
        apRELabelSpecRepo.queryByApRELabelKey(apRELabelKey).forEach {
            apREFeatureSpecDOs.add(
                transformToCustomizedApREFeatureSpecDO(it)
            )
        }
        return apREFeatureSpecDOs
    }

    /**
     * 根据特性标签查找特性规格，如果运行时环境未定义该特性标签的规格，则加载缺省规格
     */
    fun findOnlineApREFeatureSpecByLabelWithDefault(apRELabel: ApRELabel): List<ApREFeatureSpecDO> {
        findOnlineApREFeatureSpecByLabel(apRELabel.apRELabelKey).let {
            if (it.isNotEmpty()) {
                return it
            }
        }
        //附加缺省特性规格
        return findDefaultApREFeatureSpecByLabelValue(apRELabel.value)
    }

    fun findDefaultApREFeatureSpecByLabelValue(labelValue: String): List<ApREFeatureSpecDO> {
        return apREDefaultFeatureService.findApREDefaultFeatureDetail(labelValue)?.run {
            this.defaultFeatureSpecs?.map {
                transformToDefaultApREFeatureSpecDO(it)
            }
        } ?: emptyList()
    }

    /**
     * 标签以及spec合并
     */
    fun mergeApREFeatureSpecs(sourceApRELabel: ApRELabelDO, targetApRELabel: ApRELabelDO): ApRELabelDO {
        if (sourceApRELabel.apRELabelKey != targetApRELabel.apRELabelKey) {
            throw ApREMergeFeatureSpecException("it's different ApRE label key.")
        }
        if (sourceApRELabel.apREFeatureSpecs.isNullOrEmpty()) {
            return targetApRELabel
        }
        if (targetApRELabel.apREFeatureSpecs.isNullOrEmpty()) {
            return sourceApRELabel
        }
        val mergeApREFeatureSpecs = mutableListOf<ApREFeatureSpecDO>()
        mergeApREFeatureSpecs.addAll(sourceApRELabel.apREFeatureSpecs)
        targetApRELabel.apREFeatureSpecs.forEach { targetApREFeatureSpec ->
            sourceApRELabel.apREFeatureSpecs.firstOrNull { sourceApREFeatureSpec ->
                sourceApREFeatureSpec.specCode == targetApREFeatureSpec.specCode && sourceApREFeatureSpec.specType == targetApREFeatureSpec.specType
            } ?: let {
                mergeApREFeatureSpecs.add(targetApREFeatureSpec)
            }
        }
        return sourceApRELabel.copy(apREFeatureSpecs = mergeApREFeatureSpecs)
    }

    /**
     * ApREFeatureSpec -> ApREFeatureSpecDO
     */
    fun transformToCustomizedApREFeatureSpecDO(apREFeatureSpec: ApREFeatureSpec): ApREFeatureSpecDO {
        return ApREFeatureSpecDO(
            apREFeatureSpec.id,
            apREFeatureSpec.apRELabelKey,
            apREFeatureSpec.title,
            apREFeatureSpec.specType,
            apREFeatureSpec.specCode,
            apREFeatureSpec.scope,
            apREFeatureSpec.status,
            apREFeatureSpec.sourceType,
            apREFeatureSpec.sourceId,
            apREFeatureSpec.versionType,
            apREFeatureSpec.versionId,
            apREFeatureSpec.annotations,
            apREFeatureSpec.labels,
            apREFeatureSpec.gmtCreate,
            apREFeatureSpec.gmtModified,
            apREFeatureSpec.isDeleted,
        )
    }

    fun convertDefaultFeatureSpec(apREDefaultFeatureSpecDOs: List<ApREDefaultFeatureSpecDO>): List<ApREFeatureSpecDO> {
        val apREFeatureSpecDOs = mutableListOf<ApREFeatureSpecDO>()
        apREDefaultFeatureSpecDOs.mapTo(apREFeatureSpecDOs) { transformToDefaultApREFeatureSpecDO(it) }
        return apREFeatureSpecDOs
    }

    /**
     * apREDefaultFeatureSpecDO -> ApREDefaultFeatureSpecDO
     */
    fun transformToDefaultApREFeatureSpecDO(apREDefaultFeatureSpecDO: ApREDefaultFeatureSpecDO): ApREFeatureSpecDO {
        return ApREFeatureSpecDO(
            apREDefaultFeatureSpecDO.id,
            apREDefaultFeatureSpecDO.featureKey,
            apREDefaultFeatureSpecDO.title,
            apREDefaultFeatureSpecDO.specType,
            apREDefaultFeatureSpecDO.specCode,
            apREDefaultFeatureSpecDO.scope,
            apREDefaultFeatureSpecDO.status,
            apREDefaultFeatureSpecDO.annotations,
            null,
            apREDefaultFeatureSpecDO.gmtCreate,
            apREDefaultFeatureSpecDO.gmtModified,
            apREDefaultFeatureSpecDO.isDeleted,
            DEFAULT.name
        )
    }

    fun deleteByLabelKeyAndSpecCode(labelKey: String, specCode: String) {
        apRELabelSpecRepo.deleteByLabelKeyAndSpecCode(labelKey = labelKey, specCode = specCode)
    }
}