package com.alibaba.koastline.multiclusters.fed.model

import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import java.util.*

/**
 * service DO
 *
 * @property id
 * @property fedEnvName
 * @property region
 * @property site
 * @property fedClusterKey
 * @property status
 * @property tenantCluster
 * @property memberClusterList
 * @property creator
 * @property modifier
 * @property gmtCreate
 * @property gmtModified
 * @property isDeleted
 */
data class FedClusterDO(
    val id: Long,
    val fedEnvName: String,
    val region: String,
    val site: String? = null,
    val fedClusterKey: String,
    val status: String,
    val tenantCluster: String,
    val memberClusterList: List<String>,
    val isDeleted: String = IS_NOT_DELETED,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date,
    val gmtModified: Date
)

fun FedCluster.convertToFedClusterDO(
    tenantClusterName: String,
    memberClusterNameList: List<String>,
): FedClusterDO {
    return FedClusterDO(
        id = checkNotNull(id) {
            "id of fed cluster from db cannot be null "
        },
        fedEnvName = fedEnvName,
        region = region,
        site = site,
        fedClusterKey = fedClusterKey,
        status = status,
        tenantCluster = tenantClusterName,
        memberClusterList = memberClusterNameList,
        creator = creator,
        modifier = modifier,
        gmtCreate = gmtCreate,
        gmtModified = gmtModified,
        isDeleted = isDeleted,
    )
}
