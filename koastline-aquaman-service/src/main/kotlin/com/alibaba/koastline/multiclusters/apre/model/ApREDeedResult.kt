package com.alibaba.koastline.multiclusters.apre.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModelProperty

data class ApREDeedResult(
    val deedDO: ApREDeedDO,
    val matchDeclarations: List<MatchDeclaration>,
    val apREDeclarationPatchDatas: List<ApREDeclarationPatchDataDO> = emptyList()
)

data class ApREDeedResultVBeta(
    val key: String,
    val identityInfo: IdentityInfo,
    val declarations: MutableList<Declaration>,
    val matchDeclarations: List<MatchDeclaration>,
    val apREDeclarationPatchDatas: List<ApREDeclarationPatchDataDO>  = emptyList()
)

data class MixApREDeedResultVBeta(
    val identityInfo: IdentityInfo,
    val declarations: MutableList<Declaration>,
    val matchDeclarations: List<MatchDeclaration>
)

data class MatchDeclaration(
    val declarationId: String = "",
    val apres: List<ApREDO> = emptyList(),
    @ApiModelProperty("权重,默认：1")
    @JsonProperty("weight")
    val weight: Int = 1,
    @JsonIgnore
    val declaration: Declaration? = null,
) {
    /**
     * 验证声明[SRE补偿后结果]以及声明四元组不为空
     */
    fun checkAfterPatchDeclaration() {
        checkNotNull(this.declaration){"declaration不能为空."}
        checkNotNull(this.declaration.az) {"declaration.az不能为空."}
        checkNotNull(this.declaration.unit) {"declaration.unit不能为空."}
        checkNotNull(this.declaration.stage) {"declaration.stage不能为空."}
    }
}
