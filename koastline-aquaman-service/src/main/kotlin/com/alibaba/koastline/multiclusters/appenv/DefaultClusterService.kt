package com.alibaba.koastline.multiclusters.appenv

import com.alibaba.koastline.multiclusters.appenv.model.ClusterInstanceDto
import com.alibaba.koastline.multiclusters.appenv.model.ClusterMetaData
import com.alibaba.koastline.multiclusters.appenv.model.ClusterProfile
import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.appenv.model.GatewayConfig
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.appenv.params.ManagedClusterWithEnvironmentsTemplateSpec
import com.alibaba.koastline.multiclusters.common.exceptions.ApREBindingDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ClusterProfileNotFoundException
import com.alibaba.koastline.multiclusters.common.exceptions.ClusterProfileUniqueException
import com.alibaba.koastline.multiclusters.common.exceptions.GatewayCreationException
import com.alibaba.koastline.multiclusters.common.exceptions.GatewayParamsNotValidException
import com.alibaba.koastline.multiclusters.common.exceptions.ManagedClusterCreationException
import com.alibaba.koastline.multiclusters.common.exceptions.SystemComponentsCreationException
import com.alibaba.koastline.multiclusters.common.exceptions.SystemComponentsUpdateException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.dao.env.KManagedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.env.KoastlineClusterProfileRepo
import com.alibaba.koastline.multiclusters.data.dao.env.KoastlineGatewayRepo
import com.alibaba.koastline.multiclusters.data.dao.env.SystemComponentsRepo
import com.alibaba.koastline.multiclusters.data.utils.DataValidateUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.data.vo.env.KManagedClusterData
import com.alibaba.koastline.multiclusters.data.vo.env.KoastlineGatewayData
import com.alibaba.koastline.multiclusters.data.vo.env.SystemComponentsData
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*


/**
 * <AUTHOR>
 */
@Component
class DefaultClusterService @Autowired constructor(
    val objectMapper: ObjectMapper,
    val clusterProfileRepo: KoastlineClusterProfileRepo,
    val gatewayRepo: KoastlineGatewayRepo,
    val kManagedClusterRepo: KManagedClusterRepo,
    val systemComponentsRepo: SystemComponentsRepo,
) {
    val log by logger()

    companion object {
        const val QUOTA = "quota"
    }

    /**
     * koastline 模型中 注册native k8s cluster 及其相关所有信息
     */
    @Transactional
    fun registerClusterProfile(clusterProfile: ClusterProfile): Boolean {
        // insert a new environment
        val now = Date(Instant.now().toEpochMilli())

        val metaData = clusterProfile.clusterMetaData

        // 查询clusterId 是否已经存在 存在则抛出创建冲突
        clusterProfileRepo.findClusterProfileByClusterId(clusterProfile.clusterId)?.let {
            throw ClusterProfileUniqueException(clusterId = clusterProfile.clusterId)
        }

        if (metaData.clusterAnnotations.isNotEmpty() && metaData.clusterAnnotations[QUOTA] == null) {
            metaData.clusterAnnotations[QUOTA] = "false"
        }

        val newClusterProfile = ClusterProfileData(
            null,
            clusterProfile.clusterId,
            clusterProfile.clusterName,
            clusterProfile.clusterExternalId,
            clusterProfile.clusterProvider,
            clusterProfile.clusterType,
            metaData.region,
            metaData.site,
            objectMapper.writeValueAsString(metaData.clusterAnnotations),
            clusterProfile.useType,
            now,
            now,
            IS_NOT_DELETED,
            clusterProfile.status,
            "SYSTEM_ADMIN",
            "SYSTEM_ADMIN",
        )

        clusterProfileRepo.insertClusterProfile(newClusterProfile).apply {
            if (this == 0) return false
        }
        return true
    }

    /**
     * koastline 模型中 修改native k8s cluster 及其相关所有信息
     */
    @Transactional
    fun updateClusterProfile(clusterProfile: ClusterProfile): Boolean {
        val now = Date(Instant.now().toEpochMilli())

        clusterProfileRepo.findClusterProfileByClusterId(clusterProfile.clusterId)?.let { item ->
            val metaData = clusterProfile.clusterMetaData
            ClusterProfileData(
                null,
                item.clusterId,
                clusterProfile.clusterName,
                clusterProfile.clusterExternalId,
                clusterProfile.clusterProvider,
                clusterProfile.clusterType,
                metaData.region,
                metaData.site,
                objectMapper.writeValueAsString(metaData.clusterAnnotations),
                clusterProfile.useType,
                item.gmtCreate,
                now,
                IS_NOT_DELETED,
                clusterProfile.status,
                "SYSTEM_ADMIN",
                "SYSTEM_ADMIN",
            ).let {
                clusterProfileRepo.updateClusterProfile(it).run {
                    if (this == 0) throw ClusterProfileNotFoundException(it.id)
                }
            }
            return true
        }
        return false
    }

    /**
     * 简化集群ClusterProfileData核心字段录入 替代 koastline 集群信息录入接口
     */
    fun registerSimpleClusterProfileData(clusterInstanceDto: ClusterInstanceDto): Boolean {
        val now = Date(Instant.now().toEpochMilli())
        clusterInstanceDto.validate()

        // 验证集群id和name唯一性
        clusterProfileRepo.findClusterProfileByClusterId(clusterInstanceDto.clusterId)?.let {
            throw ClusterProfileUniqueException(clusterId = clusterInstanceDto.clusterId)
        }
        clusterProfileRepo.findClusterProfileByClusterName(clusterInstanceDto.clusterName)?.let {
            throw ClusterProfileUniqueException(clusterId = it.clusterId)
        }

        val newClusterProfile = ClusterProfileData(
            null,
            clusterId = clusterInstanceDto.clusterId,
            clusterName = clusterInstanceDto.clusterName,
            clusterProvider = clusterInstanceDto.clusterProvider,
            clusterIdExternal = "",
            clusterType = clusterInstanceDto.clusterType,
            region = clusterInstanceDto.region,
            site = clusterInstanceDto.site ?: "",
            annotations = clusterInstanceDto.annotations,
            useType = clusterInstanceDto.useType,
            gmtModified = now,
            gmtCreate = now,
            isDeleted = IS_NOT_DELETED,
            status = clusterInstanceDto.status,
            creator = clusterInstanceDto.operator,
            modifier = clusterInstanceDto.operator
        )

        clusterProfileRepo.insertClusterProfile(newClusterProfile).apply {
            if (this == 0) throw ApREBindingDataException("create ApRE binding data with error")
        }
        return true
    }

    /**
     * 简化集群ClusterProfileData核心字段修改 以替代 koastline 集群信息修改接口
     */
    fun updateSimpleClusterProfileData(clusterInstanceDto: ClusterInstanceDto): Boolean {
        val now = Date(Instant.now().toEpochMilli())
        clusterInstanceDto.validate()
        clusterProfileRepo.findClusterProfileByClusterId(clusterInstanceDto.clusterId)?.let { clusterProfileData ->
            clusterProfileData.copy(
                id = null,
                clusterName = clusterInstanceDto.clusterName,
                clusterProvider = clusterInstanceDto.clusterProvider,
                clusterType = clusterInstanceDto.clusterType,
                region = clusterInstanceDto.region,
                site = clusterInstanceDto.site ?: "",
                annotations = clusterInstanceDto.annotations,
                useType = clusterInstanceDto.useType,
                status = clusterInstanceDto.status,
                gmtModified = now,
                isDeleted = IS_NOT_DELETED,
                modifier = clusterInstanceDto.operator
            ).let {
                clusterProfileRepo.updateClusterProfile(it).run {
                    if (this == 0) throw ClusterProfileNotFoundException(it.id)
                }
            }
            return true
        }
        return false
    }

    fun getSimpleClusterProfileDataByClusterId(clusterId: String): ClusterProfileData {
        return clusterProfileRepo.findClusterProfileByClusterId(clusterId)
            ?: throw ClusterProfileNotFoundException(clusterId)
    }

    fun listSimpleClusterProfileDataByClusterIds(clusterIdList: List<String>): List<ClusterProfileData> {
        if(clusterIdList.isEmpty()){
            return emptyList()
        }
        return clusterProfileRepo.listClusterProfileByClusterIds(clusterIdList)
    }

    fun getSimpleClusterProfileDataByClusterName(clusterName: String): ClusterProfileData {
        return clusterProfileRepo.findClusterProfileByClusterName(clusterName)
            ?: throw ClusterProfileNotFoundException(clusterName)
    }

    fun deleteClusterProfile(clusterId: String): Boolean {
        clusterProfileRepo.deleteClusterProfileByClusterId(clusterId).apply {
            if (this == 0) return false
        }
        return true
    }

    @Transactional
    fun createManagedCluster(
        clusterId: String,
        systemComponentNamespace: String,
        clusterMetaData: ClusterMetaData
    ): ClusterProfile? {
        // check if the native k8s cluster exist or not
        val clusterNative = getSimpleClusterProfileDataByClusterId(clusterId)

        val gatewayConfig = clusterMetaData.gatewayConfigs
        if (gatewayConfig.size != 1) throw GatewayParamsNotValidException()

        val now = Date(Instant.now().toEpochMilli())
        val managedClusterKey = clusterEnvKeyGenerator()
        // check if gateway configs are provided
        log.info("gateway config $gatewayConfig")
        val gatewayId = gatewayConfig[0]?.let { item ->
            KoastlineGatewayData(
                null,
                managedClusterKey,
                item.k8sNamespace!!,
                item.host,
                item.port,
                item.schema,
                now,
                now
            ).let {
                gatewayRepo.insertGateway(it).apply {
                    if (this == 0) throw GatewayCreationException(this.toString())
                }
                gatewayRepo.findGatewayByKManagedClusterKey(it.managedClusterKey)?.id
            }
        } ?: throw GatewayCreationException(gatewayConfig.toString())

        log.info("gateway id $gatewayId")

        // insert into system component config
        systemComponentsRepo.insertSystemComponents(
            SystemComponentsData(
                null,
                managedClusterKey,
                objectMapper.writeValueAsString(gatewayConfig[0]!!.annotations),
                now,
                now
            )
        ).apply {
            if (this == 0) throw SystemComponentsCreationException()
        }
        val systemComponentsData = systemComponentsRepo.querySystemComponentsByClusterKey(managedClusterKey)
            ?: throw SystemComponentsCreationException()

        KManagedClusterData(
            null,
            managedClusterKey,
            clusterNative.id,
            gatewayId,
            systemComponentNamespace,
            systemComponentsData.id!!.toLong(),
            clusterMetaData.region,
            objectMapper.writeValueAsString(clusterMetaData.clusterLabels),
            "created",
            now,
            now
        ).apply {
            kManagedClusterRepo.createKManagedCluster(this).apply {
                if (this == 0) throw ManagedClusterCreationException()
            }
        }

        return ClusterProfile(
            clusterId,
            clusterNative.clusterName,
            clusterNative.clusterProvider,
            clusterNative.clusterType,
            clusterNative.clusterIdExternal,
            clusterMetaData
        )
    }

    /**
     * 创建kManageCluster 简化后的
     */
    fun createSimpleKManageCluster(region: String, status: String): KManagedClusterData {
        val now = Date(Instant.now().toEpochMilli())
        val managedClusterKey = clusterEnvKeyGenerator()
        val simpleKManageCluster = KManagedClusterData.createSimplifyKManageCluster(
            region = region,
            status = status,
            gmtCreate = now,
            gmtModified = now,
            managedClusterKey = managedClusterKey
        ).apply {
            kManagedClusterRepo.createKManagedCluster(this).apply {
                if (this == 0) throw ManagedClusterCreationException()
            }
        }
        return simpleKManageCluster
    }

    /**
     * 根据managerClusterKey唯一主键进行删除
     *
     * @param managerClusterKey
     */
    fun deleteSimpleKManageCluster(managerClusterKey: String){
        kManagedClusterRepo.deleteKManagedClusterByKey(managerClusterKey)
    }

    fun updateManagedClusterSystemAnnotationsByClusterKey(
        managedClusterKey: String,
        annotations: MutableMap<String, String>
    ): Boolean {
        val now = Date(Instant.now().toEpochMilli())
        log.info("start update managed cluster system config with cluster key $managedClusterKey, and annotations $annotations")
        systemComponentsRepo.querySystemComponentsByClusterKey(managedClusterKey)?.let {
            systemComponentsRepo.updateSystemComponentsByClusterKey(
                managedClusterKey,
                objectMapper.writeValueAsString(annotations),
                now
            ).run {
                if (this == 0) throw SystemComponentsUpdateException()
            }
            return true
        }
        return false
    }

    fun queryManagedClusterByManagedClusterKey(managedClusterKey: String): ClusterProfile? {
        kManagedClusterRepo.findKManagedClusterByManagedClusterKey(managedClusterKey)?.let { managedCluster ->
            val clusterData = clusterProfileRepo.findClusterProfileById(managedCluster.clusterProfileId!!) ?: let {
                log.info("queryManagedClusterByManagedClusterKey[managedClusterKey:${managedClusterKey}], no cluster found")
                return null
            }
            val gatewayConfigs = arrayListOf<GatewayConfig>()
            gatewayRepo.findGatewayByGatewayId(managedCluster.gatewayId)?.let {
                val systemComponentsData = systemComponentsRepo.querySystemComponentsByClusterKey(it.managedClusterKey)
                gatewayConfigs.add(
                    GatewayConfig(
                        it.protocol!!,
                        it.host,
                        it.port,
                        it.namespace,
                        objectMapper.readValue(systemComponentsData!!.annotations)
                    )
                )
            }
            return clusterData.toClusterMetaData(objectMapper, gatewayConfigs).apply {
                log.info("queryManagedClusterByManagedClusterKey[managedClusterKey:${managedClusterKey}], rs:${this}")
            }
        }
        log.info("queryManagedClusterByManagedClusterKey[managedClusterKey:${managedClusterKey}], no kManaged found")
        return null
    }

    fun clusterEnvKeyGenerator(length: Int = 16): String {
        return KeyGenerator.generateAlphanumericKey(length)
    }

    fun listClustersByProperties(
        clusterId: String?, clusterName: String?, clusterProvider: String?,
        clusterType: String?, region: String?, status: String?,
        pageSize: Int, pageNumber: Int
    ): PageData<ClusterProfileData> {
        val queryProperties = listOf(
            clusterId, clusterName, clusterProvider, clusterType, region, status
        )
        check(DataValidateUtils.hasNotNullProperty(queryProperties)) { "list filter conditions should be more than 0 conditions at least!" }

        val page: Page<ClusterProfileData> = PageHelper.startPage<ClusterProfileData>(pageNumber, pageSize, "gmt_modified DESC")
            .doSelectPage {
                clusterProfileRepo.findClusterProfilesByProperties(
                    clusterId = clusterId, clusterName = clusterName, clusterProvider = clusterProvider,
                    clusterType = clusterType, region = region, status = status
                )
            }
        return PageData.transformFrom(page)
    }

    /**
     * 按照clusterNameList查询ClusterProfileData
     *
     * @param clusterNameList
     * @return
     */
    fun listClusterByClusterNameList(clusterNameList: List<String>): List<ClusterProfileData> {
        if (clusterNameList.isEmpty()){
            return emptyList()
        }
        return clusterProfileRepo.listByClusterNameList(
            clusterNameList = clusterNameList
        )
    }

    /**
     * 按照clusterNameList查询ClusterProfileData
     *
     * @param clusterNameList
     * @return
     */
    fun listClusterByClusterIdList(clusterIdList: List<String>): List<ClusterProfileData> {
        if (clusterIdList.isEmpty()){
            return emptyList()
        }
        return clusterProfileRepo.listByClusterIdList(
            clusterIdList = clusterIdList
        )
    }

    @Transactional
    fun registerManagedClusterWithEnvironmentsFromTemplate(managedClusterWithEnvironmentsTemplateSpec: ManagedClusterWithEnvironmentsTemplateSpec): List<ClusterEnvironment> {
        TODO("TO BE IMPLEMENTED")
    }
}

fun ClusterProfileData.toClusterMetaData(
    objectMapper: ObjectMapper,
    gatewayConfigs: List<GatewayConfig?>
): ClusterProfile {
    val annotation = annotations
        ?.let { objectMapper.readValue<MutableMap<String, String>>(it) }
        ?: mutableMapOf()

    return ClusterProfile(
        this.clusterId,
        this.clusterName,
        this.clusterProvider,
        this.clusterType,
        this.clusterIdExternal,
        ClusterMetaData(
            gatewayConfigs,
            this.region,
            this.site,
            annotation,
            emptyMap()
        )
    )
}