package com.alibaba.koastline.multiclusters.schedule.model

data class AppOfRuntimeBaseResult (
    val runtimeBaseAppName: String,
    val runtimeBaseEnvStackId: String? = null,
    val runtimeBaseResourceGroup: String? = null,
    val appResourceList: List<AppResource> = emptyList()
)

data class AppResource (
    val appName: String,
    val totalResourceNum: Int,
    val appResourceStatusList: List<AppResourceStatus> = emptyList()
)

data class AppResourceStatus (
    val status: String,
    val num: Int
)

