package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.CallExternalSysException
import com.alibaba.koastline.multiclusters.common.exceptions.CmdbException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.checkNotEmpty
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.*
import com.alibaba.koastline.multiclusters.external.params.CmdbBoxType
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.*


@Component
class CloudCmdbApi(val objectMapper: ObjectMapper) {
    @Value("\${kl.host}")
    lateinit var host: String
    @Value("\${kl.account}")
    lateinit var account: String
    @Value("\${kl.access.key}")
    lateinit var accessKey: String

    @ExternalCall(SYS_CALLED)
    fun getBaseline(envStackId: String): CmdbStack {
        val respStr = HttpClientUtils.httpGet("$host$URL_GET_BASELINE",
            mapOf(
                "userId" to account,
                "apiVersion" to "1",
                "stackCategoryId" to envStackId
            )
        )
        val cmdbStackList = getPageRespData(objectMapper.readValue<CmdbCloudPageResp<CmdbStack>>(respStr)).list
        if (cmdbStackList.isNullOrEmpty()) {
            throw CmdbException("there is no baseline found for envStackId:$envStackId")
        }
        return cmdbStackList[0]
    }

    @ExternalCall(SYS_CALLED)
    fun getStackWithBox(stackPkId: String): CmdbStack {
        val respStr = HttpClientUtils.httpGet("$host$URL_GET_STACK_WITH_BOX_INFO",
            mapOf(
                "userId" to account,
                "apiVersion" to "1",
                "id" to stackPkId
            )
        )
        val cmdbStack = getRespData<CmdbStack>(objectMapper.readValue(respStr))
        if (cmdbStack.boxList.isNullOrEmpty()) {
            throw CmdbException("there is no box found for stackPkId:$stackPkId")
        }
        return cmdbStack
    }

    @ExternalCall(SYS_CALLED)
    fun getBoxAllInfo(boxId: String): CmdbBox {
        val respStr = HttpClientUtils.httpGet("$host$URL_GET_BOX_INFO",
            mapOf(
                "userId" to account,
                "apiVersion" to "1",
                "id" to boxId
            )
        )
        return getRespData(objectMapper.readValue(respStr))
    }

    @ExternalCall(SYS_CALLED)
    fun getStackConfig(stackId: String): CmdbStackConfig {
        val params = getAuthParams().toMutableMap().apply {
            this["stackId"] = stackId
        }
        val respStr = HttpClientUtils.httpGet("$host$URL_GET_STACK_CONFIG", params)
        return getRespData(objectMapper.readValue(respStr))
    }

    fun getIsolationEnvVars(stackId: String): Map<String, String> {
        val cmdbStackConfig = getStackConfig(stackId)
        val stackVariableList = objectMapper.readValue<List<StackVariable>>(cmdbStackConfig.stackVariable)
        val envVars = mutableMapOf<String, String>()
        stackVariableList.firstOrNull {
            it.name == "enableScmHook" && it.value == "true"
        } ?.let {
            envVars[SCM_HOOK_KEY] = SCM_HOOK_ENABLE_VALUE
        } ?:let {
            envVars[SCM_HOOK_KEY] = SCM_HOOK_DISABLE_VALUE
        }
        cmdbStackConfig.attribute.values.forEach { attributeList ->
            attributeList.firstOrNull { attribute ->
                attribute.name == "envSign" && !attribute.value.isNullOrBlank()
            } ?.let {
                envVars["ali_env_sign"] = it.value
            }
            attributeList.firstOrNull { attribute ->
                attribute.name == "envGroup" && !attribute.value.isNullOrBlank()
            } ?.let {
                envVars["ali_env_group"] = it.value
            }
            attributeList.firstOrNull { attribute ->
                attribute.name == "appDeployType" && !attribute.value.isNullOrBlank()
            } ?.let {
                envVars["ali_deploy_type"] = it.value
            }
        }
        return envVars
    }

    /**
     * 根据环境ID获取环境基线Yaml Spec
     */
    @ExternalCall(SYS_CALLED)
    fun getEnvBaselineSpec(envStackId: String, attributeName: String): String {
        return getEnvBaselineSpecByStackPKId(getBaseline(envStackId).id, attributeName)
    }

    /**
     * 根据基线stackPkId获取环境基线Yaml Spec
     */
    @ExternalCall(SYS_CALLED)
    fun getEnvBaselineSpecByStackPKId(stackPkId: String, attributeName: String): String {
        val serviceList = getBaselineServiceList(
            getStackWithBox(stackPkId).boxList!![0].id
        )
        val cmdbService = serviceList.firstOrNull{
            CmdbBoxType.ALIDOCKER.value == it.type
        } ?: throw ResourceObjectException("当前环境版本StackPkId[$stackPkId] 未找到版本Service,请尝试先针对该环境重新部署一次.")
        return cmdbService.attributeList.firstOrNull {
            attributeName == it.name
        } ?.value ?.toNullIfBlank() ?:let {
            throw ResourceObjectException("当前环境版本StackPkId[$stackPkId] 未找到版本Attribute,请尝试先针对该环境重新部署一次.")
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getBindingServerlessBaseAppInfoByEnvStackId(envStackId: String): ServerlessBaseAppInfo? {
        return getBaseline(envStackId).run {
            getBindingServerlessBaseAppInfoByStackPKId(this.id)
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getBindingServerlessBaseAppInfoByStackPKId(stackPkId: String): ServerlessBaseAppInfo? {
        val cmdbBaselineBox = getBoxAllInfo(
            checkNotEmpty(getStackWithBox(stackPkId).boxList){"环境基线版本stackPkId:${stackPkId}, boxList不能为空"} [0].id
        )
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw CmdbException("环境基线版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少services定义.")
        }
        val cmdbService = serviceList.firstOrNull { service ->
            CmdbBoxType.SERVERLESS_APP.value == service.type
        } ?: throw CmdbException("环境基线版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp service定义.")
        val serverlessBaseAppName = cmdbService.attributeList.firstOrNull {
            "runtimeAppName" == it.name
        } ?.value
        val serverlessBaseEnvStackId = cmdbService.attributeList.firstOrNull {
            "runtimeEnvStackId" == it.name
        } ?.value
        if (serverlessBaseAppName.isNullOrBlank() || serverlessBaseEnvStackId.isNullOrBlank()) {
            return null
        }
        return ServerlessBaseAppInfo(
            baseAppName = serverlessBaseAppName,
            baseEnvStackId = serverlessBaseEnvStackId
        )
    }

    @ExternalCall(SYS_CALLED)
    private fun getBaselineServiceList(stackPkId: String): List<CmdbService> {
        val cmdbBaselineBox = getBoxAllInfo(stackPkId)
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw ResourceObjectException("当前环境版本StackPkId[${stackPkId}]未找到协议版本,请尝试先针对该环境重新部署一次.")
        }
        return serviceList
    }

    private fun <T> getRespData(resp: CmdbCloudResp<T>): T {
        if (!resp.success) {
            throw CallExternalSysException(SYS_CALLED, resp.msg, resp.code)
        }
        return checkNotNull(resp.data) {"缺少返回值：${resp}"}
    }

    private fun <T> getPageRespData(resp: CmdbCloudPageResp<T>): PageResult<T> {
        if (!resp.success) {
            throw CallExternalSysException(SYS_CALLED, resp.msg, resp.code)
        }
        return resp.data!!
    }

    private fun getAuthParams(): Map<String, String> {
        return mapOf(
            "key" to account,
            "token" to accessKey,
            "userId" to  account,
            "_aop_timestamp" to Date(Instant.now().toEpochMilli()).toString()
        )
    }

    companion object {
        const val URL_GET_BASELINE = "/stack?action=getBaseline"
        const val URL_GET_STACK_WITH_BOX_INFO = "/stack?action=queryWithBoxInfo"
        const val URL_GET_BOX_INFO = "/boxNew?action=queryAllInfo"
        const val URL_GET_STACK_CONFIG = "/openApi/stack/queryAppConfig";
        const val SYS_CALLED = "cloud-cmdb-new"

        const val SCM_HOOK_KEY = "exec_scm_hook"
        const val SCM_HOOK_ENABLE_VALUE = "yes"
        const val SCM_HOOK_DISABLE_VALUE = "no"
    }
}