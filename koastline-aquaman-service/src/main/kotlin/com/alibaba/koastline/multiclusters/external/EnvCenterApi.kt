package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.EnvCenterException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.EnvCenterResp
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * date 2025/3/25 17:18
 */
@Component
class EnvCenterApi(
    @Value("\${env.center.host}")
    val envCenterHost: String
) {

    @ExternalCall(SYS_CALLED)
    fun queryDiamondSpeIps(appName: String, paramUrl: String): List<String> {
        val result = HttpClientUtils.httpGet(
            url = "$envCenterHost/service/openapi/v1/spe/resource/queryDiamondSpeIps",
            params = mapOf(
                "appName" to appName,
                "paramUrl" to paramUrl
            )
        )
        val resp = JsonUtils.readValue(result, object : TypeReference<EnvCenterResp<List<String>>>() {})
        if (!resp.successful) {
            throw EnvCenterException("queryDiamondSpeIps failed, appName: $appName, errorMsg:${resp.errorMsg}")
        }
        return resp.`object` ?: listOf()
    }

    companion object {
        private const val SYS_CALLED= "ENV_CENTER"
    }
}