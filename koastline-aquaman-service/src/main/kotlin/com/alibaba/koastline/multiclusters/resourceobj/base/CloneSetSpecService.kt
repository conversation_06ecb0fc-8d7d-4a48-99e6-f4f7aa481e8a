package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.cse.models.v1alpha1.cloneset.CloneSet
import com.alibaba.cse.models.v1alpha1.cloneset.CloneSetSpec
import com.alibaba.cse.models.v1alpha1.cloneset.CloneSetStatus
import com.alibaba.extensions.safeToLong
import com.alibaba.koastline.multiclusters.common.exceptions.ThreeWayMergeDiffException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectPatchService
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.HOST_NAME_TEMP
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.LAST_POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.NORMANDY
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.AbstractWorkloadSpecService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.google.gson.Gson
import io.kubernetes.client.openapi.models.*
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * CloneSet服务
 */
@Component("CloneSetSpecService")
class CloneSetSpecService: AbstractWorkloadSpecService(), InitializingBean {
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var workloadSpecFactory: WorkloadSpecFactory
    @Autowired
    lateinit var resourceObjectPatchService: ResourceObjectPatchService
    @Autowired
    lateinit var baseSpecService: BaseSpecService

    val gson = Gson()

    override fun afterPropertiesSet() {
        workloadSpecFactory.registryWorkloadSpecFacade(ResourceObjectProtocolEnum.CloneSet, this)
    }

    override fun checkSpec(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        when(resourceObjectFormatEnum) {
            ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                JsonUtils.writeValueAsString(YamlUtils.load(resourceObjectSpecStr)), CloneSet::class.java)

            else -> JsonUtils.gsonReadValue(resourceObjectSpecStr, CloneSet::class.java)
        }
    }

    override fun getBaseSpec(context: WorkloadSpecContext): Map<String, Any> {
        return cloudCmdbApi.getEnvBaselineSpec(context.envStackId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val cloneSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(YamlUtils.load(this)), CloneSet::class.java).run {
                // 使用五元组作为MatchLabels
                this.copy(
                    spec = spec.copy(selector = getCloneSetSelector(context.workloadMetadataConstraint))
                )
            }
            cloneSet.metadata.namespace = context.workloadMetadataConstraint.namespace
            cloneSet.metadata.name = baseSpecService.buildWorkloadName(context.workloadMetadataConstraint.appName)
            fillStackPkID(cloneSet, checkNotNull(cloudCmdbApi.getBaseline(context.envStackId).id){"未找到环境[envStackId:${context.envStackId}]对应的版本基线ID。"})
            patchAffinityAndTolerations(cloneSet, context.workloadMetadataConstraint)
            patchMetadata(cloneSet, context.workloadMetadataConstraint)
            patchPodMetadata(cloneSet.spec.template, context.workloadMetadataConstraint)
            removePodTemplateLabelOfObjectId(cloneSet.spec.template)
            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(cloneSet))
        }
    }

    override fun postModifyBaseSpec(
        resourceObjectSpec: Map<String, Any>,
        context: WorkloadSpecContext
    ): Map<String, Any> {
        return resourceObjectSpec
    }

    override fun getDeploySpec(stackPkId: String, currentResourceObjectSpecStr: String?,
                               currentResourceObjectFormatEnum: ResourceObjectFormatEnum, workloadMetadataConstraint: WorkloadMetadataConstraint): Map<String, Any> {
        checkNotNull(currentResourceObjectSpecStr)
        return cloudCmdbApi.getEnvBaselineSpecByStackPKId(stackPkId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val currentCloneSet = when(currentResourceObjectFormatEnum) {
                ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                    JsonUtils.writeValueAsString(YamlUtils.load(currentResourceObjectSpecStr)), CloneSet::class.java)

                else -> JsonUtils.gsonReadValue(currentResourceObjectSpecStr, CloneSet::class.java)
            }
            val cloneSet = copyFromCurrentWorkload(
                cloneSet = JsonUtils.gsonReadValue(
                    JsonUtils.writeValueAsString(YamlUtils.load(this)),
                    CloneSet::class.java
                ),
                currentCloneSet = currentCloneSet
            )
            fillStackPkID(cloneSet, stackPkId)
            patchAffinityAndTolerations(cloneSet, workloadMetadataConstraint)
            patchMetadata(cloneSet, workloadMetadataConstraint)
            patchPodMetadata(cloneSet.spec.template, workloadMetadataConstraint)

            processDeploySpecObjectIdLabel(cloneSet)

            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(cloneSet))
        }
    }

    /**
     * 如果selector为五元组，去除objectId
     */
    private fun processDeploySpecObjectIdLabel(cloneSet: CloneSet) {
        cloneSet.spec.selector ?.matchLabels ?.let {
            if (!it.containsKey(TEMPLATE_METADATA_LABEL_OBJECT_ID)) {
                removePodTemplateLabelOfObjectId(cloneSet.spec.template)
            }
        }
    }

    private fun copyFromCurrentWorkload(cloneSet: CloneSet, currentCloneSet: CloneSet): CloneSet {
        cloneSet.metadata.namespace = currentCloneSet.metadata.namespace
        cloneSet.metadata.name = currentCloneSet.metadata.name
        cloneSet.metadata.generation = currentCloneSet.metadata.generation
        cloneSet.metadata.resourceVersion = currentCloneSet.metadata.resourceVersion

        //复制运维操作字段
        currentCloneSet.metadata.labels?.get(METADATA_LABEL_NORMANDY_ORDER_ID) ?.let { labelOfOrderId ->
            cloneSet.metadata.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.metadata.labels,
                key = METADATA_LABEL_NORMANDY_ORDER_ID,
                value = labelOfOrderId
            )
        }
        currentCloneSet.metadata.labels?.get(POD_TEMPLATE_HASH)?.let { podTemplateHash ->
            cloneSet.metadata.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.metadata.labels,
                key = POD_TEMPLATE_HASH,
                value = podTemplateHash
            )
        }
        currentCloneSet.metadata.labels?.get(LAST_POD_TEMPLATE_HASH)?.let { lastPodTemplateHash ->
            cloneSet.metadata.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.metadata.labels,
                key = LAST_POD_TEMPLATE_HASH,
                value = lastPodTemplateHash
            )
        }
        currentCloneSet.spec.template.metadata?.labels?.get(QUOTA_NAME)
            ?.let { quotaName ->
                cloneSet.spec.template.metadata!!.labels = copyMapValueFromCurrentWorkload(
                    target = cloneSet.spec.template.metadata!!.labels,
                    key = QUOTA_NAME,
                    value = quotaName
                )
            }
        currentCloneSet.spec.template.metadata?.labels?.get(QUOTA_RESOURCE_ACCOUNT_NAME)
            ?.let { quotaAccount ->
                cloneSet.spec.template.metadata!!.labels = copyMapValueFromCurrentWorkload(
                    target = cloneSet.spec.template.metadata!!.labels,
                    key = QUOTA_RESOURCE_ACCOUNT_NAME,
                    value = quotaAccount
                )
            }
        currentCloneSet.spec.template.metadata?.annotations?.get(LINKED_QUOTA_NAME)
            ?.let { linkedQuotaName ->
                cloneSet.spec.template.metadata!!.annotations = copyMapValueFromCurrentWorkload(
                    target = cloneSet.spec.template.metadata!!.annotations,
                    key = LINKED_QUOTA_NAME,
                    value = linkedQuotaName
                )
            }
        currentCloneSet.spec.template.metadata ?.labels ?.get(TEMPLATE_METADATA_LABEL_NORMANDY_STACK_ID) ?.let { labelOfStackId ->
            cloneSet.spec.template.metadata!!.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.spec.template.metadata!!.labels,
                key = TEMPLATE_METADATA_LABEL_NORMANDY_STACK_ID,
                value = labelOfStackId
            )
        }
        currentCloneSet.spec.template.metadata ?.labels ?.get(TEMPLATE_METADATA_LABEL_NORMANDY_ORDER_ID) ?.let { labelOfOrderId ->
            cloneSet.spec.template.metadata!!.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.spec.template.metadata!!.labels,
                key = TEMPLATE_METADATA_LABEL_NORMANDY_ORDER_ID,
                value = labelOfOrderId
            )
        }
        // 复制pod template object_id label(如果存在)
        currentCloneSet.spec.template.metadata ?.labels ?.get(TEMPLATE_METADATA_LABEL_OBJECT_ID) ?.let { labelOfObjectId ->
            cloneSet.spec.template.metadata!!.labels = copyMapValueFromCurrentWorkload(
                target = cloneSet.spec.template.metadata!!.labels,
                key = TEMPLATE_METADATA_LABEL_OBJECT_ID,
                value = labelOfObjectId
            )
        }

        return cloneSet.copy(spec = cloneSet.spec.copy(
            replicas = currentCloneSet.spec.replicas,
            updateStrategy = currentCloneSet.spec.updateStrategy,
            //复制运维操作字段
            scaleStrategy = currentCloneSet.spec.scaleStrategy,
            selector = currentCloneSet.spec.selector
        ),
            status = cloneSet.status ?: CloneSetStatus()
        )
    }

    private fun copyMapValueFromCurrentWorkload(target: Map<String, String>?, key: String, value: String): Map<String, String>? {
        return target ?.toMutableMap() ?.apply {
            this[key] = value
        } ?: mapOf(
            key to value
        )
    }

    private fun fillStackPkID(cloneSet: CloneSet, stackPkId: String) {
        //注入版本基线ID
        cloneSet.metadata.annotations = cloneSet.metadata.annotations ?.apply {
            this[METADATA_ANNOTATION_ATTRIBUTE_NAME_STACK_PK_ID] = stackPkId
        } ?: mapOf(
            METADATA_ANNOTATION_ATTRIBUTE_NAME_STACK_PK_ID to stackPkId
        )
    }

    private fun patchAffinityAndTolerations(cloneSet: CloneSet, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        val podSpec = checkNotNull(cloneSet.spec.template.spec)
        if(IS_NOT_PUBLIC_CLUSTER_LIST.contains(workloadMetadataConstraint.clusterId)) {
            val affinity = (podSpec.affinity ?: V1Affinity()).apply { podSpec.affinity = this }
            val nodeAffinity = (affinity.nodeAffinity ?: V1NodeAffinity()).apply { affinity.nodeAffinity = this }
            (nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution ?: mutableListOf())
                .apply { nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution = this }
                .apply {
                    val preferredSchedulingTerm = V1PreferredSchedulingTerm().apply {
                        this.weight = 1
                        this.preference = V1NodeSelectorTerm().apply {
                            this.matchExpressions = listOf(
                                V1NodeSelectorRequirement().apply {
                                    this.key = "cse-core.alibaba-inc.com/dedicated"
                                    this.operator = "Exists"
                                },
                                V1NodeSelectorRequirement().apply {
                                    this.key = "cse-core.alibaba-inc.com/node-pool"
                                    this.operator = "In"
                                    this.values = listOf("online")
                                },
                                V1NodeSelectorRequirement().apply {
                                    this.key = "sigma.alibaba-inc.com/app-stage"
                                    this.operator = "In"
                                    this.values = listOf(workloadMetadataConstraint.stage)
                                }
                            )
                        }
                    }
                    this.add(preferredSchedulingTerm)
                }
        }
        val tolerations = (podSpec.tolerations ?: mutableListOf()).apply { podSpec.tolerations = this }
        tolerations.addAll(listOf(
            V1Toleration().apply {
                this.key = "sigma.ali/is-ecs"
                this.operator = "Exists"
            },
            V1Toleration().apply {
                this.effect = "NoSchedule"
                this.key = "sigma.ali/resource-pool"
                this.operator = "Equal"
                this.value = "sigma_public" //TODO 修改为实际对应的资源池
            },
            V1Toleration().apply {
                this.effect = "NoSchedule"
                this.key = "sigma.alibaba-inc.com/app-stage"
                this.operator = "Equal"
                this.value = workloadMetadataConstraint.stage
            }
        ))
        if (IS_NOT_PUBLIC_CLUSTER_LIST.contains(workloadMetadataConstraint.clusterId)) {
            tolerations.addAll(listOf(
                V1Toleration().apply {
                    this.effect = "NoSchedule"
                    this.key = "cse-core.alibaba-inc.com/dedicated"
                },
                V1Toleration().apply {
                    this.effect = "NoSchedule"
                    this.key = "sigma.ali/server-owner"
                    this.operator = "Equal"
                    this.value = "cse-core"
                },
                V1Toleration().apply {
                    this.effect = "NoSchedule"
                    this.key = "sigma.ali/resource-pool"
                    this.operator = "Equal"
                    this.value = "lark"
                }
            ))
        }
    }

    /**
     * 设置workload meta data
     */
    private fun patchMetadata(cloneSet: CloneSet, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        cloneSet.metadata.let { metadata ->
            //add labels
            (metadata.labels ?: mutableMapOf<String, String>().apply { metadata.labels = this }).let { labels->
                // 六元组覆盖
                labels.putAll(baseSpecService.getDefaultLabels(workloadMetadataConstraint))
                labels[BaseSpecService.POD_UPSTREAM_COMPONENT] = labels[BaseSpecService.POD_UPSTREAM_COMPONENT] ?: kotlin.run { BaseSpecService.NORMANDY }
            }
        }
    }

    /**
     * 设置pod meta data
     */
    private fun patchPodMetadata(podTemplateSpec: V1PodTemplateSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        (podTemplateSpec.metadata ?: V1ObjectMeta().apply { podTemplateSpec.metadata = this }).let { metadata ->
            (metadata.labels ?: mutableMapOf<String, String>().apply{ metadata.labels = this}).let { labels ->
                //六元组覆盖
                labels.putAll(baseSpecService.getDefaultLabels(workloadMetadataConstraint))
                labels[BaseSpecService.POD_UPSTREAM_COMPONENT] = labels[BaseSpecService.POD_UPSTREAM_COMPONENT] ?: kotlin.run { NORMANDY }
            }
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[HOST_NAME_TEMP] = annotations[HOST_NAME_TEMP] ?: run { baseSpecService.getPodHostNameFormat(workloadMetadataConstraint) }
            }
        }
    }

    /**
     * 获取workload selector
     */
    private fun getCloneSetSelector(workloadMetadataConstraint: WorkloadMetadataConstraint): V1LabelSelector {
        return V1LabelSelector().apply {
            this.matchLabels = mutableMapOf<String, String>().apply {
                this.putAll(baseSpecService.getDefaultLabels(workloadMetadataConstraint))
            }
        }
    }

    /**
     * 移除版本中的pod template label:${TEMPLATE_METADATA_LABEL_OBJECT_ID}
     */
    private fun removePodTemplateLabelOfObjectId(podTemplateSpec: V1PodTemplateSpec) {
        podTemplateSpec.metadata ?.labels ?.let {
            it.remove(TEMPLATE_METADATA_LABEL_OBJECT_ID)
        }
    }

    companion object {
        const val DEFAULT_NAME_SPACE= "cse-default"
        const val DEFAULT_NAME_SPACE_PRE= "cse-default-pre"
        const val CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME = "cloneSet"
        const val METADATA_ANNOTATION_ATTRIBUTE_NAME_STACK_PK_ID = "apps.kruise.io/stack-pk-id"
        const val METADATA_LABEL_NORMANDY_ORDER_ID = "normandy.alibabacloud.com/order-id"
        const val TEMPLATE_METADATA_LABEL_NORMANDY_STACK_ID = "normandy.alibaba-inc.com/stack-id"

        /**
         * 额度账号
         */
        const val QUOTA_RESOURCE_ACCOUNT_NAME = "alibabacloud.com/resource-account-name"

        /**
         * quota name,可以走grop接口生成。和account、site等相关
         */
        const val QUOTA_NAME = "alibabacloud.com/quota-name"

        /**
         * 是否由quota-manager接管accountId和quota-name注入。default true
         */
        const val LINKED_QUOTA_NAME = "alibabacloud.com/linked-quota-name"

        const val TEMPLATE_METADATA_LABEL_NORMANDY_ORDER_ID = "normandy.alibabacloud.com/order-id"
        const val TEMPLATE_METADATA_LABEL_OBJECT_ID = "oam.cse.alibaba-inc.com/object-id"
        val IS_NOT_PUBLIC_CLUSTER_LIST = listOf(
            "ce49c74a2ff9340e1bd8776200cf84906", //asi_zjk_pai_b
            "c8179d04db7ad4c768d542c1c5532c215", //asi_zjk_cse_a
            "ca697010d1d3d4510b9fdd34c89182807", //asi_sz_qingluan_e01
            "c76acf012475e4b10a92cd2433aed373b", //asi_bj_core_h01
            "c788f73c439244feca8c0bac4c03d7e10", //asi_sh_video_a01
            "ca3e3bdae0ce94e2285f2e2d1192ff28d", //asi_zjk_smallflow_a01
            "ca4a4d08db9f547788a55d2d9b7a712bf", //asi_sz_cskvc_335nrl
            "c008adb8b94754289a1aebbb8ceed8f59", //asi_hz_cskvc_h94j3j
        )
        //labels
        private const val INPLACESET_LABEL_SITE = "sigma.ali/site"
        private const val INPLACESET_LABEL_APPNAME = "sigma.ali/app-name"
        private const val INPLACESET_LABEL_NODEGROUP = "sigma.ali/instance-group"
        private const val INPLACESET_LABEL_UNIT = "sigma.alibaba-inc.com/app-unit"
        private const val INPLACESET_LABEL_USETYPE = "sigma.alibaba-inc.com/app-stage"
        private val TEMPLATE_LABEL_BLACK_LIST = listOf(INPLACESET_LABEL_SITE, INPLACESET_LABEL_APPNAME, INPLACESET_LABEL_NODEGROUP, INPLACESET_LABEL_UNIT, INPLACESET_LABEL_USETYPE)
        val CLONESET_NAMESPACE_LIST = listOf(DEFAULT_NAME_SPACE, "cse-default-pre")
        val gson = Gson()
        private fun setLastAppliedConfiguration(cloneSet: CloneSet, lastAppliedConfiguration: String) {
            (cloneSet.metadata).let { metadata ->
                (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                    annotations[BaseSpecService.LAST_APPLIED_CONFIGURATION] = lastAppliedConfiguration
                }
            }
        }


        public fun lastAppliedConfigInit(cloneSet: String, resourceObjectFormat: ResourceObjectFormatEnum): String {
            val cloneSetObj = when(resourceObjectFormat) {
                ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                    JsonUtils.writeValueAsString(YamlUtils.load(cloneSet)), CloneSet::class.java)

                else -> JsonUtils.gsonReadValue(cloneSet, CloneSet::class.java)
            }
            var lastAppliedClone = YamlUtils.load(JsonUtils.gsonWriteValueAsString(cloneSetObj))
            ResourceObjectService.preProcessLastAppliedConf(lastAppliedClone)
            setLastAppliedConfiguration(cloneSetObj, JsonUtils.gsonWriteValueAsString(lastAppliedClone))
            when (resourceObjectFormat) {
                ResourceObjectFormatEnum.YAML -> return YamlUtils.dump(YamlUtils.load(JsonUtils.gsonWriteValueAsString(cloneSetObj)))
                else -> return JsonUtils.writeValueAsString(YamlUtils.load(JsonUtils.gsonWriteValueAsString(cloneSetObj)))
            }

        }


        public fun whetherThreeWayMergeInitialized(
            originalResourceObjectSpecStr: String,
        ): Boolean {
            val originalResourceObjectSpec = YamlUtils.load(originalResourceObjectSpecStr.toNullIfBlank() ?: ResourceObjectService.EMPTY_JSON_OBJECT)
            return ResourceObjectService.getAnnotationsFromWorkloadSpec(originalResourceObjectSpec)?.run {
                when (this.get(ResourceObjectService.LAST_APPLIER)) {
                    ResourceObjectService.LAST_APPLIER_SCALE -> true
                    ResourceObjectService.LAST_APPLIER_DEPLOY -> true
                    null -> false
                    else -> {
                        throw ThreeWayMergeDiffException("invalid kubectl.kubernetes.io/last-applier")
                    }
                }
            } ?: false
        }
    }
}