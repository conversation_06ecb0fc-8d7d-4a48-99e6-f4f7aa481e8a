package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.apre.params.getStage
import com.alibaba.koastline.multiclusters.common.utils.UnitUtils
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.Instant
import java.util.*

@ApiModel("匹配范围定义")
@JsonIgnoreProperties(ignoreUnknown = true)
data class MatchScopeDataDO (
    val id: Long? = null,
    @ApiModelProperty("目标ID")
    val targetId: Long? = null,
    @ApiModelProperty("目标类型")
    val targetType: String? = null,
    @ApiModelProperty("范围限定ID")
    val externalId: String,
    @ApiModelProperty("范围限定类型")
    val externalType: String,
    @ApiModelProperty("排除范围")
    val exclusions: List<Exclusion>? = null,
    @ApiModelProperty("限定条件")
    val restrictions: List<Restriction>? = null,
    val creator: String, //工号
    val modifier: String, //工号
    val gmtCreate: Date? = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date? = Date(Instant.now().toEpochMilli()),
    val isDeleted: String? = "N"
)

@ApiModel("范围内额外定义")
@JsonIgnoreProperties(ignoreUnknown = true)
data class Exclusion (
    @ApiModelProperty("范围限定ID")
    val externalId: String,
    @ApiModelProperty("范围限定类型")
    val externalType: String,
) {
    fun validate(): Exclusion {
        require(externalId.isNotBlank()) { "externalId must be non-blank" }
        require(externalType.isNotBlank()) { "externalId must be non-blank" }
        return this
    }
}

@ApiModel("限定条件")
@JsonIgnoreProperties(ignoreUnknown = true)
data class Restriction (
    @ApiModelProperty("站点")
    val site: String? = null,
    @ApiModelProperty("用途")
    val stage: String? = null,
    @ApiModelProperty("单元")
    val unit: String? = null,
    @ApiModelProperty("集群ID,空代表不限制")
    val clusterIdList: List<String> = emptyList(),
    @ApiModelProperty("环境StackID")
    val envStackId: String? = null,
    @ApiModelProperty("额外Labels")
    val extendedLabels: Map<String, String>? = null,

) {

    fun validate(): Restriction {
        site?.let { require(site.isNotBlank()) { "site must be non-blank" } }
        stage?.let {
            require(stage.isNotBlank()) { "stage must be non-blank" }
            requireNotNull(getStage(stage)) { "invalid stage $stage" }
        }
        unit?.let {
            require(unit.isNotBlank()) { "unit must be non-blank" }
            require(unit.startsWith(UnitUtils.UNIT_PREFIX)) { "unit must starts with ${UnitUtils.UNIT_PREFIX}"}
        }
        clusterIdList.forEach {
            require(it.isNotBlank()) { "clusterId must be non-blank" }
        }
        envStackId?.let {require(envStackId.isNotBlank()) { "envStackId must be non-blank" }}
        return this
    }

    /**
     * 元数据限定优先级积分计算
     * 站点(1) < 单元(2) < 用途(4) < 集群(8) < 环境(16)
     * 环境暂时设置为最高
     */
    @JsonIgnore
    fun getScore(): Int {
        return computeRestrictionItemScore(isScored = !site.isNullOrBlank(), score = 1) + computeRestrictionItemScore(isScored = !unit.isNullOrBlank(), score = 2) + computeRestrictionItemScore(isScored = !stage.isNullOrBlank(), score = 4) + computeRestrictionItemScore(isScored = !clusterIdList.isNullOrEmpty(), score = 8) + computeRestrictionItemScore(isScored = !envStackId.isNullOrBlank(), score = 16)
    }

    /**
     * 计算限定条件项分数
     */
    private fun computeRestrictionItemScore(isScored: Boolean, score: Int): Int {
        if (!isScored) {
            return 0
        }
        return score
    }

    /**
     * Restriction 字符串形式的唯一标识
     */
    fun identity(): String {
        val identityPropertiesList = mutableListOf<String>()
        envStackId?.let {
            identityPropertiesList.add(it)
        }
        unit?.let {
            identityPropertiesList.add(it)
        }
        site?.let {
            identityPropertiesList.add(it)
        }
        stage?.let {
            identityPropertiesList.add(it)
        }
        identityPropertiesList.add(clusterIdList.toSortedSet().joinToString(","))
        return identityPropertiesList.joinToString("#")
    }
}

/**
 * 比较两组限定条件是否完全相同
 */
fun List<Restriction>.equalsIgnoreOrder(other: List<Restriction>): Boolean {
    if (this.size != other.size) {
        return false
    }
    return this.map { it.identity() }.toSet() == other.map { it.identity() }.toSet()
}