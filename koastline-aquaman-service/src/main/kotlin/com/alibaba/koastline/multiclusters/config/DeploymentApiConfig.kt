package com.alibaba.koastline.multiclusters.config

import com.alibaba.normandy.deployment.deployapi.client.ClientConfig
import com.alibaba.normandy.deployment.deployapi.client.DeployApiClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class DeploymentApiConfig {
    @Value("\${deploymentApi.host}")
    lateinit var deploymentApiHost: String

    @Value("\${deploymentApi.app-key}")
    lateinit var deploymentApiAppKey: String

    @Value("\${deploymentApi.app-sec}")
    lateinit var deploymentApiAppSec: String

    @Bean
    fun deployApiClientBean(): DeployApiClient {
        val clientConfig = ClientConfig()
        clientConfig.hostAddress = deploymentApiHost
        clientConfig.user = deploymentApiAppKey
        clientConfig.token = deploymentApiAppSec
        val client = DeployApiClient()
        client.clientConfig = clientConfig
        return client
    }


}