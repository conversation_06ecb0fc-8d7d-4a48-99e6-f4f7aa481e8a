package com.alibaba.koastline.multiclusters.fed

import com.alibaba.koastline.multiclusters.aop.datachange.DataChange
import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.FedClusterException
import com.alibaba.koastline.multiclusters.common.exceptions.FedPolicyException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.dao.fed.FedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureImportRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import com.alibaba.koastline.multiclusters.external.FedConfigApi
import com.alibaba.koastline.multiclusters.fed.model.FedClusterDO
import com.alibaba.koastline.multiclusters.fed.model.FedClusterStatus.Online
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyDetailsPropertiesKey.RATIOS
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyDetailsPropertiesKey.SCHEDULE_PLUGIN
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.FED_POLICY_NAME
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.FED_POLICY_UNIQUE_NAME
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.FED_SOURCE
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.FED_SPEC
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.POLICY_PRIORITY
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.RESOURCE_KIND
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyRatioPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySelectorPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySelectorPropertiesKey.MATCH_EXPRESSIONS
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySelectorPropertiesKey.TENANT_CLUSTER
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySelectorPropertiesKey.TENANT_NAME
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey.CLUSTER_OVERRIDE
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey.GLOBAL_OVERRIDE
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey.POLICY
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey.POLICY_SELECTOR
import com.alibaba.koastline.multiclusters.fed.model.FedSource.NORMANDY_FED_SOURCE
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.IS_DISABLED
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.MEMBER_CLUSTER_NAME_LIST
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.REGION
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.TENANT_CLUSTER_NAME
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.UNIQUE_NAME_KEY
import com.alibaba.koastline.multiclusters.fed.model.LABEL_KV_SPLITTER
import com.alibaba.koastline.multiclusters.fed.model.MatchExpressionsProperties
import com.alibaba.koastline.multiclusters.fed.model.convertToFedClusterDO
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterDeleteReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterConditionQueryReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyQueryCondition
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterRegisterReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterUpdateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedPolicy
import com.alibaba.koastline.multiclusters.fed.model.req.FedPolicyDetails
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.FED_POLICY_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureImportStatusEnum.ENABLED
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*

@Component
class FedClusterService {
    @Autowired
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService

    @Autowired
    lateinit var resourceObjectFeatureImportRepo: ResourceObjectFeatureImportRepo

    @Autowired
    lateinit var fedClusterRepo: FedClusterRepo

    @Autowired
    lateinit var fedConfigApi: FedConfigApi

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var fedClusterAssistant: FedClusterAssistant

    val log by logger()

    /**
     * 按照条件搜索过滤fedCluster的
     *
     * @param fedClusterConditionQueryReq
     * @return
     */
    fun listFedClusterByConditions(fedClusterConditionQueryReq: FedClusterConditionQueryReq): PageData<FedClusterDO> {
        // 主要是这个分步查询sql 进行提前过滤的方式来实现 快速计算
        // 1.首先应该从绑定集群的方向进行查询 这部分一旦确定到集群之后 数据量就会变得很小
        // 2.再查询方向 region&status&&envName 进行分页查询
        val tenantRelativeCluster = mutableListOf<FedCluster>()
        var isTenantRelative = false
        fedClusterConditionQueryReq.tenantClusterName?.let {
            tenantRelativeCluster.addAll(listFedClusterByRelativeCluster(
                relativeCluster = it
            ) { tenantClusterKeys ->
                fedClusterRepo.listByTenantClusterKeys(tenantClusterKeys)
            })
            isTenantRelative = true
        }

        val memberRelativeCluster = mutableListOf<FedCluster>()
        var isMemberRelative = false
        fedClusterConditionQueryReq.memberClusterName?.let {
            memberRelativeCluster.addAll(listFedClusterByRelativeCluster(
                relativeCluster = it
            ) { memberClusterKeys ->
                fedClusterRepo.listByMemberClusterKeys(memberClusterKeys)
            })
            isMemberRelative = true
        }

        val preFilterCluster = if (isTenantRelative && isMemberRelative) {
            (tenantRelativeCluster.toSet() - (tenantRelativeCluster.toSet() - memberRelativeCluster.toSet()).toSet()).toList()
        } else if (isTenantRelative) {
            tenantRelativeCluster
        } else if (isMemberRelative) {
            memberRelativeCluster
        } else {
            null
        }

        if (preFilterCluster != null && preFilterCluster.isEmpty()) {
            return PageData.zeroPage(fedClusterConditionQueryReq.pageSize, fedClusterConditionQueryReq.pageNumber)
        }

        val pageData = fedClusterAssistant.listFedClusterByProperties(
            pageSize = fedClusterConditionQueryReq.pageSize,
            pageNumber = fedClusterConditionQueryReq.pageNumber,
            envNameKeyWords = fedClusterConditionQueryReq.envNameKeyWords,
            region = fedClusterConditionQueryReq.region,
            status = fedClusterConditionQueryReq.status,
            ids = preFilterCluster?.map { it.id!! }
        )
        // fed.id to <tenantClusterName, memberClusterList>
        val relativeCluster = mutableMapOf<Long, Pair<String, List<String>>>()
        pageData.data?.forEach {
            val tenantCluster = resourcePoolService.listByManagedClusterKey(it.tenantManageClusterKey)
                .map { cluster -> cluster.clusterId }.first()
            val memberClusters = resourcePoolService.listByManagedClusterKey(it.memberManageClusterKey)
                .map { cluster -> cluster.clusterId }
            relativeCluster[it.id!!] = Pair(tenantCluster, memberClusters)
        }
        val allClusters = relativeCluster.map { it.value }.map { listOf(it.first) + it.second }.flatten().distinct()
        val clusterId2Name =
            defaultClusterService.listClusterByClusterIdList(allClusters).associate { it.clusterId to it.clusterName }

        return pageData.map {
            val tenantClusterName = relativeCluster[it.id]!!.first
            val memberClusterNameList = relativeCluster[it.id]!!.second
            it.convertToFedClusterDO(
                tenantClusterName = checkNotNull(clusterId2Name[tenantClusterName]) { "missing cluster data: id: $tenantClusterName" },
                memberClusterNameList = memberClusterNameList.map { member -> checkNotNull(clusterId2Name[member]) { "missing cluster data: id: $member" } }
            )
        }
    }

    fun queryFedByTenantClusterName(tenantClusterName: String) = fedClusterRepo.findByTenantClusterName(tenantClusterName)

    /**
     * 查询fedCluster按照clusterId进行查询
     *
     * @param id
     * @return
     */
    fun queryFedClusterById(id: Long): FedClusterDO? {
        val fedCluster = fedClusterRepo.findById(id) ?: return null
        val tenantCluster = resourcePoolService.listByManagedClusterKey(fedCluster.tenantManageClusterKey)
            .map { cluster -> cluster.clusterId }.first()
        val memberClusters = resourcePoolService.listByManagedClusterKey(fedCluster.memberManageClusterKey)
            .map { cluster -> cluster.clusterId }
        val allClusterMap =
            defaultClusterService.listClusterByClusterIdList((listOf(tenantCluster) + memberClusters).distinct())
                .associate { it.clusterId to it.clusterName }
        return fedCluster.convertToFedClusterDO(
            tenantClusterName = checkNotNull(allClusterMap[tenantCluster]) { "missing cluster data: id: $tenantCluster" },
            memberClusterNameList = memberClusters.map { member -> checkNotNull(allClusterMap[member]) { "missing cluster data: id: $member" } }
        )
    }

    /**
     * 按照关联集群来对fed集群进行搜索过滤
     *
     * @param relativeCluster
     * @param queryFunc
     * @return
     */
    private fun listFedClusterByRelativeCluster(
        relativeCluster: String,
        queryFunc: (List<String>) -> List<FedCluster>
    ): List<FedCluster> {
        resourcePoolService.listByClusterName(
            relativeCluster
        ).let { resourcePools ->
            if (resourcePools.isEmpty()) {
                return emptyList()
            }
            val kManageClusterKeys = resourcePools.map { it.managedClusterKey }.distinct()
            if (kManageClusterKeys.isEmpty()) {
                return emptyList()
            }
            return queryFunc.invoke(kManageClusterKeys)
        }
    }

    /**
     * 创建fedTarget数据 同时创建环境数据 并进行数据推送
     *
     * @param registerRequest
     */
    @DataChange(operatorKey = "#registerRequest.creator")
    @Transactional
    fun registerFedCluster(registerRequest: FedClusterRegisterReq) {
        registerRequest.validate()
        // 确定是否已经存在了以tenantClusterName为接入集群的fed集群
        tenantClusterIsRegistered(registerRequest.tenantClusterName)
        // 检验集群是否存在
        checkClusterIsRegistered(registerRequest.memberClustersNameList)
        val fedClusterUniqueKey = buildFedClusterKey()
        // 注册fedTarget & 绑定
        val fedTargetParamMapYamlStr = initFedTarget(
            region = registerRequest.region,
            uniqueNameKey = fedClusterUniqueKey,
            tenantClusterName = registerRequest.tenantClusterName,
            memberClusterNameList = registerRequest.memberClustersNameList
        )
        val newFedTargetFeatureImport = ResourceObjectFeatureImport(
            id = null,
            resourceObjectFeatureKey = ResourceObjectConstants.FED_TARGET_KEY,
            status = ENABLED.name,
            paramMap = fedTargetParamMapYamlStr,
            creator = registerRequest.creator,
            modifier = registerRequest.creator
        )
        if (resourceObjectFeatureImportRepo.insert(newFedTargetFeatureImport) <= 0) {
            throw FedClusterException("注册集群失败,初始化fedTarget记录失败")
        }
        // 创建集群kManage挂载TenantClusters
        val tenantKManagerCluster = defaultClusterService.createSimpleKManageCluster(
            region = registerRequest.region, status = "created"
        )
        // 绑定tenantCluster
        resourcePoolService.createResourcePoolWithClusterName(
            managedClusterKey = tenantKManagerCluster.managedClusterKey,
            clusterName = registerRequest.tenantClusterName,
            creator = registerRequest.creator
        )
        // 创建集群kManage挂载MemberClusters
        val memberKManagerCluster = defaultClusterService.createSimpleKManageCluster(
            region = registerRequest.region, status = "created"
        )
        registerRequest.memberClustersNameList.forEach { memberClusterName ->
            resourcePoolService.createResourcePoolWithClusterName(
                managedClusterKey = memberKManagerCluster.managedClusterKey,
                clusterName = memberClusterName,
                creator = registerRequest.creator
            )
        }
        // 注册集群环境
        val fedCluster = FedCluster(
            fedEnvName = registerRequest.fedClusterEnvName,
            region = registerRequest.region,
            site = registerRequest.site,
            fedClusterKey = fedClusterUniqueKey,
            status = Online.name,
            tenantManageClusterKey = tenantKManagerCluster.managedClusterKey,
            memberManageClusterKey = memberKManagerCluster.managedClusterKey,
            creator = registerRequest.creator,
            modifier = registerRequest.creator,
            gmtCreate = Date(Instant.now().toEpochMilli()),
            gmtModified = Date(Instant.now().toEpochMilli()),
            isDeleted = IS_NOT_DELETED
        )
        if (fedClusterRepo.insert(fedCluster) <= 0) {
            throw FedClusterException("注册集群失败,插入集群环境记录失败")
        }
        // 创建target和fedCluster的映射关系
        matchScopeService.createMatchScopeIgnoreWhileExist(
            MatchScopeDataDO(
                targetId = checkNotNull(newFedTargetFeatureImport.id) { "id should be increase by db" },
                targetType = MatchScopeTargetTypeEnum.FedTarget.name,
                externalId = fedCluster.id.toString(),
                externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
                creator = registerRequest.creator,
                modifier = registerRequest.creator
            )
        )
        // 创建时推送fed target
        val fedTargetCrd = fedClusterAssistant.assembleCrd(
            inputParamsIds = listOf(checkNotNull(newFedTargetFeatureImport.id) {
                "id cannot be null after db insert action!"
            })
        )
        log.info("register fed cluster with $registerRequest")
        fedConfigApi.createOrUpdateFedTarget(fedTargetCrd.first().patch)
    }

    /**
     * 判断租户集群是否已经注册
     *
     * @param tenantClusterName
     */
    private fun tenantClusterIsRegistered(tenantClusterName: String) {
        resourcePoolService.listByClusterName(
            tenantClusterName
        ).let { resourcePools ->
            if (resourcePools.isEmpty()) {
                return
            }
            val kManageClusterKeys = resourcePools.map { it.managedClusterKey }.distinct()
            if (kManageClusterKeys.isEmpty()) {
                return
            }
            fedClusterRepo.listByTenantClusterKeys(
                tenantManageClusterKeys = kManageClusterKeys
            ).let {
                if (it.isNotEmpty()) {
                    throw FedClusterException("注册集群失败,存在接入集群:${tenantClusterName}为主的联邦集群环境")
                }
            }
        }
    }

    /**
     * 更新fed集群环境
     *
     * @param fedClusterUpdateReq
     */
    @Transactional
    @DataChange(operatorKey = "#fedClusterUpdateReq.modifier")
    fun updateFedClusterEnv(
        fedClusterUpdateReq: FedClusterUpdateReq
    ) {
        fedClusterUpdateReq.validate()
        fedClusterRepo.findById(fedClusterUpdateReq.id) ?: let {
            throw FedClusterException("未找到注册id为${fedClusterUpdateReq.id}的集群环境")
        }
        if (fedClusterUpdateReq.fedEnvName == null && fedClusterUpdateReq.status == null) {
            // 没有需要更新的属性
            return
        }
        fedClusterRepo.update(
            id = fedClusterUpdateReq.id,
            status = fedClusterUpdateReq.status,
            fedEnvName = fedClusterUpdateReq.fedEnvName,
            modifier = fedClusterUpdateReq.modifier
        )
        log.info("update fed cluster with $fedClusterUpdateReq")
    }

    /**
     * 删除fed集群环境
     *
     * @param fedClusterId
     * @param modifier
     * 注：这里删除动作不对集群下发删除cr
     */
    @DataChange(operatorKey = "#modifier")
    @Transactional
    fun deleteFedClusterEnv(
        fedClusterId: Long, modifier: String
    ) {
        val originalFedCluster = fedClusterRepo.findById(fedClusterId) ?: let {
            return
        }
        // 删除resourcePool & kManagerCluster 实体
        resourcePoolService.deleteByManagedClusterKeyWithKManageCluster(
            managedClusterKey = originalFedCluster.tenantManageClusterKey,
            modifier = modifier
        )
        resourcePoolService.deleteByManagedClusterKeyWithKManageCluster(
            managedClusterKey = originalFedCluster.memberManageClusterKey,
            modifier = modifier
        )
        //删除import属性
        matchScopeService.listByTargetTypeAndExternal(
            externalId = fedClusterId.toString(),
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
            targetType = MatchScopeTargetTypeEnum.FedTarget.name,
        ).map {
            resourceObjectFeatureImportRepo.deleteById(it.targetId!!)
        }
        fedClusterRepo.deleteById(fedClusterId, modifier)
        log.info("delete fed cluster with $fedClusterId")
    }

    private fun checkClusterIsRegistered(clusterNameList: List<String>) {
        if (clusterNameList.isEmpty()) {
            return
        }
        val queryClusterNames = clusterNameList.filter { it.isNotBlank() }.distinct()
        val finalCluster = defaultClusterService.listClusterByClusterNameList(queryClusterNames)
        if (finalCluster.map { it.clusterName }.size == queryClusterNames.size) {
            return
        }
        throw FedClusterException(
            "存在异常集群参数，${
                clusterNameList.filter { clusterName ->
                    finalCluster.map { it.clusterName }.contains(clusterName)
                }
            }, 请确认参数是否正确或确认集群是否已经录入！"
        )
    }

    /**
     * 添加member集群
     *
     * @param fedMemberClusterCreateReq
     */
    @Transactional
    @DataChange(operatorKey = "#fedMemberClusterCreateReq.modifier")
    fun addMemberCluster(fedMemberClusterCreateReq: FedMemberClusterCreateReq) {
        fedMemberClusterCreateReq.validate()
        checkClusterIsRegistered(fedMemberClusterCreateReq.toAddFedMemberList)
        val originalFedCluster = fedClusterRepo.findById(fedMemberClusterCreateReq.id) ?: let {
            throw FedClusterException("未找到注册id为${fedMemberClusterCreateReq.id}的集群环境")
        }
        fedMemberClusterCreateReq.toAddFedMemberList.forEach { toAddName ->
            resourcePoolService.createResourcePoolWithClusterName(
                clusterName = toAddName,
                managedClusterKey = originalFedCluster.memberManageClusterKey,
                creator = fedMemberClusterCreateReq.modifier
            )
        }
        val finalClusterMembers = findRelatedClusters(originalFedCluster.memberManageClusterKey).map { it.clusterName }
        // 更新
        updateFedTargetMemberCluster(
            fedCluster = originalFedCluster,
            finalClusterList = finalClusterMembers,
            modifier = fedMemberClusterCreateReq.modifier
        )
        log.info("add fed  member cluster with $fedMemberClusterCreateReq")
    }

    /**
     * 删除多余集群
     *
     * @param fedMemberClusterDeleteReq
     */
    @Transactional
    @DataChange(operatorKey = "#fedMemberClusterDeleteReq.modifier")
    fun deleteMemberCluster(fedMemberClusterDeleteReq: FedMemberClusterDeleteReq) {
        fedMemberClusterDeleteReq.validate()
        checkClusterIsRegistered(listOf(fedMemberClusterDeleteReq.toDropMember))
        val originalFedCluster = fedClusterRepo.findById(fedMemberClusterDeleteReq.id) ?: let {
            throw FedClusterException("未找到注册id为${fedMemberClusterDeleteReq.id}的集群环境")
        }
        // cluster Name 2 id
        val existedMembers = findRelatedClusters(originalFedCluster.memberManageClusterKey).associate {
            it.clusterName to it.clusterId
        }.toMutableMap()

        val finalClusterMembers = if (existedMembers.contains(fedMemberClusterDeleteReq.toDropMember)) {
            resourcePoolService.deleteByManagedClusterKeyAndClusterId(
                managedClusterKey = originalFedCluster.memberManageClusterKey,
                clusterId = existedMembers[fedMemberClusterDeleteReq.toDropMember]!!,
                modifier = fedMemberClusterDeleteReq.modifier
            )
            existedMembers.remove(fedMemberClusterDeleteReq.toDropMember)
            existedMembers
        } else {
            existedMembers
        }.map { it.key }.toList()
        // 更新
        updateFedTargetMemberCluster(
            fedCluster = originalFedCluster,
            finalClusterList = finalClusterMembers,
            modifier = fedMemberClusterDeleteReq.modifier
        )
        log.info("drop fed  member cluster with $fedMemberClusterDeleteReq")
    }

    /**
     * 查找一个kManageClusterKey下绑定的所有集群
     *
     * @param kManageClusterKey
     * @return
     */
    private fun findRelatedClusters(kManageClusterKey: String): List<ClusterProfileData> {
        val clusterIds = resourcePoolService.listByManagedClusterKey(kManageClusterKey).map { it.clusterId }
        return defaultClusterService.listClusterByClusterIdList(
            clusterIdList = clusterIds
        )
    }

    /**
     * 按照条件进行fed的Policy的搜索
     *
     * @param fedClusterPolicyQueryCondition
     */
    fun listFedPolicyByConditions(fedClusterPolicyQueryCondition: FedClusterPolicyQueryCondition): PageData<FedPolicy> {
        val originalFedCluster = fedClusterRepo.findById(fedClusterPolicyQueryCondition.fedClusterTargetId) ?: let {
            throw FedClusterException("fed cluster search by id:${fedClusterPolicyQueryCondition.fedClusterTargetId} not found!")
        }
        // 优化捞取数据的的方式
        // 1.没有policySelector&overrideWords的时候 直接捞取数据进行分页 因为不需要进行预处理
        // 2.存在override keyWords时候 捞取数据的时候 db先like 再dump后看 override 部分是否存在关键字
        // 3.存在policy时候 也进行label的key-value进行过滤
        val allIndex = matchScopeService.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            externalId = fedClusterPolicyQueryCondition.fedClusterTargetId.toString(),
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
        ).map { it.targetId!! }
        val totalCount: Long
        val filterFeatureImports = if (fedClusterPolicyQueryCondition.overrideKeyWords.isNullOrBlank()
            && fedClusterPolicyQueryCondition.policySelectors.isNullOrEmpty() && fedClusterPolicyQueryCondition.fedPolicyKeyWords.isNullOrBlank()
        ) {
            val page: Page<ResourceObjectFeatureImport> =
                PageHelper.startPage<ResourceObjectFeatureImport>(
                    fedClusterPolicyQueryCondition.pageNumber,
                    fedClusterPolicyQueryCondition.pageSize,
                    "gmt_modified DESC"
                )
                    .doSelectPage {
                        resourceObjectFeatureService.queryFeatureImportPatchDOByIds(
                            allIndex
                        )
                    }
            totalCount = page.total
            PageData.transformFrom(page).data!!
        } else {
            val preFilterFeatureImports = mutableListOf<ResourceObjectFeatureImport>()
            resourceObjectFeatureService.queryFeatureImportPatchDOByConditions(
                ids = allIndex, paramKeyWords = fedClusterPolicyQueryCondition.overrideKeyWords,
            ).forEach { featureImport ->
                val paramsMap = YamlUtils.load(featureImport.paramMap!!)
                val policySpec = paramsMap[FED_SPEC] as Map<String, Any>
                if (isOverrideKeyWordsInclude(
                        fedClusterPolicyQueryCondition, policySpec
                    ) && isMatchLabels(
                        fedClusterPolicyQueryCondition, policySpec
                    ) && isMatchPolicyNameKeyWords(
                        fedClusterPolicyQueryCondition, paramsMap
                    )
                ) {
                    preFilterFeatureImports.add(featureImport)
                }
            }
            totalCount = preFilterFeatureImports.size.toLong()
            preFilterFeatureImports.subList(
                0,
                if (totalCount < fedClusterPolicyQueryCondition.pageSize) totalCount.toInt() else fedClusterPolicyQueryCondition.pageSize
            )
        }
        return PageData.of(
            pageSize = fedClusterPolicyQueryCondition.pageSize,
            pageNumber = fedClusterPolicyQueryCondition.pageNumber,
            totalCount = totalCount,
            data = filterFeatureImports.map {
                assembleFedPolicy(it, originalFedCluster)
            }
        )
    }

    fun listFedPolicyByFedClusterId(fedClusterTargetId: Long): List<FedPolicy> {
        val originalFedCluster = fedClusterRepo.findById(fedClusterTargetId) ?: let {
            throw FedClusterException("fed cluster search by id:${fedClusterTargetId} not found!")
        }
        val allIndex = matchScopeService.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            externalId = fedClusterTargetId.toString(),
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
        ).map { it.targetId!! }

        return resourceObjectFeatureService.queryFeatureImportPatchDOByIds(
            allIndex
        ).map { assembleFedPolicy(it, originalFedCluster) }
    }

    private fun isOverrideKeyWordsInclude(
        fedClusterPolicyQueryCondition: FedClusterPolicyQueryCondition,
        policySpec: Map<String, Any>
    ): Boolean {
        return if (fedClusterPolicyQueryCondition.overrideKeyWords?.isNotBlank() == true) {
            keyMapContainsKeyWords(
                policySpec[GLOBAL_OVERRIDE]?.toString(),
                policySpec[CLUSTER_OVERRIDE]?.toString(),
                fedClusterPolicyQueryCondition.overrideKeyWords
            )
        } else true
    }

    private fun isMatchLabels(
        fedClusterPolicyQueryCondition: FedClusterPolicyQueryCondition,
        policySpec: Map<String, Any>
    ): Boolean {
        return if (fedClusterPolicyQueryCondition.policySelectors?.isNotEmpty() == true) {
            val policySelectors = policySpec[POLICY_SELECTOR] as Map<String, Any>
            val matchExpressions = policySelectors[MATCH_EXPRESSIONS] as List<Map<String, Any>>
            val operators = buildSimpleMatchKvString(matchExpressions).toSet()
            operators.containsAll(fedClusterPolicyQueryCondition.policySelectors.toSet())
        } else true
    }

    private fun isMatchPolicyNameKeyWords(
        fedClusterPolicyQueryCondition: FedClusterPolicyQueryCondition,
        paramsMap: Map<String, Any>
    ): Boolean {
        return if (fedClusterPolicyQueryCondition.fedPolicyKeyWords?.isNotBlank() == true) {
            (paramsMap[FED_POLICY_NAME]?.toString()
                ?.contains(fedClusterPolicyQueryCondition.fedPolicyKeyWords) == true
                    || paramsMap[FED_POLICY_UNIQUE_NAME]?.toString()
                ?.contains(fedClusterPolicyQueryCondition.fedPolicyKeyWords) == true)
        } else true
    }


    /**
     * 按照指定path去解析
     *
     * @param featureImport
     * @param fedCluster
     * @return
     */
    private fun assembleFedPolicy(featureImport: ResourceObjectFeatureImport, fedCluster: FedCluster): FedPolicy {
        val paramsMap = YamlUtils.load(checkNotNull(featureImport.paramMap) {
            "missing paramMap in featureImport with $featureImport"
        })
        val fedUniqueName = checkNotNull(paramsMap[FED_POLICY_UNIQUE_NAME]) {
            "missing fedUniqueName name with $featureImport"
        } as String
        val fedPolicyName = paramsMap[FED_POLICY_NAME].let {
            if (it == null) fedUniqueName else (it as String)
        }
        val priority = checkNotNull(paramsMap[POLICY_PRIORITY]) {
            "missing fed priority with $featureImport"
        } as Int
        val spec = checkNotNull(paramsMap[FED_SPEC]) {
            "missing fed spec with $featureImport"
        } as Map<String, Any>
        val policy = checkNotNull(spec[POLICY]) {
            "missing fed policy with $featureImport"
        } as Map<String, Any>

        //changeLog: 原版本支持为单个值 1.22 改动支持多值
        val schedulePlugin = amendSpecPlugins(policy)

        val ratioList = checkNotNull(policy[RATIOS]) {
            "missing fed priority with $featureImport"
        } as List<Map<String, Any>>
        val memberClusterList = ratioList.map { it[FedPolicyRatioPropertiesKey.CLUSTER_NAME] as String }
        val policySelector = checkNotNull(spec[POLICY_SELECTOR]) {
            "missing fed priority with $featureImport"
        } as Map<String, Any>
        val matchExpressions = checkNotNull(policySelector[MATCH_EXPRESSIONS]) {
            "missing fed policy matchExpressions with $featureImport"
        } as List<Map<String, Any>>
        val labelKvs = matchExpressions.map {
            "${it[MatchExpressionsProperties.KEY]}$LABEL_KV_SPLITTER${
                if (it.containsNotNull(MatchExpressionsProperties.VALUES)) {
                    (it[MatchExpressionsProperties.VALUES] as List<String>).joinToString(",")
                } else ""
            }"
        }
        return FedPolicy(
            id = featureImport.id!!,
            fedClusterId = fedCluster.id!!,
            fedPolicyPriority = priority,
            fedUniqueName = fedUniqueName,
            fedPolicyName = fedPolicyName,
            schedulePlugin = schedulePlugin,
            effectedMemberClusterList = memberClusterList,
            policySelectorList = labelKvs,
            modifier = featureImport.modifier,
            gmtModified = featureImport.gmtModified,
        )
    }

    fun queryFedPolicyDetails(fedPolicyId: Long): FedPolicyDetails? {
        val featureImport = resourceObjectFeatureImportRepo.findById(fedPolicyId) ?: let {
            return null
        }
        if (featureImport.resourceObjectFeatureKey != FED_POLICY_KEY) {
            return null
        }
        val paramsMap = YamlUtils.load(checkNotNull(featureImport.paramMap) {
            "missing paramsMap in featureImport with fedPolicyId:$fedPolicyId"
        })
        val fedFedPolicyCrd = fedClusterAssistant.assembleCrd(
            inputParamsIds = listOf(checkNotNull(featureImport.id) {
                "id cannot be null after db insert action!"
            }),
            mapFeatureImportParamMap = this::amendFedPolicyFeatureImport
        )
        return FedPolicyDetails(
            id = featureImport.id!!,
            paramsMap = paramsMap,
            yamlStr = fedFedPolicyCrd.first().patch,
            modifier = featureImport.modifier,
            gmtModified = featureImport.gmtModified
        )
    }

    private fun keyMapContainsKeyWords(
        globalOverrideStr: String? = "",
        clusterOverrideStr: String? = "",
        keyWords: String
    ): Boolean {
        return (globalOverrideStr + clusterOverrideStr).contains(other = keyWords)
    }

    /**
     * 获取policySelectsKvs
     *
     * @param fedClusterTargetId
     * @return
     */
    fun listPolicySelectors(
        fedClusterTargetId: Long
    ): List<String> {
        fedClusterRepo.findById(fedClusterTargetId) ?: let {
            return emptyList()
        }
        val matchScopeDataList = matchScopeService.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            externalId = fedClusterTargetId.toString(),
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
        )
        if (matchScopeDataList.isEmpty()) {
            return emptyList()
        }

        val featureImportDOs = resourceObjectFeatureService.queryFeatureImportPatchDOByIds(
            matchScopeDataList.map { it.targetId!! }
        )

        val totalPolicySelectors = mutableListOf<String>()
        featureImportDOs.forEach {
            val params = YamlUtils.load(it.paramMap!!)
            val spec = checkNotNull(params[FED_SPEC]) {
                "missing spec with in policyInputParams"
            } as Map<String, Any>
            val policySelector = checkNotNull(spec[POLICY_SELECTOR]) {
                "missing spec with in policyInputParams"
            } as Map<String, Any>
            val matchExpressions = policySelector[MATCH_EXPRESSIONS] as List<Map<String, String>>
            totalPolicySelectors.addAll(buildSimpleMatchKvString(matchExpressions))
        }
        return totalPolicySelectors.distinct()
    }

    private fun buildSimpleMatchKvString(matchExpressions: List<Map<String, Any>>): List<String> {
        val totalPolicySelectors = mutableListOf<String>()
        matchExpressions.forEach { operator ->
            if (operator.containsNotNull(MatchExpressionsProperties.VALUES)) {
                val values = operator[MatchExpressionsProperties.VALUES] as List<String>
                values.forEach { value ->
                    totalPolicySelectors.add(
                        "${operator[MatchExpressionsProperties.KEY]}${LABEL_KV_SPLITTER}$value"
                    )
                }
            } else totalPolicySelectors.add(
                "${operator[MatchExpressionsProperties.KEY]}${LABEL_KV_SPLITTER}"
            )
        }
        return totalPolicySelectors
    }

    /**
     * 创建fedPolicy, 返回 fedUniqueName, 即系统生成的 policy 的唯一名称
     */
    @Transactional
    @DataChange(operatorKey = "#fedClusterPolicyCreateReq.creator")
    fun createFedPolicy(fedClusterPolicyCreateReq: FedClusterPolicyCreateReq, source: String): String {
        val originalFedCluster = fedClusterRepo.findById(fedClusterPolicyCreateReq.policyTargetClusterId) ?: let {
            throw FedClusterException("未找到对应fed集群! id:${fedClusterPolicyCreateReq.policyTargetClusterId}")
        }

        check(listFedPolicyByFedClusterId(fedClusterPolicyCreateReq.policyTargetClusterId)
            .all { it.fedPolicyName != fedClusterPolicyCreateReq.policyName }) {
            throw FedPolicyException("存在策略名为${fedClusterPolicyCreateReq.policyName}的策略! fed target cluster id:${fedClusterPolicyCreateReq.policyTargetClusterId}")
        }

        val uniqueNameKey = buildPolicyName()
        val tenantClusterResourcePool =
            resourcePoolService.listByManagedClusterKey(originalFedCluster.tenantManageClusterKey).first()
        val tenantCluster = defaultClusterService.getSimpleClusterProfileDataByClusterId(
            tenantClusterResourcePool.clusterId
        )

        // 注册fedPolicy & 绑定
        val fedPolicyParamMapYamlStr = initFedPolicy(
            priority = fedClusterPolicyCreateReq.policyPriority,
            resourceKind = fedClusterPolicyCreateReq.resourceKind,
            source = source,
            fedPolicyUniqueName = uniqueNameKey,
            fedPolicyName = fedClusterPolicyCreateReq.policyName,
            spec = fedClusterPolicyCreateReq.policySpec,
            region = originalFedCluster.region,
            tenantClusterName = tenantCluster.clusterName,
            tenantCluster = tenantCluster.clusterName
        )

        val newFedPolicyFeatureImport = ResourceObjectFeatureImport(
            id = null,
            resourceObjectFeatureKey = FED_POLICY_KEY,
            status = ENABLED.name,
            paramMap = fedPolicyParamMapYamlStr,
            creator = fedClusterPolicyCreateReq.creator,
            modifier = fedClusterPolicyCreateReq.creator
        )

        if (resourceObjectFeatureImportRepo.insert(newFedPolicyFeatureImport) != 1) {
            throw FedClusterException("保存fedPolicy失败,初始化fedTarget记录失败")
        }

        // 创建fedPolicy和fedCluster的映射关系
        matchScopeService.createMatchScopeIgnoreWhileExist(
            MatchScopeDataDO(
                targetId = checkNotNull(newFedPolicyFeatureImport.id) { "target id should not be null after db auto-increase" },
                targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
                externalId = originalFedCluster.id.toString(),
                externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
                creator = fedClusterPolicyCreateReq.creator,
                modifier = fedClusterPolicyCreateReq.creator
            )
        )

        // 创建时推送fedPolicy
        val fedPolicyCrd = fedClusterAssistant.assembleCrd(
            inputParamsIds = listOf(checkNotNull(newFedPolicyFeatureImport.id) {
                "id cannot be null after db insert action!"
            })
        )
        log.info("create fed policy with $fedClusterPolicyCreateReq")
        fedConfigApi.createOrUpdateFedPolicy(fedPolicyCrd.first().patch)
        return uniqueNameKey
    }

    @Transactional
    @DataChange(operatorKey = "#fedClusterPolicyUpdateReq.modifier")
    fun updateFedPolicy(fedClusterPolicyUpdateReq: FedClusterPolicyUpdateReq, source: String) {
        val featureImport = resourceObjectFeatureImportRepo.findById(
            id = fedClusterPolicyUpdateReq.policyId
        )
        val matchScopeData = matchScopeService.listByTarget(
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            targetId = featureImport!!.id!!
        ).first()

        val fedClusterId = matchScopeData.externalId.toLong()

        val originalFedCluster = fedClusterRepo.findById(fedClusterId) ?: let {
            throw FedClusterException("fed cluster id:$fedClusterId not found!")
        }

        val tenantClusterResourcePool =
            resourcePoolService.listByManagedClusterKey(originalFedCluster.tenantManageClusterKey).first()
        val tenantCluster = defaultClusterService.getSimpleClusterProfileDataByClusterId(
            tenantClusterResourcePool.clusterId
        )

        val originalInputMap = YamlUtils.load(featureImport.paramMap!!)

        val newFedPolicyParamMapYamlStr = initFedPolicy(
            priority = fedClusterPolicyUpdateReq.policyPriority,
            resourceKind = fedClusterPolicyUpdateReq.resourceKind,
            source = source,
            fedPolicyUniqueName = originalInputMap[FED_POLICY_UNIQUE_NAME] as String,
            fedPolicyName = originalInputMap[FED_POLICY_NAME]?.toString()
                ?: originalInputMap[FED_POLICY_UNIQUE_NAME] as String,
            spec = fedClusterPolicyUpdateReq.policySpec,
            region = originalFedCluster.region,
            tenantCluster = tenantCluster.clusterName,
            tenantClusterName = tenantCluster.clusterName
        )

        resourceObjectFeatureImportRepo.updateById(
            id = featureImport.id!!,
            modifier = fedClusterPolicyUpdateReq.modifier,
            paramMap = newFedPolicyParamMapYamlStr,
            status = featureImport.status,
            version = featureImport.version
        )

        val fedPolicyCrd = fedClusterAssistant.assembleCrd(
            inputParamsIds = listOf(checkNotNull(featureImport.id) {
                "id cannot be null after db insert action!"
            })
        )

        log.info("update fed policy with $fedClusterPolicyUpdateReq")
        fedConfigApi.createOrUpdateFedPolicy(fedPolicyCrd.first().patch)
    }

    @Transactional
    @DataChange(operatorKey = "#modifier")
    fun deleteFedPolicy(policyId: Long, modifier: String) {
        val originalFedPolicy = resourceObjectFeatureImportRepo.findById(policyId) ?: let {
            throw FedClusterException("fed policy by id: $policyId not found!")
        }
        check(originalFedPolicy.resourceObjectFeatureKey == FED_POLICY_KEY) {
            "resourceObject import feature must $FED_POLICY_KEY"
        }
        val paramMap = YamlUtils.load(originalFedPolicy.paramMap!!)
        val uniqueName = checkNotNull(paramMap[FED_POLICY_UNIQUE_NAME]) {
            "missing policy unique name!"
        } as String
        val fedSpec = checkNotNull(paramMap[FED_SPEC]) {
            "missing policy fed spec!"
        } as Map<String, Any>
        val fedPolicySelectors = checkNotNull(fedSpec[POLICY_SELECTOR]) {
            "missing policy fed policy selectors!"
        } as Map<String, Any>
        val tenantClusterName = checkNotNull(fedPolicySelectors[TENANT_NAME]) {
            "missing policy fed policy selectors!"
        } as String
        val region = checkNotNull(fedPolicySelectors[REGION]) {
            "missing policy fed policy selectors!"
        } as String

        // 删除约束关系
        matchScopeService.deleteMatchScopeByTarget(
            targetId = policyId,
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            modifier = modifier
        )
        // 删除特性
        resourceObjectFeatureImportRepo.deleteById(policyId)

        // 删除集群对应的policy文件
        log.info("delete fed policy with policyId:$policyId")
        fedConfigApi.deleteFedPolicy(uniqueName, tenantClusterName, region)
    }

    /**
     * 更新FedTarget的Member集群
     *
     * @param fedCluster
     * @param finalClusterList
     * @param modifier
     */
    fun updateFedTargetMemberCluster(fedCluster: FedCluster, finalClusterList: List<String>, modifier: String) {
        fedClusterAssistant.updateFedTarget(
            fedCluster = fedCluster,
            modifier = modifier,
            updateParams = mapOf(MEMBER_CLUSTER_NAME_LIST to finalClusterList),
            updateCondition = { params ->
                (params[MEMBER_CLUSTER_NAME_LIST]) != finalClusterList
            }
        )
    }

    /**
     * 创建第一次的fedTarget数据
     * 当模板变动时候也需要进行变动
     * 注意：整体属于user参数
     * @return
     */
    private fun initFedTarget(
        region: String,
        uniqueNameKey: String,
        tenantClusterName: String,
        memberClusterNameList: List<String>
    ): String {
        val innerParams = mutableMapOf(
            IS_DISABLED to "true", //下发的cr默认是不开启的 需要补全后进行开启
            REGION to region,
            UNIQUE_NAME_KEY to uniqueNameKey,
            TENANT_CLUSTER_NAME to tenantClusterName,
            MEMBER_CLUSTER_NAME_LIST to memberClusterNameList
        )
        return YamlUtils.dump(innerParams)
    }

    /**
     * 注入初始化Fed的必要参数模板
     *
     * @param priority
     * @param resourceKind
     * @param source
     * @param fedPolicyUniqueName
     * @param spec
     * @param region
     * @param tenantCluster
     * @param tenantClusterName
     * @return
     */
    private fun initFedPolicy(
        priority: Int,
        resourceKind: String,
        source: String,
        fedPolicyUniqueName: String,
        fedPolicyName: String,
        spec: Map<String, Any>,
        region: String,
        tenantCluster: String,
        tenantClusterName: String,
    ): String {
        // 串起必要参数的path依赖关系
        val mutableMap = spec.toMutableMap()
        val policySelector = checkNotNull(
            mutableMap[POLICY_SELECTOR].let {
                val newMap = (it as Map<String, Any>).toMutableMap()
                mutableMap[POLICY_SELECTOR] = newMap
                newMap
            }
        ) {
            "missing policy selector in fed policy parameter"
        }
        // 注入需要必要的参数字段
        policySelector[TENANT_CLUSTER] = tenantCluster
        policySelector[TENANT_NAME] = tenantClusterName
        policySelector[FedPolicySelectorPropertiesKey.REGION] = region
        checkNotNull(policySelector[MATCH_EXPRESSIONS]) {
            "missing match expressions in parameter policy selector"
        }
        val policy = checkNotNull(
            mutableMap[POLICY].let {
                val newMap = (it as Map<String, Any>).toMutableMap()
                mutableMap[POLICY] = newMap
                newMap
            }
        ) {
            "missing policy in fed policy parameter"
        }
        checkNotNull(policy[SCHEDULE_PLUGIN]) {
            "missing schedulePlugin in policy!"
        }
        checkNotNull(policy[RATIOS]) {
            "missing ratios in policy!"
        }
        val finalMap = mutableMapOf<String, Any>()
        finalMap[POLICY_PRIORITY] = priority
        finalMap[RESOURCE_KIND] = resourceKind
        finalMap[FED_SOURCE] = source
        finalMap[FED_POLICY_UNIQUE_NAME] = fedPolicyUniqueName
        finalMap[FED_POLICY_NAME] = fedPolicyName
        finalMap[FED_SPEC] = mutableMap
        return YamlUtils.dump(finalMap)
    }

    private fun buildPolicyName(): String = "policy-${UUID.randomUUID()}"

    private fun buildFedClusterKey(): String = UUID.randomUUID().toString()

    /**
     * 用于修改Fed policy相关的cr的修正
     * 兼容逻辑: 在fed policy的feautureImport中的schedulePlugins全部更新为List<String> 可以下掉这个兼容逻辑 请后续进行元数据类型格式的订正！
     * @param originalFeatureImports
     * @return
     */
    fun amendFedPolicyFeatureImport(originalFeatureImports: List<ResourceObjectFeatureImport>): List<ResourceObjectFeatureImport> {
        return originalFeatureImports.map { originalFeatureImport ->
            require(!originalFeatureImport.paramMap.isNullOrBlank()) { "paramMap cannot be null or blank" }
            val paramsMap = YamlUtils.load(checkNotNull(originalFeatureImport.paramMap) {
                "missing paramMap in featureImport with $originalFeatureImport"
            }).toMutableMap()
            val spec = (checkNotNull(paramsMap[FED_SPEC]) {
                "missing fed spec with $originalFeatureImport"
            } as Map<String, Any>).toMutableMap()
            val policy = (checkNotNull(spec[POLICY]) {
                "missing fed policy with $originalFeatureImport"
            } as Map<String, Any>).toMutableMap()
            //changeLog: 原版本支持为单个值 1.22 改动支持多值
            val schedulePlugin = amendSpecPlugins(policy)
            policy[SCHEDULE_PLUGIN] = schedulePlugin
            spec[POLICY] = policy
            paramsMap[FED_SPEC] = spec
            paramsMap[FED_SOURCE] = paramsMap[FED_SOURCE] ?: NORMANDY_FED_SOURCE
            originalFeatureImport.copy(
                paramMap = YamlUtils.dump(paramsMap)
            )
        }
    }

    private fun amendSpecPlugins(
        policy: Map<String, Any>,
    ): List<String> {
        val schedulePlugin = checkNotNull(policy[SCHEDULE_PLUGIN]) {
            "missing fed schedulePlugin in fed policy"
        }.let { schedulePlugin ->
            when (schedulePlugin) {
                is String -> listOf(schedulePlugin)
                is List<*> -> schedulePlugin as List<String>
                else -> throw FedPolicyException("unsupported schedulePlugin keys")
            }
        }
        return schedulePlugin
    }
}

fun Map<String, Any>.containsNotNull(key: String): Boolean {
    return this.containsKey(key) && this[key] != null
}