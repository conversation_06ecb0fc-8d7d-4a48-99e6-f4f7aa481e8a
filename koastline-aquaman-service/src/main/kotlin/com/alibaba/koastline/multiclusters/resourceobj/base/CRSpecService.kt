package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.*
import com.alibaba.koastline.multiclusters.external.model.AcniWorkloadMetadata
import com.alibaba.koastline.multiclusters.external.model.AppSubTypeEnum
import com.alibaba.koastline.multiclusters.external.model.VipServiceReq
import com.alibaba.koastline.multiclusters.resourceobj.model.AssembledCRListResult
import com.alibaba.koastline.multiclusters.resourceobj.model.CRResourceObject
import com.alibaba.koastline.multiclusters.resourceobj.model.CrType
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.google.gson.Gson
import io.kubernetes.client.util.Yaml
import org.apache.commons.collections4.ListUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import com.alibaba.koastline.multiclusters.common.utils.ListUtils.unionOfList
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceMetadataRequestDTO
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceTupleParameter

/**
 * CR服务
 */
@Component("CRSpecService")
class CRSpecService {
    val log by logger()
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var acniHomeApi: AcniHomeApi
    @Autowired
    lateinit var vipServiceCrApi: VipServiceCrApi

    @Autowired
    lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var normandyApi: NormandyApi

    val gson = Gson()

    /**
     * 获取扩容CR
     */
    fun getScaleCRSpecList(envStackId: String, workloadMetadataConstraint: WorkloadMetadataConstraint, resourceObjectFormatEnum: ResourceObjectFormatEnum, acceptableCRListResult: List<CrType>?): AssembledCRListResult {
        try {
            return cloudCmdbApi.getEnvBaselineSpec(envStackId, CR_SPEC_YAML_ATTRIBUTE_NAME).run {
                buildCRSpecList(
                    crSpecStr = this,
                    envStackId = envStackId,
                    workloadMetadataConstraint = workloadMetadataConstraint,
                    resourceObjectFormatEnum = resourceObjectFormatEnum,
                    acceptableCRListResult = acceptableCRListResult
                )
            }
        } catch (e: ResourceObjectException) {
            //环境基线不存在独立CR
            log.warn(e.message)
        }
        return AssembledCRListResult(
            workloadMetadataConstraint = workloadMetadataConstraint,
            resourceObjectFormatEnum = resourceObjectFormatEnum,
            crResourceObjectList = buildExtraCr(
                envStackId = envStackId,
                stackPkId = null,
                workloadMetadataConstraint = workloadMetadataConstraint,
                resourceObjectFormatEnum = resourceObjectFormatEnum,
                acceptableCRListResult = acceptableCRListResult
            )
        )
    }

    /**
     * 获取发布CR
     */
    fun getDeployCRSpecList(envStackId: String, stackPkId: String, workloadMetadataConstraint: WorkloadMetadataConstraint, resourceObjectFormatEnum: ResourceObjectFormatEnum): AssembledCRListResult {
        try {
            return cloudCmdbApi.getEnvBaselineSpecByStackPKId(stackPkId, CR_SPEC_YAML_ATTRIBUTE_NAME).run {
                buildCRSpecList(
                    crSpecStr = this,
                    envStackId = envStackId,
                    stackPkId = stackPkId,
                    workloadMetadataConstraint = workloadMetadataConstraint,
                    resourceObjectFormatEnum = resourceObjectFormatEnum,
                    acceptableCRListResult = null
                )
            }
        } catch (e: ResourceObjectException) {
            //环境基线不存在独立CR
            log.warn(e.message)
        }
        return AssembledCRListResult(
            workloadMetadataConstraint = workloadMetadataConstraint,
            resourceObjectFormatEnum = resourceObjectFormatEnum,
            crResourceObjectList = buildExtraCr(
                envStackId = envStackId,
                stackPkId = stackPkId,
                workloadMetadataConstraint = workloadMetadataConstraint,
                resourceObjectFormatEnum = resourceObjectFormatEnum,
                acceptableCRListResult = null
            )
        )
    }


    private fun buildCRSpecList(
        crSpecStr: String,
        envStackId: String,
        stackPkId: String? = null,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
        acceptableCRListResult: List<CrType>?
    ): AssembledCRListResult {
        return AssembledCRListResult(
            workloadMetadataConstraint = workloadMetadataConstraint,
            resourceObjectFormatEnum = resourceObjectFormatEnum,
            crResourceObjectList = ListUtils.union(
                buildBaselineCr(crSpecStr, workloadMetadataConstraint, resourceObjectFormatEnum),
                buildExtraCr(
                    envStackId = envStackId,
                    stackPkId = stackPkId,
                    workloadMetadataConstraint = workloadMetadataConstraint,
                    resourceObjectFormatEnum = resourceObjectFormatEnum,
                    acceptableCRListResult = acceptableCRListResult
                )
            )
        )
    }

    /**
     * 环境基线CR
     */
    private fun buildBaselineCr(crSpecStr: String, workloadMetadataConstraint: WorkloadMetadataConstraint, resourceObjectFormatEnum: ResourceObjectFormatEnum): List<CRResourceObject> {
        log.info("env baseline crSpecStr:${crSpecStr}")
        val crResourceObjectList = crSpecStr.trim().toNullIfBlank() ?.let { crSpecStr->
            crSpecStr.split("---").map { singleCrSpec ->
                val crObj = YamlUtils.load(singleCrSpec)
                (crObj["metadata"]!! as MutableMap<String, Any>)["namespace"] = workloadMetadataConstraint.namespace as Any

                CRResourceObject(
                    apiVersion = crObj["apiVersion"] as String,
                    kind = crObj["kind"] as String,
                    resourceObject = when(resourceObjectFormatEnum) {
                        ResourceObjectFormatEnum.YAML -> Yaml.dump(crObj)
                        else -> gson.toJson(crObj)
                    }
                )
            }
        }
        return crResourceObjectList ?: emptyList()
    }

    /**
     * 云资源的CR，暂时通过外部调用获取
     */
    private fun buildExtraCr(
        envStackId: String,
        stackPkId: String?,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
        acceptableCRListResult: List<CrType>?
    ): List<CRResourceObject> {
        val acniCrList = getExtraCrFromAcni(workloadMetadataConstraint, envStackId, resourceObjectFormatEnum)
        var vipCrList = getExtraCrFromVip(acceptableCRListResult, workloadMetadataConstraint, resourceObjectFormatEnum)
        var normandyCrList =
            getExtraCrFromNormandy(workloadMetadataConstraint, envStackId, stackPkId, resourceObjectFormatEnum)

        return unionOfList(listOf(acniCrList, vipCrList, normandyCrList))
    }


    private fun getExtraCrFromVip(
        acceptableCRListResult: List<CrType>?,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        resourceObjectFormatEnum: ResourceObjectFormatEnum
    ): List<CRResourceObject> {
        var vipCrList = emptyList<CRResourceObject>()
        if (acceptableCRListResult?.contains(CrType.VIP_SERVICE) == true) {
            vipCrList = vipServiceCrApi.getVipServiceCr(
                VipServiceMetadataRequestDTO().apply {
                    this.targetClusterId = workloadMetadataConstraint.clusterId
                    this.namespace = checkNotNull(workloadMetadataConstraint.namespace) { "queryRelatedK8sObjectList,workloadMetadataConstraint.namespace不能为空" }
                    vipServiceTupleParam = VipServiceTupleParameter().apply {
                        this.appName = workloadMetadataConstraint.appName
                        this.groupName = workloadMetadataConstraint.resourceGroup
                        this.site = workloadMetadataConstraint.site
                        this.unit = workloadMetadataConstraint.unit
                        this.useType = workloadMetadataConstraint.stage
                    }
                },
                resourceObjectFormatEnum.name
            ).map {
                CRResourceObject(
                    apiVersion = it.apiVersion,
                    kind = it.kind,
                    resourceObject = it.resourceObject!!
                )
            }
        }
        return vipCrList
    }

    private fun getExtraCrFromAcni(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String,
        resourceObjectFormatEnum: ResourceObjectFormatEnum
    ): List<CRResourceObject> {
        val acniCrList = acniHomeApi.queryRelatedK8sObjectList(
            AcniWorkloadMetadata(
                appName = workloadMetadataConstraint.appName,
                resourceGroup = workloadMetadataConstraint.resourceGroup,
                site = workloadMetadataConstraint.site,
                unit = workloadMetadataConstraint.unit,
                stage = workloadMetadataConstraint.stage,
                clusterId = workloadMetadataConstraint.clusterId,
                namespace = checkNotNull(workloadMetadataConstraint.namespace) { "queryRelatedK8sObjectList,workloadMetadataConstraint.namespace不能为空" },
                envStackId = envStackId,
                subgroup = workloadMetadataConstraint.subgroup,
                runtimeId = workloadMetadataConstraint.runtimeId
            ), resourceObjectFormatEnum.name
        ).map {
            CRResourceObject(
                apiVersion = it.apiVersion,
                kind = it.kind,
                resourceObject = it.objectContent
            )
        }
        return acniCrList
    }

    private fun getExtraCrFromNormandy(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        stackId: String,
        stackPKId: String?,
        resourceObjectFormatEnum: ResourceObjectFormatEnum
    ): List<CRResourceObject> {
        val mutableList = mutableListOf<CRResourceObject>()
        if (appCenterApi.getAppInfoByNameV2(workloadMetadataConstraint.appName).subtype.subtype == AppSubTypeEnum.ALGORITHM_MODEL) {
            mutableList.addAll(
                normandyApi.queryDataSetAndSecretCrList(
                    appName = workloadMetadataConstraint.appName,
                    stackId = stackId,
                    stackPKId = stackPKId,
                    clusterId = workloadMetadataConstraint.clusterId,
                    formatEnum = resourceObjectFormatEnum.name
                ).crResourceObjectList?.filter { it.resourceObject?.isNotBlank() == true }?.map {
                    CRResourceObject(
                        apiVersion = it.apiVersion,
                        kind = it.kind,
                        resourceObject = it.resourceObject!!
                    )
                } ?: emptyList()
            )
        }
        return mutableList
    }

    companion object {
        const val CR_SPEC_YAML_ATTRIBUTE_NAME = "appDependentResources"
    }
}

