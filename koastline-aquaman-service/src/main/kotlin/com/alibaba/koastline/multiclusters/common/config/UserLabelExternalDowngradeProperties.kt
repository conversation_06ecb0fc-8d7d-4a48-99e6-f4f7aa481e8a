package com.alibaba.koastline.multiclusters.common.config

import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
class UserLabelExternalDowngradeProperties {
    var properties = UserLabelExternalDowngradeDiamondConfig.buildProperties()

    fun isDowngrade(labelName: String): Boolean{
        return properties[labelName] ?:properties["*"] ?:false
    }
}