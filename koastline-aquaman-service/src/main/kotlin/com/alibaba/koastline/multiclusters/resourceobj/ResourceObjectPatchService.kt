package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ALL
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.*
import com.alibaba.koastline.multiclusters.resourceobj.listener.FeatureConflictException
import com.alibaba.koastline.multiclusters.resourceobj.listener.ResourceObjectFeatureImportListenerFactory
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService.Companion.EMPTY_JSON_OBJECT
import com.alibaba.koastline.multiclusters.resourceobj.model.PatchStrategyDefinition
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.Strategy
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectPatchStrategyEnum
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.io.StringReader
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.json.Json
import javax.json.JsonMergePatch


private const val BLOKER_INDICATOR = "AQUAMAN_BLOKER"

@Component
class ResourceObjectPatchService {
    val log by logger()

    @Autowired
    lateinit var featureImportListenerFactory: ResourceObjectFeatureImportListenerFactory
    @Autowired
    lateinit var commonProperties: CommonProperties

    fun patchFeature(
        protocol: String,
        version: String? = null,
        baseResourceObjectSpec: MutableMap<String, Any>,
        featureSpec: String,
        additionalPatchStrategyDefinition: PatchStrategyDefinition? = null,
        resourceObjectFeatureKey: String,
        appName: String,
        resourceObjectSceneEnum: ResourceObjectSceneEnum
    ): MutableMap<String, Any> {
        watchPatchFeature(
            appName = appName,
            baseResourceObjectSpec = baseResourceObjectSpec,
            resourceObjectProtocol = protocol,
            featureSpec = featureSpec,
            resourceObjectFeatureKey = resourceObjectFeatureKey,
            resourceObjectSceneEnum = resourceObjectSceneEnum
        )

        var patchStrategyDefinition = patchStrategyDefinitionList.firstOrNull { patchStrategyDefinition ->
            patchStrategyDefinition.protocols.firstOrNull {
                it.kind == protocol && version?.toNullIfBlank()?.run { it.version == this } ?: kotlin.run { true }
            } != null
        } ?: let {
            throw ResourceObjectException("未找到Patch策略，protocol:${protocol},version:${version}")
        }
        patchFeatureMapProperty(
            baseResourceObjectSpec,
            YamlUtils.load(featureSpec.toNullIfBlank() ?: EMPTY_JSON_OBJECT), "", patchStrategyDefinition, additionalPatchStrategyDefinition
        )
        return baseResourceObjectSpec
    }

    /**
     * 监听特性patch事件
     * @param baseResourceObjectSpec 基线版本
     * @param resourceObjectProtocol 资源对象协议
     * @param featureSpec 特性
     * @param resourceObjectFeatureKey 特性KEY
     */
    private fun watchPatchFeature(
        appName: String,
        baseResourceObjectSpec: MutableMap<String, Any>,
        resourceObjectProtocol: String,
        featureSpec: String,
        resourceObjectFeatureKey: String,
        resourceObjectSceneEnum: ResourceObjectSceneEnum
    ) {
        if (isFeatureConflictExtraApp(appName)) {
            return
        }
        if (resourceObjectSceneEnum == ResourceObjectSceneEnum.SCALE_OUT) {
            return
        }
        featureImportListenerFactory.listListener(resourceObjectFeatureKey).forEach {
            try {
                it.watch(
                    baseResourceObjectSpec = YamlUtils.dump(baseResourceObjectSpec),
                    featureSpec = featureSpec,
                    resourceObjectProtocol = ResourceObjectProtocolEnum.valueOf(resourceObjectProtocol)
                )
            } catch(e: FeatureConflictException) {
                log.error("FeatureConflictException:{}", e.message, e)
                throw e
            }
        }
    }

    /**
     * 白名单应用，跳过特性注入校验
     */
    private fun isFeatureConflictExtraApp(appName: String): Boolean {
        if (commonProperties.contains(FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG, appName)
            || commonProperties.contains(FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG, ALL)) {
            return true
        }
        return false
    }

    fun patchFeatureMapProperty(baseSpecObj: MutableMap<String, Any>, featureSpecObj: Map<String, Any>, path: String,
                                        defaultPatchStrategyDefinition: PatchStrategyDefinition, additionalPatchStrategyDefinition: PatchStrategyDefinition?) {
        featureSpecObj.keys.forEach { key ->
            if (StringUtils.equals(key, BLOKER_INDICATOR)) {
                throw ResourceObjectException("资源对象路径非法,path:${path},value:${baseSpecObj}")
            }
            val currentPath = path ?.toNullIfBlank() ?.run {"${path}.${key}"} ?:run { key }
            val baseValue = baseSpecObj[key]
            val mergeValue = featureSpecObj[key] ?: return@forEach
            if (baseValue == null && StringUtils.contains(JsonUtils.writeValueAsString(mergeValue), BLOKER_INDICATOR)) {
                return@forEach
            }
            if (mergeValue is String && StringUtils.startsWith(mergeValue, BLOKER_INDICATOR)) {
                baseValue?.run {
                    resourceObjectBlocker(mergeValue, currentPath, baseValue)
                }
                return@forEach
            }
            when(baseValue) {
                null -> baseSpecObj[key] = mergeValue as Any
                is Map<*, *> -> {
                    val strategy = getStrategy(defaultPatchStrategyDefinition, additionalPatchStrategyDefinition, currentPath,"map")
                    when (strategy.patchStrategy) {
                        ResourceObjectPatchStrategyEnum.merge.name -> {
                            // 递归处理子对象
                            patchFeatureMapProperty(baseValue as MutableMap<String, Any>, mergeValue as Map<String, Any>,
                                currentPath, defaultPatchStrategyDefinition, additionalPatchStrategyDefinition)
                        }
                        ResourceObjectPatchStrategyEnum.replace.name -> {
                            baseSpecObj[key] = mergeValue as Any
                        }
                    }
                }
                is List<*> -> {
                    val strategy = getStrategy(defaultPatchStrategyDefinition, additionalPatchStrategyDefinition, currentPath,"list")
                    when (strategy.patchStrategy) {
                        ResourceObjectPatchStrategyEnum.merge.name, ResourceObjectPatchStrategyEnum.cartesian.name, ResourceObjectPatchStrategyEnum.verify.name -> {
                            patchFeatureListProperty(baseValue as MutableList<Any>, mergeValue as List<Any>, strategy, currentPath, defaultPatchStrategyDefinition, additionalPatchStrategyDefinition)
                        }
                        ResourceObjectPatchStrategyEnum.replace.name -> {
                            baseSpecObj[key] = mergeValue.deepCopy()
                        }
                    }
                }
                else -> {
                    val strategy = getStrategy(defaultPatchStrategyDefinition, additionalPatchStrategyDefinition, currentPath,"value")
                    when (strategy.patchStrategy) {
                        ResourceObjectPatchStrategyEnum.replace.name -> {
                            baseSpecObj[key] = mergeValue as Any
                        }
                        ResourceObjectPatchStrategyEnum.merge.name -> {
                            val baseValueStr = baseValue as String
                            val mergeValueStr = mergeValue as String
                            checkNotNull(strategy.delimiter) {
                                "delimiter must be non-null for merge strategy of value type"
                            }
                            val existedValueArray = distinctSplitAndFilterOutBlankValues(baseValueStr, strategy.delimiter)
                            val finalValueArray =
                                existedValueArray + distinctSplitAndFilterOutBlankValues(mergeValueStr, strategy.delimiter)
                                    .filter { mergeValueItem -> mergeValueItem !in existedValueArray }
                            baseSpecObj[key] = finalValueArray.joinToString(strategy.delimiter)
                        }
                        ResourceObjectPatchStrategyEnum.jsonMerge.name -> {
                            val baseValueJsonObject = Json.createReader(StringReader(baseValue as String)).readObject()
                            val mergeValueJsonObject =
                                Json.createReader(StringReader(mergeValue as String)).readObject()
                            val mergePatch: JsonMergePatch = Json.createMergePatch(mergeValueJsonObject)
                            val updatedObject = mergePatch.apply(baseValueJsonObject)
                            baseSpecObj[key] = JsonUtils.writeValueAsString(
                                YamlUtils.load(
                                    JsonUtils.format(updatedObject) ?: EMPTY_JSON_OBJECT
                                )
                            )
                        }
                    }
                }
            }
        }
    }

    private fun resourceObjectBlocker(mergeValue: String, currentPath: String, baseValue: Any?) {
        when (mergeValue) {
            BLOKER_INDICATOR -> {
                throw ResourceObjectException("资源对象路径非法,path:${currentPath}")
            }

            else -> {
                if (baseValue is String) {
                    if (!validateResourceObjectValue(
                            baseValue,
                            StringUtils.removeStart(mergeValue, BLOKER_INDICATOR)
                        )
                    ) {
                        throw ResourceObjectException("资源对象取值非法,path:${currentPath},value:${baseValue},regEx:${mergeValue}")
                    }
                }
            }
        }

    }

    fun validateResourceObjectValue(resourceObjectValue: String, regEx: String): Boolean {
        val validPattern: Pattern = Pattern.compile(regEx)
        val matcher: Matcher = validPattern.matcher(resourceObjectValue)
        return matcher.matches();
    }

    private fun patchFeatureListProperty(baseSpecObjList: MutableList<Any>, mergeSpecObjList: List<Any>, strategy: Strategy, path: String,
                                         defaultPatchStrategyDefinition: PatchStrategyDefinition, additionalPatchStrategyDefinition: PatchStrategyDefinition?) {
        if (strategy.patchStrategy == ResourceObjectPatchStrategyEnum.verify.name) {
            (mergeSpecObjList as List<Map<String, Any>>).forEach { mergeItem ->
                (baseSpecObjList as List<MutableMap<String, Any>>).forEach { baseItem ->
                    patchFeatureMapProperty(
                        baseItem,
                        mergeItem,
                        path,
                        defaultPatchStrategyDefinition,
                        additionalPatchStrategyDefinition
                    )
                }
            }
            return
        }
        if (strategy.patchStrategy == CARTESIAN) {

            // 指定merge key
            (mergeSpecObjList as List<Map<String, Any>>).forEach { mergeItem ->
                (baseSpecObjList as List<MutableMap<String, Any>>).run {
                    if (this.isNotEmpty()) {
                        this.forEach { baseItem ->
                            patchFeatureMapProperty(
                                baseItem,
                                mergeItem,
                                path,
                                defaultPatchStrategyDefinition,
                                additionalPatchStrategyDefinition
                            )

                        }
                    } else {
                        baseSpecObjList.add(mergeItem)
                    }
                }
            }
            return
        }


        strategy.patchMergeKey?.toNullIfBlank()?.let { key ->
            // 指定merge key
            (mergeSpecObjList as List<Map<String, Any>>).forEach { mergeItem ->
                (baseSpecObjList as List<MutableMap<String, Any>>).firstOrNull { baseItem ->
                    mergeItem[key] == baseItem[key]
                } ?.let { baseItem ->
                            patchFeatureMapProperty(
                                baseItem,
                                mergeItem,
                                path,
                                defaultPatchStrategyDefinition,
                                additionalPatchStrategyDefinition
                            )
                } ?: let {
                    baseSpecObjList.add(mergeItem.deepCopy())
                }
            }
        } ?: let {
            baseSpecObjList.addAll(mergeSpecObjList)
        }


    }

    private fun distinctSplitAndFilterOutBlankValues(value: String, delimiter: String): List<String> {
        return value.split(delimiter).distinct().filter { it.isNotBlank() }
    }

    /**
     * TODO 校验合并双方类型是否一致
     */
    private fun checkValueType(value1: Any, value2: Any): Boolean {
        return true
    }


    companion object {
        /**
         * 获取对应路径的merge策略
         * 注：优先采用附加策略
         */
        fun getStrategy(
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            path: String,
            type: String
        ): Strategy {
        return getStrategy(additionalPatchStrategyDefinition, path,type) ?:run {
            getStrategy(defaultPatchStrategyDefinition,path,type)
        } ?:let {
            throw ResourceObjectException("未找到匹配的资源对象合并策略,path:${path},type:${type},defaultPatchStrategyDefinition:${defaultPatchStrategyDefinition},additionalPatchStrategyDefinition:${additionalPatchStrategyDefinition}")
        }
    }

    private fun getStrategy(patchStrategyDefinition: PatchStrategyDefinition?, path: String, type: String): Strategy? {
        return patchStrategyDefinition ?.run {
            patchStrategyDefinition.strategies.firstOrNull { strategy ->
                strategy.ref == path
            } ?:run {
                patchStrategyDefinition.defaultStrategies?.firstOrNull { strategy ->
                    strategy.ref == "default.${type}"
                }
            }
        }
    }

        /**
         * 因为spec.template.spec.containers.volumeMounts策略STS&CloneSet不同的原因，暂时将两个协议策略分开
         * 后期优化：改成增量DIFF配置方式
         */
    val patchStrategyDefinitionList = ObjectMapper().readValue<List<PatchStrategyDefinition>>(
            """
                [
                    {
                        "protocols":[
                            {
                                "kind":"StatefulSet"
                            },
                            {
                                "kind":"RollingSet"
                            }
                        ],
                        "strategies":[
                            {
                                "ref":"metadata.annotations.sigma.ali/upgrade-merge-labels",
                                "patchStrategy":"merge",
                                "delimiter":","
                            },
                            {
                                "ref":"metadata.annotations.sigma.ali/upgrade-merge-annotations",
                                "patchStrategy":"merge",
                                "delimiter":","
                            },
                            {
                                "ref":"spec.template.spec.volumes",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers.env",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers.resources",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.volumeMounts",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers.livenessProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.readinessProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.startupProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.lifecycle",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms",
                                "description":"笛卡尔积",
                                "patchStrategy":"cartesian"
                            },
                            {
                                "ref":"spec.template.spec.containers.tolerations",
                                "description":"最难搞的，先总体替换",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms.matchExpressions",
                                "patchMergeKey":"key",
                                "patchStrategy":"merge"
                            }
                        ],
                        "defaultStrategies":[
                            {
                                "ref":"default.list",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"default.map",
                                "patchStrategy":"merge"
                            },
                            {
                                "ref":"default.value",
                                "patchStrategy":"replace"
                            }
                        ]
                    },
                    {
                        "protocols":[
                            {
                                "kind":"CloneSet"
                            }
                        ],
                        "strategies":[
                            {
                                "ref":"metadata.annotations.sigma.ali/upgrade-merge-labels",
                                "patchStrategy":"merge",
                                "delimiter":","
                            },
                            {
                                "ref":"metadata.annotations.sigma.ali/upgrade-merge-annotations",
                                "patchStrategy":"merge",
                                "delimiter":","
                            },
                            {
                                "ref":"spec.template.spec.volumes",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers.env",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"spec.template.spec.containers.resources",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.volumeMounts",
                                "patchStrategy":"merge",
                                "patchMergeKey":"mountPath"
                            },
                            {
                                "ref":"spec.template.spec.containers.volumeMounts.name",
                                "patchStrategy":"ignore"
                            },
                            {
                                "ref":"spec.template.spec.containers.livenessProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.readinessProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.startupProbe",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.containers.lifecycle",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms",
                                "description":"笛卡尔积",
                                "patchStrategy":"cartesian"
                            },
                            {
                                "ref":"spec.template.spec.containers.tolerations",
                                "description":"最难搞的，先总体替换",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms.matchExpressions",
                                "patchMergeKey":"key",
                                "patchStrategy":"merge"
                            }
                        ],
                        "defaultStrategies":[
                            {
                                "ref":"default.list",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"default.map",
                                "patchStrategy":"merge"
                            },
                            {
                                "ref":"default.value",
                                "patchStrategy":"replace"
                            }
                        ]
                    },
                    {
                        "protocols":[
                            {
                                "kind":"ServerlessApp"
                            }
                        ],
                        "strategies":[
                            {
                                "ref":"appHooks",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"appHooks.beforeStartApp",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"appHooks.afterStartApp",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"appHooks.beforeStopApp",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"appHooks.afterStopApp",
                                "patchStrategy":"replace"
                            },
                            {
                                "ref":"appHooks.priority",
                                "patchStrategy":"replace"
                            }
                        ],
                        "defaultStrategies":[
                            {
                                "ref":"default.list",
                                "patchStrategy":"merge",
                                "patchMergeKey":"name"
                            },
                            {
                                "ref":"default.map",
                                "patchStrategy":"merge"
                            },
                            {
                                "ref":"default.value",
                                "patchStrategy":"replace"
                            }
                        ]
                    }
                ]
            """.trimIndent()
        )
        const val CARTESIAN = "cartesian"
    }

}