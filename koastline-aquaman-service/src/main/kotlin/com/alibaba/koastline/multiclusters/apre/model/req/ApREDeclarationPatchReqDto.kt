package com.alibaba.koastline.multiclusters.apre.model.req

import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_CLUSTER
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_SITE
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeclarationPatchData
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModelProperty

data class ApREDeclarationPatchCreateReqDto (
    @ApiModelProperty("类型")
    val balanceType: ApREDeclarationPatchType? = BALANCE_SITE,
    @ApiModelProperty("单元")
    val unit: String,
    @ApiModelProperty("用途标")
    val stage: String,
    @ApiModelProperty("站点")
    val site: String? = null,
    val declarationPatch: DeclarationPatch,
    @ApiModelProperty("创建人工号")
    val creator: String,
    @JsonProperty("matchScopeDataDOs")
    val matchScopeDataReqDtoList: List<MatchScopeDataReqDto>?
) {
    fun matches(apREDeclarationPatchData: ApREDeclarationPatchData): Boolean {
        return balanceType!!.name == apREDeclarationPatchData.balanceType
            && unit == apREDeclarationPatchData.unit
            && stage == apREDeclarationPatchData.stage
            && site == apREDeclarationPatchData.site
    }

    fun validate() {
        require(unit.isNotBlank()) {
            "unit of ApREDeclarationPatchCreateReqDto cannot be blank!"
        }
        require(stage.isNotBlank()) {
            "stage of ApREDeclarationPatchCreateReqDto cannot be blank!"
        }
        site?.let {
            require(it.isNotBlank()) {
                "site of ApREDeclarationPatchCreateReqDto cannot be blank!"
            }
        }
        declarationPatch.validate()
    }

    fun extractRelativeSite(): List<String> {
        return when (checkNotNull(this.balanceType) { "missing balanceType, extractRelativeSite need" }) {

            BALANCE_SITE -> {
                this.declarationPatch.patchItems.filter { it.clusterSelector == null }
                    .mapNotNull { it.site }
            }

            BALANCE_CLUSTER -> {
                listOf(checkNotNull(this.site) {
                    "apREDeclaration need site!"
                })
            }
        }
    }
}