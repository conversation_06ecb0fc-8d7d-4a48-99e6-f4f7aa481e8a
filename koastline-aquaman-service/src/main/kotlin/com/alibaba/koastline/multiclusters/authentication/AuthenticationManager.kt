package com.alibaba.koastline.multiclusters.authentication

import com.alibaba.koastline.multiclusters.authentication.models.AquamanUserDetails
import com.alibaba.koastline.multiclusters.authentication.models.Authority
import com.alibaba.koastline.multiclusters.authentication.models.UserAuthentication
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.AuthTool
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.auth.AccessKeyRepo
import com.alibaba.koastline.multiclusters.data.dao.auth.UserRepo
import com.alibaba.koastline.multiclusters.data.dao.auth.UserRoleRepo
import com.alibaba.koastline.multiclusters.data.vo.Role
import com.alibaba.koastline.multiclusters.data.vo.UserRoleData
import com.alibaba.koastline.multiclusters.data.vo.auth.AccessKey
import com.alibaba.koastline.multiclusters.data.vo.auth.UserData
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*
import org.springframework.security.core.context.SecurityContextHolder

/**
 * <AUTHOR>
 */
@Component
class AuthenticationManager {
    val log by logger()
    @Autowired
    lateinit var userRoleRepo: UserRoleRepo

    @Autowired
    lateinit var userdataRepo: UserRepo

    @Autowired
    lateinit var accessKeyRepo: AccessKeyRepo

    fun queryUserDetails(username: String): UserDetails {
        try {
            log.debug("${TraceUtils.getTraceId()}, start to query use detail for $username")
            val userData = userdataRepo.findUserByUserName(username)
            val userRoleData = userRoleRepo.findUserRoleByUserName(username)
            val accesskey = accessKeyRepo.findAccessKeyByOwner(username)
            if(userData == null || userRoleData == null || accesskey == null) throw RuntimeException("The user is not completed registered")
            log.debug("${TraceUtils.getTraceId()}, use details fetched from db: ${userData}, ${userRoleData}")
            val userAuthentication = UserAuthentication(username, accesskey.accessKey, accesskey.accessSecret, setOf(Authority.valueOf(userRoleData.role)), accesskey.openApiAcl)
            AuthTool.setAuth(userAuthentication)
            return AquamanUserDetails(
                authentication = userAuthentication,
                accessSecret = accesskey.accessSecret
            )
        } catch (e: Exception) {
            log.error(e.message, e)
            throw e
        }
    }

    /**
     * 更新接口授权
     */
    fun updateOpenApiAcl(owner: String, openApiAcl: String?) {
        accessKeyRepo.updateOpenApiAcl(owner, openApiAcl)
    }

    /**
     * 追加接口授权
     */
    fun appendOpenApiAcl(owner: String, openApiAcl: String) {
        accessKeyRepo.findAccessKeyByOwner(owner) ?.let {
            accessKeyRepo.updateOpenApiAcl(
                owner = owner,
                openApiAcl = it.openApiAcl ?.run {"${it.openApiAcl},${openApiAcl}"} ?: openApiAcl)
        }
    }

    @Transactional
    fun registerNewUser(userName: String, employeeName: String, employeeId: String, role: Role, openApiAcl: String?) {
        val now = Date(Instant.now().toEpochMilli())
        UserData(
                null,
                now,
                now,
                userName,
                employeeName,
                employeeId
        ).apply {
            userdataRepo.findUserByUserName(userName)?.let {
                throw RuntimeException("the user $userName has been registered in user data.")
            }
            accessKeyRepo.findAccessKeyByOwner(userName)?.let {
                throw RuntimeException("the user $userName has been registered in access key.")
            }
            userRoleRepo.findUserRoleByUserName(userName)?.let {
                throw RuntimeException("the user $userName has been registered in user role.")
            }
            userdataRepo.insertUser(this).let {
                if(it == 0) throw RuntimeException("cannot insert the user $this")
            }
            AccessKey(
                    null,
                    now,
                    now,
                    userName,
                    UUID.randomUUID().toString(),
                    KeyGenerator.generateAlphanumericKey(16),
                    openApiAcl = openApiAcl ?.toNullIfBlank()
            ).apply {
                accessKeyRepo.insertAccessKey(this).let {
                    if(it == 0) throw RuntimeException("cannot insert the accesskey $this")
                }
            }
            UserRoleData(
                    null,
                    now,
                    now,
                    userName,
                    role.name
            ).apply {
                userRoleRepo.insertUserRole(this).let {
                    if(it == 0) throw RuntimeException("cannot insert the user role $this")
                }
            }
        }
    }

    fun getLoginAccount(): AquamanUserDetails? {
        val authentication = SecurityContextHolder.getContext().authentication
        if (authentication != null && authentication.principal is AquamanUserDetails) {
            return (authentication.principal as AquamanUserDetails)
        }
        return null
    }

    companion object {
        const val LOGIN_ACCOUNT_UNKNOWN = "UNKNOWN"
    }
}