package com.alibaba.koastline.multiclusters.apre.base

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.ENV_LEVEL_RUNTIME_SUFFIX
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.ENV_LEVEL_STAGING
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.ENV_LEVEL_STAGING_PROJECT_SYMBOL
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.ENV_LEVEL_TESTING
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.ENV_LEVEL_TESTING_PROJECT_SYMBOL_LIST
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStandard.STANDARD_ENV_LEVEL_LIST
import com.alibaba.koastline.multiclusters.common.exceptions.ApREUniqueExistException
import com.alibaba.koastline.multiclusters.common.exceptions.EnvLevelStageMappingDataNotFoundException
import com.alibaba.koastline.multiclusters.data.dao.env.EnvLevelStageMappingDataRepo
import com.alibaba.koastline.multiclusters.data.vo.env.EnvLevelStageMappingData
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.*

@Component
class EnvLevelStageMappingService {
    @Autowired
    lateinit var envLevelStageMappingDataRepo: EnvLevelStageMappingDataRepo

    /**
     * 创建环境级别->用途标的映射关系
     */
    fun create(envLevel: String, stage: String) {
        envLevelStageMappingDataRepo.queryByEnvLevel(envLevel).firstOrNull {
            it.stage == stage
        }?.let {
            throw ApREUniqueExistException("the mapping envLevel:$envLevel,staging:$stage already exist.")
        } ?: let {
            val now = Date(Instant.now().toEpochMilli())
            envLevelStageMappingDataRepo.insert(
                EnvLevelStageMappingData(
                    null,
                    envLevel,
                    stage,
                    now,
                    now,
                    Constants.IS_NOT_DELETED
                )
            )
        }
    }

    fun listStageByEnvLevel(envLevel: String): List<String> {
        return envLevelStageMappingDataRepo.queryByEnvLevel(envLevel).map { it.stage }
    }

    fun listByEnvLevelWithNormalization(envLevel: String): List<String> {
        return envLevelStageMappingDataRepo.queryByEnvLevel(getStandardEnvLevel(envLevel)).map { it.stage }
    }

    fun listAllEnvLevelStagesMapping(): Map<String, List<String>> {
        val env2StageListMap = mutableMapOf<String, List<String>>()
        envLevelStageMappingDataRepo.listAll().groupBy {
            it.envLevel
        }.forEach { it ->
            env2StageListMap[it.key] = it.value.map { it.stage }
        }
        return env2StageListMap
    }

    fun deleteMappingByEnvLevel(envLevel: String) {
        envLevelStageMappingDataRepo.queryByEnvLevel(envLevel).ifEmpty {
            throw EnvLevelStageMappingDataNotFoundException()
        }
        envLevelStageMappingDataRepo.deleteByEnvLevel(envLevel)
    }

    /**
     * 获取标准化环境级别
     * @param envSign 初始环境级别，对应于Aone侧的envSign
     * @param 返回标准化的环境级别
     */
    fun getStandardEnvLevel(envSign: String): String {
        if (envSign.endsWith(ENV_LEVEL_RUNTIME_SUFFIX)) {
            return envSign
        }
        STANDARD_ENV_LEVEL_LIST.forEach { standardEnvLevel ->
            if (envSign.startsWith(standardEnvLevel)) {
                return standardEnvLevel
            }
        }
        if (envSign.contains(ENV_LEVEL_STAGING_PROJECT_SYMBOL)) {
            return ENV_LEVEL_STAGING
        }
        if (ENV_LEVEL_TESTING_PROJECT_SYMBOL_LIST.any { envSign.contains(it) }) {
            return ENV_LEVEL_TESTING
        }
        return envSign
    }

}

object EnvLevelStandard {
    const val ENV_LEVEL_STAGING = "staging"
    const val ENV_LEVEL_GRAY = "gray"
    const val ENV_LEVEL_BETA = "beta"
    const val ENV_LEVEL_PRODUCTION = "production"
    const val ENV_LEVEL_TESTING = "testing"
    const val ENV_LEVEL_RUNTIME_SUFFIX = "-runtime"
    const val ENV_LEVEL_STAGING_PROJECT_SYMBOL = "-aone2-intgstaging-"
    val ENV_LEVEL_TESTING_PROJECT_SYMBOL_LIST = listOf("-aone2-cr-","-aone2-appstack-")
    val STANDARD_ENV_LEVEL_LIST =
        listOf(ENV_LEVEL_STAGING, ENV_LEVEL_GRAY, ENV_LEVEL_BETA, ENV_LEVEL_PRODUCTION, ENV_LEVEL_TESTING)
}