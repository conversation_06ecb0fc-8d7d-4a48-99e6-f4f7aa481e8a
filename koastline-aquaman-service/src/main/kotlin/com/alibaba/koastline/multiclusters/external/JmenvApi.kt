package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component


@Component
class JmenvApi(val objectMapper: ObjectMapper) {
    val log by logger()
    @Value("\${jmenv.host}")
    lateinit var host: String

    @Value("\${jmenv.daily.host}")
    lateinit var dailyHost: String


    @ExternalCall(SYS_CALLED)
    fun getMiddlewareEnv(stage: String, unit: String): String {
        // $curl http://jmenv.tbsite.net:8080/env?labels=app:vipserver,site:na61,unit:CENTER_UNIT.rg_id,stage:PUBLISH
        val jmenvHost = if (stage == MetadataStageEnum.DAILY.name) dailyHost else host
        return HttpClientUtils.httpGet(
            "$jmenvHost$URL_GET_MW_ENV",
            mapOf(
                "labels" to "app:vipserver,site:na61,unit:$unit,stage:$stage",
            )
        ).trim()
    }
    companion object {
        const val URL_GET_MW_ENV = "/env"
        const val SYS_CALLED = "jmenv"
    }
}