package com.alibaba.koastline.multiclusters.common.config

import com.taobao.eagleeye.redis.clients.jedis.JedisPool
import com.taobao.eagleeye.redis.clients.jedis.JedisPoolConfig
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


/**
 * <AUTHOR>
 */
@Configuration
class RedisConfig {

    @Value("\${redis.aquaman.host}")
    lateinit var host: String

    @Value("\${redis.aquaman.port}")
    lateinit var port: String

    @Bean
    fun jedisPool(): JedisPool {
        val config = JedisPoolConfig()
        return JedisPool(config, host, port.toInt(), DEFAULT_CONNECTION_TIMEOUT, DEFAULT_SO_TIMEOUT, null, 0, null)
    }

    companion object {
        // 初始化连接超时时间
        private const val DEFAULT_CONNECTION_TIMEOUT = 5000
        // 查询超时时间
        private const val DEFAULT_SO_TIMEOUT = 2000
    }
}