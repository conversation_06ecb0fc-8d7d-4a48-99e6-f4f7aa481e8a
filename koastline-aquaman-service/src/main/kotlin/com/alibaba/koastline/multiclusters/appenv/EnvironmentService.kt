package com.alibaba.koastline.multiclusters.appenv

import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentDetails
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentSpec
import org.springframework.web.bind.annotation.RequestParam

/**
 * <AUTHOR>
 */
interface EnvironmentService {

    /**
     * get an environment by the environment key
     */
    fun getEnvironment(clusterEnvironmentKey: String): ClusterEnvironment?

    /**
     * get environment details by the environment key
     */
    fun getEnvironmentDetails(clusterEnvironmentKey: String): ClusterEnvironmentDetails?

    /**
     * get a cluster environment by external id and environment level(eg. testing)
     */
    fun getEnvironmentByExternalOwner(externalType: String, externalId: String, stageLevel: String?): List<ClusterEnvironment?>?

    /**
     * bind environment with an external id
     */
    fun bindEnvironment(clusterEnvironmentKey: String, externalId: String, externalType: String): Boolean

    /**
     * delete an environment by the environment key
     */
    fun deleteEnvironment(clusterEnvironmentKey: String): Boolean

    /**
     * build transformed ids
     */
    fun buildProductlineTransformedIds(externalType: String, externalId: String?): List<String>
    /**
     * lists cluster environments by conditions
     */
    fun queryByMatchRuleV3(envLevel: String,
                           externalId: String? = null,
                           externalType: String,
                           includeEnvType: String,
                           region: String? = null,
                           az: String? = null,
                           stage: String? = null,
                           unit: String? = null,
                           roleName: String? = null): ArrayList<ClusterEnvironment>

    @Deprecated("this api will be offline in near future")
    fun listEnvironments(clusterId: String, namespace: String): MutableList<ClusterEnvironment>
}