package com.alibaba.koastline.multiclusters.schedule.service.schedule.core


/**
 * FunnelTreeNode, build from
 * leaf node contains two kind of nodes, leafNodeWithElem node which means real group and leafNodeWithNoElem
 *
 * @param T
 * @property uniqueKey
 * @property weight
 * @property level
 * @property beforeTotal
 * @property afterTotal
 * @property subNodes
 * @property leafRef
 */
data class FunnelTreeNode<T>(
    val uniqueKey: String,
    val weight: Double,
    val level: Int,
    val beforeTotal: Int,
    var afterTotal: Int,
    val subNodes: MutableList<FunnelTreeNode<T>> = mutableListOf(),
    val leafRef: T? = null
) {

    fun isLeafNodeWithElem(): Boolean {
        return subNodes.isEmpty() && leafRef != null
    }

    fun validateTree() {
        this.validate()
        val nextLevelKeys = this.subNodes.map { it.uniqueKey }
        check(nextLevelKeys.size == nextLevelKeys.distinct().size){ "same level unique keys must be unique" }
        for (subNode in this.subNodes) {
            subNode.validateTree()
        }
    }

    fun validate() {
        require(weight >= 0) { "weight must not be negative int" }
        require(level >= 0) { "level must not be negative int" }
        require(beforeTotal >= 0) { "before total must be not negative" }
        require(afterTotal >= 0) { "after total must be not negative" }
    }

    companion object {
        @JvmStatic
        fun <T> ofRootNode(before: Int): FunnelTreeNode<T> {
            return FunnelTreeNode(
                uniqueKey = "root",
                weight = 1.0,
                level = 0,
                beforeTotal = before,
                afterTotal = before,
            )
        }

        @JvmStatic
        fun <T> ofNode(weight: Double, uniqueKey: String, before: Int, level: Int): FunnelTreeNode<T> {
            return FunnelTreeNode(
                uniqueKey = uniqueKey,
                weight = weight,
                level = level,
                beforeTotal = before,
                afterTotal = before,
            )
        }

        @JvmStatic
        fun <T> ofLeafNode(uniqueKey: String, weight: Double, before: Int, level: Int, leafRef: T): FunnelTreeNode<T> {
            return FunnelTreeNode(
                uniqueKey = uniqueKey,
                weight = weight,
                level = level,
                beforeTotal = before,
                afterTotal = before,
                leafRef = leafRef
            )
        }

        @JvmStatic
        fun <T> ofLeafNode(uniqueKey: String, weight: Int, before: Int, level: Int, leafRef: T): FunnelTreeNode<T> {
            return FunnelTreeNode(
                uniqueKey = uniqueKey,
                weight = weight.toDouble(),
                level = level,
                beforeTotal = before,
                afterTotal = before,
                leafRef = leafRef
            )
        }
    }
}

fun <T> FunnelTreeNode<T>.visitSubTree(action: (FunnelTreeNode<T>) -> Unit) {
    action(this) //solve this node
    for (subNode in this.subNodes) {
        subNode.visitSubTree(action)
    }
}

/**
 * using name space model to match prefix
 *
 * @param T
 * @param groups
 * @param strategyTree
 * @return
 */
fun <T : Group> getFunnelTreeWithMatchPrefixUniqueKey(
    groups: List<T>,
    strategyTree: PrefixUniqueKeyDistributionStrategy,
): FunnelTreeNode<T> {
    return getFunnelTree(
        groups, strategyTree
    ) { group, subStrategy ->
        group.getGroupUniqueKey().startsWith(subStrategy.getNodeUniqueKey())
    }
}

fun <T : Group> getFunnelTree(
    groups: List<T>,
    strategyTree: DistributionStrategy,
    subMatchPredicate: (T, DistributionStrategy) -> Boolean,
): FunnelTreeNode<T> {
    val uniqueKeysMap = groups.associateBy { it.getGroupUniqueKey() }
    require(uniqueKeysMap.size == groups.size) { "group unique key conflict" }
    return buildFunnelTreeNode(
        groups = groups,
        strategyTree = strategyTree,
        subMatchPredicate = subMatchPredicate
    ).also { it.validateTree() }
}

/**
 *
 *
 * @param T
 * @param groups
 * @param strategyTree
 * @param level
 * @return
 */
fun <T : Group> buildFunnelTreeNode(
    groups: List<T>,
    strategyTree: DistributionStrategy? = null,
    level: Int = 0,
    subMatchPredicate: (T, DistributionStrategy) -> Boolean,
): FunnelTreeNode<T> {
    // leaf node
    if (strategyTree == null) {
        val leafNode = groups.first()
        return FunnelTreeNode.ofLeafNode(
            uniqueKey = leafNode.getGroupUniqueKey(),
            weight = 1,
            before = leafNode.getGroupReplicas(),
            level = level,
            leafRef = leafNode
        )
    }
    // not leaf node
    val subStrategies = strategyTree.getSubDistributionStrategy()
    val currentNode = FunnelTreeNode.ofNode<T>(
        uniqueKey = strategyTree.getNodeUniqueKey(),
        weight = strategyTree.getNodeWeight().toDouble(),
        before = groups.sumOf { it.getGroupReplicas() },
        level = level,
    )
    val subTreeNodes = mutableListOf<FunnelTreeNode<T>>()
    var notScopeIn = groups.toSet()
    for (subStrategy in subStrategies) {
        // caution: using prefix unique key to match sub nodes
        val subgroups = groups.filter { group -> subMatchPredicate(group, subStrategy) }
        notScopeIn = notScopeIn - subgroups.toSet()
        subTreeNodes.add(
            buildFunnelTreeNode(
                groups = subgroups,
                level = level + 1,
                strategyTree = subStrategy,
                subMatchPredicate = subMatchPredicate
            )
        )
    }
    currentNode.subNodes.addAll(subTreeNodes)
    currentNode.subNodes.addAll(notScopeIn.map { group ->
        FunnelTreeNode.ofLeafNode(
            uniqueKey = group.getGroupUniqueKey(),
            weight = 0,
            before = group.getGroupReplicas(),
            level = level + 1,
            leafRef = group
        )
    })
    return currentNode
}

/**
 * reconcile next distribution to expected distribution
 *
 * @param T
 * @param root
 * @param operateReplicas
 * @param replicasAdjustMethod <ToCalculateGroup>:is to participate calculate units, <Int> adjust replicas
 * @return
 */
fun <T> reconcileFunnelTree(
    root: FunnelTreeNode<T>,
    operateReplicas: Int,
    replicasAdjustMethod: (List<ToCalculateGroup>, Int) -> List<ResultGroup>
): FunnelTreeNode<T> {
    val after = root.beforeTotal + operateReplicas
    require(after >= 0) { "total sum must not be negative, FunnelTreeNode:$FunnelTreeNode, operateReplicas:$operateReplicas" }
    // not leaf node will be sharing operateReplicas
    root.afterTotal = after
    if (root.subNodes.isNotEmpty()) {
        val groupMap = root.subNodes.map { node ->
            ToCalculateGroup(uniqueKey = node.uniqueKey, replicas = node.beforeTotal, weight = node.weight)
        }.associateBy { it.getGroupUniqueKey() }
        val resultGroups = replicasAdjustMethod(groupMap.values.toList(), operateReplicas)
        root.subNodes.forEach { node ->
            val matchRs = resultGroups.first { it.uniqueKey == node.uniqueKey }
            reconcileFunnelTree(root = node, operateReplicas = matchRs.replicas - node.beforeTotal, replicasAdjustMethod = replicasAdjustMethod)
        }
    }
    return root
}