package com.alibaba.koastline.multiclusters.clusterproviders

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate

/**
 * <AUTHOR>
 */
abstract class AbstactClusterRemoteSynService<R, T> {
    @Autowired
    lateinit var restTemplate: RestTemplate

    internal fun getBasicHttpHeader(): HttpHeaders {
        val httpHeaders = HttpHeaders()

        httpHeaders.contentType = MediaType.APPLICATION_JSON
        return httpHeaders
    }
}