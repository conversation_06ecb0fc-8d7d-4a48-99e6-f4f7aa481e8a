package com.alibaba.koastline.multiclusters.appenv

import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironmentDetails
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService.Companion.BU_PRODUCT_LINE_SPLITTER
import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.exceptions.*
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.data.dao.env.*
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentBindingData
import com.alibaba.koastline.multiclusters.kms.AliyunKmsClient
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.stream.Collectors

/**
 * <AUTHOR>
 */
@Component
class DefaultClusterEnvironmentService(val objectMapper: ObjectMapper) : EnvironmentService {
    val log by logger()

    @Autowired
    lateinit var clusterEnvironmentRepo: ClusterEnvironmentRepo

    @Autowired
    lateinit var clusterProfileRepo: KoastlineClusterProfileRepo

    @Autowired
    lateinit var gatewayRepo: KoastlineGatewayRepo

    @Autowired
    lateinit var clusterBindingRepo: ClusterBindingRepo

    @Autowired
    lateinit var kManagedClusterRepo: KManagedClusterRepo

    @Autowired
    lateinit var availableZoneSiteMappingRepo: AvailableZoneSiteMappingRepo

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var clusterEnvironmentConfigRepo: ClusterEnvironmentConfigRepo

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    @Qualifier("aliyunKmsClient")
    lateinit var kmsClient: AliyunKmsClient

    fun replaceClusterEnvironmentTags(envTags: String, clusterEnvironmentKey: String): Boolean {
        // add envtags validation
        try {
            objectMapper.readValue<Map<String,String>>(envTags).let {
                if (!it.containsKey("externalId") || !it.containsKey("externalType")) {
                    throw EnvironmentParamsNotValidException("envTags")
                }
            }
        } catch (e: Exception) {
            throw e
        }
        // query cluster env and update its envtags
        clusterEnvironmentRepo.findClusterEnvironmentByEnvKey(clusterEnvironmentKey).let {
            if (it == null) {
                throw ClusterEnvironmentException("cluster environment does not exist")
            }
            clusterEnvironmentRepo.updateClusterEnvironmentEnvTagsByClusterEnvKey(clusterEnvironmentKey, envTags).let {
                if(it == 0) throw ClusterEnvironmentException("cannot replace cluster environment tags")
            }
        }
        return flushRedisByClusterEnvironmentKey(clusterEnvironmentKey)
    }

    private fun flushRedisByClusterEnvironmentKey(clusterEnvironmentKey: String): Boolean {
        val newEnvDetails = getEnvironmentDetails(clusterEnvironmentKey)?: throw ClusterEnvironmentException("cluster environment does not exist")
        redisService.getValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY).let { cacheClusterEnvironmentCollection ->
            if (cacheClusterEnvironmentCollection == null) {
                objectMapper.writeValueAsString(mutableListOf(newEnvDetails)).let {
                    redisService.setValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY, it)
                }
            } else {
                objectMapper.readValue<MutableList<ClusterEnvironmentDetails>>(cacheClusterEnvironmentCollection).apply {
                    var indexMatched = -1
                    this.forEachIndexed { index, clusterEnvironmentDetails ->
                        run {
                            if (clusterEnvironmentKey == clusterEnvironmentDetails.clusterEnvironmentKey) {
                                log.info("remove an cluster environment by its key $clusterEnvironmentKey")
                                indexMatched = index
                            }
                        }
                    }
                    if(indexMatched != -1) {
                        this.removeAt(indexMatched)
                    }
                    this.add(newEnvDetails)
                    objectMapper.writeValueAsString(this).apply {
                        redisService.setValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY, this)
                    }
                }
            }
        }
        return true
    }

    fun validateClusterEnvironment(envLevel: String?, region: String, az: String, envMeta: MutableMap<String, String>): MutableList<MutableMap<String, String>> {
        if (envLevel != null) {
            envMeta["envLevel"] = envLevel
        }
        val result = mutableListOf<MutableMap<String,String>>()
        clusterEnvironmentRepo.getClusterEnvironmentByRegionAndAZ(region, az)?.onEach { clusterEnv ->
            run {
                log.info("environment $clusterEnv, start to check envMeta: $envMeta")
                var matched = true
                clusterEnv?.envLabels?.apply {
                    val envLabelRef = objectMapper.readValue<MutableMap<String, String>>(this)
                    for (item in envMeta) {
                        if (!envLabelRef.containsKey(item.key) || envLabelRef[item.key]?.lowercase() != item.value.lowercase()) {
                            matched = false
                            break
                        }
                    }
                }

                log.info("environment $clusterEnv, check if matched $matched")

                if (matched) {
                    val item = objectMapper.readValue<MutableMap<String, String>>(clusterEnv!!.envLabels)
                    availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region)?.let {
                        item[SITE] = it.site
                    }
                    item[MANAGED_CLUSTER_KEY] = clusterEnv.managedClusterKey
                    item[CLUSTER_ENVIRONMENT_KEY] = clusterEnv.clusterEnvKey!!
                    result.add(item)
                    log.info("environment matched, environment info: $item")
                }
            }
        }
        return result
    }

    // this is a temporary solution which get a global search results of cluster environments without binding checking logic
    // assemble this fun inside validateClusterEnvironment in case publish issue
    fun validateClusterEnvironmentGlobal(region: String, az: String, envMeta: MutableMap<String, String>): MutableMap<String, String> {
        var result = mutableMapOf<String, String>()
        var matched = false
        clusterEnvironmentRepo.getClusterEnvironmentByRegionAndAZ(region, az)?.onEach { clusterEnv ->
            run {
                clusterEnv?.envLabels?.apply {
                    val envLabelRef = objectMapper.readValue<MutableMap<String, String>>(this)
                    matched = true
                    for (item in envMeta) {
                        if (!envLabelRef.containsKey(item.key) || envLabelRef[item.key]?.lowercase() != item.value.lowercase()) {
                            matched = false
                            break
                        }
                    }
                }

                if (matched) {
                    result = objectMapper.readValue(clusterEnv!!.envLabels)

                    availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region)?.let {
                        result[SITE] = it.site
                    }

                    clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnv.clusterEnvKey!!).run {
                        if (this == null) {
                            result[ANNOTATIONS] = ""
                        } else {
                            result[ANNOTATIONS] = objectMapper.writeValueAsString(annotationProcessingZeus(this.annotations))
                        }
                    }
                    result[MANAGED_CLUSTER_KEY] = clusterEnv.managedClusterKey
                    return result
                }
            }
        }
        return result
    }

    fun validateClusterEnvironment(externalId: String, externalType: String, envLevel: String?, region: String, az: String, envMeta: MutableMap<String, String>): MutableMap<String, String> {
        // query all binding envs based on externalId and externalType
        val externalIdList = mutableListOf<String>()
        val bindingEnvList = mutableListOf<ClusterEnvironmentBindingData?>()
        externalIdList.add(externalId)

        if(envLevel.isNullOrBlank() && envMeta.containsKey("envLevel")) {
            // if envLevel is null then remove this from envMeta if it contains this key
            envMeta.remove("envLevel")
        }

        // query all cluster environments without validating binding relationship
        if (externalId == GLOBAL_EXTERNAL_ID && externalType == AONE_PRODUCTLINE) {
            return validateClusterEnvironmentGlobal(region, az, envMeta)
        }

        if (externalId != "alibaba") {
            externalIdList.add("alibaba")
        }
        externalIdList.forEach {
            val oneEnvList = clusterBindingRepo.listClusterBindingDataByExternalId(externalType, it)
                    ?: mutableListOf<ClusterEnvironmentBindingData>()
            if (oneEnvList.isNotEmpty()) {
                bindingEnvList.addAll(oneEnvList)
            }
        }
        log.info("binding envs includes $bindingEnvList")
        // query all cluster environment based on region, az
        var matched = false
        clusterEnvironmentRepo.getClusterEnvironmentByRegionAndAZ(region, az)?.onEach { clusterEnv ->
            run {
                clusterEnv?.envLabels?.apply {
                    val envLabelRef = objectMapper.readValue<MutableMap<String, String>>(this)
                    matched = true
                    // filter serverless env
                    if (envLabelRef.containsKey(ENV_LABEL_SERVERLESS_RUNTIME_KEY)) {
                        matched = false
                    } else {
                        for (item in envMeta) {
                            if (!envLabelRef.containsKey(item.key) || envLabelRef[item.key]?.lowercase() != item.value.lowercase()) {
                                matched = false
                                break
                            }
                        }
                    }
                }

                if (matched) {
                    val result = objectMapper.readValue<MutableMap<String, String>>(clusterEnv!!.envLabels)
                    // todo: add az alias
                    availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region)?.let {
                        result[SITE] = it.site
                    }

                    clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnv.clusterEnvKey!!).run {
                        if (this == null) {
                            result[ANNOTATIONS] = ""
                        } else {
                            result[ANNOTATIONS] = objectMapper.writeValueAsString(annotationProcessingZeus(this.annotations))
                        }
                    }
                    result[MANAGED_CLUSTER_KEY] = clusterEnv.managedClusterKey
                    return result
                }
            }
        }

        if (envMeta.containsKey("unit")) {
            val reg = "[_.]".toRegex()
            if(reg.containsMatchIn(envMeta["unit"].toString())) {
                log.error("${TraceUtils.getTraceId()},cannot find cluster environments for wrong unit format: $region, $az, $envMeta")
            }
        }
        return mutableMapOf()
    }

    private fun annotationProcessingZeus(annotations: String): MutableMap<String, Any> {
        val rawAnnotations = objectMapper.readValue<MutableMap<String, Any>>(annotations)
        if (rawAnnotations[ZEUS] != null) {
            val zeusConfig = objectMapper.readValue<MutableMap<String,String>>(rawAnnotations[ZEUS] as String)
            // encrypt password
            zeusConfig[PASSWORD] = kmsClient.decrypt(zeusConfig[PASSWORD]!!)
            rawAnnotations[ZEUS] = objectMapper.writeValueAsString(zeusConfig)
        }
       return rawAnnotations
    }

    override fun getEnvironment(clusterEnvironmentKey: String): ClusterEnvironment? {
        val clusterEnv = clusterEnvironmentRepo.findClusterEnvironmentByEnvKey(clusterEnvironmentKey)
        val clusterProfile = clusterEnv?.managedClusterKey?.let { item ->
            kManagedClusterRepo.findKManagedClusterByManagedClusterKey(item)?.let {
                clusterProfileRepo.findClusterProfileById(it.clusterProfileId!!)
            }
        }
        val clusterBindingData = clusterBindingRepo.listClusterBindingData(clusterEnvironmentKey)

        val envMeta: MutableMap<String, String> = objectMapper.readValue(clusterEnv!!.envLabels)
        log.info("clusterEnv $clusterEnv, clusterProfile $clusterProfile")
        clusterEnv.apply {
            clusterProfile?.let {
                when (clusterBindingData.size) {
                    0 -> throw ClusterEnvironmentException("no cluster binding information found")
                    else -> {
                        log.info("cluster binding exist $clusterBindingData")
                        return ClusterEnvironment(
                                clusterEnvironmentKey,
                                it.clusterProvider,
                                it.clusterType,
                                this.region,
                                this.az,
                                envMeta
                        )
                    }
                }
            }
        }
        log.info("cluster environment not found")
        return null
    }

    override fun getEnvironmentDetails(clusterEnvironmentKey: String): ClusterEnvironmentDetails? {
        val clusterEnv = clusterEnvironmentRepo.findClusterEnvironmentByEnvKey(clusterEnvironmentKey)
                ?: throw ClusterEnvironmentException("cluster env not found for key $clusterEnvironmentKey")

        val clusterProfile = clusterEnv.managedClusterKey.let { item ->
            kManagedClusterRepo.findKManagedClusterByManagedClusterKey(item)?.let {
                clusterProfileRepo.findClusterProfileById(it.clusterProfileId!!)
            }
        }
        val clusterBindingData = clusterBindingRepo.listClusterBindingData(clusterEnvironmentKey)

        val envMeta: Map<String, String> = objectMapper.readValue(clusterEnv.envLabels)
        log.info("clusterEnv $clusterEnv, clusterProfile $clusterProfile")
        clusterEnv.apply {
            clusterProfile?.let { clusterProfileData ->
                val annotations = clusterProfileData.annotations?.let { objectMapper.readValue<MutableMap<String, Any>>(it) }?: mutableMapOf()
                // fetch annotations from cluster env, and update key/value pairs for annotations
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnvironmentKey)?.apply {
                    if(this.annotations.isNotBlank()) {
                        val rawAnnotations =annotationProcessingZeus(this.annotations)
                        annotations.putAll(rawAnnotations)
                    }
                }
                when (clusterBindingData.size) {
                    0 -> throw ClusterEnvironmentException("no cluster binding information found")
                    else -> {
                        log.info("cluster binding exist $clusterBindingData")
                        return ClusterEnvironmentDetails(
                                clusterEnvironmentKey,
                                clusterProfileData.clusterProvider,
                                clusterProfileData.clusterType,
                                this.region,
                                this.az,
                                envMeta,
                                annotations,
                                clusterProfileData.clusterId,
                                clusterProfileData.clusterName
                        )
                    }
                }
            }
            return null
        }


    }

    override fun getEnvironmentByExternalOwner(externalType: String, externalId: String, stageLevel: String?): List<ClusterEnvironment>? {
        log.info("${TraceUtils.getTraceId()}, externalType $externalType")
        val envBindingInfo: List<ClusterEnvironmentBindingData?> = if (externalId == GLOBAL_EXTERNAL_ID) {
            clusterBindingRepo.findClusterBindingDataByExternalType(externalType)?.distinct()?: emptyList()
        } else {
            clusterBindingRepo.listClusterBindingDataByExternalId(externalType, externalId)
                    ?: throw ClusterBindingInfoNotFoundException()
        }

        val envKeyList = arrayListOf<String>()
        envBindingInfo.apply {
            for (clusterEnvironmentBindingData in this) {
                envKeyList.add(clusterEnvironmentBindingData?.clusterEnvKey!!)
            }
        }
        log.info("${TraceUtils.getTraceId()}, env key list $envKeyList")
        if (envKeyList.isEmpty()) return emptyList()
        val clusterEnvList = clusterEnvironmentRepo.findClusterEnvironmentByEnvKeyList(envKeyList)
        val clusterEnvsResult = arrayListOf<ClusterEnvironment>()

        clusterEnvList?.forEach {
            it?.apply {
                val labels: MutableMap<String, String> = objectMapper.readValue(this.envLabels)
                for (envBinding in envBindingInfo) {
                    if(envBinding?.clusterEnvKey == this.clusterEnvKey) {
                        labels[EXTERNAL_ID] = envBinding?.externalId!!
                        labels[EXTERNAL_TYPE] = externalType
                        break
                    }
                }
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(this.clusterEnvKey!!).run {
                    if(this == null) {
                        labels[ANNOTATIONS] = ""
                    } else {
                        labels[ANNOTATIONS] = objectMapper.writeValueAsString(annotationProcessingZeus(this.annotations))
                    }
                }

                // return all env for this externalId and externalType pair
                if (stageLevel.isNullOrBlank()) {
                    val clusterNative = kManagedClusterRepo.findKManagedClusterByManagedClusterKey(this.managedClusterKey)?.run {
                        clusterProfileRepo.findClusterProfileById(this.clusterProfileId!!)
                                ?: throw ClusterProfileNotFoundException(this.clusterProfileId)
                    }
                    clusterEnvsResult.add(ClusterEnvironment(
                            this.clusterEnvKey!!,
                            clusterNative!!.clusterProvider,
                            clusterNative.clusterType,
                            this.region,
                            this.az,
                            labels,
                            clusterNative.clusterId,
                            clusterNative.clusterName
                    ))
                } else {
                    // return only env according to the env level owned by the pair of externalId and externalType
                    if (!labels.containsKey("envLevel")) throw RuntimeException("there no envLevel set for this externalType $externalType and externalId $externalId")
                    if (labels["envLevel"].equals(stageLevel)) {
                        val clusterNative = kManagedClusterRepo.findKManagedClusterByManagedClusterKey(this.managedClusterKey)?.run {
                            clusterProfileRepo.findClusterProfileById(this.clusterProfileId!!)
                                    ?: throw ClusterProfileNotFoundException(this.clusterProfileId)
                        }

                        clusterEnvsResult.add(ClusterEnvironment(
                                this.clusterEnvKey!!,
                                clusterNative!!.clusterProvider,
                                clusterNative.clusterType,
                                this.region,
                                this.az,
                                labels,
                                clusterNative.clusterId,
                                clusterNative.clusterName
                        ))
                    }
                }
            }
        }
        return clusterEnvsResult
    }

    override fun bindEnvironment(clusterEnvironmentKey: String, externalId: String, externalType: String): Boolean {
        TODO("Not yet implemented")
    }

    @Transactional
    override fun deleteEnvironment(clusterEnvironmentKey: String): Boolean {
        clusterEnvironmentRepo.deleteClusterEnvironmentClusterEnvKey(clusterEnvironmentKey).apply {
            if (this == 0) throw ClusterEnvironmentException("Cluster Environment cannot be deleted")
            clusterBindingRepo.deleteClusterBindingDataByClusterEnvKey(clusterEnvironmentKey).apply {
                if (this == 0) throw ClusterBindingDeleteException()
                redisService.getValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY)?.let { cacheValue ->
                    objectMapper.readValue<MutableList<ClusterEnvironmentDetails>>(cacheValue).run {
                        var indexMatched = -1
                        this.forEachIndexed { index, clusterEnvironmentDetails ->
                            run {
                                if (clusterEnvironmentKey == clusterEnvironmentDetails.clusterEnvironmentKey) {
                                    log.info("remove an cluster environment by its key $clusterEnvironmentKey")
                                    indexMatched = index
                                }
                            }
                        }
                        this.removeAt(indexMatched)
                        redisService.setValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY, objectMapper.writeValueAsString(this))
                    }
                }
                return true
            }
        }
    }

    override fun buildProductlineTransformedIds(externalType: String, externalId: String?): List<String> {
        val transformedIds = arrayListOf<String>()
        when (externalId) {
            null -> transformedIds.add(ALIBABA_GROUP)
            GLOBAL_EXTERNAL_ID -> transformedIds.add(GLOBAL_EXTERNAL_ID)
            ALIBABA_GROUP -> transformedIds.add(ALIBABA_GROUP)
            else -> {
                transformedIds.add(ALIBABA_GROUP)
                val productlinesTransformed = arrayListOf<String>()
                externalId.contains(BU_PRODUCT_LINE_SPLITTER).let {
                    when (it) {
                        true -> {
                            externalId.split(BU_PRODUCT_LINE_SPLITTER).run {
                                val buId = this[0]
                                transformedIds.add(buId)
                                val productlines = this[1]
                                productlines.split("_").let {
                                    productlinesTransformed.add(it[0])
                                    for (i in 1 until it.size) {
                                        productlinesTransformed.add(productlinesTransformed[i-1] + "_" + it[i])
                                    }

                                    for (i in productlinesTransformed.indices) {
                                        transformedIds.add("$buId#" + productlinesTransformed[i])
                                    }
                                }
                            }
                            log.info("externalId has bu level config, $transformedIds")
                        }
                        false -> throw ClusterEnvironmentException("ExternalId's format is different from 'buId#12_345_678'")
                    }
                }
            }
        }
        log.info("${TraceUtils.getTraceId()}, externalId set $transformedIds")
        return transformedIds
    }

    override fun queryByMatchRuleV3(
        envLevel: String,
        externalId: String?,
        externalType: String,
        includeEnvType: String,
        region: String?,
        az: String?,
        stage: String?,
        unit: String?,
        roleName: String?
    ): ArrayList<ClusterEnvironment> {
        val filterByTuple = checkQueryByMatchRuleV3Params(envLevel, externalId, externalType, includeEnvType, region, az, stage, unit, roleName);
        if (!filterByTuple) {
            val result = arrayListOf<ClusterEnvironment>()
            val transformedIds = buildProductlineTransformedIds(externalType, externalId);
            transformedIds.forEach { it ->
                getEnvironmentByExternalOwner(externalType, it, envLevel)?.apply {
                    this.forEach{clusterEnvironment ->
                        var isServerlessRuntimeEnv = false
                        clusterEnvironment?.envTags?.get(ENV_LABEL_SERVERLESS_RUNTIME_KEY)?.let {
                            isServerlessRuntimeEnv = true
                        }
                        when(includeEnvType) {
                            INCLUDE_ENV_TYPE_SERVERLESS ->
                                if (isServerlessRuntimeEnv) {
                                    result.add(clusterEnvironment)
                                }
                            INCLUDE_ENV_TYPE_COMMON ->
                                if (!isServerlessRuntimeEnv) {
                                    result.add(clusterEnvironment)
                                }
                        }
                    }
                }
            }
            return result
        }
        val envMeta = mutableMapOf(
            "envLevel" to envLevel!!,
            "region" to region!!,
            "az" to az!!,
            "stage" to stage!!,
            "unit" to unit!!,
        )
        when(includeEnvType) {
            INCLUDE_ENV_TYPE_SERVERLESS ->
                envMeta["roleName"] = roleName!!
        }
        // query all cluster environments without validating binding relationship
        // keep the same feature as fun validateClusterEnvironment
        return findClusterEnvironmentGlobal(region!!, az!!, envMeta)?.run { arrayListOf(this) } ?: arrayListOf()
    }

    fun findClusterEnvironmentGlobal(region: String, az: String, envMeta: MutableMap<String, String>): ClusterEnvironment? {
        clusterEnvironmentRepo.getClusterEnvironmentByRegionAndAZ(region, az)?.onEach { clusterEnv ->
            run {
                var matched = true
                clusterEnv?.envLabels?.apply {
                    val envLabelRef = objectMapper.readValue<MutableMap<String, String>>(this)
                    envLabelRef[ENV_LABEL_SERVERLESS_RUNTIME_KEY]?.let {serverlessRuntim ->
                        objectMapper.readValue<MutableMap<String,String>>(serverlessRuntim)[ENV_LABEL_ROLE_NAME_KEY]?.let { roleName ->
                            envLabelRef[ENV_LABEL_ROLE_NAME_KEY] = roleName
                        }
                    }
                    for (item in envMeta) {
                        if (!envLabelRef.containsKey(item.key) || envLabelRef[item.key]?.lowercase() != item.value.lowercase()) {
                            matched = false
                            break
                        }
                    }
                }
                if (matched) {
                    val envLabelRef: MutableMap<String, String> = objectMapper.readValue(clusterEnv!!.envLabels)
                    availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(az, region)?.let {
                        envLabelRef[SITE] = it.site
                    }
                    clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnv.clusterEnvKey!!).run {
                        if (this == null) {
                            envLabelRef[ANNOTATIONS] = ""
                        } else {
                            envLabelRef[ANNOTATIONS] = objectMapper.writeValueAsString(annotationProcessingZeus(this.annotations))
                        }
                    }
                    envLabelRef[MANAGED_CLUSTER_KEY] = clusterEnv.managedClusterKey
                    val clusterNative = kManagedClusterRepo.findKManagedClusterByManagedClusterKey(clusterEnv.managedClusterKey)?.run {
                        clusterProfileRepo.findClusterProfileById(this.clusterProfileId!!)
                            ?: throw ClusterProfileNotFoundException(this.clusterProfileId)
                    }
                    return ClusterEnvironment(
                        clusterEnv.clusterEnvKey!!,
                        clusterNative!!.clusterProvider,
                        clusterNative.clusterType,
                        clusterEnv.region,
                        clusterEnv.az,
                        envLabelRef,
                        clusterNative.clusterId,
                        clusterNative.clusterName
                    )
                }
            }
        }
        return null
    }

    fun checkQueryByMatchRuleV3Params(
        envLevel: String,
        externalId: String?,
        externalType: String,
        includeEnvType: String,
        region: String?,
        az: String?,
        stage: String?,
        unit: String?,
        roleName: String?
    ): Boolean {
        //check params
        if (!arrayListOf(INCLUDE_ENV_TYPE_COMMON, INCLUDE_ENV_TYPE_SERVERLESS).contains(includeEnvType)) {
            throw EnvironmentParamsNotValidException("includeEnvType")
        }
        var filterByTuple = false
        when (includeEnvType) {
            INCLUDE_ENV_TYPE_COMMON -> {
                val notNullNum = listOf(region, az, stage, unit).filterNotNull().size;
                when (notNullNum) {
                    0 -> filterByTuple = false
                    4 -> filterByTuple = true
                    else -> throw EnvironmentParamsNotValidException("{region,az,stage,unit}")
                }
            }
            INCLUDE_ENV_TYPE_SERVERLESS -> {
                val notNullNum = listOf(region, az, stage, unit, roleName).filterNotNull().size;
                when (notNullNum) {
                    0 -> filterByTuple = false
                    5 -> filterByTuple = true
                    else -> throw EnvironmentParamsNotValidException("{region,az,stage,unit,roleName}")
                }
            }
        }
        return filterByTuple
    }

    fun listClusterEnvironments(): MutableList<ClusterEnvironmentDetails> {
        redisService.getValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY)?.let {
            return objectMapper.readValue(it)
        }
        val clusterEnvironmentDataList = clusterEnvironmentRepo.listAllClusterEnvironments()
        val result = mutableListOf<ClusterEnvironmentDetails>()
        clusterEnvironmentDataList.forEach{
            it.clusterEnvKey?.let { it1 ->
                getEnvironmentDetails(it1)?.apply {
                    result.add(this)
                }
            }
        }
        result.let {
            redisService.setValue(REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY, objectMapper.writeValueAsString(it))
        }
        return result
    }

    fun modifyClusterEnvironmentConfiguration(clusterEnvironmentKey: String, annotations: MutableMap<String, Any>): MutableMap<String, Any> {
        // todo: add special logic for zeus password processing
        if (annotations[ZEUS] != null) {
            val zeusConfig = objectMapper.readValue<MutableMap<String,String>>(annotations[ZEUS] as String)
            // encrypt password
            zeusConfig[PASSWORD] = kmsClient.encrypt(zeusConfig[PASSWORD]!!)
            annotations[ZEUS] = objectMapper.writeValueAsString(zeusConfig)
        }

        clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnvironmentKey).let {
            if (it == null) {
                clusterEnvironmentConfigRepo.createClusterEnvironmentCong(clusterEnvironmentKey, objectMapper.writeValueAsString(annotations))
            } else {
                if(it.annotations.isNotBlank()) {
                    val currentAnnotations = objectMapper.readValue<MutableMap<String, Any>>(it.annotations)

                    currentAnnotations.putAll(annotations)
                    clusterEnvironmentConfigRepo.updateClusterEnvironmentConfig(clusterEnvironmentKey, objectMapper.writeValueAsString(currentAnnotations))
                } else {
                    clusterEnvironmentConfigRepo.updateClusterEnvironmentConfig(clusterEnvironmentKey, objectMapper.writeValueAsString(annotations))
                }
            }
        }
        clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(clusterEnvironmentKey)?.let {
            return objectMapper.readValue(it.annotations)
        }
        return mutableMapOf()
    }

    private fun clusterEnvKeyGenerator(length: Int = 16): String {
        return KeyGenerator.generateAlphanumericKey(length)
    }

    fun checkClusterEnvironmentUniqueness(currentEnvMeta: Map<String, String>, envLabelsRef: Map<String, String>) {
        if (!envLabelsRef[ENV_LABEL_ENV_LEVEL_KEY].equals(currentEnvMeta[ENV_LABEL_ENV_LEVEL_KEY])) {
            return;
        }
        if (!currentEnvMeta[ENV_LABEL_STAGE_KEY].equals(envLabelsRef[ENV_LABEL_STAGE_KEY])) {
            return;
        }
        if (!envLabelsRef[ENV_LABEL_UNIT_KEY].equals(currentEnvMeta[ENV_LABEL_UNIT_KEY])) {
            return;
        }
        val originRoleName = envLabelsRef[ENV_LABEL_SERVERLESS_RUNTIME_KEY]?.run {
            objectMapper.readValue<Map<String,String>>(this)[ENV_LABEL_ROLE_NAME_KEY]
        }
        val currentRoleName = currentEnvMeta[ENV_LABEL_SERVERLESS_RUNTIME_KEY]?.run {
            objectMapper.readValue<Map<String,String>>(this)[ENV_LABEL_ROLE_NAME_KEY]
        }
        if (!originRoleName.equals(currentRoleName)) {
            return;
        }
        throw ClusterEnvironmentException("cluster environment already exist")
    }

    override fun listEnvironments(clusterId: String, namespace: String): MutableList<ClusterEnvironment> {
        val clusterProfileData = defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId)

        val environmentList = mutableListOf<ClusterEnvironment>()
        kManagedClusterRepo.findKManagedClusterByClusterProfileIdAndNamespace(clusterProfileData.id!!, namespace)?.let {
            return clusterEnvironmentRepo.findClusterEnvironmentByManagedClusterKey(it.managedClusterKey).stream().map { clusterEnv ->
                ClusterEnvironmentConverter.toClusterEnvironment(clusterEnv, clusterId, clusterProfileData.clusterName, clusterProfileData.clusterProvider, clusterProfileData.clusterType)
            }.collect(Collectors.toList())
        }
        return environmentList
    }


    companion object {
        const val ENVIRONMENT_KEY_LENGTH = 32
        const val MANAGED_CLUSTER_KEY = "managedClusterKey"
        const val CLUSTER_ENVIRONMENT_KEY = "clusterEnvironmentKey"
        const val SITE = "site"
        const val REDIS_CLUSTER_ENVIRONMENTS_COLLECTION_KEY = "cluster-environments-collection"
        const val EXPIRATION_DAYS_DEFAULT = 7L
        const val ZEUS = "zeus"
        const val PASSWORD = "password"
        const val ANNOTATIONS = "annotations"
        const val GLOBAL_EXTERNAL_ID = "global"
        const val AONE_PRODUCTLINE = "aone-productline"
        const val EXTERNAL_TYPE = "externalType"
        const val EXTERNAL_ID = "externalId"

        const val ALIBABA_GROUP = "alibaba" // alibaba group

        const val INCLUDE_ENV_TYPE_COMMON = "common"
        const val INCLUDE_ENV_TYPE_SERVERLESS = "serverless"

        const val ENV_LABEL_SERVERLESS_RUNTIME_KEY = "serverlessRuntime"
        const val ENV_LABEL_ROLE_NAME_KEY = "roleName"
        const val ENV_LABEL_ENV_LEVEL_KEY = "envLevel"
        const val ENV_LABEL_REGION_KEY = "region"
        const val ENV_LABEL_AZ_KEY = "az"
        const val ENV_LABEL_STAGE_KEY = "stage"
        const val ENV_LABEL_UNIT_KEY = "unit"
    }

}