package com.alibaba.koastline.multiclusters.apre.model


import com.alibaba.koastline.multiclusters.appenv.model.ClusterProfile
import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSourceEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.COMMON
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.util.*
import javax.validation.constraints.NotBlank

/**
 * <AUTHOR>
 */
data class ApREDO (
    val id: Long? = null,
    var runtimeEnvKey: String? = null,
    val name: String? = null,
    @field: NotBlank
    val creator: String,
    @field: NotBlank
    val managedClusterKey: String,
    @field: NotBlank
    val region: String,
    val regionName: String? = null,
    /**
     * 站点，这里应该用site
     */
    @field: NotBlank
    val az: String,
    val azName: String? = null,
    @field: NotBlank
    val stage: String,
    @field: NotBlank
    val unit: String,
    val status: String? = null,
    val metaData: MutableMap<String, String>?  = mutableMapOf(),
    val gmtCreate: Date? = null,
    val gmtModified: Date? = null,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = IS_NOT_DELETED,
    val apRELabels: List<ApRELabelDO>? = emptyList(),
    var resources: List<ResourceDO> = emptyList(),
    //矫正成为1:1对象
    var attorneys: List<Attorney> = emptyList(),
    val limitedClusterIdList: List<String> = emptyList()
) {
    constructor(id: Long?, runtimeEnvKey: String?, name: String?, creator: String, managedClusterKey: String, region: String, az: String, stage: String, unit: String, status: String?,
                metaData: MutableMap<String, String>?, gmtCreate: Date?, gmtModified: Date?, isDeleted: String?, apRELabels: List<ApRELabelDO>?, resources: List<ResourceDO>
    ): this(
        id, runtimeEnvKey, name, creator, managedClusterKey, region, null, az, null, stage, unit, status, metaData, gmtCreate, gmtModified, isDeleted, apRELabels, resources
    )

    init {
        // 初始化针对runtimeEnvKey做转换以防止各处调用方做空字符串判断
        // 但是由于init在构造器之后执行，破坏了runtimeEnvKey属性的不可变性
        // 待找到一种方法可以在《默认构造器》构造的时候做转换
        this.runtimeEnvKey = if (runtimeEnvKey.isNullOrBlank()) null else runtimeEnvKey!!.trim()
    }
}

data class ApRELabelDO(
    val id: Long? = null,
    @JsonProperty("runtimeEnvKey")
    val targetKey: String? = null,
    @field: NotBlank
    val name: String,
    @field: NotBlank
    val value: String,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date? = null,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date? = null,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = null,
    val apRELabelKey: String? = null,
    val apREFeatureSpecs: List<ApREFeatureSpecDO>? = null,
    val source: String? = null,
    val targetType: ApRELabelTargetTypeEnum? = ApRELabelTargetTypeEnum.APRE,
    val type: ApRELabelType = COMMON,
    var title: String? = null
) {
    constructor(
        id: Long?,
        targetKey: String?,
        name: String,
        value: String,
        gmtCreate: Date?,
        gmtModified: Date?,
        isDeleted: String?,
        apRELabelKey: String?,
        apREFeatureSpecs: List<ApREFeatureSpecDO>?,
        targetType: ApRELabelTargetTypeEnum,
        type: ApRELabelType = COMMON
    ) : this(
        id,
        targetKey,
        name,
        value,
        gmtCreate,
        gmtModified,
        isDeleted,
        apRELabelKey,
        apREFeatureSpecs,
        ApREFeatureSourceEnum.CUSTOMIZED.name,
        targetType,
        type,
        title = null
    )
}

data class ApREFeatureSpecDO(
    val id: Long?,
    val apRELabelKey: String?,
    val title: String?,
    val specType: String?,
    val specCode: String?,
    val scope: String?,
    val status: String?,
    val sourceType: String?,
    val sourceId: String?,
    val versionType: String?,
    val versionId: String?,
    val annotations: String?,
    val labels: String?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String?,
    val source: String?
){
    constructor(id: Long?, apRELabelKey: String?, title: String?, specType: String?, specCode: String?, scope: String?, status: String?,
                sourceType: String?, sourceId: String?, versionType: String?, versionId: String?, annotations: String?, labels: String?, gmtCreate: Date?,gmtModified: Date?, isDeleted: String?):
            this(id, apRELabelKey, title, specType, specCode, scope, status, sourceType, sourceId, versionType, versionId, annotations, labels, gmtCreate, gmtModified, isDeleted, ApREFeatureSourceEnum.CUSTOMIZED.name)
    constructor(id: Long?, apRELabelKey: String?, title: String?, specType: String?, specCode: String?, scope: String?, status: String?,
                annotations: String?, labels: String?, gmtCreate: Date?,gmtModified: Date?, isDeleted: String?, source: String?):
            this(id, apRELabelKey, title, specType, specCode, scope, status, null, null, null, null, annotations, labels, gmtCreate, gmtModified, isDeleted, source)
    companion object{
        const val APRE_FEATURE_SPEC_LABEL_BASE_ENV_STACK_ID = "envStackId"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ResourceDO(
    /**
     * 提供给AppStack使用的集群数据模型
     * TODO 待appstack融合后即可删除
     */
    val cluster: ClusterProfile? = null,
    /**
     * 融合版本的新集群模型
     */
    val clusterProfileNew: ClusterProfileNew? = null,
    /**
     * 资源池KEY
     */
    val resourcePoolKey: String? = null,
    /**
     * 资源池涉及的Label&Spec（Serverless场景）
     */
    val apRELabels: List<ApRELabelDO> = emptyList(),
    /**
     * 集群id
     */
    val clusterId: String? = null,
)

data class ResourcePoolDO(
    val id: Long,
    @field: NotBlank
    val resourcePoolKey:String,
    @field: NotBlank
    val clusterId: String,
    @field: NotBlank
    val managedClusterKey: String,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String = IS_NOT_DELETED,
    val apRELabels: List<ApRELabelDO> = emptyList(),
){
    companion object{
        @JvmStatic
        fun assemble(resourcePoolDataDO: ResourcePoolDataDO, apRELabelDOList: List<ApRELabelDO>): ResourcePoolDO{
            return ResourcePoolDO(
                resourcePoolDataDO.id,
                resourcePoolDataDO.resourcePoolKey,
                resourcePoolDataDO.clusterId,
                resourcePoolDataDO.managedClusterKey,
                resourcePoolDataDO.gmtCreate,
                resourcePoolDataDO.gmtModified,
                resourcePoolDataDO.isDeleted,
                apRELabelDOList,
            )
        }
    }
}

