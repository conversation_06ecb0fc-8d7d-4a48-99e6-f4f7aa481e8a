package com.alibaba.koastline.multiclusters.event

import com.taobao.metaq.client.MetaProducer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 *
 * Date: 2023-04-18 Time: 13:55
 *
 * <AUTHOR>
 */
@Configuration
class MetaProducerConfig {

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    fun metaProducer(): MetaProducer {
        return MetaProducer(AQUAMAN_EVENT_PRODUCER_GROUP)
    }

    companion object {
        const val AQUAMAN_CONFIG_CHANGE_EVENT_TOPIC = "AQUAMAN_CONFIG_CHANGE_EVENT_TOPIC"
        private const val AQUAMAN_EVENT_PRODUCER_GROUP = "AquamanEventProducerGroup"
    }
}