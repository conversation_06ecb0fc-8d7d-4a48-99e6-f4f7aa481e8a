package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.resourceobj.hook.CpuSeriesFeatureImportHook.Companion.CPU_MODEL_REQUIRED
import com.alibaba.koastline.multiclusters.resourceobj.hook.CpuSeriesFeatureImportHook.Companion.CPU_MODEL_PREFERRED
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@FeatureImportHookMetaBatch(arrayOf(CPU_MODEL_REQUIRED, CPU_MODEL_PREFERRED))
class CpuSeriesFeatureImportHook: FeatureImportHook {
    @Autowired
    lateinit var commonProperties: CommonProperties


    override fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport {

        if (featureImport.paramMap.isNullOrEmpty()) {
            return featureImport
        }


        val paramMap = YamlUtils.load(featureImport.paramMap!!)
        val paramProcessed = paramMap.mapValues { (key, value) ->
            val cpuModelList = value as List<String>
            val newList = mutableListOf<String>()
            cpuModelList.forEach { cpuModel ->
                newList.addAll(commonProperties.get(cpuModel))
            }
            newList
        }


        val processedImport = featureImport.copy(
            paramMap = YamlUtils.dump(paramProcessed)
        )
        return processedImport
    }

    companion object {
        const val CPU_MODEL_REQUIRED = "cpu-model-required"
        const val CPU_MODEL_PREFERRED = "cpu-model-preferred"
    }
}