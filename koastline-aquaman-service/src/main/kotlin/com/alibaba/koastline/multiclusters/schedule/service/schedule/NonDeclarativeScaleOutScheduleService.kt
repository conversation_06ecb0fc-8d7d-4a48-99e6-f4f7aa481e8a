package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataOfSiteRepo
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractScaleOutScheduleFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component(value = "NonDeclarativeScaleOutScheduleService")
class NonDeclarativeScaleOutScheduleService @Autowired constructor(
    var apREService: ApREService,
    var metadataOfSiteRepo: MetadataOfSiteRepo
): AbstractScaleOutScheduleFacade(), InitializingBean {

    override fun getApREDeedResultWithResource(content: ScheduleRequestContent): ApREDeedResult {
        return apREService.queryClustersByApREDeedContent(
            ApREDeedDO(
                identityInfo = IdentityInfo(
                    envLevel = content.declarationData!!.declaration!!.stage,
                    appName = content.resourceScope.appName,
                    envStackId = content.resourceScope.envStackId,
                    nodeGroup = content.resourceScope.resourceGroup
                ),
                declarations = mutableListOf(
                    Declaration(
                        region = content.declarationData.declaration!!.region ?.apply {
                            metadataOfSiteRepo.findBySite(content.declarationData.declaration.site)!!.region
                        },
                        az = content.declarationData.declaration.site,
                        unit = content.declarationData.declaration.unit,
                        stage = content.declarationData.declaration.stage,
                        matchApRELabels = if (checkNotNull(content.scheduleRequestParam).serverless) {
                            scheduleStandardService.getServerlessMatchApRELabelList(
                                serverlessRuntimeTemplate = checkNotNull(content.scheduleRequestParam.serverlessRuntimeTemplate),
                                envStackId = checkNotNull(content.resourceScope.envStackId),
                                envStackPkId = content.scheduleRequestParam ?.envStackPkId
                            )
                        } else {
                            content.declarationData.declaration.matchApRELabels.map {
                                MatchApRELabel(name = it.name, value = it.value,
                                    matchApREFeatureSpecs = it.matchApREFeatureSpecs?.map { spec ->
                                        MatchApREFeatureSpec(
                                            specCode = spec.specCode,
                                            specType = spec.specType,
                                            matchFeatureSpecLabels = spec.matchFeatureSpecLabels
                                        )
                                    })
                            }
                        }
                    )
                )
            )
        )
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.NON_DECLARATIVE_SCALE_OUT_SCHEDULE, this)
    }
}