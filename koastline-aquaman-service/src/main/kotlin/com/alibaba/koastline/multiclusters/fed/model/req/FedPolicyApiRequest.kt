package com.alibaba.koastline.multiclusters.fed.model.req

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 * <AUTHOR>
 * date 2024/3/19 10:41
 */

@ApiModel("查询接入集群下的 policy, OpenAPI")
data class FedClusterPolicyApiQueryCondition(
    @ApiModelProperty("fed接入集群名称", required = true)
    val tenantCluster: String?,
    @ApiModelProperty("policy筛选条件", required = false)
    val policySelectors: List<String>? = null,
    @ApiModelProperty("查询的页大小", required = true)
    val pageSize: Int?,
    @ApiModelProperty("查询的页标号", required = true)
    val pageNumber: Int?,
) {
    fun validate(){
        require(!tenantCluster.isNullOrBlank()) { "tenantCluster required" }
        require(pageSize != null) { "pageSize required" }
        require(pageNumber != null) { "pageNumber required" }
    }
}

@ApiModel("创建fedPolicy OpenAPI")
data class FedClusterPolicyApiCreateReq(
    @ApiModelProperty("接入集群名称", required = true)
    val tenantCluster: String?,
    @ApiModelProperty("fed policy名称", required = true)
    val policyName: String?,
    @ApiModelProperty("资源环境的优先级别", required = true)
    val policyPriority: Int?,
    @ApiModelProperty("被规则影响的资源类型", required = true)
    val resourceKind: String?,
    /**
     * {
     *     "clusterOverride": [
     *         {
     *             "clusterName": "asi_zjk_shenma_test01",
     *             "clusterConfig": "a: 12"
     *         }
     *     ],
     *     "policy": {
     *         "ratios": [
     *             {
     *                 "clusterName": "asi_zjk_pai_test01",
     *                 "value": 1
     *             }
     *         ],
     *         "schedulePlugin": [
     *             "RoundRobin"
     *         ]
     *     },
     *     "policySelector": {
     *         "matchExpressions": [
     *             {
     *                 "key": "app.c2.io/zone-name-hash",
     *                 "operator": "In",
     *                 "values": [
     *                     "abcdxuan"
     *                 ]
     *             }
     *         ]
     *     }
     * }
     */
    @ApiModelProperty("接入规则", required = true)
    val policySpec: Map<String, Any>?,
    @ApiModelProperty("创建人", required = true)
    val creator: String?
){
    fun validate(){
        require(!tenantCluster.isNullOrBlank()) { "tenantCluster required" }
        require(!policyName.isNullOrBlank()) { "policyName required" }
        require(policyPriority != null) { "policyPriority required" }
        require(!resourceKind.isNullOrBlank()) { "resourceKind required" }
        require(!policySpec.isNullOrEmpty()) { "policySpec required" }
        require(!creator.isNullOrEmpty()) { "creator required" }
    }
}

@ApiModel("更新fedPolicy OpenAPI")
data class FedClusterPolicyApiUpdateReq(
    @ApiModelProperty("接入集群名称", required = true)
    val tenantCluster: String?,
    @ApiModelProperty("policy 在fed中的唯一名称,用于定位要更新的policy", required = true)
    val fedUniqueName: String?,
    @ApiModelProperty("资源环境的优先级别", required = true)
    val policyPriority: Int?,
    @ApiModelProperty("被规则影响的资源类型", required = true)
    val resourceKind: String?,
    @ApiModelProperty("接入规则", required = true)
    val policySpec: Map<String, Any>?,
    @ApiModelProperty("修改人", required = true)
    val modifier: String?
){
    fun validate(){
        require(!tenantCluster.isNullOrBlank()) { "tenantCluster required" }
        require(!fedUniqueName.isNullOrBlank()) { "fedUniqueName required" }
        require(policyPriority != null) { "policyPriority required" }
        require(!resourceKind.isNullOrBlank()) { "resourceKind required" }
        require(!policySpec.isNullOrEmpty()) { "policySpec required" }
        require(!modifier.isNullOrEmpty()) { "modifier required" }
    }
}

@ApiModel("删除fedPolicy OpenAPI")
data class FedClusterPolicyApiDeleteReq(
    @ApiModelProperty("接入集群名称", required = true)
    val tenantCluster: String?,
    @ApiModelProperty("policy 在fed中的唯一名称,用于定位要更新的policy", required = true)
    val fedUniqueName: String?,
    @ApiModelProperty("修改人", required = true)
    val modifier: String?
) {
    fun validate(){
        require(!tenantCluster.isNullOrBlank()) { "tenantCluster required" }
        require(!fedUniqueName.isNullOrBlank()) { "fedUniqueName required" }
        require(!modifier.isNullOrEmpty()) { "modifier required" }
    }
}