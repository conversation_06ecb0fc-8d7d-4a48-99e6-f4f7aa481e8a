package com.alibaba.koastline.multiclusters.fed.model.req

import com.alibaba.koastline.multiclusters.fed.model.FedClusterStatus
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("当前的资源对象")
data class FedClusterRegisterReq(
    @ApiModelProperty("联邦集群环境名称", required = true)
    val fedClusterEnvName: String,
    @ApiModelProperty("地区", required = true)
    val region: String,
    @ApiModelProperty("机房", required = false)
    val site: String? = null,
    @ApiModelProperty("接入集群", required = true)
    val tenantClusterName: String,
    @ApiModelProperty("关联集群", required = true)
    val memberClustersNameList: List<String>,
    @ApiModelProperty("创建人", required = true)
    val creator: String
) {
    fun validate() {
        require(fedClusterEnvName.isNotBlank()) {
            "fedClusterEnvName cannot be blank ！"
        }
        require(region.isNotBlank()) {
            "region cannot be blank !"
        }
        site?.let {
            require(it.isNotBlank()) {
                "site cannot be blank!"
            }
        }

        require(tenantClusterName.isNotBlank()) {
            "tenantClusterName cannot be blank!"
        }
        require(memberClustersNameList.all {
            it.isNotBlank()
        }) {
            "member cluster name cannot be blank!"
        }

        require(memberClustersNameList.distinct().size == memberClustersNameList.size) {
            "member cluster have non-unique cluster name!"
        }
    }
}

@ApiModel("fed集群状态更新")
data class FedClusterUpdateReq(
    @ApiModelProperty("fed集群数据编号", required = true)
    val id: Long,
    @ApiModelProperty("fed集群状态", required = false)
    val status: String? = null,
    @ApiModelProperty("fed集群环境名称", required = false)
    val fedEnvName: String? = null,
    @ApiModelProperty("修改人", required = true)
    val modifier: String
) {
    fun validate() {
        require(modifier.isNotBlank()) {
            "modifier cannot be blank!"
        }
        status?.let {
            FedClusterStatus.valueOf(it)
        }
        fedEnvName?.let {
            require(it.isNotBlank()) {
                "fedEnvName cannot be blank"
            }
        }
    }
}

@ApiModel("fed条件过滤搜索")
data class FedClusterConditionQueryReq(
    @ApiModelProperty("fed集群region", required = false)
    val region: String? = null,
    @ApiModelProperty("fed接入集群", required = false)
    val tenantClusterName: String? = null,
    @ApiModelProperty("fed关联集群", required = false)
    val memberClusterName: String? = null,
    @ApiModelProperty("集群环境关键字", required = false)
    val envNameKeyWords: String? = null,
    @ApiModelProperty("环境状态", required = false)
    val status: String? = null,
    @ApiModelProperty("环境状态", required = true)
    val pageNumber: Int,
    @ApiModelProperty("环境状态", required = true)
    val pageSize: Int,
)

@ApiModel("fed添加新的集群")
data class FedMemberClusterCreateReq(
    @ApiModelProperty("id", required = true)
    val id: Long,
    @ApiModelProperty("添加的集群名称list", required = true)
    val toAddFedMemberList: List<String>,
    @ApiModelProperty("修改人", required = true)
    val modifier: String
) {
    fun validate() {
        require(toAddFedMemberList.all { it.isNotBlank() }) {
            "toAddFedMember cannot be blank!"
        }
        require(toAddFedMemberList.distinct().size == toAddFedMemberList.size) {
            "toAddFedMemberList have none-unique cluster Name"
        }
        require(modifier.isNotBlank()) {
            "modifier cannot be blank!"
        }
    }
}

@ApiModel("fed删除已绑定的集群")
data class FedMemberClusterDeleteReq(
    @ApiModelProperty("id", required = true)
    val id: Long,
    @ApiModelProperty("删除集群的名称", required = true)
    val toDropMember: String,
    @ApiModelProperty("修改人", required = true)
    val modifier: String
) {
    fun validate() {
        require(toDropMember.isNotBlank()) {
            "toDropMember cannot be blank!"
        }
        require(modifier.isNotBlank()) {
            "modifier cannot be blank!"
        }
    }
}


