package com.alibaba.koastline.multiclusters.resourceobj.model

import com.alibaba.koastline.multiclusters.apre.model.Restriction

/**
 * 资源对象特性导入为Asi提供
 *
 * <AUTHOR>
 * @date 2025/3/27 14:28
 **/
data class ResourceObjectFeatureImportForAsiDO(

    /**
     * 特性Key
     */
    val traitKey: String,

    /**
     * 外部类型
     */
    val externalType: String,

    /**
     * 外部Id
     */
    val externalId: String,

    /**
     * 目标Id
     */
    val targetId: Long,

    /**
     * 预置参数映射
     */
    val formData: Map<String, Any>?,

    /**
     * 生效的限制条件
     */
    val restrictions: List<Restriction>?,
)
