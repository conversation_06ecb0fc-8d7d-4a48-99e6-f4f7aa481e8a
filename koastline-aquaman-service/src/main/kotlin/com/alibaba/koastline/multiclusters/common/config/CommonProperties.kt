package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.koastline.multiclusters.common.logger
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
class CommonProperties {
    var properties = CommonDiamondConfig.buildProperties()
    val log by logger()

    fun contains(key: String, value: String): Boolean {
        return properties[key] ?.contains(value) ?:false
    }

    fun firstOrNull(key: String): String? {
        return properties[key]?.firstOrNull()
    }

    fun get(key: String): List<String> {
        return properties[key] ?: emptyList()
    }

    fun keepCache(): Boolean {
        val rs = !contains(REDIS_DEGRADE_CONFIG, ALL)
        if (!rs) {
            log.warn("cache degrade~~")
        }
        return rs
    }

    companion object {
        const val LOW_RANK_MODIFIER = "LOW_RANK_MODIFIER"
        const val ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG = "ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG"
        const val ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG = "ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG"
        const val ATOM_SWITCH_APP_WHITE_LIST = "ATOM_SWITCH_APP_WHITE_LIST"
        const val ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST = "ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST"
        const val ATOM_AQUAMAN_CHECK_SWITCH = "ATOM_AQUAMAN_CHECK_SWITCH"
        const val VIDEO_CLOUD_APP_LIST = "VIDEO_CLOUD_APP_LIST"
        const val SERVER_OWNER_APP_LIST = "SERVER_OWNER_APP_LIST"
        const val ASI_MIX_CLUSTER_NAME_LIST_CONFIG = "ASI_MIX_CLUSTER_NAME_LIST_CONFIG"
        const val FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG = "FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG"
        const val DATA_BASE_OPEN_LOG_SCENE_CONFIG = "DATA_BASE_OPEN_LOG_SCENE_CONFIG"
        const val REDIS_DEGRADE_CONFIG = "REDIS_DEGRADE_CONFIG"

        /**
         * SPE定制化应用列表
         */
        const val SPE_EXTRA_APP_LIST = "SPE_EXTRA_APP_LIST"
        const val ALL = "*"
        const val SCALE_OUT_POST_CHECK = "SCALE_OUT_POST_CHECK"

        /*
        batch size limit setting for api request
         */
        const val BATCH_SIZE_LIMIT = "BATCH_SIZE_LIMIT"
        const val DEFAULT_BATCH_SIZE_LIMIT = 999
        const val LIGHTWEIGHT_IMAGE = "LIGHTWEIGHT_IMAGE"

        // asi traitKey scope
        const val PAGE_QUERY_ASI_AWARE_TRAIT_KEY = "ASI_AWARE_TRAIT_KEYS"
        const val LOCAL_APP_2_GROUP = "LOCAL_APP_2_GROUP"

        // SPE Diamond 多应用发布
        const val SPE_DIAMOND_MULTI_APP_PUBLISH = "SPE_DIAMOND_MULTI_APP_PUBLISH"
    }
}