package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.model.AppDeployWay
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.runtime.RuntimeCoordinateResourceCreateReq
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.AppOfRuntimeBaseReq
import com.alibaba.koastline.multiclusters.schedule.model.AppOfRuntimeBaseResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.RUNTIME
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class RuntimeScheduleService @Autowired constructor(
    val apREService: ApREService,
    val apREFeatureSpecService: ApREFeatureSpecService,
    val defaultClusterService: DefaultClusterService,
    val resourcePoolService: ResourcePoolService,
    val scheduleStandardService: ScheduleStandardService,
    val skylineApi: SkylineApi,
    val userLabelService: UserLabelService,
    val runtimeWorkloadMetaService: RuntimeWorkloadMetaService,
    val apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService,
    val apREDeclarationPatchService: ApREDeclarationPatchService,
    val apREDeedService: ApREDeedService,
    val appCenterApi: AppCenterApi,
    val userLabelBaseService: UserLabelBaseService
) {

    /**
     * 搜索可使用的合法坐标 剔除已经注册了的坐标
     *
     * @param appName
     * @param resourceGroup
     * @param scheduleEnvType
     * @return
     */
    fun seekAvailableRuntimeCoordinates(
        appName: String, resourceGroup: String, scheduleEnvType: ScheduleEnvType
    ): ScheduleResult {
        require(appName.isNotBlank()) { "appName cannot be blank in seek available runtime coordinates" }
        require(resourceGroup.isNotBlank()) { "appName cannot be blank in seek available runtime coordinates" }
        if (scheduleEnvType != RUNTIME) {
            throw ScheduleException("unsupported schedule env type!")
        }
        val deedKey = apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup)?.apREDeedKey
            ?: throw ApREException("缺少分组资源声明，请到环境绑定分组详情页面，填写分组资源声明!")

        val runningRuntimeWorkloadMetaList = runtimeWorkloadMetaService.listRunningWorkloadList(
            appName = appName, resourceGroup = resourceGroup
        )

        // ApRE 再requireApREDeedResultByApREDeedContent中已经merge所有分区下资源
        // 优先deed授权的站点被ApREDeclaration管控可选的站点范围
        val deed = apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(
            apREDeedService.findApREDeedByKey(deedKey)
        )
        val deedResult = apREService.queryClustersByApREDeedContent(deed)

        val availableApRE = deedResult.matchDeclarations.map { it.apres }.flatten().distinctBy { it.runtimeEnvKey }
        val availableCoordinates = mutableListOf<WorkloadExpectedState>()
        availableApRE.forEach { apRE ->
            val clusterList = apRE.resources.map { it.clusterProfileNew!! }
            clusterList.forEach { cluster ->
                val toAddMeta = WorkloadExpectedState(
                    WorkloadMetadataConstraint(
                        appName = appName,
                        resourceGroup = resourceGroup,
                        site = apRE.az,
                        unit = apRE.unit,
                        stage = apRE.stage,
                        clusterId = cluster.clusterId,
                    ).let {
                        it.copy(
                            namespace = scheduleStandardService.generateNameSpace(it),
                            runtimeId = it.resolveBaseAppRuntimeId()
                        )
                    },
                    clusterProfile = cluster,
                    params = emptyMap()
                )
                if(!matchRunningRuntimeMeta(runningRuntimeWorkloadMetaList, toAddMeta.workloadMetadataConstraint)){
                    availableCoordinates.add(
                        toAddMeta
                    )
                }
            }
        }
        return ScheduleResult(availableCoordinates)
    }

    /**
     * 预添加的Workload元数据是否跟运行中Workload列表匹配
     */
    private fun matchRunningRuntimeMeta(runningWorkloadMetaList: List<RuntimeWorkloadMeta>,
                             toAddWorkloadMeta: WorkloadMetadataConstraint): Boolean{
        return runningWorkloadMetaList.any { runningWorkloadMeta ->
            runningWorkloadMeta.toSixMetaTuple() == toAddWorkloadMeta.toSixMetaTuple()
        }
    }

    /**
     * 注册workloadRuntime并且挂载至ApRE上
     *
     * @param registerReq
     */
    fun registerWorkloadAndMountResource(
        registerReq: RuntimeCoordinateResourceCreateReq
    ) {
        runtimeWorkloadMetaService.registryRuntimeWorkload(
            workloadMetadataConstraint = registerReq.workloadMetadataConstraint,
            annotations = JsonUtils.readValue<Map<String, String>>(
                registerReq.annotations,
                JsonUtils.objectTypeReference()
            ),
            labels = JsonUtils.readValue<Map<String, String>>(registerReq.labels, JsonUtils.objectTypeReference()),
            creator = registerReq.creator,
            workloadVisitable = isGroupHided(registerReq.workloadMetadataConstraint.resourceGroup)
        )
    }

    /**
     * 获取运行时running workload
     *
     * @param appName
     * @param envStackId
     * @param resourceGroup
     * @param scheduleEnvType
     * @return
     */
    fun requireRunningWorkload(
        appName: String,
        envStackId: String? = null,
        resourceGroup: String? = null,
        scheduleEnvType: ScheduleEnvType
    ): ScheduleResult {
        require(scheduleEnvType == RUNTIME) { "only support serverless runtime workload list" }
        val runtimeWorkloadList = runtimeWorkloadMetaService.listRunningWorkloadList(
            appName = appName, envStackId = envStackId, resourceGroup = resourceGroup
        )
        val matchOnlineClustersMap =
            resourcePoolService.listClusterProfileNew(runtimeWorkloadList.map { it.clusterId }.distinct())
                .filter { it.status == ClusterStatus.ONLINE.status }.associateBy { it.clusterId }
        val workloadExpectedStates = mutableListOf<WorkloadExpectedState>()
        runtimeWorkloadList.forEach { runtimeWorkloadMeta ->
            if (matchOnlineClustersMap.containsKey(runtimeWorkloadMeta.clusterId)) {
                workloadExpectedStates.add(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(
                            appName = appName,
                            resourceGroup = runtimeWorkloadMeta.resourceGroup,
                            site = runtimeWorkloadMeta.site,
                            stage = runtimeWorkloadMeta.stage,
                            unit = runtimeWorkloadMeta.unit,
                            clusterId = runtimeWorkloadMeta.clusterId,
                            namespace = runtimeWorkloadMeta.nameSpace,
                            runtimeId = runtimeWorkloadMeta.runtimeWorkloadId
                        ),
                        clusterProfile = matchOnlineClustersMap[runtimeWorkloadMeta.clusterId],
                        params = emptyMap()
                    )
                )
            }
        }
        return ScheduleResult(workloadExpectedStates)
    }

    fun cancelWorkloadWhileUnmountResource(workloadMetadataConstraint: WorkloadMetadataConstraint, modifier: String) {
        runtimeWorkloadMetaService.cancelRunningRuntimeWorkload(workloadMetadataConstraint, modifier)
    }

    fun setGroupHided(resourceGroup: String, status: String, modifier: String) {
        require(resourceGroup.isNotBlank()) { "resource group cannot be blank" }
        require(status == RUNTIME_STATUS_ONLINE || status == RUNTIME_STATUS_OFFLINE) { "status must be online or offline" }

        userLabelService.createAndOverrideWhileExist(
            UserLabelCreateReqDto(
                externalId = resourceGroup, externalType = UserLabelExternalType.RESOURCE_GROUP,
                labelName = RUNTIME_WORKLOAD_VISIBLE, labelValue = status, creator = modifier
            )
        )

        val appName = skylineApi.getAppGroup(resourceGroup).appName
        val runtimeWorkloadList = runtimeWorkloadMetaService.listRunningWorkloadList(
            appName = appName, resourceGroup = resourceGroup
        )
        runtimeWorkloadList.forEach { runtimeWorkloadMeta ->
            val (matchLabel, _) = runtimeWorkloadMetaService.requireMatchApRELabelWithResource(
                unit = runtimeWorkloadMeta.unit,
                stage = runtimeWorkloadMeta.stage,
                site = runtimeWorkloadMeta.site,
                clusterId = runtimeWorkloadMeta.clusterId,
                appName = runtimeWorkloadMeta.appName
            )
            // 定位到spec 更新runtime workload状态 同时更新将spec id中的status 一并进行更新
            matchLabel ?.apREFeatureSpecs?.firstOrNull {
                runtimeWorkloadMetaService.matchSpec(
                    apREFeatureSpecDO = it,
                    runtimeId = runtimeWorkloadMeta.runtimeWorkloadId
                )
            } ?.let { spec ->
                updateApREFeatureSpecStatus(spec, status)
            }
        }
    }

    private fun isGroupHided(resourceGroup: String): Boolean {
        return userLabelBaseService.findByExternalAndLabelWithCache(
            externalId = resourceGroup,
            externalType = UserLabelExternalType.RESOURCE_GROUP.name,
            labelName = RUNTIME_WORKLOAD_VISIBLE
        ) ?.run {
            this.labelValue == RUNTIME_STATUS_ONLINE
        } ?:false
    }

    fun setGroupBuffer(resourceGroup: String, bufferConfig: String, modifier: String) {
        require(resourceGroup.isNotBlank()) { "resource group cannot be blank" }
        require(bufferConfig.isNotBlank()) { "bufferConfig cannot be blank" }

        userLabelService.createAndOverrideWhileExist(
            UserLabelCreateReqDto(
                externalId = resourceGroup, externalType = UserLabelExternalType.RESOURCE_GROUP,
                labelName = RUNTIME_WORKLOAD_BUFFER, labelValue = bufferConfig, creator = modifier
            )
        )
    }

    fun isStandardRuntimeSchedule(scheduleEnvType: ScheduleEnvType?, appName: String): Boolean {
        scheduleEnvType ?.let {
            if (scheduleEnvType != RUNTIME) {
                return false
            }
        }
        val appInfo = appCenterApi.getAppBasInfoByName(appName)
        if (appInfo.deployWay == AppDeployWay.runtime.name) {
            return true
        }
        return false
    }

    /**
     * 计算基座资源分布
     */
    fun listAppOfRuntimeBase(
        appOfRuntimeBaseReq: AppOfRuntimeBaseReq
    ): AppOfRuntimeBaseResult {
        val runningWorkload = runtimeWorkloadMetaService.listRunningWorkloadList(
            appName = appOfRuntimeBaseReq.runtimeBaseAppName,
            envStackId = appOfRuntimeBaseReq.runtimeBaseEnvStackId,
            resourceGroup = appOfRuntimeBaseReq.runtimeBaseResourceGroup
        )
        return AppOfRuntimeBaseResult(
            runtimeBaseAppName = appOfRuntimeBaseReq.runtimeBaseAppName,
            runtimeBaseEnvStackId = appOfRuntimeBaseReq.runtimeBaseEnvStackId,
            runtimeBaseResourceGroup = appOfRuntimeBaseReq.runtimeBaseResourceGroup,
            appResourceList = skylineApi.listAppOfRuntimeBase(
                runtimeWorkloadIds = runningWorkload.map { it.runtimeWorkloadId }
            )
        )
    }

    fun batchListAppOfRuntimeBase(
        appOfRuntimeBaseReqList: List<AppOfRuntimeBaseReq>
    ): List<AppOfRuntimeBaseResult> {
        return appOfRuntimeBaseReqList.map { appOfRuntimeBaseReq ->
            listAppOfRuntimeBase(appOfRuntimeBaseReq)
        }
    }

    private fun updateApREFeatureSpecStatus(spec: ApREFeatureSpecDO, status: String) {
        apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(
            spec.copy(
                status = status
            ).toUpdateDto()
        )
    }

    companion object {
        const val RUNTIME_WORKLOAD_VISIBLE = "RUNTIME_VISIBILITY"
        const val RUNTIME_WORKLOAD_BUFFER = "RUNTIME_BUFFER_CONFIG"
        const val RUNTIME_STATUS_OFFLINE = "offline"
        const val RUNTIME_STATUS_ONLINE = "online"
    }
}

fun ApREFeatureSpecDO.toUpdateDto() =
    ApREFeatureSpecCreateReqDto(
        apRELabelKey = this.apRELabelKey,
        title = this.title!!,
        specType = this.specType!!,
        specCode = this.specCode!!,
        scope = ApREFeatureSpecScopeEnum.valueOf(this.scope!!),
        status = ApREFeatureSpecStatusEnum.valueOf(this.status!!),
        sourceType = this.sourceType,
        sourceId = this.sourceId,
        versionType = this.versionType,
        versionId = this.versionId,
        annotations = JsonUtils.readValue<Map<String, String>>(this.annotations!!, JsonUtils.objectTypeReference()),
        labels = JsonUtils.readValue<Map<String, String>>(this.labels!!, JsonUtils.objectTypeReference())
    )
