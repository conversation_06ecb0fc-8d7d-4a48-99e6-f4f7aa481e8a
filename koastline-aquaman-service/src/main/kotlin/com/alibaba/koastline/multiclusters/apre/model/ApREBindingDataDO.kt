package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.data.vo.PageData

/**
 * 按照类型的将授权信息完全展示出来
 *
 * @property externalType
 * @property externalId
 * @property pageData
 */
data class AttorneyScope(
    val externalType: String,
    val externalId: String,
    val pageData: PageData<AttorneyApRE>
)

/**
 * 授权的ApRE以及是继承类是自定义类别
 *
 * @property apREName
 * @property apREKey
 * @property unit
 * @property site
 * @property stage
 * @property supportedCluster
 * @property supportedFeatureLabel
 */
data class AttorneyApRE(
    val apREName: String,
    val apREKey: String,
    val unit: String,
    val site: String,
    val stage: String,
    val includeScopeType: IncludeScopeType,
    val supportedCluster: List<String>,
    val supportedFeatureLabel: List<ApRELabelDO>,
    val attorney: Attorney
)