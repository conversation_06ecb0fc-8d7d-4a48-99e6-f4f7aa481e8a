package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.exceptions.AppCenterException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.SignUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component


@Component
class AppCenterApi(val objectMapper: ObjectMapper) {
    val log by logger()
    @Value("\${appcenter.host}")
    lateinit var appCenterHost: String
    @Value("\${appcenter.account}")
    lateinit var appCenterAccount: String
    @Value("\${appcenter.access.key}")
    lateinit var appCenterAccessKey: String
    @Autowired
    lateinit var redisService: RedisService

    @ExternalCall(SYS_CALLED)
    fun getAppInfoByName(appName: String): AppInfo {
        return HttpClientUtils.httpGet("$appCenterHost$URL_GET_APP_INFO_BY_NAME",
            getSignParams().run {
                this["appName"] = appName
                this
            }
        ).run {
            val res = objectMapper.readValue<AppCenterResponse<AppInfo>>(this)
            if (!res.successful) {
                throw AppCenterException("查询应用信息失败，appName:${appName}，错误码：${res.errorCode},错误信息：${res.errorMsg}")
            }
            res.data!!
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getAppBasInfoByName(appName: String): AppBasInfo {
        return HttpClientUtils.httpGet("$appCenterHost$URL_GET_APP_BAS_INFO_BY_NAME",
            getSignParams().run {
                this["appName"] = appName
                this
            }
        ).run {
            val res = objectMapper.readValue<AppCenterResponse<AppBasInfo>>(this)
            if (!res.successful) {
                throw AppCenterException("查询应用信息失败，appName:${appName}，错误码：${res.errorCode},错误信息：${res.errorMsg}")
            }
            res.data!!
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getAppInfoByNameV2(appName: String): AppInfoV2 {
        redisService.getValue("$URL_GET_APP_INFO_BY_NAME$appName")?.let { cacheVal ->
            return objectMapper.readValue<AppInfoV2>(cacheVal)
        }
        return HttpClientUtils.httpGet("$appCenterHost$URL_NEW_GET_APP_INFO_BY_NAME$appName",
            getSignParams().run {
                this
            }
        ).run {
            val res = objectMapper.readValue<AppCenterResponse<AppInfoV2>>(this)
            if (!res.successful) {
                throw AppCenterException("查询应用信息失败，appName:${appName}，错误码：${res.errorCode},错误信息：${res.errorMsg}")
            }
            redisService.setValueWithExpire(
                "$URL_GET_APP_INFO_BY_NAME$appName",
                objectMapper.writeValueAsString(res.data!!),
                60 * 10
            )
            res.data!!
        }
    }

    @ExternalCall(SYS_CALLED)
    fun isCloudNativeApp(appName: String): Boolean {
        return getAppBasInfoByName(appName).let {
            it.deploySys == "appstack" && it.deployWay != "runtime" && it.deployWay != "serverless"
        }
    }

    @ExternalCall(SYS_CALLED)
    fun isRuntimeApp(appName: String): Boolean {
        return getAppBasInfoByName(appName).run {
            this.deployWay == AppDeployWay.runtime.name
        }
    }

    @ExternalCall(SYS_CALLED)
    fun whetherAppInAppstackAoneInfusionGray(appName: String): Boolean {
        return HttpClientUtils.httpGet("$appCenterHost$URL_IS_GREY_APP",
            getSignParams().run {
                this["greyCode"] = "AONE_ENV_NEW_PAGE_GRAY_APPS"
                this["greyValue"] = getAppInfoByName(appName).id.toString()
                this
            }
        ).run {
            val res = objectMapper.readValue<AppCenterResponse<Boolean>>(this)
            if (!res.successful) {
                throw AppCenterException("检查应用是否在融合灰度失败，appName:${appName}，错误码：${res.errorCode},错误信息：${res.errorMsg}")
            }
            res.data!!
        }
    }

    /**
     * 获取应用标签
     */
    @ExternalCall(SYS_CALLED)
    fun getAppTagByAppName(appName: String, tagName: String): AppTag? {
        return HttpClientUtils.httpGet("$appCenterHost$URL_GET_KV_TAG",
            getSignParams().run {
                this["appIdentifier"] = appName
                this["tagName"] = tagName
                this["sysName"] = "scmenv"
                this
            }
        ).run {
            val res = objectMapper.readValue<AppCenterResponse<AppCenterResult>>(this)
            if (!res.successful) {
                throw AppCenterException("查询应用标签失败，appName:${appName}，错误码：${res.errorCode},错误信息：${res.errorMsg}")
            }
            if (res.data?.valid != true) {
                return null
            }
            return objectMapper.readValue<AppCenterResponse<AppTag>>(this).data
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getTestingResourceSpec(appName: String): ResourceSpec? {
        try {
            val appTag = getAppTagByAppName(
                appName = appName,
                tagName = "testing_spec"
            )
            if (null == appTag || appTag.tagValue.isNullOrBlank()) {
                return null
            }
            val specArray = appTag.tagValue.split("-")
            return ResourceSpec(
                cpu = specArray[0],
                memory = (specArray[1].toInt()/1024).toString(),
                disk = specArray[2]
            )
        } catch (e: Exception) {
            log.error(e.message, e)
        }
        return null
    }

    private fun getSignParams(): MutableMap<String, String> {
        return mutableMapOf(
            "__token__" to SignUtils.getAppCenterToken(accessKey = appCenterAccessKey),
            "sys_name" to appCenterAccount
        )
    }

    companion object {
        const val URL_GET_APP_INFO_BY_NAME = "/appinfo/v1/app/ext/getAppInfoByName"
        const val URL_GET_APP_BAS_INFO_BY_NAME = "/appinfo/v1/app/ext/getAppBasInfoByName"
        const val URL_NEW_GET_APP_INFO_BY_NAME = "/appinfo/v3/objects/"
        const val URL_IS_GREY_APP = "/appinfo/v1/app/ext/isGreyApp"
        const val URL_GET_KV_TAG = "/appinfo/v1/app/tag/ext/getKVTag"
        const val SYS_CALLED = "app-center"
    }
}