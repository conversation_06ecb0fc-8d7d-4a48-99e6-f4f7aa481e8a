package com.alibaba.koastline.multiclusters.apre.base

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.formatUnit
import com.alibaba.koastline.multiclusters.apre.model.MetadataConstraintDO
import com.alibaba.koastline.multiclusters.apre.model.MetadataOfSiteDO
import com.alibaba.koastline.multiclusters.common.exceptions.*
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataConstraintRepo
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataOfSiteRepo
import com.alibaba.koastline.multiclusters.data.utils.DataValidateUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.MetadataConstraint
import com.alibaba.koastline.multiclusters.data.vo.env.MetadataOfSite
import com.alibaba.koastline.multiclusters.external.HcrmApi
import com.alibaba.koastline.multiclusters.external.model.UnitGroup
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.*

@Component
class MetadataService{
    val log by logger()

    @Autowired
    lateinit var metadataConstraintRepo: MetadataConstraintRepo
    @Autowired
    lateinit var metadataOfSiteRepo: MetadataOfSiteRepo
    @Autowired
    lateinit var hcrmApi: HcrmApi

    /**
     * 创建元数据约束(站点、单元、用途)
     * 1.POD离线数据统计做数据初始化;2.运行时环境增加四元组数据限定
     */
    fun createMetadataConstraint(site: String, unit: String, stage: String, creator: String): MetadataConstraintDO {
        metadataConstraintRepo.findByMetadataConstraint(site, formatUnit(unit)!!, stage) ?.let {
            throw MetadataConstraintUniqueExistException(it.site, it.unit, it.stage)
        }
        val now = Date(Instant.now().toEpochMilli())
        val metadataConstraint = MetadataConstraint(
            null,
            site,
            formatUnit(unit)!!,
            stage,
            now,
            now,
            Constants.IS_NOT_DELETED,
            creator,
            creator
        )
        metadataConstraintRepo.insert(metadataConstraint).let {
            if (it == 0 ) throw MetadataException("create metadata constraint causes exception")
        }
        return convert(metadataConstraint)
    }

    fun listMetadataConstraintByProperties(
        site: String?, unit: String?, stage: String?, keyWords: String?, pageNumber: Int, pageSize: Int
    ): PageData<MetadataConstraintDO> {

        val queryProperties = mutableListOf(
            site, unit, stage, keyWords
        )
        check(DataValidateUtils.hasNotNullProperty(queryProperties)) { "list filter conditions should be more than 0 conditions at least!" }

        val page: Page<MetadataConstraintDO> = PageHelper.startPage<MetadataConstraintDO>(pageNumber, pageSize, "gmt_modified DESC")
            .doSelectPage {
                metadataConstraintRepo.listByProperties(
                    site = site, unit = formatUnit(unit), stage = stage, keyWords = keyWords,
                ).map { convert(it) }
            }
        return PageData.transformFrom(page)
    }

    fun listSiteByUnitAndStage(unit: String, stage: String): List<MetadataConstraintDO> {
        check(DataValidateUtils.notEmptyAndAllNotBlank(listOf(unit, stage))) {
            "unit and site cannot be blank in listSiteByUnitAndStage"
        }
        return metadataConstraintRepo.listByUnitAndStage(unit = unit, stage = stage).map { convert(it) }
    }

    fun listAllMetadataConstraint(): List<MetadataConstraintDO> {
        return metadataConstraintRepo.listAll().map {
            convert(it)
        }
    }

    fun deleteByMetadataConstraint(site: String, unit: String, stage: String, modifier: String) {
        metadataConstraintRepo.deleteByMetadataConstraint(site, unit, stage, modifier) .let {
            if (it ==0) throw MetadataConstraintNotFoundException(site, unit, stage)
        }
    }

    fun checkMetadataConstraint(site: String, unit: String, stage: String): Boolean {
        return metadataConstraintRepo.findByMetadataConstraint(site, unit, stage) ?.run {
            true
        } ?:run {
            false
        }
    }

    fun checkMetadataConstraint(region: String, site: String, unit: String, stage: String): Boolean {
        metadataOfSiteRepo.findBySiteAndRegion(site, region) ?:let {
            return false
        }
        return metadataConstraintRepo.findByMetadataConstraint(site, unit, stage) ?.run {
            true
        } ?:run {
            false
        }
    }

    fun checkMetadataConstraintWithError(region: String, site: String, unit: String, stage: String) {
        metadataOfSiteRepo.findBySiteAndRegion(site, region) ?:let {
            throw MetadataException("站点：{$site, $region}不存在")
        }
        metadataConstraintRepo.findByMetadataConstraint(site, unit, stage) ?:run {
            throw MetadataException("运维元数据约束：{$site, $unit,$stage}不存在")
        }
    }

    fun listMetadataOfSiteByProperties(
        site: String?, region:String?, pageSize:Int, pageNumber:Int
    ):PageData<MetadataOfSiteDO>{
        val queryProperties = mutableListOf(
            site, region
        )
        check(DataValidateUtils.hasNotNullProperty(queryProperties)){ "list filter conditions should be more than 0 conditions at least!" }

        val page: Page<MetadataOfSiteDO> = PageHelper.startPage<MetadataOfSite>(pageNumber, pageSize,"gmt_modified DESC")
            .doSelectPage{
                metadataOfSiteRepo.listByProperties(
                    site = site, region = region
                ).map { convert(it) }
            }
        return PageData.transformFrom(page)
    }

    fun listAllMetadataOfSite():List<MetadataOfSiteDO>{
        val sites = metadataOfSiteRepo.listAll()
        return sites.map {
            convert(it)
        }
    }

    fun createMetadataOfSite(site: String, region: String, creator: String): MetadataOfSiteDO {
        metadataOfSiteRepo.findBySite(site)?.let {
            throw MetadataOfSiteUniqueExistException(site)
        }
        val now = Date(Instant.now().toEpochMilli())
        val metadataOfSite = MetadataOfSite(
            id = null,
            site = site,
            region = region,
            gmtCreate = now,
            gmtModified = now,
            isDeleted = Constants.IS_NOT_DELETED,
            creator = creator,
            modifier = creator
        )
        metadataOfSiteRepo.insert(metadataOfSite).let {
            if (it == 0) throw MetadataException("create metadata of site causes exception")
        }
        return convert(metadataOfSite)
    }

    fun getMetadataOfSite(site: String): MetadataOfSiteDO? {
        metadataOfSiteRepo.findBySite(site) ?.let {
            return convert(it)
        }
        return null
    }

    fun deleteMetadataOfSite(site: String, modifier:String) {
        metadataOfSiteRepo.deleteBySite(site, modifier).let {
            if (it == 0) throw MetadataOfSiteNotFoundException(site)
        }
    }

    fun checkMetadataOfSiteAndRegion(site: String, region: String): Boolean {
        metadataOfSiteRepo.findBySite(site) ?.let {
            return region == it.region
        }
        return false
    }

    /**
     * 查询统一设置（除显式声明外）的分组单元列表配置
     */
    fun listUnitizationUnitGroup(appName: String, appGroupName: String): List<UnitGroup> {
        return hcrmApi.listUnitizationUnitGroup(
            appName = appName,
            appGroupName = appGroupName,
        )
    }

    private fun convert(metadataConstraint: MetadataConstraint): MetadataConstraintDO {
        return MetadataConstraintDO(
            metadataConstraint.id,
            metadataConstraint.site,
            metadataConstraint.unit,
            metadataConstraint.stage,
            metadataConstraint.gmtCreate,
            metadataConstraint.gmtModified,
            metadataConstraint.isDeleted,
            metadataConstraint.creator,
            metadataConstraint.modifier
        )
    }

    private fun convert(metadataOfSite: MetadataOfSite): MetadataOfSiteDO {
        return MetadataOfSiteDO(
            metadataOfSite.id,
            metadataOfSite.site,
            metadataOfSite.region,
            metadataOfSite.creator,
            metadataOfSite.modifier,
            metadataOfSite.gmtCreate,
            metadataOfSite.gmtModified,
            metadataOfSite.isDeleted
        )
    }
}