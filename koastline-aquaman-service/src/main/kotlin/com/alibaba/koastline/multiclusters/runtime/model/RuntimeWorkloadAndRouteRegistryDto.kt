package com.alibaba.koastline.multiclusters.runtime.model

import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadRunningStatus
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadStatus

data class RuntimeWorkloadAndRouteRegistryDto(
    val appName: String,
    val runtimeKey: String,
    val resourceGroup: String,
    val operator: String,
    val runtimeWorkloadReqDtoList: List<RuntimeWorkloadReqDto>
)

data class RuntimeWorkloadReqDto (
    val site: String,
    val stage: String,
    val unit: String,
    val clusterId: String,
    val status: RuntimeWorkloadStatus,
    val runningStatus: RuntimeWorkloadRunningStatus,
    /**
     * RuntimeWorkload路由信息
     */
    val routeMessage: RuntimeWorkloadRouteMessage?
)

data class RuntimeWorkloadRouteMessage (
    val title: String,
    val scope: ApREFeatureSpecScopeEnum,
    val status: ApREFeatureSpecStatusEnum,
    val annotations: Map<String, String>
)
