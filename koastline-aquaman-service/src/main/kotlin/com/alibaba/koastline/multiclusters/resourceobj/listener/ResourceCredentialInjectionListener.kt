package com.alibaba.koastline.multiclusters.resourceobj.listener

import com.alibaba.cse.models.v1alpha1.cloneset.CloneSet
import com.alibaba.cse.models.v1alpha1.cloneset.CloneSetSpec
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import io.kubernetes.client.openapi.models.V1Container
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * @author: fudai.yf
 * @since: 2023/10/10
 */
@Component
class ResourceCredentialInjectionListener: ResourceObjectFeatureImportListener, InitializingBean {
    val log by logger()
    @Autowired
    private lateinit var resourceObjectFeatureImportListenerFactory: ResourceObjectFeatureImportListenerFactory

    /**
     * Watch feature import for resource credential injection.
     *
     * This is a conflict when **aliyun sls logtail envs** exist both in **the base main container spec** and
     * **the feature main container spec**, in which case the function will throw an **FeatureConflictException**.
     */
    override fun watch(
        baseResourceObjectSpec: String,
        resourceObjectProtocol: ResourceObjectProtocolEnum,
        featureSpec: String
    ) {
        // Only process CloneSet.
        if (resourceObjectProtocol != ResourceObjectProtocolEnum.CloneSet) {
            return
        }
        log.info("call ResourceCredentialInjectionListener,params with " +
            "baseResourceObjectSpec:{},featureSpec:{},resourceObjectProtocol:{}",
            baseResourceObjectSpec, featureSpec, resourceObjectProtocol
        )

        // Collect env vars from base resource object spec.
        val workloadYaml = YamlUtils.load(baseResourceObjectSpec)
        val cloneSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(workloadYaml), CloneSet::class.java)
        val cloneSetEnvs = resolveEnvNamesFromMainContainer(cloneSet.spec).ifEmpty { return }

        // Collect env vars from feature spec.
        val addedFeatureSpec = runCatching {
            JsonUtils.gsonReadValue(
                JsonUtils.writeValueAsString(YamlUtils.load(featureSpec)),
                CloneSet::class.java
            )
        }.getOrElse { return }
        val featureSpecEnvs = resolveEnvNamesFromMainContainer(addedFeatureSpec.spec).ifEmpty { return }

        // Throw exception when aliyun sls logtail envs both in base spec and feature spec.
        if ((featureSpecEnvs intersect cloneSetEnvs intersect ALIYUN_SLS_LOGTAIL_ENVS).isNotEmpty()) {
            throw FeatureConflictException(
                "SLS日志采集配置冲突，请参考 https://aliyuque.antfin.com/cloudcenter/resource/tr45c7w3nfkc47dw 进行处理"
            )
        }
    }

    /**
     * Revolve env names from clone set main container spec.
     */
    private fun resolveEnvNamesFromMainContainer(spec: CloneSetSpec): Set<String> {
        val templateSpec = spec.template.spec ?: return emptySet()
        val mainContainer = templateSpec.containers.filter { it.isMainContainer() }
        return mainContainer.mapNotNull { it.env?.map { e -> e.name } }.flatten().toSet()
    }

    /**
     * Check whether the current container is main container..
     */
    private fun V1Container.isMainContainer(): Boolean {
        return name == MAIN_CONTAINER_NAME
    }

    companion object {

        /**
         * Env vars of aliyun SLS log collecting service.
         */
        private val ALIYUN_SLS_LOGTAIL_ENVS = setOf(
            "ALIYUN_LOGTAIL_USER_DEFINED_ID",
            "ALIYUN_LOGTAIL_USER_ID",
            "ALIYUN_LOGTAIL_CONFIG"
        )

        /**
         * Main container name.
         */
        private const val MAIN_CONTAINER_NAME = "main"
    }

    override fun afterPropertiesSet() {
        resourceObjectFeatureImportListenerFactory.registryListener("APP_GROUP_RESOURCE_CREDENTIAL_INJECTION", this)
        resourceObjectFeatureImportListenerFactory.registryListener("APP_ENV_RESOURCE_CREDENTIAL_INJECTION", this)
    }
}