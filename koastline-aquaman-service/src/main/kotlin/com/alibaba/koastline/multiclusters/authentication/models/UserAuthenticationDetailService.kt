package com.alibaba.koastline.multiclusters.authentication.models

import com.alibaba.koastline.multiclusters.authentication.AuthenticationManager
import com.alibaba.koastline.multiclusters.common.logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 */
@Component
class UserAuthenticationDetailService : UserDetailsService {
    val log by logger()
    @Autowired
    lateinit var authenticationManager: AuthenticationManager

    override fun loadUserByUsername(username: String?): UserDetails {
        log.debug("load user by username $username")
        val userDetails = authenticationManager.queryUserDetails(username!!)
        log.debug("user details is $userDetails")
        return userDetails
    }
}