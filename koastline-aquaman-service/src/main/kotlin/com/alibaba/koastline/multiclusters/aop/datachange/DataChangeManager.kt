package com.alibaba.koastline.multiclusters.aop.datachange

import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeDataException
import com.alibaba.koastline.multiclusters.data.dao.env.DataChangeOrderRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.DataChangeOrder
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*

@Component
class DataChangeManager {

    @Autowired
    lateinit var dataChangeOrderRepo: DataChangeOrderRepo

    fun noteEvent(dataChangeOrder: DataChangeOrder) {
        if (dataChangeOrderRepo.insert(dataChangeOrder) != 1) {
            throw RuntimeDataException("note Event with error")
        }
    }

    fun findDataChange(id: Long): DataChangeOrder {
        return dataChangeOrderRepo.find(id)
    }

    fun listDataChangeByChangeEvent(
        changeEvent: String,
        startTime: Date,
        finishTime: Date,
        limit: Int = DEFAULT_LIMIT
    ): List<DataChangeOrder> {
        require(limit > 0){ "limit must be positive" }
        return dataChangeOrderRepo.listByChangeEvent(changeEvent, startTime, finishTime, limit)
    }

    fun listByOperator(operator: String, limit: Int = DEFAULT_LIMIT): List<DataChangeOrder> {
        require(limit > 0){ "limit must be positive" }
        return dataChangeOrderRepo.listByOperator(operator, limit)
    }

    fun listByProperties(dateChangeOrderQueryCondition: DateChangeOrderQueryCondition): PageData<DataChangeOrder> {
        val page: Page<DataChangeOrder> =
            PageHelper.startPage<DataChangeOrder>(
                dateChangeOrderQueryCondition.pageNumber,
                dateChangeOrderQueryCondition.pageSize, "finish_time DESC"
            ).doSelectPage {
                dataChangeOrderRepo.listByProperties(
                    changeEvent = dateChangeOrderQueryCondition.changeEvent,
                    traceId = dateChangeOrderQueryCondition.traceId,
                    finishTime = dateChangeOrderQueryCondition.finishTime,
                    startTime = dateChangeOrderQueryCondition.fromTime,
                    operator = dateChangeOrderQueryCondition.operator,
                    success = dateChangeOrderQueryCondition.success?.let { isSucces(it) }
                )
            }
        return PageData.transformFrom(page)
    }

    companion object {
        const val OPS_SUCCESS = "Y"
        const val OPS_FALSE = "N"
        const val DEFAULT_LIMIT = 10
    }
}

data class DateChangeOrderQueryCondition(
    val changeEvent: String? = null,
    val operator: String? = null,
    val fromTime: Date? = null,
    val finishTime: Date? = null,
    val pageNumber: Int,
    val pageSize: Int,
    val traceId: String? = null,
    val success: Boolean? = null,
)