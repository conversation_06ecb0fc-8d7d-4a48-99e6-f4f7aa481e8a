package com.alibaba.koastline.multiclusters.schedule.model

/**
 * 缩容声明
 */
data class ScaleInDeclaration (
    /**
     * 声明ID
     */
    var id: String,
    /**
     * 站点
     */
    val az: String,
    /**
     * 用途
     */
    val stage: String,
    /**
     * 单元
     */
    val unit: String,
    /**
     * 权重
     */
    val weight: Int
)

/**
 * 基于声明计算缩容结果
 */
data class ScaleInDeclarationScheduleResult(
    /**
     * 缩容声明ID
     */
    val scaleInDeclarationId: String,
    /**
     * 权重
     */
    val weight: Int = 0,
    /**
     * 声明涉及Workload当前总资源数
     */
    val runningTotalNum: Int = 0,
    /**
     * 缩容资源数
     */
    val scaleInNum: Int = 0,
    /**
     * 运行态资源Workload列表
     */
    val workloadMetadataConstraintAssembleList: MutableList<WorkloadMetadataConstraintAssemble> = mutableListOf()
)