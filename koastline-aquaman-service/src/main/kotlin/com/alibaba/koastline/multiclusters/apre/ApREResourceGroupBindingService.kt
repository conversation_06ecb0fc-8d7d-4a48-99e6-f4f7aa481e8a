package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREResourceGroupBindingDataDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApRENotFoundException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREResourceGroupBindingException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREResourceGroupBindingUniqueExistException
import com.alibaba.koastline.multiclusters.data.dao.env.ApREResourceGroupBindingRepo
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApREResourceGroupBindingData
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.*

@Component
class ApREResourceGroupBindingService(val objectMapper: ObjectMapper) {
    @Autowired
    lateinit var apREResourceGroupBindingRepo: ApREResourceGroupBindingRepo
    @Autowired
    lateinit var defaultClusterService: DefaultClusterService
    @Autowired
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo
    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    fun createApREResourceGroupBindingData(apREResourceGroupBindingDataDO: ApREResourceGroupBindingDataDO): Long {
        //校验是否已存在
        apREResourceGroupBindingRepo.findByCondition(apREResourceGroupBindingDataDO.appName,
            apREResourceGroupBindingDataDO.resourceGroup,
            apREResourceGroupBindingDataDO.runtimeEnvKey
        ) ?.let {
            throw ApREResourceGroupBindingUniqueExistException(it.appName, it.resourceGroup, it.runtimeEnvKey)
        }
        checkApREResourceGroupBindingData(apREResourceGroupBindingDataDO)
        val now = Date(Instant.now().toEpochMilli())
        val apREResourceGroupBindingData = ApREResourceGroupBindingData(
            null,
            apREResourceGroupBindingDataDO.appName,
            apREResourceGroupBindingDataDO.resourceGroup,
            apREResourceGroupBindingDataDO.runtimeEnvKey,
            objectMapper.writeValueAsString(apREResourceGroupBindingDataDO.selector),
            now,
            now,
            Constants.IS_NOT_DELETED
        )
        apREResourceGroupBindingRepo.insert(apREResourceGroupBindingData)
        return apREResourceGroupBindingData.id!!
    }

    fun updateApREResourceGroupBindingData(id: Long, selector: ApREBindingTerm) {
        checkApREResourceGroupBindingData(
            getApREResourceGroupBindingDataById(id).copy(
                selector = selector
            )
        )
        apREResourceGroupBindingRepo.updateById(id, objectMapper.writeValueAsString(selector))
    }

    fun getApREResourceGroupBindingDataById(id: Long): ApREResourceGroupBindingDataDO {
        return apREResourceGroupBindingRepo.findById(id).run {
            convert(this)
        }
    }

    fun getApREResourceGroupBindingDataByCondition(appName: String,resourceGroup: String,runtimeEnvKey: String): ApREResourceGroupBindingDataDO? {
        return apREResourceGroupBindingRepo.findByCondition(appName, resourceGroup, runtimeEnvKey) ?.run {
            convert(this)
        }
    }

    fun delApREResourceGroupBindingDataById(id: Long) {
        apREResourceGroupBindingRepo.delById(id)
    }

    private fun convert(apREResourceGroupBindingData: ApREResourceGroupBindingData): ApREResourceGroupBindingDataDO {
        return ApREResourceGroupBindingDataDO(
            apREResourceGroupBindingData.id,
            apREResourceGroupBindingData.appName,
            apREResourceGroupBindingData.resourceGroup,
            apREResourceGroupBindingData.runtimeEnvKey,
            objectMapper.readValue(apREResourceGroupBindingData.selector),
            apREResourceGroupBindingData.gmtCreate,
            apREResourceGroupBindingData.gmtModified,
            apREResourceGroupBindingData.isDeleted
        )
    }

    private fun checkApREResourceGroupBindingData(apREResourceGroupBindingDataDO: ApREResourceGroupBindingDataDO) {
        //校验ApRE是否存在
        val apREClusterIdList = appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(apREResourceGroupBindingDataDO.runtimeEnvKey) ?.run {
            resourcePoolService.listByManagedClusterKey(this.managedClusterKey).map {
                it.clusterId
            }
        } ?:let {
            throw ApRENotFoundException()
        }
        //校验clusterId是否已存在
        apREResourceGroupBindingDataDO.selector.required!!.clusters.let { clusterIdList ->
            if (clusterIdList.isNullOrEmpty()) {
                throw ApREResourceGroupBindingException("未约定定向集群,BindingData:${apREResourceGroupBindingDataDO}")
            }
            clusterIdList.forEach { clusterId ->
                //找不到集群会抛异常
                defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId)
                //ApRE未配置集群
                if (!apREClusterIdList.contains(clusterId)) {
                    throw ApREResourceGroupBindingException("ApRE:${apREResourceGroupBindingDataDO.runtimeEnvKey}未配置集群:${clusterId}")
                }
            }
        }
    }
}