package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.AoneGreyException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.model.AoneGreyResp
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * date 2025/3/25 15:24
 */
@Component
class AoneGreyApi(
    @Value("\${aone.base.host}")
    val aoneBaseHost: String
) {

    fun isObjectGrey(greyCode: String, objectType: String, objectValue: String): Boolean {
        val result = HttpClientUtils.httpGet(
            url = "$aoneBaseHost/base/service/grey/isObjectGrey",
            params = mapOf(
                "greyCode" to greyCode,
                "objectType" to objectType,
                "objectValue" to objectValue
            )
        )
        val greyResp = JsonUtils.readValue(result, AoneGreyResp::class.java)
        if (!greyResp.successful) {
            throw AoneGreyException("isObjectGrey failed, greyCode: $greyCode," +
                    "objectType: $objectType, objectValue: $objectValue, errorMsg:${greyResp.errorMsg}")
        }
        return greyResp.`object`!!
    }

}