package com.alibaba.koastline.multiclusters.schedule.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("调度策略请求")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ScheduleStrategyReqDto(
    @ApiModelProperty("应用名",required = true)
    val appName: String,
    @ApiModelProperty("分组名",required = true)
    val resourceGroup: String,
    @ApiModelProperty("调度策略环境类型",required = true)
    val scheduleStrategyEnvType: ScheduleStrategyEnvType,
    @ApiModelProperty("基座应用(ServerlessApp类型必填)",required = false)
    val serverlessBaseAppName: String? = null,
    @ApiModelProperty("是否声明式",required = true)
    val declarative: <PERSON><PERSON>an,
    @ApiModelProperty("资源声明(非声明式必填)",required = false)
    val declaration: OrientedDeclaration?
)


enum class ScheduleStrategyEnvType {
    /**
     * ASI
     */
    ASI,

    /**
     * SERVERLESS_APP
     */
    SERVERLESS_APP

}

