package com.alibaba.koastline.multiclusters.resourceobj.utils

import com.alibaba.koastline.multiclusters.common.exceptions.ThreeWayMergeDiffException
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.common.utils.toNullIfEmpty
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectPatchService
import com.alibaba.koastline.multiclusters.resourceobj.model.PatchStrategyDefinition
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectPatchStrategyEnum
import org.springframework.stereotype.Component
import javax.json.*

private const val PATCH_DIRECTIVE_KEY = "\$patch"

private const val DELETE_DIRECTIVE = "delete"

object ThreeWayMerge {
        /*
    merge patch into target
    path is cumulative record of how depth recursive reach
     */
        fun merge (
            target: JsonValue,
            patch: JsonValue,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            mergeOptions: MergeOptions,
        ): JsonValue {
            if (patch == JsonValue.NULL) {
                return JsonValue.NULL
            }
            if (target.valueType != patch.valueType) {
                if (whetherJsonBoolean(target) && whetherJsonBoolean(patch)) {
                    return patch
                }
                if (whetherJsonNumber(target) && whetherJsonNumber(patch)) {
                    return patch
                }
                throw ThreeWayMergeDiffException("type of target=${target} is ${target.valueType}, but type of patch=${patch} is ${patch.valueType}, path=${path}")
            } else if (target.valueType == JsonValue.ValueType.OBJECT && patch.valueType == JsonValue.ValueType.OBJECT) {
                return mergeObject(
                    target as JsonObject,
                    patch as JsonObject,
                    path,
                    defaultPatchStrategyDefinition,
                    additionalPatchStrategyDefinition,
                    mergeOptions,
                )
            } else if (target.valueType == JsonValue.ValueType.ARRAY && patch.valueType == JsonValue.ValueType.ARRAY) {
                return mergeArray(
                    target as JsonArray,
                    patch as JsonArray,
                    path,
                    defaultPatchStrategyDefinition,
                    additionalPatchStrategyDefinition,
                    mergeOptions,
                )
            } else {
                return patch
            }

        }

        private fun mergeArray(
            t: JsonArray,
            p: JsonArray,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            mergeOptions: MergeOptions,
        ): JsonValue {
            val builder = Json.createArrayBuilder(t)
            val strategy = ResourceObjectPatchService.getStrategy(
                defaultPatchStrategyDefinition,
                additionalPatchStrategyDefinition,
                path,
                "list"
            )
            when (strategy.patchStrategy) {
                ResourceObjectPatchStrategyEnum.replace.name -> {
                    return p
                }

                /*
                    Notice: items of Json list can be heterogeneous
                    - list of list is forbidden
                    - list of heterogeneous item is forbidden
                    - list of String and list of Object are allowed
                     */
                ResourceObjectPatchStrategyEnum.merge.name -> {

                    if (validateJsonArrayType(t) == JsonValue.ValueType.OBJECT && validateJsonArrayType(p) == JsonValue.ValueType.OBJECT) {
                        p.forEach{ itemPatch: JsonValue ->
                            val objectItemPatch = itemPatch as JsonObject
                            t.firstOrNull{
                                (it as JsonObject)[strategy.patchMergeKey] == objectItemPatch[strategy.patchMergeKey]
                            }?.let {
                                val index = t.indexOfFirst {
                                    (it as JsonObject)[strategy.patchMergeKey] == objectItemPatch[strategy.patchMergeKey]
                                }
                                if (
                                    objectItemPatch.containsKey(PATCH_DIRECTIVE_KEY) &&
                                    objectItemPatch.get(PATCH_DIRECTIVE_KEY) == Json.createValue(DELETE_DIRECTIVE)
                                ) {
                                    builder.set(index, JsonValue.NULL)
                                } else {
                                    val recursiveRet = mergeObject(
                                        it as JsonObject,
                                        objectItemPatch,
                                        path,
                                        defaultPatchStrategyDefinition,
                                        additionalPatchStrategyDefinition,
                                        mergeOptions,
                                    )
                                    builder.set(index, recursiveRet)
                                }
                            }?:let {
                                if (
                                    !whetherIgnoreDelete(objectItemPatch, mergeOptions)
                                ) {
                                    builder.add(
                                        objectItemPatch
                                    )
                                }
                            }
                        }

                    } else {
                        return p
                    }
                }

                ResourceObjectPatchStrategyEnum.cartesian.name -> run {
                    if (validateJsonArrayType(t) != JsonValue.ValueType.OBJECT || validateJsonArrayType(p) != JsonValue.ValueType.OBJECT) {
                        return p
                    }
                    if (t.size == 0) {
                        builder.add(p.get(0) as JsonObject)
                        return@run
                    }
                    /*
                    N 个对象和 1 个目标 Diff 后，得到的是 N 个 Diff 片段，分别 Apply
                     */
                    val lengthOfTarget = t.size
                    val lengthOfPatch = p.size
                    check(lengthOfPatch == lengthOfTarget)
                    for (i in 0 until lengthOfTarget) {
                        val itemPatch = p[i] as JsonObject
                        val itemTarget = t[i] as JsonObject
                        val recursiveRet = mergeObject(
                            itemTarget,
                            itemPatch,
                            path,
                            defaultPatchStrategyDefinition,
                            additionalPatchStrategyDefinition,
                            mergeOptions,
                        )
                        builder.set(i, recursiveRet)
                    }

                }

                else -> {
                    throw ThreeWayMergeDiffException("ResourceObjectPatchStrategy ${strategy.patchStrategy} is invalid")
                }
            }
            val listOfNonNullableItem = builder.build().filter { it != JsonValue.NULL }
            val newBuilder = Json.createArrayBuilder()
            listOfNonNullableItem.forEach { item: JsonValue ->
                newBuilder.add(item)
            }
            return newBuilder.build()
        }

    private fun whetherIgnoreDelete(
        objectItemPatch: JsonObject,
        mergeOptions: MergeOptions
    ): Boolean {
        // if array item contains delete directive and merge options declare ignore of deletion
        // then ignore this item in merge
        return objectItemPatch.containsKey(PATCH_DIRECTIVE_KEY) &&
                objectItemPatch.get(PATCH_DIRECTIVE_KEY) == Json.createValue(DELETE_DIRECTIVE) && mergeOptions.ignoreUnmatchedNulls
    }

    private fun mergeObject(
            t: JsonObject,
            p: JsonObject,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            mergeOptions: MergeOptions,
        ): JsonValue {
            val builder = Json.createObjectBuilder(t)
            val strategy = ResourceObjectPatchService.getStrategy(
                defaultPatchStrategyDefinition,
                additionalPatchStrategyDefinition,
                path,
                "map"
            )
            when (strategy.patchStrategy) {
                ResourceObjectPatchStrategyEnum.replace.name -> {
                    return p
                }

                ResourceObjectPatchStrategyEnum.merge.name, ResourceObjectPatchStrategyEnum.cartesian.name -> {

                    p.forEach { key: String, value: JsonValue ->
                        val nextPath = path.toNullIfBlank()?.run { "${path}.${key}" } ?: run { key }
                        if (t.containsKey(key)) {
                            /*
                                    t.containsKey(key) is true, then t.get(key) must be not null
                                     */
                            val recursiveRet = merge(
                                t[key]!!,
                                value,
                                nextPath,
                                defaultPatchStrategyDefinition,
                                additionalPatchStrategyDefinition,
                                mergeOptions,
                            )
                            if (recursiveRet == JsonValue.NULL) {
                                builder.remove(key)
                            } else {
                                builder.add(key, recursiveRet)
                            }
                        } else {
                            if (!(value == JsonValue.NULL && mergeOptions.ignoreUnmatchedNulls)) {
                                builder.add(
                                    key, value
                                )
                            }

                        }
                    }
                }

                else -> {
                    throw ThreeWayMergeDiffException("ResourceObjectPatchStrategy ${strategy.patchStrategy} is invalid")
                }
            }
            return builder.build()
        }


        /*
        diff compute patch which can convert source to target
        path is cumulative record of how depth recursive reach
        diffOptions is whether Deletions & Additions/Modifications are ignored or not
         */
        fun diff(
            source: JsonValue,
            target: JsonValue,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            diffOptions: DiffOptions
        ): JsonValue? {
            /*
            !Notice:
            JsonValue.NULL: explicit directive to delete item, must process it
            null: implicit meaning of NoOps (No Operations), just ignore it
             */
            if (target == JsonValue.NULL) {
                /*
                case 0: null target
                 */
                if (!diffOptions.ignoreDeletions) {
                    return JsonValue.NULL
                } else {
                    return null
                }

            }
            if (source.valueType != target.valueType) {
                /*
                case 1: different types
                 */
                if (whetherJsonBoolean(source) && whetherJsonBoolean(target)) {
                    return target
                }
                if (whetherJsonNumber(source) && whetherJsonNumber(target)) {
                    return target
                }

                throw ThreeWayMergeDiffException("type of source=${source} is ${source.valueType}, but type of target=${target} is ${target.valueType}, path=${path}")

            } else if (source.valueType == JsonValue.ValueType.OBJECT && target.valueType == JsonValue.ValueType.OBJECT) {
                return diffObject(
                    source as JsonObject,
                    target as JsonObject,
                    path,
                    defaultPatchStrategyDefinition,
                    additionalPatchStrategyDefinition,
                    diffOptions
                )

            } else if (source.valueType == JsonValue.ValueType.ARRAY && target.valueType == JsonValue.ValueType.ARRAY) {
                return diffArray(
                    source as JsonArray,
                    target as JsonArray,
                    path,
                    defaultPatchStrategyDefinition,
                    additionalPatchStrategyDefinition,
                    diffOptions
                )
            } else {
                /*
                case 4: all types are primitives
                 */
                if (!diffOptions.ignoreChangesAndAdditions) {
                    if (source != target) {
                        return target
                    } else {
                        return null
                    }

                } else {
                    return null
                }

            }
        }

        private fun diffArray(
            s: JsonArray,
            t: JsonArray,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            diffOptions: DiffOptions
        ): JsonValue? {
            /*
                case 3: all types are lists
                 */
            val builder = Json.createArrayBuilder()
            val strategy = ResourceObjectPatchService.getStrategy(
                defaultPatchStrategyDefinition,
                additionalPatchStrategyDefinition,
                path,
                "list"
            )
            when (strategy.patchStrategy) {
                ResourceObjectPatchStrategyEnum.replace.name -> {

                    if (!diffOptions.ignoreChangesAndAdditions) {
                        if (s != t) {
                            return t
                        } else {
                            return null
                        }

                    } else {
                        return null
                    }

                }

                /*
                    Notice: items of Json list can be heterogeneous
                    - list of list is forbidden
                    - list of heterogeneous item is forbidden
                    - list of String and list of Object are allowed
                     */
                ResourceObjectPatchStrategyEnum.merge.name -> {

                    if (validateJsonArrayType(s) == JsonValue.ValueType.OBJECT && validateJsonArrayType(t) == JsonValue.ValueType.OBJECT) {
                        s.forEach { itemSource: JsonValue ->
                            val objectItemSource = itemSource as JsonObject
                            t.firstOrNull{
                                objectItemSource[strategy.patchMergeKey] == (it as JsonObject)[strategy.patchMergeKey]
                            }?.let {
                                if (objectItemSource != (it as JsonObject)) {
                                    val diffVal = diffObject(
                                        objectItemSource,
                                        it,
                                        path,
                                        defaultPatchStrategyDefinition,
                                        additionalPatchStrategyDefinition,
                                        diffOptions
                                    )
                                    diffVal?.let {
                                        val retItem = it as JsonObject
                                        builder.add(
                                            Json.createObjectBuilder(retItem).apply {
                                                when {
                                                    objectItemSource[strategy.patchMergeKey] != null -> {
                                                        this.add(
                                                            strategy.patchMergeKey,
                                                            objectItemSource[strategy.patchMergeKey]
                                                        )
                                                    }
                                                }
                                            }.build()
                                        )
                                    }
                                }
                            }?:let {
                                if (!diffOptions.ignoreDeletions) {

                                    builder.add(
                                        Json.createObjectBuilder().apply {
                                            this.add(
                                                strategy.patchMergeKey, objectItemSource[strategy.patchMergeKey]
                                            )
                                            this.add(
                                                PATCH_DIRECTIVE_KEY, Json.createValue(DELETE_DIRECTIVE)
                                            )
                                        })
                                }
                            }


                        }

                        t.forEach { itemTarget: JsonValue ->
                            val objectItemTarget = itemTarget as JsonObject
                            s.firstOrNull {
                                (it as JsonObject)[strategy.patchMergeKey] == objectItemTarget[strategy.patchMergeKey]
                            } ?:let {
                                if (!diffOptions.ignoreChangesAndAdditions) {
                                    builder.add(objectItemTarget)
                                }
                            }
                        }
                    } else {
                        return t
                    }
                }

                ResourceObjectPatchStrategyEnum.cartesian.name -> {
                    /*
                    patch 的目标片段是一个，不允许多个
                     */
                    check(t.size == 1)
                    if (validateJsonArrayType(s) != JsonValue.ValueType.OBJECT || validateJsonArrayType(t) != JsonValue.ValueType.OBJECT) {
                        return t
                    }
                    t.forEach { itemTarget: JsonValue ->
                        val objectItemTarget = itemTarget as JsonObject
                        if (s.isNotEmpty()) {
                            s.forEach {
                                val objectItemSource = it as JsonObject
                                if (objectItemSource != objectItemTarget) {
                                    diffObject(
                                        objectItemSource,
                                        objectItemTarget,
                                        path,
                                        defaultPatchStrategyDefinition,
                                        additionalPatchStrategyDefinition,
                                        diffOptions
                                    )?.let {
                                        val retItem = it as JsonObject
                                        builder.add(
                                            Json.createObjectBuilder(retItem).build()
                                        )
                                    }
                                }

                            }

                        } else {
                            if (!diffOptions.ignoreChangesAndAdditions) {
                                builder.add(objectItemTarget)
                            }
                        }

                    }

                }

                else -> {
                    throw ThreeWayMergeDiffException("ResourceObjectPatchStrategy ${strategy.patchStrategy} is invalid")
                }
            }
            return builder.build()
        }

        private fun diffObject(
            s: JsonObject,
            t: JsonObject,
            path: String,
            defaultPatchStrategyDefinition: PatchStrategyDefinition,
            additionalPatchStrategyDefinition: PatchStrategyDefinition?,
            diffOptions: DiffOptions
        ): JsonValue? {
            /*
                case 2: all types are maps
                 */
            val builder = Json.createObjectBuilder()
            val strategy = ResourceObjectPatchService.getStrategy(
                defaultPatchStrategyDefinition,
                additionalPatchStrategyDefinition,
                path,
                "map"
            )
            when (strategy.patchStrategy) {
                ResourceObjectPatchStrategyEnum.replace.name -> {
                    if (!diffOptions.ignoreChangesAndAdditions) {
                        if (s != t) {
                            return t
                        } else {
                            return null
                        }

                    } else {
                        return null
                    }

                }

                ResourceObjectPatchStrategyEnum.merge.name, ResourceObjectPatchStrategyEnum.cartesian.name -> {
                    s.forEach { key: String, value: JsonValue ->

                        val nextPath = path.toNullIfBlank()?.run { "${path}.${key}" } ?: run { key }
                        if (t.containsKey(key)) {
                            if (value != t[key]) {
                                /*
                                    t.containsKey(key) is true, then t.get(key) must be not null
                                     */
                                val diffVal = diff(
                                    value,
                                    t[key]!!,
                                    nextPath,
                                    defaultPatchStrategyDefinition,
                                    additionalPatchStrategyDefinition,
                                    diffOptions
                                )
                                diffVal?.let {
                                    builder.add(key, it)
                                }
                            }
                        } else if (!diffOptions.ignoreDeletions) {
                            if (value is JsonArray) {
                                val diffVal = diffArray(
                                    value,
                                    JsonValue.EMPTY_JSON_ARRAY,
                                    nextPath,
                                    defaultPatchStrategyDefinition,
                                    additionalPatchStrategyDefinition,
                                    diffOptions
                                )

                                diffVal?.let {
                                    builder.add(key, it)
                                }
                            } else if (value is JsonObject) {

                                val diffVal = diffObject(
                                    value,
                                    JsonValue.EMPTY_JSON_OBJECT,
                                    nextPath,
                                    defaultPatchStrategyDefinition,
                                    additionalPatchStrategyDefinition,
                                    diffOptions
                                )

                                diffVal?.let {
                                    builder.add(key, it)
                                }
                            } else {
                                builder.addNull(key)

                            }
                        }
                    }
                    t.forEach { key: String, value: JsonValue? ->
                        if (!s.containsKey(key) && !diffOptions.ignoreChangesAndAdditions) {
                            builder.add(key, value)
                        }
                    }
                }

                else -> {
                    throw ThreeWayMergeDiffException("ResourceObjectPatchStrategy ${strategy.patchStrategy} is invalid")
                }
            }
            return builder.build()
        }

        fun validateJsonArrayType(array: JsonArray) :JsonValue.ValueType {
            if (array.size == 0) {
                return JsonValue.ValueType.OBJECT
            }
            if (array.filterIsInstance<JsonArray>().size != 0) {
                throw ThreeWayMergeDiffException("array ${array} contains array of array")
            }
            if (array.filterIsInstance<JsonString>().size == array.size) {
                return JsonValue.ValueType.STRING
            }
            if (array.filterIsInstance<JsonObject>().size == array.size) {
                return JsonValue.ValueType.OBJECT
            }
            throw ThreeWayMergeDiffException("array ${array} contains heterogeneous items")
        }

    fun whetherJsonBoolean(value: JsonValue): Boolean{
        if (JsonValue.ValueType.TRUE == value.valueType || JsonValue.ValueType.FALSE == value.valueType) {
            return true
        }
        if (JsonValue.ValueType.STRING == value.valueType) {
            val regex = """
                ('|"|\\"|\\')*(y|Y|yes|Yes|YES|n|N|no|No|NO|true|True|TRUE|false|False|FALSE|on|On|ON|off|Off|OFF)('|"|\\"|\\')*
            """.trimIndent().toRegex(RegexOption.IGNORE_CASE)

            if (regex.matches(value.toString())) {
                return true
            }
        }
        return false
    }

    fun whetherJsonNumber(value: JsonValue): Boolean{
        if (JsonValue.ValueType.NUMBER == value.valueType) {
            return true
        }
        // https://www.json.org/json-en.html
        if (JsonValue.ValueType.STRING == value.valueType) {
            val regex = """
                ('|"|\\"|\\')*(((-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(\d+(\.\d+)?%))(,((-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(\d+(\.\d+)?%)))*)('|"|\\"|\\')*
            """.trimIndent().toRegex()

            if (regex.matches(value.toString())) {
                return true
            }
        }
        return false
    }




    data class DiffOptions(

        /*
        ignoreChangesAndAdditions = false: consider ChangesAndAdditions when diff
        ignoreChangesAndAdditions = true: ignore ChangesAndAdditions when diff
         */
        val ignoreChangesAndAdditions: Boolean,

        /*
        ignoreDeletions = false: consider deletions when diff
        ignoreDeletions = true: ignore deletions when diff
         */
        val ignoreDeletions: Boolean,
    )

    data class MergeOptions(

        /*
        ignoreUnmatchedNulls = false: consider null as delete
        ignoreUnmatchedNulls = true: consider null as NoOps
         */
        val ignoreUnmatchedNulls: Boolean,
    )


}
