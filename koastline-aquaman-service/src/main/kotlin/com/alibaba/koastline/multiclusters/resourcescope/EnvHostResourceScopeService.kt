package com.alibaba.koastline.multiclusters.resourcescope

import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.resourcescope.EnvHostResourceScopeRepo
import com.alibaba.koastline.multiclusters.data.vo.resourcescope.EnvHostResourceScope
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeCreateDto
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeDO
import com.alibaba.koastline.multiclusters.resourcescope.model.HostResourceScope
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * @author:    <EMAIL>
 * @description:  环境主机资源范围配置
 * @date:    2025/3/7 3:47 PM
 */
@Service
class EnvHostResourceScopeService {
    private val logger by logger()
    @Autowired
    lateinit var envHostResourceScopeRepo: EnvHostResourceScopeRepo

    @Transactional
    fun createAndOverrideWhileExist(envHostResourceScopeCreateDto: EnvHostResourceScopeCreateDto) {
        envHostResourceScopeRepo.findByCurrentEnvStackId(envHostResourceScopeCreateDto.currentEnvStackId)?.let {
            envHostResourceScopeRepo.deleteByCurrentEnvStackId(envHostResourceScopeCreateDto.currentEnvStackId, envHostResourceScopeCreateDto.creator)
        }
        envHostResourceScopeRepo.insert(envHostResourceScope = EnvHostResourceScope(
            appName = envHostResourceScopeCreateDto.appName,
            currentEnvStackId = envHostResourceScopeCreateDto.currentEnvStackId,
            baseEnvStackId = envHostResourceScopeCreateDto.baseEnvStackId,
            resourceScope = JsonUtils.writeValueAsString(envHostResourceScopeCreateDto.resourceScope),
            creator = envHostResourceScopeCreateDto.creator,
            modifier = envHostResourceScopeCreateDto.creator,
        ))
    }

    fun findByCurrentEnvStackId(currentEnvStackId: String): EnvHostResourceScopeDO? {
        return envHostResourceScopeRepo.findByCurrentEnvStackId(currentEnvStackId)?.run {
            convert(this)
        }
    }

    fun listByAppName(appName: String): List<EnvHostResourceScopeDO> {
        return envHostResourceScopeRepo.listByAppName(appName).map {
            convert(it)
        }
    }

    fun deleteByCurrentEnvStackId(currentEnvStackId: String, modifier: String) {
        envHostResourceScopeRepo.deleteByCurrentEnvStackId(currentEnvStackId, modifier)
    }

    private fun convert(envHostResourceScope: EnvHostResourceScope): EnvHostResourceScopeDO {
        return EnvHostResourceScopeDO(
            id = envHostResourceScope.id!!,
            appName = envHostResourceScope.appName,
            currentEnvStackId = envHostResourceScope.currentEnvStackId,
            baseEnvStackId = envHostResourceScope.baseEnvStackId,
            resourceScope = JsonUtils.readValue(envHostResourceScope.resourceScope, HostResourceScope::class.java),
            creator = envHostResourceScope.creator,
            modifier = envHostResourceScope.modifier,
            gmtCreate = envHostResourceScope.gmtCreate,
            gmtModified = envHostResourceScope.gmtModified,
            isDeleted = envHostResourceScope.isDeleted,
        )
    }
}