package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.VipCrException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.VipCrObject
import com.alibaba.koastline.multiclusters.external.model.VipServiceReq
import com.alibaba.koastline.multiclusters.external.model.VipServiceResponse
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.normandy.actor.client.AResult
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceMetadataRequestDTO
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceMetadataResultDTO
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class VipServiceCrApi(val objectMapper: ObjectMapper) {

    val log by logger()

    @Value("\${vipservice.host}")
    lateinit var vipServiceHost: String

    @Autowired
    lateinit var externalCallDowngradeProperties: ExternalCallDowngradeProperties

    /**
     * 去 VIP 平台查询相关补充 CR
     *
     * @param vipServiceReq 五元组及其他环境信息
     */
    @ExternalCall(SYS_CALLED)
    fun getVipServiceCr(vipServiceReq: VipServiceMetadataRequestDTO, encoding: String): List<VipCrObject> {

        if (externalCallDowngradeProperties.isDowngrade(SYS_CALLED, "getVipServiceCr")) {
            log.info("$SYS_CALLED getVipServiceCr downgrade.")
            return emptyList()
        }

        require(encoding in ResourceObjectFormatEnum.values().map { it.name }) {
            "illegal encoding `$encoding` appears when query related vip cr objects"
        }


        val typedEncoding = ResourceObjectFormatEnum.valueOf(encoding)

        val rawResp = HttpClientUtils.httpPost(
            "$vipServiceHost$VIP_SERVICE_PATH",
            JsonUtils.writeValueAsString(vipServiceReq),
            null
        )

        val resp = JsonUtils.readValue(rawResp, AResult::class.java)

        if (!resp.isSuccess) {
            val errMsg = "$SYS_CALLED getVipServiceCr failed, req:${JsonUtils.writeValueAsString(vipServiceReq)}, resp:${JsonUtils.writeValueAsString(resp)}"
            throw VipCrException(errMsg)
        }

        val data = JsonUtils.readValue(JsonUtils.writeValueAsString(resp.data), VipServiceMetadataResultDTO::class.java)

        return data.services?.map {
                it -> getCrObject(it, typedEncoding)
        } ?: emptyList()

    }

    fun getCrObject(objStr: String, encoding: ResourceObjectFormatEnum): VipCrObject {
        val obj = JsonUtils.readValue(objStr, VipCrObject::class.java)
        if (encoding.equals(ResourceObjectFormatEnum.YAML)) {
            var yamlStr = YamlUtils.json2yaml(objStr)
            return obj.copy(resourceObject = yamlStr)
        } else {
            return obj.copy(resourceObject = objStr)
        }
    }

    companion object {
        const val VIP_SERVICE_PATH = "/stageprop/vip/getVipserviceMeta"
        const val SYS_CALLED = "vipservice-cr"
    }
}