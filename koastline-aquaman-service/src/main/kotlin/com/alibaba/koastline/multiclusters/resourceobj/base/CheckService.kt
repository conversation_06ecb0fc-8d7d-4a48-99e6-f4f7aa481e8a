package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.atomcore.facade.param.StrategyParam
import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.common.exceptions.MetadataException
import com.alibaba.koastline.multiclusters.external.annotation.InternalLog
import org.springframework.stereotype.Component


@Component
class CheckService {

    @InternalLog("checkQueryStrategy")
    fun checkQueryStrategy(strategyParam: StrategyParam, aquamanRet: StrategiesResultVO, atomRet: StrategiesResultVO) {
        if (
            (atomRet.cpuRatio != aquamanRet.cpuRatio || atomRet.resourcePool != aquamanRet.resourcePool)
        ) {
            throw MetadataException("checkQueryStrategyFailed")
        }
    }

    @InternalLog("checkGetSigmaConfigMap")
    fun checkGetSigmaConfigMap(
        appName: String,
        groupName: String? = null,
        idc: String? = null,
        unit: String? = null,
        env: String? = null,
        atomData: Map<String, Map<String, String>>,
        aquamanData: Map<String, Map<String, String>>
    ) {
        if (atomData != aquamanData) {
            throw MetadataException("checkGetSigmaConfigMapFailed")
        }
    }

}