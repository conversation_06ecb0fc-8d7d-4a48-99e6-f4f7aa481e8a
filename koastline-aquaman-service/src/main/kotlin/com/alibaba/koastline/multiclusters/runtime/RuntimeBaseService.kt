package com.alibaba.koastline.multiclusters.runtime

import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeDataException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.RuntimeRepo
import com.alibaba.koastline.multiclusters.data.dao.env.RuntimeWorkloadRepo
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeData
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkload
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadRunningStatus
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class RuntimeBaseService {
    val log by logger()
    @Autowired
    lateinit var runtimeRepo: RuntimeRepo
    @Autowired
    lateinit var runtimeWorkloadRepo: RuntimeWorkloadRepo

    /**
     * 注册Runtime，重复注册做更新操作
     */
    fun registryRuntime(runtimeCreateReqDto: RuntimeCreateReqDto): Long {
        runtimeRepo.findByRuntimeKey(runtimeCreateReqDto.runtimeKey) ?.let {
            checkRuntime(it, runtimeCreateReqDto)
            //补偿环境StackId字段值
            if (it.envStackId.isNullOrBlank() && !runtimeCreateReqDto.envStackId.isNullOrBlank()) {
                runtimeRepo.updateEnvStackIdById(it.id!!, runtimeCreateReqDto.operator, runtimeCreateReqDto.envStackId)
            }
            return it.id!!
        } ?:let {
            val runtimeData = RuntimeData(
                appName = runtimeCreateReqDto.appName,
                runtimeKey = runtimeCreateReqDto.runtimeKey,
                envStackId = runtimeCreateReqDto.envStackId,
                resourceGroupName = runtimeCreateReqDto.resourceGroupName,
                type = runtimeCreateReqDto.type.name,
                creator = runtimeCreateReqDto.operator,
                modifier = runtimeCreateReqDto.operator
            )
            runtimeRepo.insert(runtimeData)
            return runtimeData.id!!
        }
    }

    /**
     * 注销Runtime，需要检查是否还存在运行中的Workload
     */
    fun unRegistryRuntime(appName: String, runtimeKey: String, operator: String) {
        val runtime = findRuntime(appName, runtimeKey)
        if (isExistRunningWorkload(runtimeKey)) {
            throw RuntimeDataException("Runtime存在未卸载的Workload，runtimeKey = $runtimeKey")
        }
        runtimeRepo.deleteById(runtime.id!!, operator)
    }

    fun findRuntime(appName: String, runtimeKey: String): RuntimeData {
        return runtimeRepo.findByRuntimeKey(runtimeKey) ?.apply {
            if (this.appName != appName) {
                throw RuntimeDataException("Runtime信息错误，require:appName=${appName}&runtime=${runtimeKey},fact:${this}")
            }
        } ?:let {
            throw RuntimeDataException("未找到Runtime,require:appName=${appName}&runtime=${runtimeKey}")
        }
    }

    private fun isExistRunningWorkload(runtimeKey: String): Boolean{
        runtimeWorkloadRepo.listByRuntimeKey(runtimeKey).firstOrNull {
            it.runningStatus == RuntimeWorkloadRunningStatus.INSTALLED.name
        } ?.let {
            return true
        }
        return false
    }

    /**
     * 注册RuntimeWorkload，重复注册做更新操作
     */
    fun registryRuntimeWorkload(runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto): Long {
        runtimeWorkloadRepo.listByRuntimeKey(runtimeWorkloadCreateReqDto.runtimeKey).firstOrNull {
            isSameRuntimeWorkload(it, runtimeWorkloadCreateReqDto)
        } ?.let {
            runtimeWorkloadRepo.updateBothStatusById(
                id = it.id!!,
                modifier = runtimeWorkloadCreateReqDto.operator,
                status = runtimeWorkloadCreateReqDto.status.name,
                runningStatus = runtimeWorkloadCreateReqDto.runningStatus.name)
            return it.id!!
        }
        val runtimeWorkload = RuntimeWorkload(
            runtimeKey = runtimeWorkloadCreateReqDto.runtimeKey,
            site = runtimeWorkloadCreateReqDto.site,
            stage = runtimeWorkloadCreateReqDto.stage,
            unit = runtimeWorkloadCreateReqDto.unit,
            clusterId = runtimeWorkloadCreateReqDto.clusterId,
            status = runtimeWorkloadCreateReqDto.status.name,
            runningStatus = runtimeWorkloadCreateReqDto.runningStatus.name,
            creator = runtimeWorkloadCreateReqDto.operator,
            modifier = runtimeWorkloadCreateReqDto.operator
        )
        runtimeWorkloadRepo.insert(runtimeWorkload)
        return runtimeWorkload.id!!
    }

    /**
     * 注销RuntimeWorkload，做删除操作
     */
    fun unRegistryRuntimeWorkload(runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto) {
        runtimeWorkloadRepo.listByRuntimeKey(runtimeWorkloadCreateReqDto.runtimeKey).firstOrNull {
            isSameRuntimeWorkload(it, runtimeWorkloadCreateReqDto)
        } ?.let {
            if (it.runningStatus == RuntimeWorkloadRunningStatus.INSTALLED.name) {
                throw RuntimeDataException("RuntimeWorkload未卸载，不可注销，runtimeWorload:${it}")
            }
            runtimeWorkloadRepo.deleteById(it.id!!, runtimeWorkloadCreateReqDto.operator)
        }
    }

    fun updateRuntimeWorkloadStatus(runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto) {
        val runtimeWorkload = runtimeWorkloadRepo.listByRuntimeKey(runtimeWorkloadCreateReqDto.runtimeKey).firstOrNull {
            isSameRuntimeWorkload(it, runtimeWorkloadCreateReqDto)
        } ?:let {
            throw RuntimeException("未找到RuntimeWorkload:${runtimeWorkloadCreateReqDto}")
        }
        runtimeWorkloadRepo.updateStatusById(
            id = runtimeWorkload.id!!,
            status = runtimeWorkloadCreateReqDto.status.name,
            modifier = runtimeWorkloadCreateReqDto.operator)
    }

    fun updateRuntimeWorkloadRunningStatus(runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto) {
        val runtimeWorkload = runtimeWorkloadRepo.listByRuntimeKey(runtimeWorkloadCreateReqDto.runtimeKey).firstOrNull {
            isSameRuntimeWorkload(it, runtimeWorkloadCreateReqDto)
        } ?:let {
            return
        }
        runtimeWorkloadRepo.updateRunningStatusById(
            id = runtimeWorkload.id!!,
            runningStatus = runtimeWorkloadCreateReqDto.runningStatus.name,
            modifier = runtimeWorkloadCreateReqDto.operator)
    }

    fun listInstalledWorkloadByRuntimeKey(appName: String, runtimeKey: String): List<RuntimeWorkload> {
        return runtimeWorkloadRepo.listByRuntimeKey(runtimeKey).filter {
            it.runningStatus == RuntimeWorkloadRunningStatus.INSTALLED.name
        }
    }

    private fun isSameRuntimeWorkload(originalRuntimeWorkload: RuntimeWorkload, runtimeWorkloadCreateReqDto: RuntimeWorkloadCreateReqDto): Boolean {
        return originalRuntimeWorkload.site == runtimeWorkloadCreateReqDto.site
                && originalRuntimeWorkload.runtimeKey == runtimeWorkloadCreateReqDto.runtimeKey
                && originalRuntimeWorkload.stage == runtimeWorkloadCreateReqDto.stage
                && originalRuntimeWorkload.unit == runtimeWorkloadCreateReqDto.unit
                && originalRuntimeWorkload.clusterId == runtimeWorkloadCreateReqDto.clusterId
    }

    private fun checkRuntime(originalRuntime: RuntimeData, runtimeCreateReqDto: RuntimeCreateReqDto) {
        if (originalRuntime.runtimeKey != runtimeCreateReqDto.runtimeKey
            || originalRuntime.appName != runtimeCreateReqDto.appName
            || originalRuntime.resourceGroupName != runtimeCreateReqDto.resourceGroupName
            || !MetadataUtils.matchDeclarationMetadata(originalRuntime.envStackId, runtimeCreateReqDto.envStackId)
        ) {
            throw RuntimeDataException("Runtime信息不匹配，originalRuntime:${originalRuntime},runtimeCreateReqDto:${runtimeCreateReqDto}")
        }
    }
}