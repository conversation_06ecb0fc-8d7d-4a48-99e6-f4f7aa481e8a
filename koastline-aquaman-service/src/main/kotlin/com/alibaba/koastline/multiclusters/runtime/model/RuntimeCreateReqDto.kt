package com.alibaba.koastline.multiclusters.runtime

import com.alibaba.koastline.multiclusters.runtime.params.RuntimeType
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadRunningStatus
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadStatus

data class RuntimeCreateReqDto (
    val appName: String,
    val envStackId: String?,
    val runtimeKey: String,
    val resourceGroupName: String,
    val type: RuntimeType,
    val operator: String
)

data class RuntimeWorkloadCreateReqDto (
    val runtimeKey: String,
    val site: String,
    val unit: String,
    val stage: String,
    val clusterId: String,
    val status: RuntimeWorkloadStatus,
    val runningStatus: RuntimeWorkloadRunningStatus,
    val operator: String
)


