package com.alibaba.koastline.multiclusters.external.model

import com.fasterxml.jackson.annotation.JsonProperty

data class PodAggregatorResp (
    val pagedQueryResult: PagedQueryResult?,
    val code: Int,
    val message: String?
)
data class PagedQueryResult (
    val page: Int,
    val pageSize: Int,
    val instances: List<PodInstance>
)

data class PodInstance (
    @JsonProperty("sn")
    val sn: String,
    @JsonProperty("appName")
    val appName: String,
    @JsonProperty("instanceGroup")
    val resourceGroup: String,
    @JsonProperty("site")
    val site: String,
    @JsonProperty("stage")
    val stage: String,
    @JsonProperty("deployUnit")
    val unit: String,
    @JsonProperty("clusterName")
    val clusterName: String,
    @JsonProperty("runtimeId")
    val clusterPoolId: String?
)