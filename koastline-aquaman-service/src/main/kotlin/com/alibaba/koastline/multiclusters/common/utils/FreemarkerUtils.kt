package com.alibaba.koastline.multiclusters.common.utils

import freemarker.template.Configuration
import freemarker.template.Template
import java.io.StringReader
import java.io.StringWriter

object FreemarkerUtils {

    /**
     * 解析映射Freemarker模板
     */
    fun parseTemplate(template: String, context: Map<String, Any>): String {
        val sw = StringWriter()
        val template = Template("template", StringReader(template), Configuration(Configuration.VERSION_2_3_31))
        template.process(context, sw)
        return sw.toString()
    }

    /**
     * yamlString往yaml中注入需要垫缩进 模版中使用的函数 勿删！
     *
     *
     * @param blankNumber
     * @param yamlString
     * @return
     */
    fun innerYamlPad(blankNumber: Int, yamlString: String): String {
        require(blankNumber >= 0) {
            "tabBlankNumber should not be negative int!"
        }
        val padTabs = List(blankNumber * 2) { BLANK }.joinToString("")
        return yamlString.replace(LINE_BREAK, LINE_BREAK + padTabs)
    }

    private const val BLANK = " "
    private const val LINE_BREAK = "\n"
}
