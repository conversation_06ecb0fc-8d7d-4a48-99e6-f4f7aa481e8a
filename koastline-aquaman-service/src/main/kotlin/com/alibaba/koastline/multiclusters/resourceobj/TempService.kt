package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.common.exceptions.AppNotInAppstackAoneInfusionGrayException
import com.alibaba.koastline.multiclusters.common.exceptions.AppStackOriginalAppException
import com.alibaba.koastline.multiclusters.external.AcniHomeApi
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.DeploymentApi
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class TempService {

    @Autowired
    lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var acniHomeApi: AcniHomeApi

    @Autowired
    lateinit var deploymentApi: DeploymentApi

    fun regExReplace(regEx: String, text: String, replace: String): String {
        return regEx.toRegex().replace(text, replace)
    }

    fun asiPodVolumeInjectCheckAndConfig(appName: String) {
        /*
        check whether this app is appstack claud native app (expect serverless & runtime)
         */
        if (appCenterApi.isCloudNativeApp(appName)) {
            throw AppStackOriginalAppException("应用 $appName 是在 AppStack 部署的云原生应用，不能开启安全自动注入")
        }


        /*
        check whether this app is in aone&appstack infusion gray
         */
        when {
            !appCenterApi.whetherAppInAppstackAoneInfusionGray(appName) -> {
                throw AppNotInAppstackAoneInfusionGrayException("应用 $appName 找AONE加入融合版本")
            }
        }

        /*
        notify acni to inject volume configs in aquaman asynchronously
         */
        acniHomeApi.notifyAcniToInjectVolumes(appName)

    }
}