package com.alibaba.koastline.multiclusters.resourceobj.model

import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.oam.models.v1alpha1.trait.Trait
import com.alibaba.oam.models.v1alpha1.trait.TraitList
import com.alibaba.oam.models.v1alpha1.trait.TraitSpec
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("扩容组成资源对象请求")
data class ScaleOutAssembledResourceObjectRequest (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("当前的资源对象",required = false)
    val currentResourceObject: String?,
    @ApiModelProperty("输入参数(可以为多级结构体)",required = true)
    val inputParams: Map<String, Any>,
    @ApiModelProperty("返回资源对象协议",required = true)
    val resourceObjectProtocol: String = ResourceObjectProtocolEnum.StatefulSet.name,
    @ApiModelProperty("返回资源对象协议版本",required = false)
    val resourceObjectProtocolVersion: String? = null,
    @ApiModelProperty("返回资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML
)

@ApiModel("扩容组装CR列表请求")
data class ScaleOutAssembledCRListRequest (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("输入参数(可以为多级结构体)",required = true)
    val inputParams: Map<String, Any>,
    @ApiModelProperty("返回资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML,
    @ApiModelProperty("若有本参数，则只返回列表中的 CR 类型，其他类型的 CR 不返回", required = false)
    val acceptableCrTypeList: List<CrType>?
)

@ApiModel("发布组成资源对象请求")
data class DeployAssembledResourceObjectRequest (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("版本stackPkId",required = true)
    val stackPkId: String,
    @ApiModelProperty("当前的资源对象",required = true)
    val currentResourceObject: String?,
    @ApiModelProperty("输入参数(可以为多级结构体)",required = false)
    val inputParams: Map<String, Any>? = emptyMap(),
    @ApiModelProperty("返回资源对象协议",required = true)
    val resourceObjectProtocol: String = ResourceObjectProtocolEnum.StatefulSet.name,
    @ApiModelProperty("返回资源对象协议版本",required = false)
    val resourceObjectProtocolVersion: String? = null,
    @ApiModelProperty("返回资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML
)

@ApiModel("发布组成版本请求")
data class DeployAssembledVersionRequest (
    @ApiModelProperty("应用名", required = true)
    val appName: String,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("返回发布版本协议",required = true)
    val versionProtocol: String,
    @ApiModelProperty("初始工作负载",required = false)
    val workload: String? = null,
    @ApiModelProperty("额外输入参数",required = false)
    val extendedParams: Map<String, Any>? = emptyMap(),
    @ApiModelProperty("用户输入参数，例如通过变更", required = false)
    val userTraits: List<UserTrait> = emptyList(),
)

@ApiModel("用户特性输入")
data class UserTrait(
    @ApiModelProperty("特性Key", required = true)
    val key: String,
    @ApiModelProperty("特性Version", required = false)
    val version: String? = null,
    @ApiModelProperty("特性参数", required = false)
    val content: Map<String, Any> = emptyMap(),
    @ApiModelProperty("修改人", required = true)
    val modifier: String,
)

@ApiModel("发布组装CR列表请求")
data class DeployAssembledCRListRequest (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("版本stackPkId",required = true)
    val stackPkId: String,
    @ApiModelProperty("输入参数(可以为多级结构体)",required = false)
    val inputParams: Map<String, Any>? = emptyMap(),
    @ApiModelProperty("返回资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML
)

/**
 * 和五元组注入相关的protocol
 *
 */
enum class ResourceObjectProtocolEnum {
    StatefulSet,
    CloneSet,
    ServerlessApp,
    RollingSet,
}

enum class ResourceObjectProtocolExtEnum{
    FedCluster
}

enum class CrType {
    VIP_SERVICE,
    ACNI_SECRET,
}

inline fun getResourceObjectProtocol(protocol: String): ResourceObjectProtocolEnum {
    return ResourceObjectProtocolEnum.valueOf(protocol)
}

enum class ResourceObjectProtocolVersionEnum {
    V1
}

@ApiModel("资源对象场景枚举")
enum class ResourceObjectSceneEnum{
    @ApiModelProperty("扩容")
    SCALE_OUT,
    @ApiModelProperty("发布")
    DEPLOY
}

@ApiModel("资源对象格式枚举")
enum class ResourceObjectFormatEnum {
    /**
     * yaml
     */
    YAML,

    /**
     * json
     */
    JSON
}
@ApiModel("资源对象合并方式枚举")
enum class ResourceObjectMergeTypeEnum {
    /**
     * 三路合并
     */
    THREE_WAY_MERGE,

    /**
     * 非三路合并（普通）
     */
    ORDINARY
}