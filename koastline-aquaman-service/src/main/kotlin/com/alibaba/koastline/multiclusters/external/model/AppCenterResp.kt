package com.alibaba.koastline.multiclusters.external.model

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.fasterxml.jackson.annotation.JsonProperty

data class AppCenterResponse<T>(
    val successful: Boolean,
    val errorCode: String?,
    val errorMsg: String?,
    @JsonProperty("object")
    val data: T?
)

data class AppInfo(
    val id: Long,
    val buId: Long,
    val name: String,
    val productId: Long,
    @JsonProperty("product_fullline_id_path")
    val productFullLineIdPath: String,
    val status: AppStatusEnum,
    val runtimeAppId: Long? = null,
    val runtimeAppName: String? = null,
    val level: AppLevelEnum? = null,
    val tags: Map<String, Any>? = null,
    val devloyType: String? = null,
) {
    fun getProductLinePath(): String {
        return "${buId}${MatchScopeService.BU_PRODUCT_LINE_SPLITTER}${productFullLineIdPath}"
    }
}

data class AppBasInfo(
    val deploySys: String? = null,
    val deployWay: String? = null
)

data class AppInfoV2(
    val tags: Map<String, AppcenterTag>? = null,
    val subtype: AppSubType,
)

data class AppcenterTag(
    val key: String,
    val value: String? = null,
    val isDeleted: String,
)

enum class AppLevelEnum {
    // 未定义
    UNDEFINED,

    // A-集团核心
    GRADE0,

    // B-BU核心
    GRADE1,

    // C-非核心
    GRADE2,

    // 边缘应用
    GRADE4
}
enum class AppDeployWay {
    /**
     * severless app
     */
    serverless,

    /**
     * 基座
     */
    runtime,
}

/**
 * 应用标签
 */
data class AppTag(
    val id: Long,
    /**
     * 应用ID
     */
    val appId: Long,
    /**
     * 标签KEY
     */
    val tagKey: String,
    /**
     * 标签Value
     */
    val tagValue: String,
)

data class AppCenterResult(
    /**
     * 返回结果是否正确
     */
    val valid: Boolean? = false
)

data class AppSubType(
    val subtype: AppSubTypeEnum
)

enum class AppSubTypeEnum {
    NORMAL,
    T4,
    RPM,
    SERVERLESS,
    RUNTIME,
    EMPTY,
    APPSTACK,
    FUNCTION,
    ALGORITHM_MODEL,
    CDN,
    CLOUD_NATIVE,
    FUNC_GROUP,
}

enum class AppStatusEnum {
    /**
     * 新建
     */
    NEW,
    /**
     * 审核中
     */
    IN_AUDIT,
    /**
     * 未通过审核
     */
    REJECT_AUDIT,
    /**
     * 已撤销
     */
    IS_REVOKED,
    /**
     * 待上线
     */
    READY_TO_ONLINE,
    /**
     * 已经上线
     */
    ONLINE,
    /**
     * 待下线
     */
    READY_TO_OFFLINE,
    /**
     * 已下线
     */
    OFFLINE,
    /**
     * 删除
     */
    DELETED,
    /**
     * 审核
     */
    REVIEW,
    /**
     * 待删除
     */
    READY_TO_DELETE,
    /**
     * 下线失败
     */
    OFFLINE_FAILED,
    /**
     * 冻结的
     */
    FROZEN,
}