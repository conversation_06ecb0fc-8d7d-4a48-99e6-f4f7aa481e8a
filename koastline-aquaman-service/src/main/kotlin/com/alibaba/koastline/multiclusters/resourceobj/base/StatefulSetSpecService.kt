package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.INPLACESET_BLACK_LABEL_LIST
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.INPLACESET_WHITE_BASE_WORKLOAD_LABEL_LIST
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.INPLACESET_WHITE_WORKLOAD_ANNO_LIST
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.INPLACESET_WHITE_WORKLOAD_LABEL_LIST
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.NORMANDY
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.AbstractWorkloadSpecService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.google.gson.Gson
import io.kubernetes.client.openapi.models.*
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * StatefulSet服务
 * 预构建StatefulSet Workload
 * 考虑到全局默认属性设置的稳定性，暂时将初始默认属性还放在代码服务中，待后期评估是否迁移到配置中（做好全局配置变更安全防控&校验&审批流程）
 */
@Component("StatefulSetSpecService")
class StatefulSetSpecService : AbstractWorkloadSpecService(),InitializingBean {

    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var workloadSpecFactory: WorkloadSpecFactory
    @Autowired
    lateinit var baseSpecService: BaseSpecService

    val gson = Gson()

    override fun afterPropertiesSet() {
        workloadSpecFactory.registryWorkloadSpecFacade(ResourceObjectProtocolEnum.StatefulSet, this)
    }

    override fun checkSpec(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        when(resourceObjectFormatEnum) {
            ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                JsonUtils.writeValueAsString(YamlUtils.load(resourceObjectSpecStr)), V1StatefulSet::class.java)
            else -> JsonUtils.gsonReadValue(resourceObjectSpecStr, V1StatefulSet::class.java)
        }
    }

    /**
     * 获取预组装的sts workload
     */
    override fun getBaseSpec(context: WorkloadSpecContext): Map<String, Any> {
        return cloudCmdbApi.getEnvBaselineSpec(context.envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val v1StatefulSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(YamlUtils.load(this)), V1StatefulSet::class.java)
            fillAttributesToStatefulSet(v1StatefulSet, context)
            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(v1StatefulSet))
        }
    }

    override fun postModifyBaseSpec(
        resourceObjectSpec: Map<String, Any>,
        context: WorkloadSpecContext
    ): Map<String, Any> {
        val statefulSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(resourceObjectSpec), V1StatefulSet::class.java)
        baseSpecService.patchAffinityAndTolerations(statefulSet.spec!!.template!!)
        baseSpecService.modifyForMixDeployCluster(statefulSet.spec!!.template!!, context.workloadMetadataConstraint)
        return YamlUtils.load(JsonUtils.gsonWriteValueAsString(statefulSet))
    }

    override fun getDeploySpec(stackPkId: String, currentResourceObjectSpecStr: String?,
                               currentResourceObjectFormatEnum: ResourceObjectFormatEnum, workloadMetadataConstraint: WorkloadMetadataConstraint): Map<String, Any> {
        checkNotNull(currentResourceObjectSpecStr)
        return cloudCmdbApi.getEnvBaselineSpecByStackPKId(stackPkId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val baseStatefulSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(YamlUtils.load(this)), V1StatefulSet::class.java)
            val currentV1StatefulSet = when(currentResourceObjectFormatEnum) {
                ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                    JsonUtils.writeValueAsString(YamlUtils.load(currentResourceObjectSpecStr)), V1StatefulSet::class.java)
                else -> JsonUtils.gsonReadValue(currentResourceObjectSpecStr, V1StatefulSet::class.java)
            }
            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(
                buildDeploySpec(baseStatefulSet, currentV1StatefulSet, workloadMetadataConstraint)
            ))
        }
    }

    private fun buildDeploySpec(baseStatefulSet: V1StatefulSet, currentV1StatefulSet: V1StatefulSet, workloadMetadataConstraint: WorkloadMetadataConstraint): V1StatefulSet {
        //组装当前STS信息
        baseStatefulSet.metadata!!.name = currentV1StatefulSet.metadata!!.name
        baseStatefulSet.metadata!!.namespace = currentV1StatefulSet.metadata!!.namespace
        //发布三路合并去除非用户干预信息
        baseStatefulSet.metadata!!.annotations = baseStatefulSet.metadata!!.annotations ?.filter {
            INPLACESET_WHITE_WORKLOAD_ANNO_LIST.contains(it.key)
        }
        baseStatefulSet.metadata!!.labels = currentV1StatefulSet.metadata!!.labels?.filter {
            INPLACESET_WHITE_WORKLOAD_LABEL_LIST.contains(it.key)
        }.orEmpty() + baseStatefulSet.metadata!!.labels?.filter {
            INPLACESET_WHITE_BASE_WORKLOAD_LABEL_LIST.contains(it.key)
        }.orEmpty()
        baseStatefulSet.spec!!.template.metadata ?.let { metadata ->
            metadata.labels = metadata.labels ?.filter {
                !INPLACESET_BLACK_LABEL_LIST.contains(it.key)
            }
        }
        baseSpecService.setDefaultAffinity(baseStatefulSet.spec!!.template!!.spec!!, workloadMetadataConstraint.clusterId)
        baseSpecService.patchAffinityAndTolerations(baseStatefulSet.spec!!.template!!)
        return baseStatefulSet
    }

    /**
     * 填充扩展信息到StatefulSet
     * @param statefulSet
     * @param context
     */
    private fun fillAttributesToStatefulSet(statefulSet: V1StatefulSet, context: WorkloadSpecContext) {
        setLastAppliedConfiguration(statefulSet, getLastAppliedConfiguration(statefulSet))
        setBasicInfo(statefulSet, context.workloadMetadataConstraint.appName, context)
        setReplicas(statefulSet.spec!!)
        setStatefulSetMetadata(statefulSet, context)
        setStatefulSetSelector(statefulSet.spec!!, context.workloadMetadataConstraint)
        baseSpecService.setPodMetadata(statefulSet.spec!!.template!!, context.workloadMetadataConstraint)
        baseSpecService.setDnsPolicy(statefulSet.spec!!.template!!.spec!!)
        baseSpecService.setDefaultAffinity(statefulSet.spec!!.template!!.spec!!, context.workloadMetadataConstraint.clusterId)
        baseSpecService.setIsolationEnvVars(statefulSet.spec!!.template!!.spec!!.containers, context.envStackId, context.workloadMetadataConstraint.stage)
    }

    /**
     * 填充基础信息
     */
    private fun setBasicInfo(statefulSet: V1StatefulSet, appName: String, context: WorkloadSpecContext) {
        statefulSet.apiVersion = STATEFULSET_API_VERSION
        statefulSet.kind = STATEFULSET_KIND_NAME
        (statefulSet.metadata ?: V1ObjectMeta().apply { statefulSet.metadata = this }) .let { metadata ->
            metadata.name = baseSpecService.buildWorkloadName(appName)
            metadata.namespace = context.workloadMetadataConstraint.namespace ?: WorkloadUtils.buildNamespace(appName)
        }
    }

    /**
     * 设置初始副本为0
     */
    private fun setReplicas(statefulSetSpec: V1StatefulSetSpec) {
        statefulSetSpec.replicas = 0
    }

    /**
     * 设置workload meta data
     */
    private fun setStatefulSetMetadata(statefulSet: V1StatefulSet, context: WorkloadSpecContext) {
        (statefulSet.metadata ?: V1ObjectMeta().apply { statefulSet.metadata = this }).let { metadata ->
            //add labels
            (metadata.labels ?: mutableMapOf<String, String>().apply { metadata.labels = this }).let { labels->
                // 六元组覆盖
                labels.putAll(baseSpecService.getDefaultLabels(context.workloadMetadataConstraint))
                labels[STATEFULSET_LABEL_MODE] = labels[STATEFULSET_LABEL_MODE] ?: "sigma"
                labels[BaseSpecService.INPLACESET_LABEL_STACKID] = labels[BaseSpecService.INPLACESET_LABEL_STACKID] ?: context.envStackId
                labels[BaseSpecService.POD_UPSTREAM_COMPONENT] = labels[BaseSpecService.POD_UPSTREAM_COMPONENT] ?: kotlin.run { NORMANDY }
            }
            //add annotations
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[BaseSpecService.DISABLE_CASCADING_DELETION] = annotations[BaseSpecService.DISABLE_CASCADING_DELETION] ?: "true"
            }
        }
    }

    /**
     * 设置workload selector
     */
    private fun setStatefulSetSelector(statefulSetSpec: V1StatefulSetSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        (statefulSetSpec.selector ?: V1LabelSelector().apply { statefulSetSpec.selector = this }).let { selector ->
            (selector.matchLabels ?: mutableMapOf<String, String>().apply { selector.matchLabels = this}).let { matchLabels ->
                // 六元组覆盖
                matchLabels.putAll(baseSpecService.getDefaultLabels(workloadMetadataConstraint))
            }
        }
    }








    private fun setLastAppliedConfiguration(statefulSet: V1StatefulSet, lastAppliedConfiguration: String) {
        (statefulSet.metadata ?: V1ObjectMeta().apply { statefulSet.metadata = this }).let { metadata ->
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[BaseSpecService.LAST_APPLIED_CONFIGURATION] = lastAppliedConfiguration
            }
        }
    }

    private fun getLastAppliedConfiguration(statefulSet: V1StatefulSet): String {
        val lastAppliedConfigurationStr = gson.toJson(V1StatefulSet().apply {
                this.spec = V1StatefulSetSpec().apply {
                    this.template = statefulSet.spec!!.template
                }
            }
        )
        //重新序列化做lastAppliedConfiguration处理，防止污染原sts
        val lastAppliedConfigurationSts = gson.fromJson(lastAppliedConfigurationStr, V1StatefulSet::class.java).apply {
            this.spec?.template?.metadata?.labels?.let { labels ->
                BaseSpecService.TEMPLATE_LABEL_BLACK_LIST.forEach {
                    labels.remove(it)
                }
            }
        }
        return gson.toJson(lastAppliedConfigurationSts)
    }







    companion object {
        private const val STATEFULSET_API_VERSION = "apps/v1"
        private const val STATEFULSET_KIND_NAME = "StatefulSet"
        private const val STATEFULSET_HOST_NAME_UNIT_TAG_LENGTH_LIMIT = 10

        //labels

        //statefulset labels
        private const val STATEFULSET_LABEL_MODE = "statefulset.sigma.ali/mode"

        const val STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME = "ackeeYaml"
    }
}