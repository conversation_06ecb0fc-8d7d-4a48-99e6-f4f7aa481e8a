package com.alibaba.koastline.multiclusters.apre.params

/**
 * 运行时环境缺省特性应用范围
 */
enum class ApREDefaultFeatureUsageEnum {
    /**
     * 缺省导入，如果ApRE -> 未指定特性 || 指定特性&未指定规格，则默认引入
     */
    DEFAULT_IMPORT,

    /**
     * 关联导入，如果需要ApRE -> 指定特性&未指定规格，则默认引入
     */
    REFERENCE_IMPORT
}

inline fun checkFeatureUsage(featureUsage: String): String {
    return ApREDefaultFeatureUsageEnum.valueOf(featureUsage).name
}