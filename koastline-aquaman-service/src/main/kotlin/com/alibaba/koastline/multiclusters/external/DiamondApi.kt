package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.schedule.model.DiamondResult
import org.springframework.stereotype.Component

/**
 * @author:    <EMAIL>
 * @description:  Diamond调用
 * @date:    2025/2/14 9:54 AM
 */
@Component
class DiamondApi {
    /**
     * 查询diamond配置对应的IP列表
     */
    @ExternalCall(SYS_CALLED)
    fun queryDiamondConfigIps(paramUrl: String): List<String> {
        val diamondApiUrlForQueryIps =
            "${DIAMOND_URL_HOST_QUERY}?${paramUrl.split("?")[1]}"
        val diamondResult = HttpClientUtils.httpGet(
            url = diamondApiUrlForQueryIps
        ).run {
            JsonUtils.readValue(this, DiamondResult::class.java)
        }
        return diamondResult.data.flatMap { it.value }
    }

    companion object {
        const val SYS_CALLED = "diamond"
        const val DIAMOND_URL_HOST_QUERY = "https://diamond-inner.alibaba-inc.com/diamond-ops/pop/aone/hostQuery"
        const val DIAMOND_DNS = "diamond-inner.alibaba-inc.com"
    }
}