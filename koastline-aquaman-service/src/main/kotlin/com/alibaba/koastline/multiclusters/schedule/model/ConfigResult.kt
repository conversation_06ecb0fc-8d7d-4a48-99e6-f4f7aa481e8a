package com.alibaba.koastline.multiclusters.schedule.model

import com.fasterxml.jackson.annotation.JsonProperty

data class ConfigResult(
    val successful: <PERSON><PERSON><PERSON>,
    @JsonProperty("object")
    val data: ConfigObject? = null
)

data class ConfigObject(
    val publishParams: List<AppBoxParam>
)

data class AppBoxParam(
    /**
     * 参数名，作为判断条件
     */
    var paramName: String? = null,
    /**
    * 参数中文名
    */
    val cnName: String? = null,
    /**
    * 新值
    */
    val newValue: String? = null,
    /**
     * 旧值
     */
    val oldValue: String? = null
)

data class SpeResult(
    val success: Boolean,
    val result: SpeData? = null
)

data class SpeData(
    val ips: List<String>
)

data class DiamondResult(
    val successful: <PERSON><PERSON>an,
    @JsonProperty("object")
    val data: Map<String, List<String>> = emptyMap(),
    val errorMsg: String? = null,
)
