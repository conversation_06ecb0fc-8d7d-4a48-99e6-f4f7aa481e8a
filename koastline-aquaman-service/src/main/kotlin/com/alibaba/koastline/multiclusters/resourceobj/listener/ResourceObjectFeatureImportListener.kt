package com.alibaba.koastline.multiclusters.resourceobj.listener

import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum

/**
 * 特性注入监听器
 */
interface ResourceObjectFeatureImportListener {

    /**
     * 验证特性注入
     * @baseResourceObjectSpec 环境基线版本,yaml格式
     * @resourceObjectProtocol 资源对象协议
     * @featureSpec 特性注入片段,yaml格式
     * @throws FeatureConflictException 特性冲突异常
     */
    @Throws(FeatureConflictException::class)
    fun watch(
        baseResourceObjectSpec: String,
        resourceObjectProtocol: ResourceObjectProtocolEnum,
        featureSpec: String,
    )
}

class FeatureConflictException(msg: String) : BizException(msg)