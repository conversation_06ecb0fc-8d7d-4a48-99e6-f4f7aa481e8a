package com.alibaba.koastline.multiclusters.resourceobj.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class PatchStrategyDefinition(
    @JsonProperty("protocols")
    val protocols: List<Protocol>,
    @JsonProperty("strategies")
    val strategies: List<Strategy>,
    @JsonProperty("defaultStrategies")
    val defaultStrategies: List<Strategy>? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Protocol(
    @JsonProperty("kind")
    val kind: String,
    @JsonProperty("version")
    val version: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Strategy(
    @JsonProperty("ref")
    val ref: String,
    @JsonProperty("patchStrategy")
    val patchStrategy: String,
    @JsonProperty("patchMergeKey")
    val patchMergeKey: String? = null,
    @JsonProperty("delimiter")
    val delimiter: String? = null,
    @JsonProperty("description")
    val description: String? = null
)