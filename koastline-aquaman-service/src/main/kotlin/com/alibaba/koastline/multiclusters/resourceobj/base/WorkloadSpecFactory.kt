package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.WorkloadSpecFacade
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import org.springframework.stereotype.Component

@Component
class WorkloadSpecFactory {
    private val workloadSpecFacadeMap =  mutableMapOf<ResourceObjectProtocolEnum, WorkloadSpecFacade>()

    fun registryWorkloadSpecFacade(resourceObjectProtocolEnum: ResourceObjectProtocolEnum, workloadSpecFacade: WorkloadSpecFacade) {
        workloadSpecFacadeMap[resourceObjectProtocolEnum] ?.let {
            throw ResourceObjectException("重复的WorkloadSpecFacade服务注册，protocol:${resourceObjectProtocolEnum},workloadSpecFacade:${workloadSpecFacade}")
        } ?:let {
            workloadSpecFacadeMap[resourceObjectProtocolEnum] = workloadSpecFacade
        }
    }

    fun getWorkloadSpecFacade(resourceObjectProtocolEnum: ResourceObjectProtocolEnum): WorkloadSpecFacade {
        return workloadSpecFacadeMap[resourceObjectProtocolEnum] ?:let {
            throw ResourceObjectException("未找到协议：${resourceObjectProtocolEnum}对应的WorkloadSpecFacade服务")
        }
    }
}