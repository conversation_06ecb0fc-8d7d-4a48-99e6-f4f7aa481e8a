package com.alibaba.koastline.multiclusters.schedule.service.fiter.facade

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent

/**
 * 调度计算资源池过滤器
 */
interface ScheduleFilterFacade {

    /**
     * 针对运行时环境关联虚拟资源池进行过滤（过滤因子如：负载、额度等）
     */
    fun doScheduleFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration
}