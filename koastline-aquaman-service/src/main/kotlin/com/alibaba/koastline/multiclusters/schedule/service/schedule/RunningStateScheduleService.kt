package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.ComponentDataService
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.params.ComponentCodeEnum
import com.alibaba.koastline.multiclusters.apre.params.ComponentRefObjectTypeEnum
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService.Companion.CLONESET_NAMESPACE_LIST
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum.CloneSet
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum.RollingSet
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum.ServerlessApp
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum.StatefulSet
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostResourceScopeService
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeCreateDto
import com.alibaba.koastline.multiclusters.runtime.RuntimeService
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadDesc
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.RUNTIME
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.ScheduleFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 运行态资源调度计算
 * 功能：
 * 针对运行态资源按照集群归并计算
 * 适配场景：
 * 定向重启、定向下线、置换（定向缩容部分）、发布（原地升级&同集群滚动升级）、PodList
 */
@Component(value = "RunningStateScheduleService")
class RunningStateScheduleService: ScheduleFacade, InitializingBean {
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var skylineApi: SkylineApi
    @Autowired
    lateinit var defaultClusterService: DefaultClusterService
    @Autowired
    lateinit var componentDataService: ComponentDataService
    @Autowired
    lateinit var runtimeService: RuntimeService
    @Autowired
    lateinit var runtimeScheduleService: RuntimeScheduleService
    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        if (runtimeScheduleService.isStandardRuntimeSchedule(
                scheduleEnvType = content.scheduleRequestParam?.scheduleEnvType,
                appName = content.resourceScope.appName
            )
        ) {
            return buildStandardRuntimeScheduleResult(content)
        }

        if (runtimeService.isHaloRuntimeSchedule(
                scheduleEnvType = content.scheduleRequestParam ?.scheduleEnvType,
                envStackId = content.resourceScope.envStackId)) {
            return buildHaloRuntimeScheduleResult(content)
        }
        return buildCommonScheduleResult(content)
    }

    /**
     * 获取runtime workload
     * 2024.1.2 workloadName可能存在 基座轮转过程中
     * @param content
     * @return
     */
    private fun buildStandardRuntimeScheduleResult(content: ScheduleRequestContent): ScheduleResult {
        val resourceScope =  content.resourceScope

        val runtimeWorkloadResult = runtimeScheduleService.requireRunningWorkload(
            appName = resourceScope.appName,
            resourceGroup = resourceScope.resourceGroup,
            envStackId = resourceScope.envStackId,
            scheduleEnvType = RUNTIME
        )

        val finalWorkloadExpectedStates = mutableListOf<WorkloadExpectedState>()

        runtimeWorkloadResult.workloadExpectedStates.forEach { workloadExpectedState ->
            val workloadMetadataConstraint = workloadExpectedState.workloadMetadataConstraint
            val clusterProfile = workloadExpectedState.clusterProfile?.copy(
                componentDataList = componentDataService.listComponentDataByRefAndCode(
                    ComponentRefObjectTypeEnum.CLUSTER_UUID.name, workloadMetadataConstraint.clusterId, listOf(
                        ComponentCodeEnum.POD_LISTER.name
                    )
                )
            )
            finalWorkloadExpectedStates.add(
                WorkloadExpectedState(
                    workloadMetadataConstraint = workloadMetadataConstraint,
                    clusterProfile = clusterProfile,
                    params = workloadExpectedState.params,
                    workloadDesc = WorkloadDesc(
                        scheduleEnvType = RUNTIME, resourceObjectProtocolEnum = RollingSet
                    )
                )
            )
        }
        return ScheduleResult(finalWorkloadExpectedStates)
    }

    private fun buildHaloRuntimeScheduleResult(content: ScheduleRequestContent): ScheduleResult {
        return runtimeService.computeRuntimeScheduleResult(content.resourceScope.envStackId!!).run {
            this.copy(
                workloadExpectedStates = workloadExpectedStates.map { workloadExpectedState ->
                    fillPodListComponent(workloadExpectedState = workloadExpectedState, content.includeClusterInfoToResult).copy(
                        workloadDesc = WorkloadDesc(scheduleEnvType = RUNTIME, resourceObjectProtocolEnum = CloneSet)
                    )
                }
            )
        }
    }

    private fun buildCommonScheduleResult(content: ScheduleRequestContent): ScheduleResult {
        val resourceScope = content.resourceScope
        val workloadExpectedStates = mutableListOf<WorkloadExpectedState>()
        val cachedClusterProfileNew = mutableMapOf<String, ClusterProfileNew>()
        skylineApi.listWorkloadMetadataConstraintThroughServerList(resourceScope.appName,
            resourceScope.envStackId,
            resourceScope.resourceGroup,
            resourceScope.resourceSNList
        ).forEach { workloadMetadataConstraintAssemble ->
            val params = mutableMapOf<String, String>()
            params[ScheduleResultParamConstants.RESOURCE_NUM] = workloadMetadataConstraintAssemble.num.toString()
            if (workloadMetadataConstraintAssemble.resourceSnList.isEmpty()) {
                params[ScheduleResultParamConstants.RESOURCE_SN_LIST] = workloadMetadataConstraintAssemble.resourceSnList.joinToString(",")
            }
            val scheduleEnvType = content.scheduleRequestParam ?.scheduleEnvType ?:ASI
            workloadExpectedStates.add(
                WorkloadExpectedState(
                    workloadMetadataConstraint = workloadMetadataConstraintAssemble.workloadMetadataConstraint,
                    clusterProfile = if (content.includeClusterInfoToResult) {
                        val clusterId = workloadMetadataConstraintAssemble.workloadMetadataConstraint.clusterId
                        cachedClusterProfileNew[clusterId] ?: run {
                            val clusterProfile = defaultClusterService.getSimpleClusterProfileDataByClusterId(workloadMetadataConstraintAssemble.workloadMetadataConstraint.clusterId).run {
                                ClusterProfileNew(
                                    clusterId = this.clusterId,
                                    clusterName = this.clusterName,
                                    clusterProvider = this.clusterProvider,
                                    clusterType = this.clusterType,
                                    siteList = this.site.split(","),
                                    componentDataList = componentDataService.listComponentDataByRefAndCode(
                                        ComponentRefObjectTypeEnum.CLUSTER_UUID.name, this.clusterId, listOf(
                                            ComponentCodeEnum.POD_LISTER.name)),
                                    useType = this.useType
                                )
                            }
                            cachedClusterProfileNew[clusterId] = clusterProfile
                            clusterProfile
                        }
                    } else null,
                    params = params,
                    workloadDesc = WorkloadDesc(
                        scheduleEnvType = scheduleEnvType,
                        resourceObjectProtocolEnum = computeResourceObjectProtocol(workloadMetadataConstraintAssemble.workloadMetadataConstraint, scheduleEnvType)
                    )
                )
            )
        }
        return ScheduleResult(
            workloadExpectedStates
        )
    }

    private fun computeResourceObjectProtocol(workloadMetadataConstraint: WorkloadMetadataConstraint, scheduleEnvType: ScheduleEnvType): ResourceObjectProtocolEnum {
        if (scheduleEnvType == SERVERLESS_APP) {
            return ServerlessApp
        }
        if (CLONESET_NAMESPACE_LIST.contains(workloadMetadataConstraint.namespace)) {
            return CloneSet
        }
        return StatefulSet
    }

    private fun fillPodListComponent(workloadExpectedState: WorkloadExpectedState, includeClusterInfoToResult: Boolean): WorkloadExpectedState {
        if (!includeClusterInfoToResult) {
            return workloadExpectedState
        }
        workloadExpectedState.clusterProfile ?.let { clusterProfile ->
            return workloadExpectedState.copy(
                clusterProfile = clusterProfile.copy(
                    componentDataList = componentDataService.listComponentDataByRefAndCode(
                        ComponentRefObjectTypeEnum.CLUSTER_UUID.name, clusterProfile.clusterId, listOf(
                            ComponentCodeEnum.POD_LISTER.name))
                )
            )
        }
        return workloadExpectedState.copy(
            clusterProfile = defaultClusterService.getSimpleClusterProfileDataByClusterId(workloadExpectedState.workloadMetadataConstraint.clusterId).run {
                ClusterProfileNew(
                    clusterId = this.clusterId,
                    clusterName = this.clusterName,
                    clusterProvider = this.clusterProvider,
                    clusterType = this.clusterType,
                    siteList = this.site.split(","),
                    componentDataList = componentDataService.listComponentDataByRefAndCode(
                        ComponentRefObjectTypeEnum.CLUSTER_UUID.name, this.clusterId!!, listOf(
                            ComponentCodeEnum.POD_LISTER.name)),
                    useType = this.useType
                )
            }
        )
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.RUNNING_STATE_SCHEDULE, this)
    }
}