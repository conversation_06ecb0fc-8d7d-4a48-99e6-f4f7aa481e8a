package com.alibaba.koastline.multiclusters.resourceobj.base.facade

import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectPostCheckException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectPreCheckException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.CryptUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.LAST_POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.ROLLBACKINDEPLOY_ALARM
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum

abstract class  AbstractWorkloadSpecService: WorkloadSpecFacade {
    val log by logger()

    override fun preCheck(resourceObjectSpecStr: String?, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        try {
            checkNotNull(resourceObjectSpecStr)
            checkSpec(resourceObjectSpecStr, resourceObjectFormatEnum)
        } catch (e: Exception) {
            log.error(e.message, e)
            throw ResourceObjectPreCheckException("preCheck spec error, msg:${e.message}, resourceObjectSpecStr:${resourceObjectSpecStr}")
        }
    }

    override fun postCheck(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        try {
            checkSpec(resourceObjectSpecStr, resourceObjectFormatEnum)
        } catch (e: Exception) {
            log.error(e.message, e)
            throw ResourceObjectPostCheckException("postCheckSpec spec error, msg:${e.message}, resourceObjectSpecStr:${resourceObjectSpecStr}")
        }
    }

    override fun postCheckForScaleOut(
        currentResourceObjectSpecStr: String?,
        patchedResourceObjectSpecStr: String,
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
    ) {
        if (currentResourceObjectSpecStr == null) {
            return
        }
        val currentPodHash = addPodHashLabel(currentResourceObjectSpecStr, resourceObjectFormatEnum).second
        val patchedPodHash = addPodHashLabel(patchedResourceObjectSpecStr, resourceObjectFormatEnum).second
        if (currentPodHash != patchedPodHash) {
            log.error("postCheckForScaleOut error, currentPodHash:$currentPodHash, patchedPodHash:$patchedPodHash, currentResourceObjectSpecStr:$currentResourceObjectSpecStr, patchedResourceObjectSpecStr:$patchedResourceObjectSpecStr")
            throw ResourceObjectPostCheckException("postCheckForScaleOut spec error, currentPodHash:$currentPodHash, patchedPodHash:$patchedPodHash")
        }
    }

    abstract fun checkSpec(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum)

    override fun postApply(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum): String {
        val applied = addPodHashLabel(resourceObjectSpecStr, resourceObjectFormatEnum)
        return applied.first
    }

    /*
    paas pod spec hash calculate, which indicate whether pod will restart
    https://aliyuque.antfin.com/xtask/igz1p8/emgu7fiv7509p0ma#gzEjm
     */
    fun addPodHashLabel(
        resourceObjectSpecStr: String,
        resourceObjectFormatEnum: ResourceObjectFormatEnum
    ): Pair<String, String> {
        val podSpec = ResourceObjectService.getPodSpecFromWorkloadSpec(YamlUtils.load(resourceObjectSpecStr))
        val containers = podSpec?.get("containers") as? MutableList<MutableMap<String, Any>?>?
        containers?.forEach { container ->
            container?.remove("resources")
            (container?.get("env") as? MutableList<MutableMap<String, Any>?>?)?.removeIf {
                it?.get("name") == "SIGMA_LOG_SUFFIX"
            }
        }
        val filteredPodSpec = mapOf<String, Any>("containers" to containers as Any)
        var resourceObjectSpec = YamlUtils.load(resourceObjectSpecStr)
        resourceObjectSpec = ResourceObjectService.preLoadResourceSpecLabels(resourceObjectSpec)
        val workloadLabels =
            ResourceObjectService.getLabelsFromWorkloadSpec(resourceObjectSpec)
        val oldOldPodTemplateHash = workloadLabels?.get(LAST_POD_TEMPLATE_HASH) as String?
        val oldPodTemplateHash = workloadLabels?.get(POD_TEMPLATE_HASH) as String?
        val newPodTemplateHash = CryptUtils.md5Encrypt(YamlUtils.dump(filteredPodSpec))
        if (oldOldPodTemplateHash != null && oldOldPodTemplateHash != newPodTemplateHash && oldPodTemplateHash != newPodTemplateHash) {
            workloadLabels?.put(ROLLBACKINDEPLOY_ALARM, true.toString())
        } else {
            workloadLabels?.remove(ROLLBACKINDEPLOY_ALARM)
        }
        workloadLabels?.put(POD_TEMPLATE_HASH, newPodTemplateHash)
        oldPodTemplateHash?.run {
            workloadLabels?.put(LAST_POD_TEMPLATE_HASH, this)
        }
        val patchedResourceObjectSpec = when (resourceObjectFormatEnum) {
            ResourceObjectFormatEnum.YAML -> YamlUtils.dump(resourceObjectSpec)
            else -> JsonUtils.writeValueAsString(resourceObjectSpec)
        }
        return Pair(patchedResourceObjectSpec, newPodTemplateHash)

    }
}