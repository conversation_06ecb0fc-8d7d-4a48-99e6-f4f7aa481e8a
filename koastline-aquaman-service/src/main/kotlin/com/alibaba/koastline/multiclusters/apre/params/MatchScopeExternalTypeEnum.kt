package com.alibaba.koastline.multiclusters.apre.params

/**
 * 匹配范围枚举
 */
enum class MatchScopeExternalTypeEnum {
    AONE_PRODUCTLINE,
    APPLICATION,
    RESOURCE_GROUP,
    ENV_STACKID,
    CLUSTER_ID, // only for exclusion usage
    //fed
    FED_CLUSTER,
    // 当前暂不完善QUOTA_NAME的优先级 & 算法
    // 目前看做QUOTA_NAME生效范围的特性无其他父类/子类更高优先级特性
    QUOTA_NAME,
}

val MatchScopeExternalTypePriority = mapOf(
    // MatchScopeExternalTypeEnum.QUOTA_NAME.name to 1,
    MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name to  1,
    MatchScopeExternalTypeEnum.APPLICATION.name to  2,
    MatchScopeExternalTypeEnum.ENV_STACKID.name to  3,
    MatchScopeExternalTypeEnum.RESOURCE_GROUP.name to  4,
)

inline fun checkMatchScopeExternalType(externalType: String): String {
    return MatchScopeExternalTypeEnum.valueOf(externalType).name
}