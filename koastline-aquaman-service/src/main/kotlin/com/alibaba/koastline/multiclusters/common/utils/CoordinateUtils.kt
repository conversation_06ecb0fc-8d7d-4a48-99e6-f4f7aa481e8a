package com.alibaba.koastline.multiclusters.common.utils

object CoordinateUtils {

    /**
     * Coordinate(externalId / externalType / stage / unit / site)的查询参数进行校验
     *
     * @param stage
     * @param unit
     * @param site
     * @param externalId
     * @param externalType
     */
    fun coordinatesPropertiesValidate(
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        externalId: String,
        externalType: String,
    ) {
        require(externalId.isNotBlank()) { "externalId(appName, appGroup) cannot be blank" }

        require(externalType.isNotBlank()) { "externalType cannot be blank" }

        stage?.let {
            require(it.isNotBlank()) { "stage cannot blank" }
        }

        site?.let {
            require(it.isNotBlank()) { "site cannot be blank" }
        }

        unit?.let {
            require(it.isNotBlank()) { "unit cannot be blank" }
        }
    }
}