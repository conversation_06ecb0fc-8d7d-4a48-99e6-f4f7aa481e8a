package com.alibaba.koastline.multiclusters.external.model

import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum


data class NormandyResponse<T>(

    val success: Boolean,
    val code: Int,
    val message: String,
    val data: T,
)

data class CrResourceObjectList(

    val crResourceObjectList: List<CrResourceObject>?,
    val formatEnum: ResourceObjectFormatEnum,
)

data class CrResourceObject(
    val apiVersion: String,
    val kind: String,
    val resourceObject: String?,
)

