package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus

/**
 * <AUTHOR>
 * 区别于旧版集群模型
 * @see com.alibaba.koastline.multiclusters.appenv.model.ClusterProfile
 * 新版集群环境直接关联多组件，之前的gateway、system_component统一存放于component
 */
data class ClusterProfileNew (
    val clusterId: String,
    val clusterName: String,
    val clusterProvider: String,
    val clusterType: String,
    val siteList: List<String>,
    val componentDataList: List<ComponentDataDO>,
    val useType: String,
    val status: String = ClusterStatus.ONLINE.status
)