package com.alibaba.koastline.multiclusters.schedule.model

import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.common.utils.CryptUtils
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.NameSpaceSplitter
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.Instant

@ApiModel("调度计算结果")
data class ScheduleResult(
    val workloadExpectedStates: List<WorkloadExpectedState>,
    val deployMode: DeployMode = DeployMode.NORMAL
)

/**
 * 发布模式
 */
enum class DeployMode {
    /**
     * 普通模式
     */
    NORMAL,
    /**
     * 主机模式
     */
    HOST
}

@ApiModel("Workload调度计算预期终态结果")
data class WorkloadExpectedState(
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("集群数据")
    val clusterProfile: ClusterProfileNew? = null,
    @ApiModelProperty("Workload描述信息")
    val workloadDesc: WorkloadDesc? = null,
    /**
     * 扩展参数，KEY @see ScheduleResultParamConstants
     */
    @ApiModelProperty("其他参数，基于不同业务场景返回结果不同")
    val params: Map<String, String>
)

/**
 * 核心数据 请勿随意改动属性和唯一键生成规则
 *
 * @property appName
 * @property resourceGroup
 * @property site
 * @property unit
 * @property stage
 * @property subgroup
 * @property clusterId
 * @property namespace
 * @property runtimeId
 */
@ApiModel("Workload运维元数据约束")
data class WorkloadMetadataConstraint(
    @ApiModelProperty("应用名", required = true)
    val appName: String,
    @ApiModelProperty("分组名", required = true)
    val resourceGroup: String,
    @ApiModelProperty("站点", required = true)
    val site: String,
    @ApiModelProperty("单元", required = true)
    val unit: String,
    @ApiModelProperty("用途", required = true)
    val stage: String,
    @ApiModelProperty("逻辑子组", required = true)
    val subgroup: String = "default",
    @ApiModelProperty("集群ID", required = true)
    val clusterId: String,
    @ApiModelProperty("namespace", required = true)
    val namespace: String? = null,
    @ApiModelProperty("RuntimeID", required = false)
    val runtimeId: String? = null,
    @ApiModelProperty("workloadName", required = false)
    val workloadName: String? = null
) {
    fun toMetadataConstraintString(): String {
        return "${appName}&${resourceGroup}&${subgroup}&${site}&${stage}&${unit}&${clusterId}${namespace}&${runtimeId}&${workloadName}"
    }

    fun toThreeTuplesMetadataConstraintString(): String {
        return "${site}$NameSpaceSplitter${stage}$NameSpaceSplitter${unit}"
    }

    fun toWorkloadCoordinateMetaDataConstraintString(): String {
        return "${toThreeTuplesMetadataConstraintString()}$NameSpaceSplitter${subgroup}$NameSpaceSplitter${clusterId}$NameSpaceSplitter${runtimeId}"
    }

    fun toSixMetaWorkloadString(): String{
        return "${appName}$NameSpaceSplitter${resourceGroup}$NameSpaceSplitter${site}$NameSpaceSplitter${unit}$NameSpaceSplitter${stage}$NameSpaceSplitter${clusterId}"
    }

    /**
     * 六元组，应用、分组、站点、单元、用途、集群ID，顺序不能乱
     */
    fun toSixMetaTuple(): List<String>{
        return listOf(
            appName,resourceGroup,site,unit,stage,clusterId
        )
    }

    fun firmValidate4BaseApp() {
        require(appName.isNotBlank()) { "appName cannot be blank" }
        require(resourceGroup.isNotBlank()) { "resourceGroup cannot be blank" }
        require(site.isNotBlank()) { "site cannot be blank" }
        require(unit.isNotBlank()) { "unit cannot be blank" }
        require(stage.isNotBlank()) { "stage cannot be blank" }
        require(subgroup.isNotBlank()) { "subgroup cannot be blank" }
        require(clusterId.isNotBlank()) { "clusterId cannot be blank" }
        require(!namespace.isNullOrBlank()) { "namespace cannot be null or blank" }
        require(!runtimeId.isNullOrBlank()) { "runtimeId cannot be null or blank" }
    }

    fun resolveBaseAppRuntimeId() = CryptUtils.md5Encrypt(
        this.copy(
            namespace = null, runtimeId = null
        ).toMetadataConstraintString()
    )

    /**
     * 6元组拼接时间戳 取Md5 中间 16位置 加上 [$appName:] 前缀
     *
     */
    fun generateWorkloadName() = "$appName:" + CryptUtils.md5Encrypt(
        this.copy(
            namespace = null, runtimeId = null, workloadName = null
        ).toMetadataConstraintString() + NameSpaceSplitter + Instant.now().epochSecond
    ).substring(8, 24)

    fun matchServerlessRuntime(other: WorkloadMetadataConstraint):Boolean{
        return this.site == other.site
                && this.unit == other.unit
                && this.stage == other.stage
                && this.appName == other.appName
                && this.resourceGroup == other.resourceGroup
                && this.runtimeId == other.runtimeId
    }
}

data class WorkloadMetadataConstraintAssemble(
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    var num: Int,
    val resourceSnList: MutableList<String> = mutableListOf()
) {

    /**
     * 基于三元组（站点、单元、用途）判定是否匹配
     */
    fun matches(declaration: Declaration): Boolean {
        return workloadMetadataConstraint.unit == declaration.unit
                && workloadMetadataConstraint.site == declaration.az
                && workloadMetadataConstraint.stage == declaration.stage
    }
}

data class WorkloadDesc(
    /**
     *  调度类型
     */
    val scheduleEnvType: ScheduleEnvType,
    /**
     * Workload协议
     */
    val resourceObjectProtocolEnum: ResourceObjectProtocolEnum
)