package com.alibaba.koastline.multiclusters.resourceobj.model

import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.swagger.annotations.ApiModelProperty

data class AssembledResourceObjectResult (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("资源对象",required = true)
    val resourceObject: String,
    @ApiModelProperty("资源对象协议",required = true)
    val resourceObjectProtocol: String = ResourceObjectProtocolEnum.StatefulSet.name,
    @ApiModelProperty("资源对象协议版本",required = false)
    val resourceObjectProtocolVersion: String? = null,
    @ApiModelProperty("资源对象生成方式",required = true)
    val resourceObjectBuildTypeEnum: ResourceObjectBuildTypeEnum,
    @ApiModelProperty("资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML,
    @ApiModelProperty("资源对象合并方式：三路合并/非三路合并", required = false)
    val resourceObjectMergeType: ResourceObjectMergeTypeEnum = ResourceObjectMergeTypeEnum.ORDINARY
)

data class AssembledVersionResult (
    @ApiModelProperty("应用名", required = true)
    val appName: String,
    @ApiModelProperty("环境StackId",required = true)
    val envStackId: String,
    @ApiModelProperty("返回发布版本协议",required = true)
    val versionProtocol: String,
    @ApiModelProperty("资源对象",required = true)
    val version: String,
    @ApiModelProperty("特性列表",required = true)
    val traitList: String,
)

data class AssembledCRListResult (
    @ApiModelProperty("元数据约束",required = true)
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    @ApiModelProperty("资源对象格式，YAML/JSON",required = true)
    val resourceObjectFormatEnum: ResourceObjectFormatEnum? = ResourceObjectFormatEnum.YAML,
    @ApiModelProperty("CR资源对象列表",required = true)
    val crResourceObjectList: List<CRResourceObject> = emptyList()
)

data class CRResourceObject(
    @ApiModelProperty("版本")
    val apiVersion: String,
    @ApiModelProperty("类型")
    val kind: String,
    @ApiModelProperty("资源对象",required = true)
    val resourceObject: String,
)

enum class ResourceObjectBuildTypeEnum {
    /**
     * 资源对象首次创建
     */
    CREATE,

    /**
     * 基于已有的资源对象合并特性片段生成
     */
    PATCH
}