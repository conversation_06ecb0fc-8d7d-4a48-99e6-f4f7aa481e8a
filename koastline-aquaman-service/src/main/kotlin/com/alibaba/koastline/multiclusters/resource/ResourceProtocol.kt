package com.alibaba.koastline.multiclusters.resource

import com.alibaba.env.orchestration.protocol.common.V2MetaData
import com.alibaba.env.orchestration.protocol.common.V2ResourceStatus

/**
 * @author:    <EMAIL>
 * @description:  资源协议通用模版
 * @date:    2024/5/15 3:28 PM
 */
data class ResourceProtocol (
    /**
     * 协议类型
     */
    val kind: String,
    /**
     * 元数据
     */
    val metadata: V2MetaData,
    /**
     * 资源状态
     */
    val status: V2ResourceStatus? = null
){
    fun getVersion() = metadata.generation ?: 1
}