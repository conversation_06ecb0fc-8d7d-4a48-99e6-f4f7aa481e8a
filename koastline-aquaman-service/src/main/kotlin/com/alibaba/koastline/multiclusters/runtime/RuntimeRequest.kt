package com.alibaba.koastline.multiclusters.runtime

import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.swagger.annotations.ApiModel

@ApiModel("注册绑定可用runtime坐标")
data class RuntimeCoordinateResourceCreateReq(
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    val annotations: String,
    val labels: String,
    val creator: String
)

@ApiModel("解绑定可用runtime坐标")
data class RuntimeCoordinateResourceDeleteReq(
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    val modifier: String
)