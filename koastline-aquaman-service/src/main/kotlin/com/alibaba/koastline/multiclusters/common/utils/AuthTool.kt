package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.koastline.multiclusters.authentication.models.UserAuthentication
/**
 * <AUTHOR>
 */
object AuthTool {
    private val currentAuthentication = ThreadLocal<UserAuthentication>()

    fun setAuth(authentication: UserAuthentication) {
        currentAuthentication.set(authentication)
    }

    fun unsetAuth() {
        currentAuthentication.remove()
    }

    fun getAuth(): UserAuthentication {
        return currentAuthentication.get()
    }
}