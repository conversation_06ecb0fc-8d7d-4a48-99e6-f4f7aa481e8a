package com.alibaba.koastline.multiclusters.external.model

import org.apache.commons.codec.digest.DigestUtils
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * date 2024/4/15 11:56
 */
object HcrmApiExt {
    @JvmStatic
    fun buildApiHeadersWithSign(accessKey: String, secretKey: String): MutableMap<String, String> {
        val dateTimeFormatter = SimpleDateFormat("yyyyMMddHHmmssSSS")
        val time = dateTimeFormatter.format(Date())
        return mutableMapOf(
            HeaderTenantId to TenantId,
            HeaderUserId to UserId,
            HeaderAccessKey to accessKey,
            HeaderTimestamp to time,
            HeaderToken to DigestUtils.md5Hex("$accessKey::$time::$secretKey")
        )
    }
    private const val HeaderTenantId = "Hcrm-Tenant-Id"
    private const val HeaderUserId = "Hcrm-User-Id"
    const val HeaderAccessKey = "Hcrm-Access-Key"
    private const val HeaderTimestamp = "Hcrm-Timestamp"
    private const val HeaderToken = "Hcrm-Token"
    private const val TenantId = "alibaba"
    private const val UserId = "63763"
}
data class HcrmApiResponse<T>(
    var success: Boolean? = null,
    var code: String? = null,
    var message: String? = null,
    var traceId: String? = null,
    var data: T? = null,
) {
    fun isSuccess() = success == true
}
data class HcrmPageData<T>(
    var total: Int? = null,
    var size: Int? = null,
    var current: Int? = null,
    var pages: Int? = null,
    var records: List<T>? = null,
)