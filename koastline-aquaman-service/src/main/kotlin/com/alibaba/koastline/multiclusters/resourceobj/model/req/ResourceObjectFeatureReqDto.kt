package com.alibaba.koastline.multiclusters.resourceobj.model.req

import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.env.DEFAULT_VERSION
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFeatureUseScope
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureDisplayThemeEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureEffectiveStageEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureTypeEnum
import io.swagger.annotations.ApiModelProperty

data class ResourceObjectFeatureCreateReqDto(
    @ApiModelProperty("资源对象特性Key（可选），不存在则自动创建", required = false)
    val resourceObjectFeatureKey: String?,
    @ApiModelProperty("标题", required = true)
    val title: String,
    @ApiModelProperty("使用范围", required = true)
    val useScope: List<ResourceObjectFeatureUseScope>,
    @ApiModelProperty("创建人工号", required = true)
    val creator: String,
    @ApiModelProperty("特性注入适用的协议范围，逗号分隔", required = true)
    val feasibleProtocols: String,
    @ApiModelProperty("特性类型：新增输入（INPUT）、拦截（INPUT_BLOCK）",required = false)
    val type: ResourceObjectFeatureTypeEnum = ResourceObjectFeatureTypeEnum.INPUT,
    @ApiModelProperty("特性的生效阶段，取值为 AFTER_VERSIONOUT（出版本后） 和 DURING_VERSIONOUT（出版本时）", required = false)
    val effectiveStage: ResourceObjectFeatureEffectiveStageEnum = ResourceObjectFeatureEffectiveStageEnum.AFTER_VERSIONOUT,
    @ApiModelProperty("协议参数的 Json Schema 定义", required = false)
    val jsonSchema: String? = null,
    @ApiModelProperty("特性的前端展示样式", required = false)
    val displayTheme: ResourceObjectFeatureDisplayThemeEnum = ResourceObjectFeatureDisplayThemeEnum.TREE,
    @ApiModelProperty("匹配范围", required = false)
    val matchScopeData: MatchScopeDataReqDto? = null,
    @ApiModelProperty("版本号", required = false)
    val version: String = DEFAULT_VERSION,
    @ApiModelProperty("租户/归属系统", required = false)
    val submitters: String = "SYSTEM",
)

data class ResourceObjectFeatureUpdateReqDto (
    @ApiModelProperty("资源对象特性主键ID", required = false)
    val id: Long?,
    @ApiModelProperty("资源对象特性Key", required = false)
    val resourceObjectFeatureKey: String?,
    @ApiModelProperty("标题",required = true)
    val title: String,
    @ApiModelProperty("使用范围",required = false)
    val useScope: List<ResourceObjectFeatureUseScope> = emptyList(),
    @ApiModelProperty("修改人工号",required = true)
    val modifier: String,
    @ApiModelProperty("特性注入适用的协议范围，逗号分隔",required = false)
    val feasibleProtocols: String? = null,
    @ApiModelProperty("协议参数的 Json Schema 定义", required = false)
    val jsonSchema: String? = null,
    @ApiModelProperty("特性类型：新增输入（INPUT）、拦截（INPUT_BLOCK）",required = false)
    val type: ResourceObjectFeatureTypeEnum? = null,
    @ApiModelProperty("特性的生效阶段，取值为 AFTER_VERSIONOUT（出版本后） 和 DURING_VERSIONOUT（出版本时）", required = false)
    val effectiveStage: ResourceObjectFeatureEffectiveStageEnum? = null,
    @ApiModelProperty("特性的前端展示样式", required = false)
    val displayTheme: ResourceObjectFeatureDisplayThemeEnum? = null,
    @ApiModelProperty("匹配范围", required = false)
    val matchScopeData: MatchScopeDataReqDto? = null,
    @ApiModelProperty("版本号", required = false)
    val version: String? = null,
    @ApiModelProperty("租户/归属系统", required = false)
    val submitters: String? = null,

)

data class ResourceObjectFeatureGetReqDto (
    @ApiModelProperty("特性注入适用的协议",required = false)
    val feasibleProtocolFilter: String?,

    @ApiModelProperty("特性Key列表",required = false)
    val traitListFilter: List<String>?,

    @ApiModelProperty("特性作用阶段",required = false)
    val traitEffectiveStageListFilter: List<ResourceObjectFeatureEffectiveStageEnum>?,

    @ApiModelProperty("特性类型",required = false)
    val traitTypeFilter: ResourceObjectFeatureTypeEnum?,

    @ApiModelProperty("匹配范围", required = false)
    val matchScopes: List<MatchScopeDataReqDto>? = null,

    @ApiModelProperty("租户名称", required = false)
    val submitters: String = "SYSTEM",

    @ApiModelProperty("类型", required = false)
    val type: ResourceObjectFeatureTypeEnum = ResourceObjectFeatureTypeEnum.ALL,
) {
  fun validate(): ResourceObjectFeatureGetReqDto {

    require(
      matchScopes?.any {
        it.externalType == MatchScopeExternalTypeEnum.APPLICATION.name
      } ?: true
    ) { "if matchScopes exist, appName is required" }
    return this
  }
}


data class ResourceObjectFeatureGetByKeyAndVersionReqDto(

    @ApiModelProperty("特性Key", required = true)
    val traitKey: String,
    @ApiModelProperty("特性Version", required = false)
    val version: String? = null,
)