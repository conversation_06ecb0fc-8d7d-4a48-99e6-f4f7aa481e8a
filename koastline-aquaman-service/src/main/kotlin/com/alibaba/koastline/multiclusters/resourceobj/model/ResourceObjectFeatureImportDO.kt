package com.alibaba.koastline.multiclusters.resourceobj.model

import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureImportStatusEnum
import com.fasterxml.jackson.annotation.JsonProperty
import java.util.*

data class ResourceObjectFeatureImportDO(
    /**
     * ID
     */
    val id: Long,
    /**
     * 资源对象特性Key
     */
    val resourceObjectFeatureKey: String,
    /**
     * 状态,@see ResourceObjectFeatureImportStatusEnum
     */
    val status: String = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
    /**
     * 预置参数映射
     */
    val paramMap: Map<String, Any>,
    /**
     * 创建人
     */
    val creator: String,
    /**
     * 修改人
     */
    val modifier: String,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String,
    /**
     * 匹配范围
     */
    val matchScopeDataDOList: List<MatchScopeDataDO>
)