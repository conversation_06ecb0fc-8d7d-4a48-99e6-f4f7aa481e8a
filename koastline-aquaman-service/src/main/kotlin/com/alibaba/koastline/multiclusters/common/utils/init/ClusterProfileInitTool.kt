package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import okhttp3.OkHttpClient
import java.io.File

/**
 * 集群数据初始化工具，非业务功能
 */
object ClusterProfileInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()
    fun init() {
        val userName = "admin"
        val secret = "test123"
        val file = ClusterProfileInitTool.javaClass.getClassLoader().getResource("init/cluster_profile_init_data.txt")!!.file
        val lines: List<String> = File(file).readLines()

        val clusters = HttpClientUtils.httpGet(
            "https://pre-registry.asi.alibaba-inc.com/api/v1/clusters", emptyMap(), null
        ).run { JsonUtils.readValue(this, Map::class.java)["clusters"] as List<Map<String, String>> }

        lines.forEach { clusterId ->
            val rs = HttpClientUtils.httpGet(
                "https://pre-aquaman.koastline.alibaba-inc.com/apis/multiclusters/cluster-manage/cluster/cluster-id/${clusterId}",
                emptyMap(),
                AuthorizationAttribute(
                    userName,
                    secret
                )
            ).run { JsonUtils.readValue(this, Map::class.java) }
            if (rs["success"] as Boolean) {
                println("已添加:${clusterId}")
                return@forEach
            }
            val cluster = clusters.first { it["id"] == clusterId }
            println(HttpClientUtils.httpPost(
                "https://pre-aquaman.koastline.alibaba-inc.com/apis/multiclusters/cluster-manage/cluster",
                JsonUtils.writeValueAsString(mapOf(
                    "clusterExternalId" to "",
                    "clusterId" to clusterId,
                    "clusterName" to cluster["name"],
                    "clusterProvider" to "alibaba",
                    "clusterType" to "alibaba-asi",
                    "clusterMetaData" to mapOf(
                        "region" to cluster["region"],
                        "site" to "",
                        "clusterAnnotations" to emptyMap<String, String>(),
                        "clusterLabels" to emptyMap<String, String>(),
                        "gatewayConfigs" to emptyList<Map<String, String>>()
                    )
                )),
                AuthorizationAttribute(
                    userName,
                    secret
                )
            ))

        }
        println("finish init data.")
    }
}

fun main(args: Array<String>) {
    ClusterProfileInitTool.init()
}