package com.alibaba.koastline.multiclusters.schedule.service.fiter

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.AbstractScheduleFilterFacade
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 调度计算服务
 * 功能：随机返回其中一个集群
 */
@Component
class RandomScheduleFilterProcessor : AbstractScheduleFilterFacade(), ScheduleFilterFacade, InitializingBean {
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory
    override fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
        if (matchDeclaration.apres.isNullOrEmpty()) {
            return matchDeclaration
        }
        return matchDeclaration.copy(
            apres = listOf(
                matchDeclaration.apres[(0 until matchDeclaration.apres.size).random()].run {
                    this.copy(
                        resources = listOf(
                            this.resources[(0 until this.resources.size).random()]
                        )
                    )
                }
            )
        )
    }

    override fun afterPropertiesSet() {
        scheduleFilterServiceFactory.registryScheduleFilterService(this)
    }
}