package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.apre.attorney.ApREAttorneyService
import com.alibaba.koastline.multiclusters.apre.base.MetadataService
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.formatUnit
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType.CLUSTER
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType.CLUSTER_LABEL
import com.alibaba.koastline.multiclusters.apre.model.req.ApREDeclarationPatchCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_CLUSTER
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_SITE
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.valueOf
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.CoordinateUtils
import com.alibaba.koastline.multiclusters.data.utils.DataValidateUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ApREDeclarationExtensionService(val objectMapper: ObjectMapper) {
    val log by logger()

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService

    @Autowired
    lateinit var apREService: ApREService

    @Autowired
    lateinit var apREAttorneyService: ApREAttorneyService

    @Autowired
    lateinit var metadataService: MetadataService


    /**
     * 特性均衡 & 集群均衡 需要自动添加集群环境授权
     *
     * @param apREDeclarationPatchCreateReqDto
     */
    @Transactional
    fun createApREDeclarationPatchWithMatchScopeAndAttorney(
        apREDeclarationPatchCreateReqDto: ApREDeclarationPatchCreateReqDto
    ) {
        apREDeclarationPatchCreateReqDto.validate()
        checkNotNull(apREDeclarationPatchCreateReqDto.matchScopeDataReqDtoList?.firstOrNull()) {
            "create apREDeclarationPatch with matchScope and attorney missing matchScopeDataReq!"
        }
        apREDeclarationPatchService.createApREDeclarationPatchWithMatchScope(
            apREDeclarationPatchCreateReqDto
        )
    }

    /**
     * 获取Feat Balance 策略
     *
     * @param stage
     * @param unit
     * @param site
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listFeatBalanceApREDeclarationWithProperties(
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        externalId: String,
        externalType: String,
        includeScopeType: IncludeScopeType,
        pageSize: Int,
        pageNumber: Int
    ): PageData<FeatBalanceApREDeclaration> {
        // 校验参数是否正常
        CoordinateUtils.coordinatesPropertiesValidate(
            stage = stage, unit = unit, site = site, externalId = externalId, externalType = externalType
        )

        return apREDeclarationPatchService.listApREDeclarationPatchWithMatchScopeByProperties(
            stage = stage,
            unit = unit,
            site = site,
            balanceType = BALANCE_CLUSTER,
            includeScopeType = includeScopeType,
            externalId = externalId,
            externalType = externalType,
            pageSize = pageSize,
            pageNumber = pageNumber,
            jsonSelector = getBalanceClusterJsonSelector(CLUSTER_LABEL)
        ).map {
            val (memberExternalId, memberExternalType) = getApREDeclarationPatchDataExternal(it)
            FeatBalanceApREDeclaration(
                apREDeclaration = it,
                includeScopeType = matchScopeService.getIncludeType(
                    goalExternalId = externalId, goalExternalType = valueOf(externalType),
                    externalId = memberExternalId, externalType = valueOf(memberExternalType)
                ),
                existedFeatBalanceList = extractExistedFeatureBalanceList(it)
            )
        }
    }

    /**
     * 获取Cluster Balance 策略
     *
     * @param stage
     * @param unit
     * @param site
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listClusterBalanceApREDeclarationWithProperties(
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        externalId: String,
        externalType: String,
        includeScopeType: IncludeScopeType,
        pageSize: Int,
        pageNumber: Int
    ): PageData<ClusterBalanceApREDeclaration> {
        // 校验参数是否正常
        CoordinateUtils.coordinatesPropertiesValidate(
            stage = stage, unit = unit, site = site, externalId = externalId, externalType = externalType
        )

        return apREDeclarationPatchService.listApREDeclarationPatchWithMatchScopeByProperties(
            stage = stage,
            unit = unit,
            site = site,
            balanceType = BALANCE_CLUSTER,
            includeScopeType = includeScopeType,
            externalId = externalId,
            externalType = externalType,
            pageSize = pageSize,
            pageNumber = pageNumber,
            jsonSelector = getBalanceClusterJsonSelector(CLUSTER)
        ).map {
            val (memberExternalId, memberExternalType) = getApREDeclarationPatchDataExternal(it)
            ClusterBalanceApREDeclaration(
                apREDeclaration = it,
                includeScopeType = matchScopeService.getIncludeType(
                    goalExternalId = externalId, goalExternalType = valueOf(externalType),
                    externalId = memberExternalId, externalType = valueOf(memberExternalType)
                ),
                existedClusterBalanceList = extractExistedClusterBalanceList(apREDeclarationPatchData = it)
            )
        }
    }

    /**
     * 获取Site Balance 策略
     *
     * @param stage
     * @param unit
     * @param site
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listSiteBalanceApREDeclarationWithProperties(
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        externalId: String,
        externalType: String,
        includeScopeType: IncludeScopeType,
        pageSize: Int,
        pageNumber: Int
    ): PageData<SiteBalanceApREDeclaration> {
        // 校验参数是否正常
        CoordinateUtils.coordinatesPropertiesValidate(
            stage = stage, unit = unit, site = site, externalId = externalId, externalType = externalType
        )

        return apREDeclarationPatchService.listApREDeclarationPatchWithMatchScopeByProperties(
            stage = stage,
            unit = unit,
            site = site,
            balanceType = BALANCE_SITE,
            includeScopeType = includeScopeType,
            externalId = externalId,
            externalType = externalType,
            pageSize = pageSize,
            pageNumber = pageNumber,
            jsonSelector = null,
        ).map {
            val (memberExternalId, memberExternalType) = getApREDeclarationPatchDataExternal(it)
            SiteBalanceApREDeclaration(
                apREDeclaration = it,
                includeScopeType = matchScopeService.getIncludeType(
                    goalExternalId = externalId, goalExternalType = valueOf(externalType),
                    externalId = memberExternalId, externalType = valueOf(memberExternalType)
                ),
                existedSiteBalanceList = extractExistedSiteBalanceList(apREDeclarationPatchData = it),
            )
        }
    }

    private fun getBalanceClusterJsonSelector(clusterType: ClusterSelectorType): String {
        // 给定注入字段
        val firstType = if(clusterType == CLUSTER_LABEL){
            CLUSTER_LABEL.name
        }else{
            CLUSTER.name
        }
        val secondType = if(clusterType == CLUSTER_LABEL){
            CLUSTER.name
        }else {
            CLUSTER_LABEL.name
        }

        return """
            (
                JSON_CONTAINS(
                  JSON_EXTRACT(
                    declaration_patch,
                    '${'$'}.patchItems[*].clusterSelector.selectType'
                  ),
                  '"$firstType"'
                ) = 1 
                AND
                JSON_CONTAINS(
                  JSON_EXTRACT(
                    declaration_patch,
                    '${'$'}.patchItems[*].clusterSelector.selectType'
                  ),
                  '"$secondType"'
                ) = 0
            )
        """.trimIndent()
    }

    /**
     * 按照坐标查询到可用的所有特性列表
     * 特性来源为集群标签
     *
     * @param unit
     * @param stage
     * @param site
     * @return
     */
    fun findAvailableFeatLabels(unit: String, stage: String, site: String): List<ClusterLabel> {
        require(DataValidateUtils.notEmptyAndAllNotBlank(listOf(unit, site, stage))) {
            "site/stage/unit cannot be blank in findAvailableFeatLabels"
        }
        val clusterLabelList = mutableListOf<ClusterLabel>()
        apREService.listApREDetailsBySiteAndUnitAndStage(unit = formatUnit(unit)!!, stage = stage, site = site)
            .forEach { fullApRE ->
                val clusterLabels = fullApRE.resources.flatMap { it.apRELabels }.filter { it.type == CONSOLE }
                    .map { ClusterLabel(name = it.name, value = it.value) }
                clusterLabelList.addAll(clusterLabels)
            }
        return clusterLabelList.distinct()
    }

    /**
     * 按照坐标查询到可用的所有集群列表
     * 特性来源为集群
     *
     * @param unit
     * @param stage
     * @param site
     * @return
     */
    fun findAvailableClusters(unit: String, stage: String, site: String): List<String> {
        require(DataValidateUtils.notEmptyAndAllNotBlank(listOf(unit, site, stage))) {
            "site/stage/unit cannot be blank in findAvailableClusters"
        }
        val availableClusters = mutableListOf<String>()
        apREService.listApREDetailsBySiteAndUnitAndStage(unit = formatUnit(unit)!!, stage = stage, site = site)
            .forEach { fullApRE ->
                availableClusters.addAll(fullApRE.resources.mapNotNull { it.clusterId })
            }
        return availableClusters.distinct()
    }

    /**
     * 查询详细的特性均衡策略
     * 判断现在均衡策略中使用的特性有哪些 + 可以被添加进入的特性有哪些 + 以及判断特性是否完全失效
     */
    fun findFeatBalanceApREDeclarationWithPropertiesVerify(
        apREDeclarationId: Long,
    ): FeatBalanceApREDeclaration? {
        val apREDeclarationPatchData = findApREDeclarationPatchByIdWithCheckingType(
            apREDeclarationId = apREDeclarationId,
            apREDeclarationPatchType = BALANCE_CLUSTER
        ) ?: return null
        val apREList = apREService.listApREDetailsBySiteAndUnitAndStage(
            unit = apREDeclarationPatchData.unit,
            stage = apREDeclarationPatchData.stage,
            site = apREDeclarationPatchData.site!!
        )
        // 找到目前设置规则的特性
        val allLabels = apREList.flatMap { apREDO -> apREDO.resources.flatMap { it.apRELabels } }
        val consoleFeatLabels = allLabels.filter { it.type == CONSOLE }
        val verifyFeatBalanceList = extractExistedFeatureBalanceList(apREDeclarationPatchData).map { featBalance ->
            val feats = featBalance.clusterLabelList
            val isEffective = feats.all { feat ->
                consoleFeatLabels.any { label -> feat.name == label.name && feat.value == label.value }
            }
            featBalance.copy(effected = isEffective)
        }
        val extraAppendFeatBalanceList =
            consoleFeatLabels.map { ClusterLabel(name = it.name, value = it.value) }.distinctBy { it.name + it.value }

        return FeatBalanceApREDeclaration(
            apREDeclaration = apREDeclarationPatchData,
            existedFeatBalanceList = verifyFeatBalanceList,
            extraAppendFeatBalanceList = extraAppendFeatBalanceList,
        )
    }

    /**
     * 查询详细的集群均衡策略
     * 判断现在均衡策略中使用的集群有哪些 + 可以被添加进入的集群有哪些 + 以及判集群是否完全失效
     *
     * @param apREDeclarationId
     * @return
     */
    fun findClusterBalanceApREDeclarationWithPropertiesVerify(
        apREDeclarationId: Long,
    ): ClusterBalanceApREDeclaration? {
        val apREDeclarationPatchData = findApREDeclarationPatchByIdWithCheckingType(
            apREDeclarationId = apREDeclarationId,
            apREDeclarationPatchType = BALANCE_CLUSTER
        ) ?: return null
        val apREList = apREService.listApREDetailsBySiteAndUnitAndStage(
            unit = apREDeclarationPatchData.unit,
            stage = apREDeclarationPatchData.stage,
            site = apREDeclarationPatchData.site!!
        )
        // 找到目前设置规则的集群
        val allClusters = apREList.flatMap { apREDO -> apREDO.resources.map { it.clusterId } }.filterNotNull()
        val verifyClusterBalanceList = extractExistedClusterBalanceList(apREDeclarationPatchData).map { clusterBalance ->
            val clusters = clusterBalance.clusterList
            val isEffective = clusters.all { cluster ->
                allClusters.contains(cluster)
            }
            clusterBalance.copy(effected = isEffective)
        }
        val extraAppendClusterBalanceList = allClusters.distinct()

        return ClusterBalanceApREDeclaration(
            apREDeclaration = apREDeclarationPatchData,
            existedClusterBalanceList = verifyClusterBalanceList,
            extraAppendClusterBalanceList = extraAppendClusterBalanceList,
        )
    }

    /**
     * 查询详细的集群均衡策略
     * 判断现在均衡策略中使用的集群有哪些 + 可以被添加进入的集群有哪些 + 以及判集群是否完全失效
     *
     * @param apREDeclarationId
     * @return
     */
    fun findSiteBalanceApREDeclarationWithPropertiesVerify(
        apREDeclarationId: Long,
    ): SiteBalanceApREDeclaration? {
        val apREDeclarationPatchData = findApREDeclarationPatchByIdWithCheckingType(
            apREDeclarationId = apREDeclarationId,
            apREDeclarationPatchType = BALANCE_SITE
        ) ?: return null

        val allSites = metadataService.listSiteByUnitAndStage(
            unit = apREDeclarationPatchData.unit,
            stage = apREDeclarationPatchData.stage
        ).map { it.site }.distinct()

        // 找到目前设置规则的站点
        val verifySiteBalanceList = extractExistedSiteBalanceList(apREDeclarationPatchData).map { siteBalance ->
            val settledSite = siteBalance.site
            val isEffective = allSites.contains(settledSite)
            siteBalance.copy(effected = isEffective)
        }

        return SiteBalanceApREDeclaration(
            apREDeclaration = apREDeclarationPatchData,
            existedSiteBalanceList = verifySiteBalanceList,
            extraAppendSiteBalanceList = allSites,
        )
    }

    /**
     * 抽取特性均衡的设置特性列表
     *
     * @param apREDeclarationPatchData
     * @return
     */
    private fun extractExistedFeatureBalanceList(apREDeclarationPatchData: ApREDeclarationPatchDataDO): List<FeatBalance> {
        return apREDeclarationPatchData.declarationPatch.patchItems.map {
            val labels = requireNotNull(it.clusterSelector?.labels) { "clusterSelector of labels should not be null!" }
            FeatBalance(
                weight = it.weight,
                clusterLabelList = labels,
            )
        }
    }

    /**
     * 抽取集群均衡的设置集群列表
     *
     * @param apREDeclarationPatchData
     * @return
     */
    private fun extractExistedClusterBalanceList(apREDeclarationPatchData: ApREDeclarationPatchDataDO): List<ClusterBalance> {
        return  apREDeclarationPatchData.declarationPatch.patchItems.map {
            val clusters =
                requireNotNull(it.clusterSelector?.clusterIds) { "clusterSelector of labels should not be null!" }
            ClusterBalance(
                weight = it.weight,
                clusterList = clusters,
            )
        }
    }

    /**
     * 抽取站点均衡的设置站点列表
     *
     * @param apREDeclarationPatchData
     * @return
     */
    private fun extractExistedSiteBalanceList(apREDeclarationPatchData: ApREDeclarationPatchDataDO): List<SiteBalance> {
        return apREDeclarationPatchData.declarationPatch.patchItems.map {
            val settledSite = requireNotNull(it.site) { "site of labels should not be null!" }
            SiteBalance(
                weight = it.weight,
                site = settledSite,
            )
        }
    }

    private fun findApREDeclarationPatchByIdWithCheckingType(
        apREDeclarationId: Long,
        apREDeclarationPatchType: ApREDeclarationPatchType
    ): ApREDeclarationPatchDataDO? {
        require(apREDeclarationId > 0) {
            "apREDeclarationId should be more than zero"
        }
        val apREDeclarationPatchData =
            apREDeclarationPatchService.findApREDeclarationPatchById(apREDeclarationId) ?: return null

        require(apREDeclarationPatchData.balanceType == apREDeclarationPatchType.name) {
            "balanceType of should be $apREDeclarationPatchType"
        }

        checkNotNull(apREDeclarationPatchData.stage) {
            "stage of cannot be null"
        }
        if(apREDeclarationPatchType != BALANCE_SITE){
            checkNotNull(apREDeclarationPatchData.site) {
                "site of cannot be null"
            }
        }
        checkNotNull(apREDeclarationPatchData.unit) {
            "unit of cannot be null"
        }
        return apREDeclarationPatchData
    }

    private fun getApREDeclarationPatchDataExternal(apREDeclarationPatchData: ApREDeclarationPatchDataDO):Pair<String, String> {
        return Pair(
            checkNotNull(apREDeclarationPatchData.matchScopeDataDOs?.first()?.externalId) {
                "missing externalId of MatchScopeData in apREDeclarationPatchData!"
            },
            checkNotNull(apREDeclarationPatchData.matchScopeDataDOs?.first()?.externalType) {
                "missing externalType of MatchScopeData in apREDeclarationPatchData!"
            }
        )
    }
}