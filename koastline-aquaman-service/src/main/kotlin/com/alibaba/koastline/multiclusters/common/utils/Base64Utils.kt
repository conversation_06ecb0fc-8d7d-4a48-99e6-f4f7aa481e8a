package com.alibaba.koastline.multiclusters.common.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.*

object Base64Utils {
    val objectMapper = ObjectMapper()

    fun encode(obj: Any): String {
        return String(
                Base64.getEncoder().encode(
                    objectMapper.writeValueAsString(obj).toByteArray(Charsets.UTF_8)
                ), Charsets.UTF_8
            )
    }

    inline fun <reified T> decode(content: String): T {
        return objectMapper.readValue<T>(
            String(
                Base64.getDecoder().decode(
                    content.encodeToByteArray()
                ), Charsets.UTF_8
            )
        )
    }
}