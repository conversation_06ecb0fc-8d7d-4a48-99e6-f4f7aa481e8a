package com.alibaba.koastline.multiclusters.schedule.service.schedule.facade

import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_DELIMITER
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO.Companion.APRE_FEATURE_SPEC_LABEL_BASE_ENV_STACK_ID
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.isRepeated
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException.Companion.DO_NOT_FOUND_CLUSTER_ROUTER
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException.Companion.INVALID_PARAM
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException.Companion.SCHEDULE_RESULT_IS_WRONG
import com.alibaba.koastline.multiclusters.schedule.model.*
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import com.alibaba.koastline.multiclusters.schedule.service.fiter.ClusterLoadScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.RandomScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.RunningStateScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.ValidApREScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.X86ClusterScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.schedule.PodListScheduleService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import org.springframework.beans.factory.annotation.Autowired
import kotlin.math.ceil

abstract class AbstractScaleOutScheduleFacade: ScheduleFacade {
    val log by logger()
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService
    @Autowired
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService
    @Autowired
    lateinit var scheduleServiceFactory:ScheduleServiceFactory
    @Autowired
    lateinit var skylineApi: SkylineApi
    @Autowired
    lateinit var scheduleStrategyService: ScheduleStrategyService
    @Autowired
    lateinit var podListScheduleService: PodListScheduleService
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi

    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        preCheck(content)
        val apREDeedResult = getApREDeedResultWithResource(content).run {
            if (content.scheduleRequestParam!!.replicas!! == 1) {
                filterMatchDeclarationWhileScaleOutOnlyOne(this)
            } else {
                this
            }
        }
        val declarationReplicasMap = scheduleStandardService.doComputeDeclarationExpectedReplicas(apREDeedResult.matchDeclarations.map {
            ScheduleWeight( uniqueKey = it.declarationId, weight = it.weight) }.shuffled(), content.scheduleRequestParam!!.replicas!!)
        val workloadExpectedStateList =  mutableListOf<WorkloadExpectedState>()
        apREDeedResult.matchDeclarations.forEach { matchDeclaration ->
            val declaration = checkNotNull(matchDeclaration.declaration){"参数：declaration不能为空."}
            val clusterBalancePatchData = apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup,
                stage = checkNotNull(declaration.stage) {"扩容获取集群均衡策略，参数stage不能为空."},
                unit = checkNotNull(declaration.unit) {"扩容获取集群均衡策略，参数unit不能为空."},
                site = checkNotNull(declaration.az) {"扩容获取集群均衡策略，参数az不能为空."})
            val filteredMatchDeclaration = doMatchDeclarationScheduleFilter(
                matchDeclaration = matchDeclaration, content = content, isClusterBalancedMode = clusterBalancePatchData != null)
            if (clusterBalancePatchData == null) {
                val resourceList = filteredMatchDeclaration.apres.flatMap { it.resources }
                workloadExpectedStateList.addAll(
                    doComputeWorkloadReplicas(
                        content,
                        declaration,
                        resourceList,
                        declarationReplicasMap[matchDeclaration.declarationId]!!
                    )
                )
            } else {
                //集群均衡
                splitMatchDeclarationByClusterBalance(
                    resourceScope = content.resourceScope,
                    matchDeclaration = filteredMatchDeclaration,
                    apREDeclarationPatchDataDO = clusterBalancePatchData,
                    scaleOutReplicas = declarationReplicasMap[matchDeclaration.declarationId]!!).forEach {
                    workloadExpectedStateList.addAll(
                        doComputeWorkloadReplicas(
                            content,
                            declaration,
                            it.first,
                            it.second
                        )
                    )
                }
            }
        }
        return expendWorkloadMeta(content, workloadExpectedStateList).apply {
            postCheck(content, this)
        }
    }

    /**
     * TODO: 增加 workloadName 补偿 获取运行时的 workloadName 如果出现有对应的运行时资源存在 workloadName 则透出
     *
     * @param content
     * @param workloadExpectedStateList
     * @return
     */
    private fun expendWorkloadMeta(
        content: ScheduleRequestContent,
        workloadExpectedStateList: List<WorkloadExpectedState>
    ): ScheduleResult {
        //ScheduleResult(amendWorkloadName(content, workloadExpectedStateList))
        return ScheduleResult(workloadExpectedStateList)
    }

    private fun amendWorkloadName(
        content: ScheduleRequestContent,
        workloadExpectedStateList: List<WorkloadExpectedState>
    ): List<WorkloadExpectedState> {
        val scheduleRequestParam =
            checkNotNull(content.scheduleRequestParam) { "missing scheduleRequestParam in ScheduleRequestContent" }
        if (!scheduleRequestParam.serverless) {
            return workloadExpectedStateList
        }
        // serverless 模式下需要对存量的资源进进行 workloadName 订正
        val runningWorkloads = podListScheduleService.doSchedule(content)
        // 判断是否存在交集
        val hasRunningStateWorkloadExpectedStateList = workloadExpectedStateList.filter { resultWorkload ->
            runningWorkloads.workloadExpectedStates.any {
                it.workloadMetadataConstraint.matchServerlessRuntime(
                    resultWorkload.workloadMetadataConstraint
                )
            }
        }
        if (hasRunningStateWorkloadExpectedStateList.isEmpty()) {
            // 没有存在运行时资源 需要生成补偿
            return workloadExpectedStateList.map {
                it.copy(
                    workloadMetadataConstraint = it.workloadMetadataConstraint.copy(
                        workloadName = it.workloadMetadataConstraint.generateWorkloadName()
                    )
                )
            }
        }
        // 存在运行时资源match上且存在运行时workloadName
        val others = workloadExpectedStateList.toSet() - hasRunningStateWorkloadExpectedStateList.toSet()
        val amendWorkloadNameList = mutableListOf<WorkloadExpectedState>()
        hasRunningStateWorkloadExpectedStateList.mapTo(amendWorkloadNameList) { hasRunningStateWorkload ->
            val matchRunning = runningWorkloads.workloadExpectedStates.first {
                hasRunningStateWorkload.workloadMetadataConstraint.matchServerlessRuntime(it.workloadMetadataConstraint)
            }
            if (matchRunning.workloadMetadataConstraint.workloadName == null) {
                hasRunningStateWorkload
            } else {
                hasRunningStateWorkload.copy(
                    workloadMetadataConstraint = matchRunning.workloadMetadataConstraint
                )
            }
        }
        return amendWorkloadNameList + others.toList()
    }


    /**
     * 如果只扩容一台资源，过滤空资源的匹配声明
     */
    private fun filterMatchDeclarationWhileScaleOutOnlyOne(apREDeedResult: ApREDeedResult): ApREDeedResult {
        if (apREDeedResult.matchDeclarations.size <= 1) {
            return apREDeedResult
        }
        val matchDeclarations = apREDeedResult.matchDeclarations.filter {
            it.apres.isNotEmpty()
        }
        if (matchDeclarations.isEmpty()) {
            return apREDeedResult
        }
        return apREDeedResult.copy(
            matchDeclarations = matchDeclarations
        )
    }

    /**
     * 按照集群均衡策略拆分声明匹配,计算同类型资源池集合（集群）以及对应的扩容副本数
     * 将Resource拆分到符合的集群中
     * @param resourceScope 资源范围
     * @param matchDeclaration 资源声明以及匹配的ApRE&Resource
     * @param apREDeclarationPatchDataDO 集群均衡策略
     * @param replicas 扩容副本数
     * @return list of(待扩容的目标资源池集合->对应扩容的副本数)
     */
    private fun splitMatchDeclarationByClusterBalance(resourceScope: ResourceScope, matchDeclaration: MatchDeclaration, apREDeclarationPatchDataDO: ApREDeclarationPatchDataDO, scaleOutReplicas: Int): List<Pair<List<ResourceDO>, Int>> {
        val balancedPatchItemMap = apREDeclarationPatchDataDO.declarationPatch.patchItems.associate {
            checkNotNull(it.clusterSelector){"集群均衡策略，clusterSelector不能为空。"}.identity() to mutableListOf<ResourceDO>()
        }
        matchDeclaration.apres.forEach { apRE ->
            apRE.resources.forEach { resource ->
                scheduleStrategyService.matchResource(resource, apREDeclarationPatchDataDO).forEach { patchItem ->
                    balancedPatchItemMap[patchItem.clusterSelector!!.identity()]!!.add(resource)
                }
            }
        }
        //计算现状运行态PatchItem资源数量
        val currentResourceNumByPatchItemMap = computeCurrentResourceNumByPatchItem(balancedPatchItemMap, resourceScope, matchDeclaration)
        //计算预期PatchItem扩容数量
        val result = mutableListOf<Pair<List<ResourceDO>, Int>>()
        scheduleStandardService.doComputeExpectedReplicasByRunningState(
            scheduleWeightList = apREDeclarationPatchDataDO.declarationPatch.patchItems.map {
                val uniqueKey = it.clusterSelector!!.identity()
                ScheduleWeight(uniqueKey = uniqueKey, weight = it.weight,
                    runningResourceNum = currentResourceNumByPatchItemMap[uniqueKey] ?:0
                )
            },
            modifiedReplicas = scaleOutReplicas
        ) { runningResourceNum, expectedReplicas ->
            expectedReplicas - runningResourceNum
        }.forEach { (key, value) ->
            result.add(Pair(balancedPatchItemMap[key]!!.toList(), value))
        }
        //兜底校验
        require(scaleOutReplicas == result.sumOf { it.second }) {
            "计算错误，期望扩容副本数:${scaleOutReplicas},实际计算结果:${JsonUtils.writeValueAsString(result)}"
        }
        log.info("splitMatchDeclarationByClusterBalance,resourceScope:{},matchDeclaration:{},apREDeclarationPatchDataDO:{},scaleOutReplicas:{},result:{}",
            JsonUtils.writeValueAsString(resourceScope),
            JsonUtils.writeValueAsString(matchDeclaration),
            JsonUtils.writeValueAsString(apREDeclarationPatchDataDO),
            scaleOutReplicas,
            JsonUtils.writeValueAsString(result)
        )
        return result
    }

    /**
     * 资源范围内按照均衡策略PatchItem归组计算运行态资源数量
     * @param balancedPatchItemMap
     * @param resourceScope
     * @param matchDeclaration
     * @return map of(key:均衡策略集群选择器身份唯一键,value:资源数量)
     */
    private fun computeCurrentResourceNumByPatchItem(balancedPatchItemMap: Map<String, List<ResourceDO>>, resourceScope: ResourceScope, matchDeclaration: MatchDeclaration): Map<String, Int> {
        //校验相同集群不能同时在不同的PatchItem
        checkRepeatedResource(balancedPatchItemMap)
        //基于运行态计算集群资源分布
        val currentResourceNumByClusterMap = computeResourceNumGroupByCluster(resourceScope, matchDeclaration.declaration!!)
        //过滤掉PatchItem未覆盖的集群,并基于PatchItem维度归组计算资源数量
        return balancedPatchItemMap.mapValues {
            gatherResourceNumWithinScope(it.value, currentResourceNumByClusterMap)
        }
    }

    /**
     * 汇总集群范围内的资源数量
     * @param resourceDOList 集群列表
     * @param currentResourceNumByClusterMap 集群对应资源数量
     */
    private fun gatherResourceNumWithinScope(resourceDOList: List<ResourceDO>, currentResourceNumByClusterMap: Map<String, Int>): Int {
        val clusterIds = resourceDOList.map { it.clusterProfileNew!!.clusterId }
        return currentResourceNumByClusterMap.filter {
            clusterIds.contains(it.key)
        }.values.sum()
    }

    /**
     * 资源范围内按照集群归组计算运行态资源数量
     * @param resourceScope 资源范围
     * @param declaration 资源声明
     * @return map of(key:集群ID，value：集群下资源数量)
     */
    private fun computeResourceNumGroupByCluster(resourceScope: ResourceScope, declaration: Declaration): Map<String, Int> {
        val workloadMetadataConstraintAssembleList = skylineApi.listWorkloadMetadataConstraintThroughServerList(
            appName = resourceScope.appName,
            envStackId = null,
            resourceGroup = resourceScope.resourceGroup
        ).filter {
            it.matches(declaration)
        }
        return workloadMetadataConstraintAssembleList.groupBy(
            keySelector = {it.workloadMetadataConstraint.clusterId},
            valueTransform = {it.num}
        ).mapValues { it.value.sum() }
    }

    /**
     * 验证是否存在重复的资源配置
     */
    private fun checkRepeatedResource(balancedResourceGroup: Map<String, List<ResourceDO>>) {
        val clusterIds = balancedResourceGroup.flatMap { balancedResourceGroupEntry ->
            balancedResourceGroupEntry.value.map { it.clusterProfileNew!!.clusterId }
        }
        if(clusterIds.isRepeated()) {
            throw ScheduleException("均衡策略存在重复的集群定义，集群均衡配置:${JsonUtils.writeValueAsString(balancedResourceGroup)}")
        }
    }

    private fun preCheck(content: ScheduleRequestContent) {
        if (content.scheduleRequestParam!!.replicas!! <=0) {
            throw ScheduleException("前置校验：变更副本数[${content.scheduleRequestParam!!.replicas!!}]非法，必须大于零.", INVALID_PARAM)
        }
        //校验envStackId不能为空
        content.resourceScope.envStackId ?.toNullIfBlank() ?:let {
            throw ScheduleException("前置校验：环境StackId不能为空.", INVALID_PARAM)
        }
        scheduleStandardService.checkServerlessParams(content)
    }

    /**
     * 校验结果是否准确
     */
    private fun postCheck(content: ScheduleRequestContent, scheduleResult: ScheduleResult) {
        scheduleResult.workloadExpectedStates.firstOrNull {
            it.params[ScheduleResultParamConstants.SCALE_NUM]!!.toInt() <= 0
        } ?.let {
            throw ScheduleException("后置验证调度计算数量错误, 输入:$content, 调度结果:$scheduleResult", SCHEDULE_RESULT_IS_WRONG)
        }
    }

    /**
     * 计算声明下多集群变更副本数，默认按照均分方式计算
     * @param content 扩容调度上下文
     * @param declaration 资源声明
     * @param resourceList 资源池列表
     * @param replicas  变更副本数
     * @return
     * visible for testing
     */
    protected fun doComputeWorkloadReplicas(content: ScheduleRequestContent, declaration: Declaration, resourceList: List<ResourceDO>, replicas: Int): List<WorkloadExpectedState> {
        val resourceTotalCount = resourceList.size.apply {
            if (this == 0) throw ScheduleException( buildMessageForInvalidResource(content.resourceScope, declaration,
                checkNotNull(content.scheduleRequestParam) { "参数content.scheduleRequestParam不能为空."},
            ), DO_NOT_FOUND_CLUSTER_ROUTER)
        }
        val resourceReplicasList = mutableListOf<Int>()
        val workloadExpectedStateList = mutableListOf<WorkloadExpectedState>()
        resourceList.forEach { resource ->
            val remainder = replicas - resourceReplicasList.sum()
            if (remainder <= 0) {
                return workloadExpectedStateList
            }
            ceil(replicas.toDouble() / resourceTotalCount).toInt().let { num ->
                if (num >= remainder ) {
                    workloadExpectedStateList.add(buildWorkloadExpectedState(
                        content.resourceScope,
                        declaration,
                        resource,
                        remainder,
                        checkNotNull(content.scheduleRequestParam).serverless,
                        content.scheduleRequestParam.serverlessRuntimeTemplate,
                        content.scheduleRequestParam.envStackPkId))
                    return workloadExpectedStateList
                }
                resourceReplicasList.add(num)
                workloadExpectedStateList.add(buildWorkloadExpectedState(
                    content.resourceScope,
                    declaration,
                    resource,
                    num,
                    checkNotNull(content.scheduleRequestParam).serverless,
                    content.scheduleRequestParam.serverlessRuntimeTemplate,
                    content.scheduleRequestParam.envStackPkId))
            }
        }
        return workloadExpectedStateList
    }

    private fun buildWorkloadExpectedState(resourceScope: ResourceScope, declaration: Declaration, resourceDO: ResourceDO, replicas: Int, serverless: Boolean, serverlessRuntimeTemplate : String? = null, envStackPkId: String? = null): WorkloadExpectedState {
        return WorkloadExpectedState(
            workloadMetadataConstraint = WorkloadMetadataConstraint(
                appName = resourceScope.appName,
                resourceGroup = resourceScope.resourceGroup!!,
                site = declaration.az!!,
                unit = declaration.unit!!,
                stage = declaration.stage!!,
                clusterId = resourceDO.clusterProfileNew!!.clusterId,
                runtimeId = if (serverless) {
                    // 扩容动作中直接使用最新的 runtimeId 也就是最KunLun中最新的数据进行查询
                    resolveRuntimeId(resourceDO, declaration, checkNotNull(serverlessRuntimeTemplate), resourceScope, envStackPkId)
                } else null,
            ).run {
                val workloadName = if (serverless) {
                    matchRunningWorkload(this) ?.workloadName
                } else null
                // 如果是存在历史上runningState的历史存在数据时候 这里返回 workload 需要额外返回 workloadName
                this.copy(
                    namespace = scheduleStandardService.generateNameSpace(this),
                    workloadName = workloadName
                )
            },
            clusterProfile = resourceDO.clusterProfileNew,
            params = mutableMapOf(
                ScheduleResultParamConstants.SCALE_NUM to replicas.toString()
            )
        )
    }

    private fun matchRunningWorkload(workloadMetadataConstraint: WorkloadMetadataConstraint): WorkloadMetadataConstraint? {
        return skylineApi.listWorkloadMetadataConstraintThroughServerList(
            appName = workloadMetadataConstraint.appName,
            resourceGroup = workloadMetadataConstraint.resourceGroup
        ).firstOrNull {
            workloadMetadataConstraint.runtimeId == it.workloadMetadataConstraint.runtimeId
        } ?.workloadMetadataConstraint
    }


    private fun resolveRuntimeId(resourceDO: ResourceDO, declaration: Declaration, serverlessRuntimeTemplate : String, resourceScope: ResourceScope, envStackPkId: String? = null): String {
        // 扩容动作中直接使用最新的 runtimeId 也就是最KunLun中最新的数据进行查询
        val runtimeBaseEnvStackId = envStackPkId ?.run {
            cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(this) ?.baseEnvStackId
        } ?:run {
            scheduleStandardService.getStackServerlessBaseAppBindingData(
                envStackId = resourceScope.envStackId, resourceGroup = resourceScope.resourceGroup
            ).extraParams ?.runtimeBaseEnvStackId
        }
        declaration.matchApRELabels ?.firstOrNull() ?.matchApREFeatureSpecs ?.firstOrNull() ?.let{
            if (!it.specCode.isNullOrEmpty()) {
                return it.specCode
            }
        }
        resourceDO.apRELabels ?.forEach { featureLabel ->
            featureLabel.apREFeatureSpecs ?.firstOrNull { featureSpec ->
                featureSpec.specType == serverlessRuntimeTemplate
                        && matchRuntimeBaseEnvStackId(runtimeBaseEnvStackId, featureSpeclabels = featureSpec.labels)
            } ?.let { featureSpec->
                return checkNotNull(featureSpec.specCode){"特性规格specCode不可为空。"}
            }
        }
        throw ScheduleException("特性中未找到RT模板，resourceDO: ${JsonUtils.writeValueAsString(resourceDO)}, serverlessRuntimeTemplate:${serverlessRuntimeTemplate}, runtimeBaseEnvStackId:${runtimeBaseEnvStackId}")
    }

    private fun matchRuntimeBaseEnvStackId(runtimeBaseEnvStackId: String?, featureSpeclabels: String?): Boolean {
        runtimeBaseEnvStackId ?: return true
        featureSpeclabels ?: return false
        JsonUtils.readValue<Map<String, String>>(featureSpeclabels, JsonUtils.objectTypeReference()).let {
            if (it[APRE_FEATURE_SPEC_LABEL_BASE_ENV_STACK_ID] == runtimeBaseEnvStackId) return true
        }
        return false
    }

    /**
     * 注册过滤器，并针对资源声明使用资源进行过滤
     * @param matchDeclaration  资源声明以及匹配的ApRE&Resource
     * @param content 扩容上下文
     * @param isClusterBalancedMode 是否集群均衡
     * @return 已过滤返回符合运行时状态的集群资源
     */
    private fun doMatchDeclarationScheduleFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent, isClusterBalancedMode: Boolean): MatchDeclaration {
        return scheduleFilterServiceFactory.getScheduleFilterServiceBuilder()
            .apply {
                if(!isClusterBalancedMode) {
                    //非均衡场景下加入X86集群过滤
                    this.addFilterProcessor(X86ClusterScheduleFilterProcessor::class.java)
                }
            }
            .addFilterProcessor(ValidApREScheduleFilterProcessor::class.java)
            .apply {
                if (!isClusterBalancedMode) {
                    //非均衡场景下加入运行态过滤
                    this.addFilterProcessor(RunningStateScheduleFilterProcessor::class.java)
                }
            }
            .addFilterProcessor(ClusterLoadScheduleFilterProcessor::class.java)
            .apply {
                if (!isClusterBalancedMode) {
                    //非均衡场景下随机到一个集群
                    this.addFilterProcessor(RandomScheduleFilterProcessor::class.java)
                }
            }
            .doScheduleFilter(matchDeclaration, content)
    }

    private fun buildMessageForInvalidResource(resourceScope: ResourceScope, declaration: Declaration, scheduleRequestParam: ScheduleRequestParam): String {
        if (scheduleRequestParam.serverless) {
            return "当前环境缺少可用的Runtime，请联系${
                checkNotNull(scheduleRequestParam.serverlessRuntimeTemplate).split(RUNTIME_TEMPLATE_DELIMITER)[0]
            }应用Owner或AppOps帮您处理，并提供下列信息:[应用:${resourceScope.appName},分组:${resourceScope.resourceGroup},站点：${declaration.az},用途：${declaration.stage},单元：${declaration.unit}," +
                    "Runtime需求：${JsonUtils.writeValueAsString(
                        declaration.matchApRELabels?.firstOrNull()?.matchApREFeatureSpecs?.firstOrNull() ?: scheduleRequestParam.serverlessRuntimeTemplate
                    )}];如Runtime已创建，则请检查Runtime基座是否授权给该业务应用."
        }
        return "当前环境缺少资源授权，请联系SRE或者@子伟@赵宇帮您处理，并提供下列信息:[应用:${resourceScope.appName},分组:${resourceScope.resourceGroup},站点：${declaration.az},用途：${declaration.stage},单元：${declaration.unit}"
    }

    abstract fun getApREDeedResultWithResource(content: ScheduleRequestContent): ApREDeedResult
}