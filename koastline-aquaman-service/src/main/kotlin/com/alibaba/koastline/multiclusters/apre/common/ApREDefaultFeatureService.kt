package com.alibaba.koastline.multiclusters.apre.common

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_VALUE_COMPUTE
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.params.ApREDefaultFeatureUsageEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.checkFeatureUsage
import com.alibaba.koastline.multiclusters.apre.params.checkScope
import com.alibaba.koastline.multiclusters.apre.params.checkStatus
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.dao.env.ApREDefaultFeatureRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ApREDefaultFeatureSpecRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDefaultFeature
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDefaultFeatureSpec
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*
import java.util.concurrent.locks.ReentrantLock

@Component
class ApREDefaultFeatureService {
    val log by logger()
    @Autowired
    lateinit var apREDefaultFeatureRepo: ApREDefaultFeatureRepo
    @Autowired
    lateinit var apREDefaultFeatureSpecRepo: ApREDefaultFeatureSpecRepo

    private var defaultImportUsageWithSpecListCache: Pair<Long, List<ApREDefaultFeatureDO>> = Pair(0L, emptyList())
    private val cacheLoadLock = ReentrantLock()
    /**
     * 创建缺省特性以及特性规格，存在即更新
     */
    @Transactional
    fun createApREFeatureIgnoreWhileExistWithFeatureSpec(apREDefaultFeatureDO: ApREDefaultFeatureDO): ApREDefaultFeatureDO{
        val apREDefaultFeature = apREDefaultFeatureRepo.queryByCode(apREDefaultFeatureDO.code) ?.let {
            val newApREDefaultFeature = ApREDefaultFeature(
                it.id,
                it.featureKey,
                it.code,
                checkFeatureUsage(apREDefaultFeatureDO.featureUsage),
                it.gmtCreate,
                Date(Instant.now().toEpochMilli()),
                it.isDeleted
            )
            apREDefaultFeatureRepo.updateById(newApREDefaultFeature).let {
                if (it == 0) throw ApREException("update apre default feature causes exception")
            }
            newApREDefaultFeature
        } ?:run {
            val now = Date(Instant.now().toEpochMilli())
            val newApREDefaultFeature = ApREDefaultFeature(
                null,
                KeyGenerator.generateAlphanumericKey(APRE_DEFAULT_FEATURE_KEY_LENGTH),
                apREDefaultFeatureDO.code,
                checkFeatureUsage(apREDefaultFeatureDO.featureUsage),
                now,
                now,
                Constants.IS_NOT_DELETED
            )
            apREDefaultFeatureRepo.insert(newApREDefaultFeature).let {
                if (it == 0) throw ApREException("create apre default feature causes exception")
            }
            newApREDefaultFeature
        }
        //处理特性规格
        val apREDefaultFeatureSpecs = mutableListOf<ApREDefaultFeatureSpecDO>()
        apREDefaultFeatureDO.defaultFeatureSpecs ?.forEach { apREDefaultFeatureSpecDO ->
            apREDefaultFeatureSpecs.add(
                createApREFeatureSpecUpdateWhileExist(
                    apREDefaultFeatureSpecDO.copy(featureKey = apREDefaultFeature.featureKey)
                )
            )
        }
        return ApREDefaultFeatureDO(
            apREDefaultFeature.id,
            apREDefaultFeature.featureKey,
            apREDefaultFeature.code,
            apREDefaultFeature.featureUsage,
            apREDefaultFeature.gmtCreate,
            apREDefaultFeature.gmtModified,
            apREDefaultFeature.isDeleted,
            apREDefaultFeatureSpecs
        )
    }

    /**
     * 创建缺省特性规格，存在即更新
     */
    @Transactional
    fun createApREFeatureSpecUpdateWhileExist(apREDefaultFeatureSpecDO: ApREDefaultFeatureSpecDO) : ApREDefaultFeatureSpecDO {
        val apREDefaultFeatureSpec =  apREDefaultFeatureSpecRepo.queryByFeatureKey(apREDefaultFeatureSpecDO.featureKey!!).stream().filter {
            it.specType == apREDefaultFeatureSpecDO.specType && it.specCode == apREDefaultFeatureSpecDO.specCode
        }.findFirst().let { apREDefaultFeatureSpecOptional->
            if (apREDefaultFeatureSpecOptional.isPresent) {
                val originalApREDefaultFeatureSpec = apREDefaultFeatureSpecOptional.get()
                val newApREDefaultFeatureSpec = ApREDefaultFeatureSpec(
                    originalApREDefaultFeatureSpec.id,
                    apREDefaultFeatureSpecDO.featureKey,
                    apREDefaultFeatureSpecDO.title,
                    if (apREDefaultFeatureSpecDO.specType.isNullOrBlank())
                        ApRELabelExt.DEFAULT_APRE_FEATURE_SPEC_TYPE_COMMON
                    else
                        apREDefaultFeatureSpecDO.specType.trim().lowercase(),
                    apREDefaultFeatureSpecDO.specCode,
                    checkScope(apREDefaultFeatureSpecDO.scope),
                    checkStatus(apREDefaultFeatureSpecDO.status),
                    apREDefaultFeatureSpecDO.annotations,
                    originalApREDefaultFeatureSpec.gmtCreate,
                    Date(Instant.now().toEpochMilli()),
                    originalApREDefaultFeatureSpec.isDeleted
                )
                apREDefaultFeatureSpecRepo.updateById(newApREDefaultFeatureSpec) .let {
                    if (it == 0) throw ApREException("update default feature spec causes exception,${newApREDefaultFeatureSpec}")
                }
                newApREDefaultFeatureSpec
            } else {
                val now = Date(Instant.now().toEpochMilli())
                val newApREDefaultFeatureSpec = ApREDefaultFeatureSpec(
                    null,
                    apREDefaultFeatureSpecDO.featureKey,
                    apREDefaultFeatureSpecDO.title,
                    if (apREDefaultFeatureSpecDO.specType.isNullOrBlank())
                        ApRELabelExt.DEFAULT_APRE_FEATURE_SPEC_TYPE_COMMON
                    else
                        apREDefaultFeatureSpecDO.specType.trim().lowercase(),
                    apREDefaultFeatureSpecDO.specCode,
                    checkScope(apREDefaultFeatureSpecDO.scope),
                    checkStatus(apREDefaultFeatureSpecDO.status),
                    apREDefaultFeatureSpecDO.annotations,
                    now,
                    now,
                    Constants.IS_NOT_DELETED
                )
                apREDefaultFeatureSpecRepo.insert(newApREDefaultFeatureSpec) .let {
                    if (it == 0) throw ApREException("insert default feature spec causes exception,${newApREDefaultFeatureSpec}")
                }
                newApREDefaultFeatureSpec
            }
        }
        return ApREDefaultFeatureSpecDO(
            apREDefaultFeatureSpec.id,
            apREDefaultFeatureSpec.featureKey,
            apREDefaultFeatureSpec.title,
            apREDefaultFeatureSpec.specType,
            apREDefaultFeatureSpec.specCode,
            apREDefaultFeatureSpec.scope,
            apREDefaultFeatureSpec.status,
            apREDefaultFeatureSpec.annotations,
            apREDefaultFeatureSpec.gmtCreate,
            apREDefaultFeatureSpec.gmtModified,
            apREDefaultFeatureSpec.isDeleted
        )
    }

    /**
     * 查找所有默认导入的特性以及特性规格
     */
    fun findApREDefaultFeatureDetailWithDefaultImportUsage(): List<ApREDefaultFeatureDO> {
        //本地缓存，TODO 可以做成通用本地缓存方法（注释+切面实现）
        val now = Instant.now().toEpochMilli()
        if (now <= defaultImportUsageWithSpecListCache.first) {
            return defaultImportUsageWithSpecListCache.second
        }
        cacheLoadLock.lock()
        try {
            if (now <= defaultImportUsageWithSpecListCache.first) {
                return defaultImportUsageWithSpecListCache.second
            }
            val apREDefaultFeatureDOs = mutableListOf<ApREDefaultFeatureDO>()
            apREDefaultFeatureRepo.queryByFeatureUsage(ApREDefaultFeatureUsageEnum.DEFAULT_IMPORT.name).forEach {
                apREDefaultFeatureDOs.add(
                    ApREDefaultFeatureDO(
                        it.id,
                        it.featureKey,
                        it.code,
                        it.featureUsage,
                        it.gmtCreate,
                        it.gmtModified,
                        it.isDeleted,
                        findOnlineApREDefaultFeatureSpecByFeatureKey(it.featureKey)
                    )
                )
            }
            defaultImportUsageWithSpecListCache = Pair(Instant.now().toEpochMilli() + DEFAULT_IMPORT_USAGE_WITH_SPEC_LIST_EXPIRE_TIME, apREDefaultFeatureDOs)
            return apREDefaultFeatureDOs
        } finally {
            cacheLoadLock.unlock()
        }
    }

    /**
     * 根据代码查找缺省特性以及特性规格详情
     */
    fun findApREDefaultFeatureDetail(code: String): ApREDefaultFeatureDO? {
        //逐步废弃缺省特性规格，暂时只保留&兼容CPU特性以及对应规格
        //AppStack下线即可去除缺省CPU规格
        if (code != APRE_LABEL_FEATURE_VALUE_COMPUTE) {
            return null
        }
        val defaultFeatureKey = "LGVlbUwHNzm76AS4DeXFHPLqbJi8FNc7"
        return ApREDefaultFeatureDO(
            id = 1L,
            featureKey = defaultFeatureKey,
            code = APRE_LABEL_FEATURE_VALUE_COMPUTE,
            featureUsage = ApREDefaultFeatureUsageEnum.DEFAULT_IMPORT.name,
            defaultFeatureSpecs = listOf(
                ApREDefaultFeatureSpecDO(
                    id = 1L,
                    featureKey = defaultFeatureKey,
                    title = "2C4G",
                    specType = "common",
                    specCode = "2-4",
                    scope = "publish",
                    status = ApREFeatureSpecStatusEnum.online.name,
                    annotations = "{\"cpu\":\"2\",\"memory\":\"4Gi\"}",
                ),
                ApREDefaultFeatureSpecDO(
                    id = 2L,
                    featureKey = defaultFeatureKey,
                    title = "4C8G",
                    specType = "common",
                    specCode = "4-8",
                    scope = "publish",
                    status = ApREFeatureSpecStatusEnum.online.name,
                    annotations = "{\"cpu\":\"4\",\"memory\":\"8Gi\"}",
                ),
                ApREDefaultFeatureSpecDO(
                    id = 3L,
                    featureKey = defaultFeatureKey,
                    title = "8C16G",
                    specType = "common",
                    specCode = "8-16",
                    scope = "publish",
                    status = ApREFeatureSpecStatusEnum.online.name,
                    annotations = "{\"cpu\":\"8\",\"memory\":\"16Gi\"}",
                ),
                ApREDefaultFeatureSpecDO(
                    id = 4L,
                    featureKey = defaultFeatureKey,
                    title = "16C32G",
                    specType = "common",
                    specCode = "16-32",
                    scope = "publish",
                    status = ApREFeatureSpecStatusEnum.online.name,
                    annotations = "{\"cpu\":\"16\",\"memory\":\"32Gi\"}",
                ),
                ApREDefaultFeatureSpecDO(
                    id = 5L,
                    featureKey = defaultFeatureKey,
                    title = "8C24G",
                    specType = "common",
                    specCode = "8-24",
                    scope = "publish",
                    status = ApREFeatureSpecStatusEnum.online.name,
                    annotations = "{\"cpu\":\"8\",\"memory\":\"24Gi\"}",
                ),
            )
        )
    }

    /**
     * 根据特性KEY查找缺省特性
     */
    private fun findOnlineApREDefaultFeatureSpecByFeatureKey(featureKey: String): List<ApREDefaultFeatureSpecDO> {
        val apREDefaultFeatureSpecDOs = mutableListOf<ApREDefaultFeatureSpecDO>()
        apREDefaultFeatureSpecRepo.queryByFeatureKeyAndStatus(featureKey, ApREFeatureSpecStatusEnum.online.name).forEach {
            apREDefaultFeatureSpecDOs.add(
                ApREDefaultFeatureSpecDO(
                    it.id,
                    it.featureKey,
                    it.title,
                    it.specType,
                    it.specCode,
                    it.scope,
                    it.status,
                    it.annotations,
                    it.gmtCreate,
                    it.gmtModified,
                    it.isDeleted
                )
            )
        }
        return apREDefaultFeatureSpecDOs
    }

    companion object {
        const val APRE_DEFAULT_FEATURE_KEY_LENGTH = 32
        // 缺省导入特性及规格缓存时长,单位:ms
        const val DEFAULT_IMPORT_USAGE_WITH_SPEC_LIST_EXPIRE_TIME = 5*60 *1000
    }
}