package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ResourcePoolCreateOrUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.common.exceptions.ApREConsoleResolveException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelException
import com.alibaba.koastline.multiclusters.common.exceptions.CreateDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourcePoolUniqueExistException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ResourcePoolRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus
import com.alibaba.koastline.multiclusters.data.vo.env.ResourcePoolData
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * refactor in 2023.1.4 by boche
 */
@Component
class ResourcePoolService {
    @Autowired
    lateinit var resourcePoolRepo: ResourcePoolRepo

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var apRELabelService: ApRELabelService

    @Autowired
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo

    @Autowired
    lateinit var apRELabelDefinitionService: ApRELabelDefineService


    /**
     * 查询资源池标签
     * @param runtimeEnvKey ApRE 主键
     */
    fun findApRERelativeResourcePoolLabelsByRuntimeEnvKey(runtimeEnvKey: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)?.run {
            listByManagedClusterKey(this.managedClusterKey).forEach {
                apRELabelDOs.addAll(
                    apRELabelService.findApRELabelByTarget(
                        it.resourcePoolKey,
                        ApRELabelTargetTypeEnum.RESOURCE_POOL.name
                    )
                )
            }
        }
        // 注入title
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    /**
     * 更新白屏化管控范围内 ResourcePool 及其 ApRELabel
     */
    @Transactional
    fun updateResourcePoolIgnoreWhileExistWithLabel(resourcePoolUpdateReqDto: ResourcePoolCreateOrUpdateReqDto): ResourcePoolDO {
        log.info("updateResourcePoolIgnoreWhileExistWithLabel:$resourcePoolUpdateReqDto")
        val existedResourcePool = resourcePoolRepo.findByManagedClusterKeyAndClusterId(
            managedClusterKey = resourcePoolUpdateReqDto.managedClusterKey,
            clusterId = resourcePoolUpdateReqDto.clusterId
        )
        existedResourcePool?.let { existedResourcePool ->
            // 删除白屏化监管范围内的ApRELabel
            apRELabelService.deleteApRELabelByTargetAndType(
                existedResourcePool.resourcePoolKey,
                ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                ApRELabelType.CONSOLE
            )
            val apRELabels = mutableListOf<ApRELabelDO>()
            resourcePoolUpdateReqDto.apRELabelList.forEach {
                apRELabels.add(apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(it))
            }
            return ResourcePoolDO(
                id = existedResourcePool.id!!,
                gmtCreate = existedResourcePool.gmtCreate,
                gmtModified = existedResourcePool.gmtModified,
                resourcePoolKey = existedResourcePool.resourcePoolKey,
                clusterId = existedResourcePool.clusterId,
                isDeleted = existedResourcePool.isDeleted,
                apRELabels = apRELabels,
                managedClusterKey = existedResourcePool.managedClusterKey
            )
        } ?: return createResourcePoolIgnoreWhileExistWithLabel(resourcePoolUpdateReqDto)
    }


    /**
     *  创建resourcePool,如果检测存在则忽略;同时级联创建label和spec
     */
    @Transactional
    fun createResourcePoolIgnoreWhileExistWithLabel(resourcePoolCreateReqDto: ResourcePoolCreateOrUpdateReqDto): ResourcePoolDO {
        log.info("createResourceIgnoreWhileExistWithLabel:$resourcePoolCreateReqDto")
        val newResourcePoolDataDO = createResourcePool(resourcePoolCreateReqDto)
        //级联创建label及spec
        resourcePoolCreateReqDto.apRELabelList.let { originalApRELabelList ->
            originalApRELabelList.forEach {
                apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(
                    it.copy(
                        targetKey = newResourcePoolDataDO.resourcePoolKey,
                        targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                    )
                )
            }
        }
        return checkNotNull(findResourcePoolDetailByKey(newResourcePoolDataDO.resourcePoolKey)) { "create resource pool with label and feature spec with error" }
    }

    /**
     * 创建resourcePool,如果检测存在则忽略
     */
    @Transactional
    fun createResourcePool(resourcePoolCreateReqDto: ResourcePoolCreateOrUpdateReqDto): ResourcePoolDO {
        log.info("createResourcePool:$resourcePoolCreateReqDto")
        val newResourcePoolDataDO = createResourcePoolWithClusterId(
            resourcePoolCreateReqDto.managedClusterKey,
            resourcePoolCreateReqDto.clusterId,
            resourcePoolCreateReqDto.creator
        )
        return checkNotNull(findResourcePoolDetailByKey(newResourcePoolDataDO.resourcePoolKey)) { "create resource pool with label and feature spec with error" }
    }

    fun createResourcePoolLabel(apRELabelCreateReqDto: ApRELabelCreateReqDto): ApRELabelDO {
        val resourcePoolKey = checkNotNull(apRELabelCreateReqDto.targetKey) { "resource pool cannot be blank" }
        resourcePoolRepo.findByResourcePoolKey(resourcePoolKey) ?: throw ApRELabelException("resource pool ref by $resourcePoolKey not found")
        val newResourcePoolDataDO = apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(
            apRELabelCreateReqDto
        )
        log.info("createResourcePoolLabel:$apRELabelCreateReqDto")
        return newResourcePoolDataDO
    }


    fun createResourcePoolWithClusterName(
        managedClusterKey: String,
        clusterName: String,
        creator: String
    ): ResourcePoolDataDO {
        return createResourcePoolWithClusterId(
            managedClusterKey,
            defaultClusterService.getSimpleClusterProfileDataByClusterName(clusterName).clusterId,
            creator
        )
    }

    fun createResourcePoolWithClusterId(
        managedClusterKey: String,
        clusterId: String,
        creator: String
    ): ResourcePoolDataDO {
        defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId)
        resourcePoolRepo.findByManagedClusterKeyAndClusterId(managedClusterKey, clusterId)?.let {
            // 重复创建检验
            throw ResourcePoolUniqueExistException(it.managedClusterKey, it.clusterId)
        }
        val resourcePoolData = ResourcePoolData(
            clusterId = clusterId,
            managedClusterKey = managedClusterKey,
            creator = creator,
            modifier = creator,
            resourcePoolKey = KeyGenerator.generateAlphanumericKey(RESOURCE_POOL_KEY_LENGTH)
        )
        if (resourcePoolRepo.insert(resourcePoolData) == 0) throw CreateDataException(resourcePoolData)
        val resourcePoolId =
            checkNotNull(resourcePoolData.id) { "resourcePoolData id cannot be null in createResourcePoolWithClusterId " }
        return getResourcePoolById(resourcePoolId)
    }

    /**
     * 根据resourcePool key 级联查询 resource Pool 的属性及其相关特性
     */
    fun findResourcePoolDetailByKey(resourcePoolKey: String): ResourcePoolDO? {
        val resourcePoolDataDO = getResourcePoolByResourcePoolKey(resourcePoolKey) ?: let {
            return null
        }
        return ResourcePoolDO.assemble(
            resourcePoolDataDO,
            apRELabelService.findApRELabelByTarget(resourcePoolKey, ApRELabelTargetTypeEnum.RESOURCE_POOL.name)
        )
    }

    fun findResourcePoolDetailWithAllLabelSpecByKey(resourcePoolKey: String): ResourcePoolDO? {
        val resourcePoolDataDO = getResourcePoolByResourcePoolKey(resourcePoolKey) ?: let {
            return null
        }
        return ResourcePoolDO.assemble(
            resourcePoolDataDO,
            apRELabelService.findAllApRELabelByTarget(resourcePoolKey, RESOURCE_POOL.name)
        )
    }

    fun getResourcePoolById(id: Long): ResourcePoolDataDO {
        return resourcePoolRepo.findById(id).convert()
    }


    fun getResourcePoolByKey(key: String): ResourcePoolDataDO? {
        return resourcePoolRepo.findByResourcePoolKey(key)?.convert()
    }

    fun getResourcePoolByResourcePoolKey(resourcePoolKey: String): ResourcePoolDataDO? {
        return resourcePoolRepo.findByResourcePoolKey(resourcePoolKey)?.convert()
    }

    fun listByManagedClusterKey(managedClusterKey: String): List<ResourcePoolDataDO> {
        return resourcePoolRepo.findByManagedClusterKey(managedClusterKey).map { it.convert() }
    }

    fun listByClusterId(clusterId: String): List<ResourcePoolDataDO> {
        return resourcePoolRepo.findByClusterId(clusterId).map { it.convert() }
    }

    fun listByClusterName(clusterName: String): List<ResourcePoolDataDO> {
        val cluster = defaultClusterService.clusterProfileRepo.findClusterProfileByClusterName(clusterName) ?: let {
            return emptyList()
        }
        return resourcePoolRepo.findByClusterId(cluster.clusterId).map { it.convert() }
    }

    fun listResourceDOByManagedClusterKey(managedClusterKey: String): List<ResourceDO> {
        return resourcePoolRepo.findByManagedClusterKey(managedClusterKey).map { resourcePoolData ->
            defaultClusterService.getSimpleClusterProfileDataByClusterId(resourcePoolData.clusterId).run {
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew(
                        clusterId = this.clusterId,
                        clusterName = this.clusterName,
                        clusterProvider = this.clusterProvider,
                        clusterType = this.clusterType,
                        siteList = this.site.split(","),
                        componentDataList = emptyList(),
                        useType = this.useType,
                        status = this.status
                    ),
                    resourcePoolKey = resourcePoolData.resourcePoolKey,
                    apRELabels = apRELabelService.findApRELabelByTarget(
                        resourcePoolData.resourcePoolKey,
                        ApRELabelTargetTypeEnum.RESOURCE_POOL.name
                    )
                )
            }
        }
    }

    fun listClusterProfileByManagedClusterKey(managedClusterKey: String): List<ClusterProfileNew> {
        val clusterIds = resourcePoolRepo.findByManagedClusterKey(managedClusterKey).map { it.clusterId }
        if (clusterIds.isEmpty()) {
            return emptyList()
        }
        return listClusterProfileNew(clusterIds)
    }

    fun listOnlineResourceDOByManagedClusterKey(managedClusterKey: String): List<ResourceDO> {
        return listResourceDOByManagedClusterKey(managedClusterKey).filter {
            it.clusterProfileNew!!.status == ClusterStatus.ONLINE.status
        }
    }

    fun listClusterProfileNew(clusterIdList: List<String>): List<ClusterProfileNew> {
        return defaultClusterService.listSimpleClusterProfileDataByClusterIds(clusterIdList).map { cluster ->
            ClusterProfileNew(
                clusterId = cluster.clusterId,
                clusterName = cluster.clusterName,
                clusterProvider = cluster.clusterProvider,
                clusterType = cluster.clusterType,
                siteList = cluster.site.split(","),
                componentDataList = emptyList(),
                useType = cluster.useType
            )
        }
    }

    fun getClusterProfileNew(clusterId: String): ClusterProfileNew {
        defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId).run {
            return ClusterProfileNew(
                clusterId = this.clusterId,
                clusterName = this.clusterName,
                clusterProvider = this.clusterProvider,
                clusterType = this.clusterType,
                siteList = this.site.split(","),
                componentDataList = emptyList(),
                useType = this.useType
            )
        }
    }

    /**
     * 删除 resourcePool 实体类
     */
    @Transactional
    fun deleteWithLabelsByResourcePoolKeyWhileCheck(resourcePoolKey: String, modifier: String) {
        require(resourcePoolKey.isNotBlank()){ "resourcePool key cannot be blank" }
        require(modifier.isNotBlank()){ "modifier cannot be blank" }
        // 查询是否存在接管范围外的 ApRELabel 如果存在则抛出异常
        if (apRELabelService.isHasApiRelativeObjectResource(resourcePoolKey = resourcePoolKey)) {
            throw ApREConsoleResolveException("更新中存在删除资源resourcePool:${resourcePoolKey}! 此类资源存在非console管理范围内的伴生资源！")
        }
        getResourcePoolByResourcePoolKey(resourcePoolKey).let {
            // 删除 resourcePool 本体
            deleteByResourcePoolKey(resourcePoolKey, modifier)
            // 删除伴生的 ApRELabel
            apRELabelService.deleteApRELabelByTargetAndType(
                targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                targetKey = resourcePoolKey,
                type = ApRELabelType.CONSOLE
            )
        }
        log.info("deleteResourcePoolWithApRELabel: resourcePoolKey：$resourcePoolKey, modifier:$modifier")
    }

    @Transactional
    fun deleteResourcePoolApRELabelWithCheck(apRELabelKey: String, modifier: String) {
        require(apRELabelKey.isNotBlank()) { "apre label key cannot be blank" }
        val existedApRELabel = apRELabelService.findApRELabelByLabelKey(
            apRELabelKey = apRELabelKey
        ) ?: return
        if (existedApRELabel.isServerlessLabel()) {
            throw ApREConsoleResolveException("更新中存在删除资源apRELabelKey:${apRELabelKey}! 此类资源存在非console管理范围内的伴生资源！")
        }
        if(existedApRELabel.targetType != RESOURCE_POOL){
            throw ApRELabelException("所删label不属于 resource pool label")
        }
        apRELabelService.deleteApRELabelByApRELabelKey(apRELabelKey)
        log.info("deleteResourcePoolWithApRELabel: apRELabelKey：$apRELabelKey, modifier:$modifier")
    }


    fun deleteByManagedClusterKeyAndClusterId(managedClusterKey: String, clusterId: String, modifier: String) {
        resourcePoolRepo.deleteByManagedClusterKeyAndClusterId(managedClusterKey, clusterId, modifier)
    }

    /**
     * 按照kManager删除resourcePool数据
     *
     *
     * @param managedClusterKey
     * @param modifier
     */
    @Transactional
    fun deleteByManagedClusterKeyWithKManageCluster(managedClusterKey: String, modifier: String) {
        defaultClusterService.deleteSimpleKManageCluster(managedClusterKey)
        resourcePoolRepo.deleteByManagedClusterKey(managedClusterKey, modifier)
    }

    fun deleteByResourcePoolKey(resourcePoolKey: String, modifier: String) {
        resourcePoolRepo.deleteByResourcePoolKey(resourcePoolKey, modifier)
    }

    fun listRelativeClusters(): List<String> {
        return resourcePoolRepo.listRelativeClusters()
    }

    /**
     * 合并相同集群的label&spec
     */
    fun mergeResourceByCluster(resources: List<ResourceDO>): List<ResourceDO> {
        val mergedResourceMap = mutableMapOf<String, ResourceDO>()
        resources.forEach { originalResource ->
            mergedResourceMap.values.firstOrNull { mergedResource ->
                mergedResource.clusterProfileNew!!.clusterId == originalResource.clusterProfileNew!!.clusterId
            }?.let {
                mergeResourceByOriginalToMerged(
                    originalResource = originalResource,
                    mergedResource = it
                ).let { newMergedResource ->
                    mergedResourceMap.put(newMergedResource.resourcePoolKey!!, newMergedResource)
                }
            } ?: let {
                mergedResourceMap.put(originalResource.resourcePoolKey!!, originalResource)
            }
        }
        return mergedResourceMap.values.toList()
    }

    /**
     * 合并原始的资源label&Spec -> 合并的资源
     */
    private fun mergeResourceByOriginalToMerged(originalResource: ResourceDO, mergedResource: ResourceDO): ResourceDO {
        if (originalResource.apRELabels.isEmpty()) {
            return mergedResource
        }
        val mergedApRELabelMap = mergedResource.apRELabels.associateBy { it.apRELabelKey }.toMutableMap()
        originalResource.apRELabels.forEach { originalApRELLabel ->
            mergedApRELabelMap.values.firstOrNull { mergedApRELabel ->
                mergedApRELabel.name == originalApRELLabel.name && mergedApRELabel.value == originalApRELLabel.value
            }?.let {
                mergeLabelByOriginalToMerged(
                    originalLabel = originalApRELLabel,

                    mergedLabel = it
                ).let { newMergedApRELabel ->
                    mergedApRELabelMap.put(newMergedApRELabel.apRELabelKey, newMergedApRELabel)
                }
            } ?: let {
                mergedApRELabelMap.put(originalApRELLabel.apRELabelKey, originalApRELLabel)
            }
        }
        return mergedResource.copy(apRELabels = mergedApRELabelMap.values.toList())
    }

    /**
     * 合并原始的Label -> 合并的Label
     */
    private fun mergeLabelByOriginalToMerged(originalLabel: ApRELabelDO, mergedLabel: ApRELabelDO): ApRELabelDO {
        if (originalLabel.apREFeatureSpecs.isNullOrEmpty()) {
            return mergedLabel
        }
        val mergedLabelSpecMap =
            mergedLabel.apREFeatureSpecs?.associateBy { it.specCode }?.toMutableMap() ?: mutableMapOf()
        originalLabel.apREFeatureSpecs.forEach { originalLabelSpec ->
            mergedLabelSpecMap.values.firstOrNull { mergedLabelSpec ->
                mergedLabelSpec.specCode == originalLabelSpec.specCode
            } ?: let {
                mergedLabelSpecMap.put(originalLabelSpec.specCode, originalLabelSpec)
            }
        }
        return mergedLabel.copy(apREFeatureSpecs = mergedLabelSpecMap.values.toList())
    }


    private fun ResourcePoolData.convert(): ResourcePoolDataDO {
        return ResourcePoolDataDO(
            checkNotNull(this.id) { " ResourcePoolData id cannot be null in convert process " },
            this.resourcePoolKey,
            this.clusterId,
            this.managedClusterKey,
            this.creator,
            this.modifier,
            this.gmtCreate,
            this.gmtModified,
            this.isDeleted
        )
    }

    companion object {
        const val RESOURCE_POOL_KEY_LENGTH = 32
        val log by logger()
    }
}