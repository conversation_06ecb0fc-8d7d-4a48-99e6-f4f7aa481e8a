package com.alibaba.koastline.multiclusters.apre.common

import com.alibaba.koastline.multiclusters.apre.ApRELabelDefineService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.model.req.FeatureSpecDefinitionCreateReq
import com.alibaba.koastline.multiclusters.apre.model.req.FeatureSpecDefinitionUpdateReq
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.FeatureSpecDefinitionException
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.dao.env.FeatureSpecDefinitionRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.FeatureSpecDefinition
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.*

@Service
class ApREFeatureSpecDefineService {

    @Autowired
    private lateinit var featureSpecDefinitionRepo: FeatureSpecDefinitionRepo

    @Autowired
    private lateinit var labelDefinition: ApRELabelDefineService

    fun createApREFeatureSpecDefinition(
        featureSpecDefinitionCreateReq: FeatureSpecDefinitionCreateReq
    ) {
        val newLabelSpecDef = featureSpecDefinitionCreateReq.convertToFeatureSpecDefinition()
        labelDefinition.findApRELabelDefinitionsByApRELabelDefKey(newLabelSpecDef.refKey) ?: let {
            throw FeatureSpecDefinitionException("cannot find labelDef Key")
        }
        featureSpecDefinitionRepo.findBySpecCode(newLabelSpecDef.specCode)?.let {
            throw FeatureSpecDefinitionException("specCode must be unique, but existed")
        }

        val featureSpecDef = newLabelSpecDef.copy(
            featureSpecDefinitionId = KeyGenerator.generateAlphanumericKey(ApRELabelExt.APRE_LABEL_DEF_KEY_LENGTH),
            isDeleted = "N",
        )
        featureSpecDefinitionRepo.insert(featureSpecDef).let {
            if (it == 0) throw FeatureSpecDefinitionException("create feature spec template with error!")
        }
    }

    fun findByFeatureSpecDefinitionId(
        featureSpecDefinitionId: String
    ): FeatureSpecDefinition? {
        require(featureSpecDefinitionId.isNotBlank()) { "featureSpecDefinitionId cannot be blank" }
        return featureSpecDefinitionRepo.findByFeatureSpecDefinitionId(featureSpecDefinitionId)
    }

    fun listByTitle(title: String): List<FeatureSpecDefinition> {
        return featureSpecDefinitionRepo.listByTitle(title)
    }

    fun listByRef(refKey: String): List<FeatureSpecDefinition> {
        return featureSpecDefinitionRepo.listByRefKey(refKey)
    }

    fun listByProperties(
        pageSize: Int,
        pageNumber: Int,
        title: String? = null,
        specCode: String? = null,
        scope: String? = null,
        status: String? = null,
        refKey: String? = null
    ): PageData<FeatureSpecDefinition> {
        val page: Page<FeatureSpecDefinition> = PageHelper.startPage<FeatureSpecDefinition>(pageNumber, pageSize)
            .doSelectPage {
                featureSpecDefinitionRepo.listFeatureSpecDefinitionsByProperties(
                    title = title, specCode = specCode, scope = scope, status = status, refKey = refKey
                )
            }
        return PageData.transformFrom(page)

    }

    fun updateByFeatureSpecDefinitionId(
        featureSpecDefinitionUpdateReq: FeatureSpecDefinitionUpdateReq
    ) {
        val featureSpecDefinitionId = checkNotNull(featureSpecDefinitionUpdateReq.featureSpecDefinitionId) {
            "feature spec definition missing featureSpecDefinitionId"
        }
        val existedFeatureSpecDef =
            featureSpecDefinitionRepo.findByFeatureSpecDefinitionId(featureSpecDefinitionId) ?: let {
                throw ApREException("cannot find feature spec template with $featureSpecDefinitionId")
            }
        featureSpecDefinitionRepo.updateByFeatureSpecDefinitionId(
            featureSpecDefinitionUpdateReq.mergeToNewFeatureSpecDefinition(existedFeatureSpecDef)
        )
    }

    fun deleteApREFeatureSpecDefinitionByFeatureSpecDefinitionId(
        featureSpecDefinitionId: String, modifier: String
    ) {
        require(featureSpecDefinitionId.isNotBlank()) { "featureSpecDefinitionId cannot be blank!" }
        featureSpecDefinitionRepo.deleteByLabelFeatureSpecDefinitionId(featureSpecDefinitionId, modifier)
    }

    fun deleteApREFeatureSpecDefinitionByRefKey(
        refKey: String, modifier: String
    ) {
        require(refKey.isNotBlank()) { "refKey cannot be blank!" }
        featureSpecDefinitionRepo.deleteByLabelFeatureSpecDefinitionId(refKey, modifier)
    }
}
