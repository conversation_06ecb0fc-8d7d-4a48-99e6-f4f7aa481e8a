package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.StackServerlessBaseAppBindingService
import com.alibaba.koastline.multiclusters.common.exceptions.CmdbException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.checkNotEmpty
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.external.model.ServerlessBaseAppInfo
import com.alibaba.koastline.multiclusters.external.params.CmdbBoxType
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.WorkloadSpecFacade
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ScenePlanSpec
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import org.apache.commons.lang3.StringUtils
import com.alibaba.kondyle.ops.domain.CreatePlanRequest
import com.alibaba.kondyle.ops.domain.ServiceConfig
import com.alibaba.koastline.multiclusters.schedule.service.schedule.extra.ServerlessRunningScheduleService
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class ServerlessSpecService: WorkloadSpecFacade, InitializingBean {
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var workloadSpecFactory: WorkloadSpecFactory
    @Autowired
    lateinit var defaultClusterService: DefaultClusterService
    @Autowired
    lateinit var baseSpecService: BaseSpecService
    @Autowired
    lateinit var serverlessRunningScheduleService: ServerlessRunningScheduleService
    @Autowired
    lateinit var stackServerlessBaseAppBindingService: StackServerlessBaseAppBindingService

    override fun preCheck(resourceObjectSpecStr: String?, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        check(resourceObjectSpecStr, resourceObjectFormatEnum)
    }

  override fun postApply(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum): String {
    return resourceObjectSpecStr
  }

    override fun postCheckForScaleOut(
        currentResourceObjectSpecStr: String?,
        patchedResourceObjectSpecStr: String,
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
    ) {
        return
    }



    override fun getBaseSpec(context: WorkloadSpecContext): Map<String, Any> {
        val scenePlanString = getEnvBaselineScenePlan(context.envStackId)
        var scenePlanObject: CreatePlanRequest
        val jarUrl = getEnvBaselineJarUrl(context.envStackId)
        if (StringUtils.isNotBlank(scenePlanString)) {
            scenePlanObject = JsonUtils.gsonReadValue(
                JsonUtils.writeValueAsString(YamlUtils.load(scenePlanString)),
                CreatePlanRequest::class.java
            )
        } else {
            scenePlanObject = CreatePlanRequest()
        }
        scenePlanObject.jarURI = jarUrl
        scenePlanObject.apply {
            this.app = context.workloadMetadataConstraint.appName
            this.k8sId = context.workloadMetadataConstraint.clusterId.run { defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId = this).clusterName }
            this.site = context.workloadMetadataConstraint.site
            this.group = context.workloadMetadataConstraint.resourceGroup
            this.unit = context.workloadMetadataConstraint.unit
            this.stage = context.workloadMetadataConstraint.stage
            this.runtimeId = checkNotNull(context.workloadMetadataConstraint.runtimeId) { "runtimeId must not be null." }
            this.serviceConfigs = buildServiceConfigs(context.workloadMetadataConstraint)
        }
        return YamlUtils.load(JsonUtils.gsonWriteValueAsString(scenePlanObject))
    }

    override fun postModifyBaseSpec(
        resourceObjectSpec: Map<String, Any>,
        context: WorkloadSpecContext
    ): Map<String, Any> {
        return resourceObjectSpec
    }

    override fun getDeploySpec(
        stackPkId: String,
        currentResourceObjectSpecStr: String?,
        currentResourceObjectFormatEnum: ResourceObjectFormatEnum,
        workloadMetadataConstraint: WorkloadMetadataConstraint
    ): Map<String, Any> {
        val targetWorkloadMetadataConstraint = getTargetWorkloadMetadataConstraint(
            currentResourceObjectSpecStr = currentResourceObjectSpecStr,
            originalWorkloadMetadataConstraint = workloadMetadataConstraint,
            stackPkId = stackPkId
        )
        val scenePlanString = getScenePlanByStackPkId(stackPkId)
        var scenePlanObject: CreatePlanRequest
        val jarUrl = getEnvBaselineJarUrlByStackPkId(stackPkId)
        if (StringUtils.isNotBlank(scenePlanString)) {
          scenePlanObject = JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(scenePlanString)),
            CreatePlanRequest::class.java
          )
        } else {
          scenePlanObject = CreatePlanRequest()
        }
        scenePlanObject.jarURI = jarUrl
        scenePlanObject.apply {
          this.app = targetWorkloadMetadataConstraint.appName
          this.k8sId = targetWorkloadMetadataConstraint.clusterId.run { defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterId = this).clusterName }
          this.site = targetWorkloadMetadataConstraint.site
          this.group = targetWorkloadMetadataConstraint.resourceGroup
          this.unit = targetWorkloadMetadataConstraint.unit
          this.stage = targetWorkloadMetadataConstraint.stage
          this.runtimeId = checkNotNull(targetWorkloadMetadataConstraint.runtimeId) { "runtimeId must not be null." }
          this.serviceConfigs = buildServiceConfigs(targetWorkloadMetadataConstraint)
        }.apply {
            targetWorkloadMetadataConstraint.workloadName ?.let {
                this.workloadName = it
            }
        }
        return YamlUtils.load(JsonUtils.gsonWriteValueAsString(scenePlanObject))
    }

    private fun getTargetWorkloadMetadataConstraint(
        currentResourceObjectSpecStr: String?,
        originalWorkloadMetadataConstraint: WorkloadMetadataConstraint,
        stackPkId: String
    ): WorkloadMetadataConstraint {
        val currentScenePlanSpec = currentResourceObjectSpecStr ?.run {
            JsonUtils.readValue(this, ScenePlanSpec::class.java)
        } ?:let{
            return originalWorkloadMetadataConstraint
        }
        return originalWorkloadMetadataConstraint.copy(
            workloadName = currentScenePlanSpec.name,
            runtimeId = currentScenePlanSpec.distrPlan.location.label
        ).let {workloadMetadataConstraint ->
            getAoneServerlessBaseAppInfo(stackPkId) ?.run {
                serverlessRunningScheduleService.seekServerlessAppMigrationBaseApp(
                    workloadMetadataConstraint,
                    migrationToBaseApp = this.baseAppName,
                    migrationToEnvStackId = this.baseEnvStackId
                )
            } ?:workloadMetadataConstraint
        }
    }

    fun getAoneServerlessBaseAppInfo(stackPkId: String): ServerlessBaseAppInfo? {
        return cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(stackPkId) ?:run {
            stackServerlessBaseAppBindingService.getStackServerlessBaseAppBindingData(
                envStackId = cloudCmdbApi.getStackWithBox(stackPkId).categoryId
            ) ?.let {stackServerlessBaseAppBindingDataDO ->
                stackServerlessBaseAppBindingDataDO.extraParams ?.runtimeBaseEnvStackId ?.toNullIfBlank() ?.let {runtimeBaseEnvStackId ->
                    ServerlessBaseAppInfo(
                        baseAppName = stackServerlessBaseAppBindingDataDO.serverlessBaseAppName,
                        baseEnvStackId = runtimeBaseEnvStackId
                    )
                }
            }
        }
    }

    private fun buildServiceConfigs(workloadMetadataConstraint: WorkloadMetadataConstraint):List<ServiceConfig> {
        return listOf(
            ServiceConfig().apply {
                this.type = SERVICE_CONFIG_SKYLINE_TYPE
                this.name = SERVICE_CONFIG_SKYLINE_NAME
                this.isMasked = false
                this.isAsync = true
                this.configStr = JsonUtils.writeValueAsString(mapOf(
                    "host" to SERVICE_SKYLINE_HOST,
                    "appUseType" to workloadMetadataConstraint.stage,
                    "group" to workloadMetadataConstraint.resourceGroup,
                    "buffGroup" to SERVICE_SKYLINE_BUFF_GROUP,
                    "hostnameTemplate" to  baseSpecService.getPodHostNameFormat(workloadMetadataConstraint)
                ))
            }
        )
    }

    /**
     * 获取环境基线Yaml Spec
     */
    fun getEnvBaselineJarUrl(envStackId: String): String {
        val cmdbBaselineBox = cloudCmdbApi.getBoxAllInfo(
            checkNotEmpty(cloudCmdbApi.getStackWithBox(
                cloudCmdbApi.getBaseline(envStackId).id
            ).boxList){"环境基线版本envStackId:${envStackId}, boxList不能为空"} [0].id
        )
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw CmdbException("环境基线版本envStackId:${envStackId}, boxStackId:${cmdbBaselineBox.id}, 缺少services定义.")
        }
        val cmdbService = serviceList.firstOrNull { service ->
            CmdbBoxType.SERVERLESS_APP.value == service.type
        } ?: throw CmdbException("环境基线版本envStackId:${envStackId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp service定义.")
        return cmdbService.attributeList.firstOrNull {
            "appJar" == it.name
        } ?.value ?.toNullIfBlank() ?:let {
            throw CmdbException("环境基线版本envStackId:${envStackId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp jar信息.")
        }
    }

    fun getEnvBaselineScenePlan(envStackId: String): String {
        val cmdbBaselineBox = cloudCmdbApi.getBoxAllInfo(
            checkNotEmpty(cloudCmdbApi.getStackWithBox(
                cloudCmdbApi.getBaseline(envStackId).id
            ).boxList){"环境基线版本envStackId:${envStackId}, boxList不能为空"} [0].id
        )
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw CmdbException("环境基线版本envStackId:${envStackId}, boxStackId:${cmdbBaselineBox.id}, 缺少services定义.")
        }
        val cmdbService = serviceList.firstOrNull { service ->
            CmdbBoxType.SERVERLESS_APP.value == service.type
        } ?: throw CmdbException("环境基线版本envStackId:${envStackId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp service定义.")
        return cmdbService.attributeList.firstOrNull {
            "scenePlan" == it.name
        } ?.value ?.toNullIfBlank() ?:let {
            return ""
        }
    }

    /**
     * 获取环境基线Yaml Spec
     */
    fun getEnvBaselineJarUrlByStackPkId(stackPkId: String): String {
        val cmdbBaselineBox = cloudCmdbApi.getBoxAllInfo(
            checkNotEmpty(cloudCmdbApi.getStackWithBox(
                stackPkId
            ).boxList){"环境版本stackPkId:${stackPkId}, boxList不能为空"} [0].id
        )
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw CmdbException("环境版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少services定义.")
        }
        val cmdbService = serviceList.firstOrNull { service ->
            CmdbBoxType.SERVERLESS_APP.value == service.type
        } ?: throw CmdbException("环境版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp service定义.")
        return cmdbService.attributeList.firstOrNull {
            "appJar" == it.name
        } ?.value ?.toNullIfBlank() ?:let {
            throw CmdbException("环境版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp jar信息.")
        }
    }

    fun getScenePlanByStackPkId(stackPkId: String): String {
        val cmdbBaselineBox = cloudCmdbApi.getBoxAllInfo(
            checkNotEmpty(cloudCmdbApi.getStackWithBox(
                stackPkId
            ).boxList){"环境版本stackPkId:${stackPkId}, boxList不能为空"} [0].id
        )
        val serviceList = cmdbBaselineBox.serviceList
        if (serviceList.isNullOrEmpty()) {
            throw CmdbException("环境版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少services定义.")
        }
        val cmdbService = serviceList.firstOrNull { service ->
            CmdbBoxType.SERVERLESS_APP.value == service.type
        } ?: throw CmdbException("环境版本stackPkId:${stackPkId}, boxStackId:${cmdbBaselineBox.id}, 缺少serverlessapp service定义.")
        return cmdbService.attributeList.firstOrNull {
            "scenePlan" == it.name
        } ?.value ?.toNullIfBlank() ?:let {
            return ""
        }
    }

    override fun postCheck(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        check(resourceObjectSpecStr, resourceObjectFormatEnum)
    }

    /**
     * 验证serverless资源对象Spec，暂不实现
     */
    private fun check(resourceObjectSpecStr: String?, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        return
    }

    override fun afterPropertiesSet() {
        workloadSpecFactory.registryWorkloadSpecFacade(ResourceObjectProtocolEnum.ServerlessApp, this)
    }

    companion object {
        private const val SERVICE_CONFIG_SKYLINE_TYPE = 11
        private const val SERVICE_CONFIG_SKYLINE_NAME = "skylineReg"
        private const val SERVICE_SKYLINE_HOST = "intra-sky.alibaba-inc.com"
        private const val SERVICE_SKYLINE_BUFF_GROUP = "c2_app_buffer"
    }
}