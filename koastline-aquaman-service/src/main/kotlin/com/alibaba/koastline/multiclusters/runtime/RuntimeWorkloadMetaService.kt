package com.alibaba.koastline.multiclusters.runtime

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.SERVERLESS_PREFIX
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum.publish
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelSpecException
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeWorkloadMetaException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.RuntimeWorkloadMetaRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeRefEnvStackRespDto
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RuntimeWorkloadMetaService @Autowired constructor(
    val apREFeatureSpecService: ApREFeatureSpecService,
    val apRELabelService: ApRELabelService,
    val apREService: ApREService,
    val resourcePoolService: ResourcePoolService,
    val skylineApi: SkylineApi,
    val runtimeWorkloadMetaRepo: RuntimeWorkloadMetaRepo,
) {

    val log by logger()

    /**
     * 注册runtime workload
     *
     * @param workloadMetadataConstraint
     * @param annotations
     * @param labels
     * @param creator
     */
    @Transactional
    fun registryRuntimeWorkload(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        annotations: Map<String, String>,
        labels: Map<String, String>,
        workloadVisitable: Boolean,
        creator: String,
    ) {
        workloadMetadataConstraint.firmValidate4BaseApp()
        runtimeWorkloadMetaRepo.findByRuntimeWorkloadId(workloadMetadataConstraint.runtimeId!!)?.let {
            throw RuntimeException("workloadMetadataConstraint:$workloadMetadataConstraint has been registered")
        }

        val runtimeWorkloadMeta = assembleRuntimeWorkload(workloadMetadataConstraint, creator)
        if (runtimeWorkloadMetaRepo.insert(runtimeWorkloadMeta) != 1) {
            throw RuntimeWorkloadMetaException("insert meta with error")
        }

        val (_, resource) = requireMatchApRELabelWithResource(
            unit = workloadMetadataConstraint.unit,
            site = workloadMetadataConstraint.site,
            stage = workloadMetadataConstraint.stage,
            appName = workloadMetadataConstraint.appName,
            clusterId = workloadMetadataConstraint.clusterId
        )

        apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(
            assembleLabelCascadeSpecCreateDto(
                resourcePoolKey = checkNotNull(resource.resourcePoolKey) { "missing resource pool key in registryRuntimeWorkload" },
                workloadMetadataConstraint = workloadMetadataConstraint,
                annotations = annotations,
                labels = labels,
                workloadVisitable = workloadVisitable
            )
        )

        log.info("register new runtime workload with workloadMetadataConstraint${workloadMetadataConstraint}, annotations:${annotations}, labels:${labels}, creator:${creator}")
    }

    /**
     * 查找所有相关的runtime Workload
     *
     * @param appName
     * @param envStackId
     * @param resourceGroup
     * @return
     */
    fun listRunningWorkloadList(
        appName: String, envStackId: String? = null, resourceGroup: String? = null
    ): List<RuntimeWorkloadMeta> {
        require(!(resourceGroup.isNullOrBlank() && envStackId.isNullOrBlank())) {
            "listRunningWorkloadList must be using envStackId or resourceGroup to explore runtime workload list, blank equals null"
        }
        val notNullResourceGroupList = if (!resourceGroup.isNullOrBlank() && envStackId == null) {
            listOf(resourceGroup)
        } else {
            val resourceGroups =
                skylineApi.getEnvStackResourceGroupScopeByStackId(checkNotNull(envStackId) { "missing envStackId" })
            if (resourceGroup.isNullOrBlank()) {
                resourceGroups
            } else {
                resourceGroups.filter { it == resourceGroup }
            }
        }
        if (notNullResourceGroupList.isEmpty()) {
            return emptyList()
        }
        return runtimeWorkloadMetaRepo.findByAppNameAndAppGroupList(
            appName = appName,
            resourceGroupList = notNullResourceGroupList
        )
    }

    fun listRunningWorkloadListByApp(
        appName: String
    ): List<RuntimeWorkloadMeta> {
        return runtimeWorkloadMetaRepo.listByAppName(
            appName = appName
        )
    }

    fun findRuntimeWorkloadMeta(runtimeWorkloadId: String): RuntimeWorkloadMeta? {
        require(runtimeWorkloadId.isNotBlank()) { "runtimeWorkloadId cannot be blank" }
        return runtimeWorkloadMetaRepo.findByRuntimeWorkloadId(runtimeWorkloadId)
    }

    fun listEnvStackIdByRuntimeWorkloadId(runtimeWorkloadIdList: List<String>): List<RuntimeRefEnvStackRespDto> {
        val runtimeRefEnvStackRespDtoList = mutableListOf<RuntimeRefEnvStackRespDto>()
        runtimeWorkloadIdList.distinct().forEach { runtimeWorkloadId ->
            findRuntimeWorkloadMeta(runtimeWorkloadId)?.let { runtimeWorkloadMeta ->
                runtimeRefEnvStackRespDtoList.firstOrNull {
                    it.resourceGroup == runtimeWorkloadMeta.resourceGroup
                }?.let {
                    //已经基于分组查询过的环境，直接添加记录
                    runtimeRefEnvStackRespDtoList.add(
                        RuntimeRefEnvStackRespDto(
                            runtimeWorkloadId = runtimeWorkloadId,
                            resourceGroup = it.resourceGroup,
                            envStackId = it.envStackId
                        )
                    )
                } ?: let {
                    skylineApi.listBindingEnvStackIdByResourceGroup(resourceGroup = runtimeWorkloadMeta.resourceGroup)
                        .forEach { envStackId ->
                            runtimeRefEnvStackRespDtoList.add(
                                RuntimeRefEnvStackRespDto(
                                    runtimeWorkloadId = runtimeWorkloadId,
                                    resourceGroup = runtimeWorkloadMeta.resourceGroup,
                                    envStackId = envStackId
                                )
                            )
                        }
                }
            }
        }
        return runtimeRefEnvStackRespDtoList;
    }

    /**
     * 销毁已经注册的workload
     *
     * @param workloadMetadataConstraint
     */
    @Transactional
    fun cancelRunningRuntimeWorkload(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        modifier: String
    ) {
        workloadMetadataConstraint.firmValidate4BaseApp()
        // 存在existedRuntimeWorkload说明存在过对应的ApRELabel曾经记录过
        val existedRuntimeWorkload =
            runtimeWorkloadMetaRepo.findByRuntimeWorkloadId(workloadMetadataConstraint.runtimeId!!) ?: let {
                throw RuntimeWorkloadMetaException("runtime workload meta not found with $workloadMetadataConstraint")
            }

        if (runtimeWorkloadMetaRepo.deleteById(
                id = requireNotNull(existedRuntimeWorkload.id) { "missing runtimeWorkload.id" },
                modifier = modifier
            ) != 1
        ) {
            throw RuntimeWorkloadMetaException("delete runtime workload meta with error")
        }

        val (matchLabel, _) = requireMatchApRELabelWithResource(
            unit = workloadMetadataConstraint.unit,
            stage = workloadMetadataConstraint.stage,
            site = workloadMetadataConstraint.site,
            clusterId = workloadMetadataConstraint.clusterId,
            appName = workloadMetadataConstraint.appName
        )
        checkNotNull(matchLabel) { "match label resource not found" }

        matchLabel.apREFeatureSpecs?.firstOrNull { matchSpec(it, workloadMetadataConstraint.runtimeId) }?.let {
            apREFeatureSpecService.deleteByLabelKeyAndSpecCode(
                checkNotNull(it.apRELabelKey) { "missing label key" },
                checkNotNull(it.specCode) { "missing spec code" })
        }

        log.info("cancel runtime workload with workloadMetadataConstraint${workloadMetadataConstraint}, modifier:${modifier}")
    }

    fun matchLabel(apRELabel: ApRELabelDO, appName: String) =
        (apRELabel.type == SERVERLESS) && (apRELabel.value == SERVERLESS_PREFIX + appName)

    fun matchSpec(apREFeatureSpecDO: ApREFeatureSpecDO, runtimeId: String) =
        apREFeatureSpecDO.sourceId == runtimeId

    /**
     * 组装label & spec dto
     *
     * @param resourcePoolKey
     * @param workloadMetadataConstraint
     * @param annotations
     * @param labels
     * @return
     */
    private fun assembleLabelCascadeSpecCreateDto(
        resourcePoolKey: String,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        annotations: Map<String, String>,
        labels: Map<String, String>,
        workloadVisitable: Boolean,
    ): ApRELabelCreateReqDto {
        return ApRELabelCreateReqDto(
            targetKey = resourcePoolKey,
            targetType = RESOURCE_POOL.name,
            type = SERVERLESS,
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            value = ApREService.RUNTIME_TEMPLATE_PREFIX + workloadMetadataConstraint.appName,
            apREFeatureSpecList = listOf(
                assembleSpecCreateDto(workloadMetadataConstraint, annotations, labels, null, workloadVisitable)
            )
        )
    }

    /**
     * 组装spce CreateDto
     *
     * @param workloadMetadataConstraint
     * @param annotations
     * @param labels
     * @param apRELabelKey
     * @return
     */
    private fun assembleSpecCreateDto(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        annotations: Map<String, String>,
        labels: Map<String, String>,
        apRELabelKey: String? = null,
        workloadVisitable: Boolean,
    ): ApREFeatureSpecCreateReqDto {
        val runtimeId = requireNotNull(workloadMetadataConstraint.runtimeId) { "runtime id cannot be blank" }
        return ApREFeatureSpecCreateReqDto(
            apRELabelKey = apRELabelKey,
            title = "",
            specCode = runtimeId,
            specType = buildRuntimeSpecType(
                runtimeBaseApp = workloadMetadataConstraint.appName,
                annotations = annotations
            ),
            scope = publish,
            status = if (workloadVisitable) ApREFeatureSpecStatusEnum.online else ApREFeatureSpecStatusEnum.offline,
            sourceId = workloadMetadataConstraint.runtimeId,
            sourceType = DEFAULT_RUNTIME_SOURCE_TYPE,
            annotations = annotations,
            labels = labels,
        )
    }

    private fun assembleRuntimeWorkload(
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        creator: String
    ): RuntimeWorkloadMeta {
        return RuntimeWorkloadMeta(
            appName = workloadMetadataConstraint.appName,
            resourceGroup = workloadMetadataConstraint.resourceGroup,
            runtimeWorkloadId = checkNotNull(workloadMetadataConstraint.runtimeId) { "missing runtimeId" },
            site = workloadMetadataConstraint.site,
            unit = workloadMetadataConstraint.unit,
            stage = workloadMetadataConstraint.stage,
            clusterId = workloadMetadataConstraint.clusterId,
            status = RuntimeState.INSTALLED.name,
            creator = creator,
            modifier = creator,
            isDeleted = "N",
            nameSpace = checkNotNull(workloadMetadataConstraint.namespace) { "missing namespace" }
        )
    }

    /**
     * 检查是否存在指定的标签
     *
     * @param unit
     * @param site
     * @param stage
     * @param clusterId
     * @param appName
     * @return
     */
    fun requireMatchApRELabelWithResource(
        unit: String, site: String, stage: String, clusterId: String, appName: String
    ): Pair<ApRELabelDO?, ResourcePoolDO> {
        val matchResource = apREService.listResourcePoolByMetadata(
            unit = unit,
            site = site,
            stage = stage,
            clusterId = clusterId
        ).apply {
            if (this.size > 1) {
                throw RuntimeWorkloadMetaException("runtime workload meta data must have only one matched resource pool!")
            }
        }.firstOrNull() ?.let {
            resourcePoolService.findResourcePoolDetailWithAllLabelSpecByKey(it.resourcePoolKey)
        } ?:let {
            throw RuntimeWorkloadMetaException("runtime workload meta data have no matched resource pool!")
        }
        return Pair(matchResource.apRELabels.firstOrNull { matchLabel(it, appName) }, matchResource)
    }

    private fun buildRuntimeSpecType(runtimeBaseApp: String, annotations: Map<String, String>): String {
        return apREService.generateServerlessRuntimeTemplateName(runtimeBaseApp, null,
            annotations["cpu"] ?: let {
                throw ApRELabelSpecException("ApRELabelSpec注释信息必须包含cpu规格.")
            },
            annotations["memory"] ?: let {
                throw ApRELabelSpecException("ApRELabelSpec注释信息必须包含memory规格.")
            }
        )
    }

    companion object {
        const val DEFAULT_RUNTIME_SOURCE_TYPE: String = "AONE"
    }
}

enum class RuntimeState {
    UNINSTALLED,
    INSTALLED
}