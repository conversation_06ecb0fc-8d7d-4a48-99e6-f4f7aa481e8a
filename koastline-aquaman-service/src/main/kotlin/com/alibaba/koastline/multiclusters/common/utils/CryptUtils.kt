package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.koastline.multiclusters.common.logger
import java.security.MessageDigest
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * <AUTHOR>
 */

object CryptUtils {

    val log by logger()

    fun md5Encrypt(data: String): String {
        val md5Object = MessageDigest.getInstance("MD5")
        val dataBytes = data.toByteArray()
        md5Object.update(dataBytes)
        val encryptedData = md5Object.digest()
        return encryptedData.joinToString("") { "%02x".format(it) }
    }

    fun encrypt(password: String, input: String):String {
        //create aes instance
        val cipher = Cipher.getInstance("AES")
        val keySpec = SecretKeySpec(password.toByteArray(), "AES")

        try {
            cipher.init(Cipher.ENCRYPT_MODE, keySpec)
            // encrypt
            val encrypt = cipher.doFinal(input.toByteArray())
            return Base64.getEncoder().encodeToString(encrypt)
        } catch (e: Exception) {
            throw e
        }
    }

    fun decrypt(password: String, input: String):String {
        //create aes instance
        val cipher = Cipher.getInstance("AES")
        val keySpec = SecretKeySpec(password.toByteArray(), "AES")
        cipher.init(Cipher.DECRYPT_MODE, keySpec)

        // decrypt
        val decrypt:ByteArray
        try {
            decrypt = cipher.doFinal(Base64.getDecoder().decode(input))
        }catch (e: Exception){
            log.info(e.message)
            return ""
        }
        return String(decrypt)
    }
}