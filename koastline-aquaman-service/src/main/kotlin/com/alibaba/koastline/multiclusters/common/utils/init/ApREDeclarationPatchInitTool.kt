package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import okhttp3.OkHttpClient
import java.io.File

object ApREDeclarationPatchInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()
    fun init() {
        val userName = "admin"
        val secret = "secret1111"
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("init/apre_declaration_patch_init_data.txt")!!.file
        val lines: List<String> = File(file).readLines()
        val objectMapper = ObjectMapper()
        val unitSiteMap = mutableMapOf<String, MutableList<String>> ()
        val stage = "PUBLISH"

        lines.forEach { line ->
            line.split("\t").let {
                val site = it[0].lowercase()
                val unit = MetadataUtils.formatUnit(it[1])!!
                println("$site,$unit")
                unitSiteMap[unit] ?.let {
                    it.add(site)
                } ?:let {
                    unitSiteMap[unit] = mutableListOf(site)
                }
            }
        }
        println("unitSiteMap:${unitSiteMap}")
        unitSiteMap.forEach { (unit, sites) ->
            HttpClientUtils.httpGet(
                "http://localhost:7001/apis/apre/v4/ApREDeclarationPatch/listWithMatchScopeByCondition",
                mapOf(
                    "unit" to unit,
                    "stage" to stage,
                ),
                AuthorizationAttribute(
                    userName,
                    secret
                )
            ).let {
                val rs = objectMapper.readValue<Map<String, Any>>(it)
                if ((rs["data"] as List<Any>).isNotEmpty()) {
                    println("$unit-${stage}已存在，rs:$rs")
                    return@forEach
                }
            }
            val result = HttpClientUtils.httpPost(
                "http://localhost:7001/apis/apre/v4/ApREDeclarationPatch/createWithMatchScope",
                objectMapper.writeValueAsString(
                    ApREDeclarationPatchDataDO(
                        balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                        stage = stage,
                        unit = unit,
                        creator = "system",
                        modifier = "system",
                        declarationPatch = DeclarationPatch(
                            sites.map {
                                PatchItem(site = it)
                            }
                        ),
                        matchScopeDataDOs = listOf(
                            MatchScopeDataDO(
                                externalId = "alibaba",
                                externalType = "AONE_PRODUCTLINE",
                                creator = "admin",
                                modifier = "admin"
                            )
                        )
                    )
                ),
                AuthorizationAttribute(
                    userName,
                    secret
                )
            )
            println("$unit-${stage} done，rs: $result")
        }
        println("finish init data.")
    }
}

fun main(args: Array<String>) {
    ApREDeclarationPatchInitTool.init()
}