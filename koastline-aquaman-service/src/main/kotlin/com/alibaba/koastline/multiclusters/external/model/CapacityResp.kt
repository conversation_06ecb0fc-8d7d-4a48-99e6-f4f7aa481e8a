package com.alibaba.koastline.multiclusters.external.model

data class CapacityResp<T>(
    /**
     * 是否执行成功(不可作为是否调用成功标示)
     */
    var success: Boolean,
    /**
     * 错误信息
     */
    val msg: String? = null,
    /**
     * 返回值内容
     */
    val data: T? = null,
    /**
     * 错误码
     */
    val code: String? = null
)

data class VpaCurrentSpec (
    /**
     * 应用名
     */
    val appName: String,
    /**
     * 分组名
     */
    val appGroup: String?,
    /**
     * 站点
     */
    val site: String?,
    /**
     * 单元标(无 CENTER_UNIT. 前缀)
     */
    val unit: String?,
    /**
     * 用途标
     */
    val env: String?,
    /**
     * vpa基线spec
     */
    val spec: VpaBaselineSpec
)

data class VpaBaselineSpec (
    /**
     * millicpu, 可能不是整核
     */
    val cpu: Int
)
