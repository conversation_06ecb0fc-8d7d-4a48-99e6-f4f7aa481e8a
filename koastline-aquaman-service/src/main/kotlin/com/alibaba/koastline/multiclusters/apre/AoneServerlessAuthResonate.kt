package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.apre.attorney.ExtraApREBindingDataService
import com.alibaba.koastline.multiclusters.apre.model.RuntimeProperties
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.event.consumer.severlessauth.AdmissionChangeRequest
import com.alibaba.koastline.multiclusters.event.consumer.severlessauth.AdmissionTargetType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional


@Component
class AoneServerlessAuthResonate @Autowired constructor(
    val extraApREBindingDataService: ExtraApREBindingDataService
) {
    val log by logger()

    @Transactional
    fun resonateAoneServerlessAuth(admissionChangeRequest: AdmissionChangeRequest) {
        val serverlessBaseApp = admissionChangeRequest.runtimeAppName
        admissionChangeRequest.add.forEach { addAdmission ->
            extraApREBindingDataService.createServerlessAttorneyWhileExisted2Update(
                serverlessBaseApp = ApRELabelExt.serverlessNameFormat(serverlessBaseApp),
                externalType = AdmissionTargetType.valueOf(addAdmission.targetType).map2MatchScopeTargetType(),
                creator = AUTH_CREATOR,
                properties = RuntimeProperties(),
                externalId = addAdmission.getMatchScopeTargetId()
            )
        }
        admissionChangeRequest.del.forEach { deleteAdmission ->
            extraApREBindingDataService.deleteServerlessAttorney(
                serverlessBaseApp = ApRELabelExt.serverlessNameFormat(serverlessBaseApp),
                externalType = AdmissionTargetType.valueOf(deleteAdmission.targetType).map2MatchScopeTargetType(),
                externalId = deleteAdmission.getMatchScopeTargetId(),
                modifier = AUTH_CREATOR
            )
        }
        log.info("change new serverless base app auth with admissionChangeRequest:${admissionChangeRequest}")
    }

    companion object {
        const val AUTH_CREATOR = "appCenter"
    }
}