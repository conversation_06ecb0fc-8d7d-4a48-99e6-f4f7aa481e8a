package com.alibaba.koastline.multiclusters.resourceobj.model.req

import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType.AONE_PRODUCTLINE
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType.APPLICATION
import com.alibaba.koastline.multiclusters.resourceobj.params.toUserLabelType
import io.swagger.annotations.ApiModelProperty

data class UserLabelCreateReqDto(
    @ApiModelProperty("范围ID",required = true)
    val externalId: String,
    @ApiModelProperty("范围类型",required = true)
    val externalType: UserLabelExternalType,
    @ApiModelProperty("标签",required = true)
    val labelName: String,
    @ApiModelProperty("标签值",required = true)
    val labelValue: String?,
    @ApiModelProperty("创建人工号",required = true)
    val creator: String
) {
    fun validate(): UserLabelCreateReqDto {
        require(externalId.isNotBlank()) { "externalId must be non-blank" }
        require(labelName.isNotBlank()) { "labelName must be non-blank" }
        if (userLabelTypes.contains(externalType)) {
            require(toUserLabelType(labelName) != null) { "labelName invalid" }
        }
        require(creator.isNotBlank()) { "creator must be non-blank" }
        return this
    }

    private val userLabelTypes = listOf(
        AONE_PRODUCTLINE,
        APPLICATION,
    )
}