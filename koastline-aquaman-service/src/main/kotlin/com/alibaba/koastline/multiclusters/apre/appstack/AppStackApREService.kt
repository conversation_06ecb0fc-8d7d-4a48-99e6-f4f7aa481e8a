package com.alibaba.koastline.multiclusters.apre.appstack

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.*
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.base.MetadataService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.*
import com.alibaba.koastline.multiclusters.common.exceptions.*
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.AvailableZoneSiteMappingRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * <EMAIL>
 * TODO 兼容AppStack功能，待融合完删除
 */
@Component
class AppStackApREService {
    val log by logger()
    @Autowired
    lateinit var apREService: ApREService
    @Autowired
    lateinit var clusterService: DefaultClusterService
    @Autowired
    lateinit var apREDeedService: ApREDeedService
    @Autowired
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo
    @Autowired
    lateinit var availableZoneSiteMappingRepo: AvailableZoneSiteMappingRepo
    @Autowired
    lateinit var metadataService: MetadataService
    @Autowired
    lateinit var apRELabelService: ApRELabelService
    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    /**
     * 根据运行时环境声明诉求契约Key查找符合的集群资源
     * @param apREDeedKey 运行时环境声明诉求契约KEY
     * @return
     */
    fun queryClustersByApREDeedKey(apREDeedKey: String): ApREDeedResult {
        val apREDeed = apREDeedService.findApREDeedByKey(apREDeedKey)
        apREDeed.declarations!![0].let {
            val region = it.region ?.toNullIfBlank()
                ?.run { it.region }
                ?:run {
                    it.az ?.run {
                        metadataService.getMetadataOfSite(this) ?.run { this.region }
                }
            }
            if (region.isNullOrBlank() ||
                    it.stage.isNullOrBlank() ||
                    it.unit.isNullOrBlank() ||
                    it.az.isNullOrBlank()) {
                throw ApREDeedException("AppStack发布模式下，四元组不允许为空,{region:${region},stage:${it.stage},unit:${it.unit},az:${it.az}}")
            }
            val apre = appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region, it.az, it.stage, it.unit).run {
                if (this.isEmpty()) {
                    throw ApRENotFoundException()
                }
                apREService.findCombinedApREBaseDetailByKey(this.minBy {apRE ->
                    apRE.id!!
                }.runtimeEnvKey, null)!!
            } .apply {
                val resources = mutableListOf<ResourceDO>()
                clusterService.queryManagedClusterByManagedClusterKey(this.managedClusterKey) ?.let { clusterProfile ->
                    availableZoneSiteMappingRepo.queryAvailableZoneSiteMappingByAvailableZoneAndRegion(this.az,this.region) ?.let { availableZoneSiteMappingData ->
                        clusterProfile.clusterMetaData.clusterAnnotations["site"] = availableZoneSiteMappingData.site
                    }
                    resources.add(
                        ResourceDO(
                            clusterProfile
                        )
                    )
                }
                this.resources = resources
            }

            return ApREDeedResult(
                deedDO = apREDeed,
                matchDeclarations = listOf(
                    MatchDeclaration(
                        apres = listOf(apre)
                    )
                )
            )
        }
    }

    /**
     * 注册运行时环境特性规格
     */
    @Transactional
    fun registryFeatureSpec(registryFeatureDO: RegistryFeatureDO) {
        val appRuntimeEnvironmentData = registryFeatureDO.runtimeEnvKey ?.let { runtimeEnvKey ->
            appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey) ?: throw ApRENotFoundException()
        } ?:let {
            appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(registryFeatureDO.region!!,
                registryFeatureDO.az!!, registryFeatureDO.stage!!, registryFeatureDO.unit!!).run {
                if (this.isEmpty())
                    throw ApRENotFoundException()
                else {
                    this.minBy {apRE -> apRE.id!! }
                }
            }
        }
        if (registryFeatureDO.apRELabels.isNullOrEmpty()) {
            throw ApREParamsException("apRELabels is null or empty.")
        }
        val resourcePool = findResourcePool(appRuntimeEnvironmentData.managedClusterKey, appRuntimeEnvironmentData.runtimeEnvKey)
        //基于集群进行绑定
        registryFeatureDO.apRELabels ?.forEach { apRELabel ->
            if (apRELabel.apREFeatureSpecs.isNullOrEmpty()) {
                throw ApREParamsException("the apRELabel(name = ${apRELabel.name},value = ${apRELabel.value})'apREFeatureSpecs is null or empty.")
            }
            apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(
                ApRELabelCreateReqDto(
                    name = apRELabel.name,
                    value = apRELabel.value,
                    targetKey = resourcePool.resourcePoolKey,
                    apREFeatureSpecList = apRELabel.apREFeatureSpecs.map { apREFeatureSpec ->
                        val annotations = apREFeatureSpec.annotations ?.run {
                            JsonUtils.readValue(this, mutableMapOf<String, String>().javaClass)
                        } ?: emptyMap()
                        ApREFeatureSpecCreateReqDto(
                            title = apREFeatureSpec.title ?: "",
                            specCode = apREFeatureSpec.specCode ?: "",
                            specType = apREService.generateServerlessRuntimeTemplateName(apRELabel.value, null,
                                annotations["cpu"] ?:let {
                                    throw ApRELabelSpecException("ApRELabelSpec注释信息必须包含cpu规格.")
                                },
                                annotations["memory"] ?:let {
                                    throw ApRELabelSpecException("ApRELabelSpec注释信息必须包含memory规格.")
                                }
                            ),
                            scope = ApREFeatureSpecScopeEnum.valueOf(checkScope(apREFeatureSpec.scope)),
                            status = ApREFeatureSpecStatusEnum.valueOf(checkStatus(apREFeatureSpec.status)),
                            sourceType = apREFeatureSpec.sourceType,
                            sourceId = apREFeatureSpec.sourceId,
                            versionType = apREFeatureSpec.versionType,
                            versionId = apREFeatureSpec.versionId,
                            annotations = annotations
                        )
                    },
                    targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                    type = SERVERLESS
                )
            )
        }
    }

    /**
     * 更新运行时环境特性规格状态
     */
    fun modifyApREFeatureSpecStatus(modifyFeatureSpecStatusDO: ModifyFeatureSpecStatusDO) {
        if (modifyFeatureSpecStatusDO.apRELabels.isNullOrEmpty()) {
            return
        }
        val appRuntimeEnvironmentData = modifyFeatureSpecStatusDO.runtimeEnvKey ?.run {
            appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(modifyFeatureSpecStatusDO.runtimeEnvKey!!) ?: throw ApRENotFoundException()
        } ?:run {
            appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(modifyFeatureSpecStatusDO.region!!,
                modifyFeatureSpecStatusDO.az!!, modifyFeatureSpecStatusDO.stage!!, modifyFeatureSpecStatusDO.unit!!) .run {
                if (this.isEmpty()) throw ApRENotFoundException()
                else {
                    this.minBy {apRE -> apRE.id!! }
                }
            }
        }
        val resourcePool = findResourcePool(appRuntimeEnvironmentData.managedClusterKey, appRuntimeEnvironmentData.runtimeEnvKey)
        modifyFeatureSpecStatusDO.apRELabels!!.forEach {

            try {
                apRELabelService.modifyApREFeatureSpecStatus(
                    it.copy(
                        targetKey = resourcePool.resourcePoolKey,
                        targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL
                    )
                )
            } catch (e: ApRELabelSpecNotFoundException) {
                log.warn("modifyApREFeatureSpecStatus兼容旧版Runtime状态更新，modifyFeatureSpecStatusDO：${modifyFeatureSpecStatusDO}")
                apRELabelService.modifyApREFeatureSpecStatus(it.copy(targetKey = appRuntimeEnvironmentData.runtimeEnvKey, targetType = ApRELabelTargetTypeEnum.APRE))
            } catch (e: ApRELabelNotFoundException) {
                log.warn("modifyApREFeatureSpecStatus兼容旧版Runtime状态更新，modifyFeatureSpecStatusDO：${modifyFeatureSpecStatusDO}")
                apRELabelService.modifyApREFeatureSpecStatus(it.copy(targetKey = appRuntimeEnvironmentData.runtimeEnvKey, targetType = ApRELabelTargetTypeEnum.APRE))
            }
        }
    }



    /**
     * 基于ApRE AppStack链路默认绑定的集群，查找相对应的ApRE资源池
     */
    private fun findResourcePool(managedClusterKey: String, runtimeEnvKey: String): ResourcePoolDataDO {
        //找到ApRE AppStack路径绑定的唯一集群
        val clusterProfile = clusterService.queryManagedClusterByManagedClusterKey(managedClusterKey) ?:let {
            throw ApREException("未找到ApRE[key:${runtimeEnvKey}]默认绑定集群.")
        }
        return  resourcePoolService.listByManagedClusterKey(managedClusterKey).firstOrNull {
            it.clusterId == clusterProfile.clusterId
        } ?:let {
            throw ApREException("ApRE[key:${runtimeEnvKey},managedClusterKey:${managedClusterKey}]未绑定资源池集群[clusterId:${clusterProfile.clusterId}],请联系资源PE进行绑定.")
        }
    }
}