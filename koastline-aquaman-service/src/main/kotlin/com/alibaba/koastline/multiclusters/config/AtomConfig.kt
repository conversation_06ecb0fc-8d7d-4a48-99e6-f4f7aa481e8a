package com.alibaba.koastline.multiclusters.config

import com.alibaba.atomcore.facade.factory.ApiConfig
import com.alibaba.atomcore.facade.factory.ApiFactoryBean
import com.alibaba.atomcore.facade.gamma.AstroLabelApi
import com.alibaba.atomcore.facade.gamma.label.SigmaConfigMapApi
import com.alibaba.atomcore.facade.gamma.strategy.StrategyApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class AtomConfig {
    @Value("\${atom.host}")
    lateinit var atomHost: String
    @Value("\${atom.account}")
    lateinit var atomAccount: String
    @Value("\${atom.access.key}")
    lateinit var atomAccessKey: String
    @Bean
    fun apiConfigBean(): ApiConfig {
        val apiConfig = ApiConfig()
        apiConfig.serverAddress = atomHost
        apiConfig.accessId = atomAccount
        apiConfig.accessKey = atomAccessKey
        return apiConfig
    }

    @Bean
    fun strategyApiBean(apiConfig: ApiConfig): StrategyApi {
        return ApiFactoryBean.getInstance(StrategyApi::class.java, apiConfig)
    }

    @Bean
    fun astroLabelApiBean(apiConfig: ApiConfig): AstroLabelApi {
        return ApiFactoryBean.getInstance(AstroLabelApi::class.java, apiConfig)
    }

    @Bean
    fun sigmaConfigMapApiBean(apiConfig: ApiConfig): SigmaConfigMapApi {
        return ApiFactoryBean.getInstance(SigmaConfigMapApi::class.java, apiConfig)
    }
}