package com.alibaba.koastline.multiclusters.event

import com.alibaba.koastline.multiclusters.event.consumer.app.AoneAppMessageListener
import com.alibaba.koastline.multiclusters.event.consumer.app.SkylineMessageListener
import com.alibaba.koastline.multiclusters.event.consumer.severlessauth.ServerlessBaseAppAuthMessageListener
import com.taobao.metaq.client.MetaPushConsumer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


@Configuration
class MetaConsumerConfig(
    private val aoneAppMessageListener: AoneAppMessageListener,
    private val skylineMessageListener: SkylineMessageListener,
    private val serverlessBaseAppAuthMessageListener: ServerlessBaseAppAuthMessageListener
) {

    @Bean(name = ["aoneAppMetaqConsumer"])
    fun aoneAppMetaqConsumer(): MetaPushConsumer {
        val consumer = MetaPushConsumer(AQUAMAN_EVENT_CONSUMER_GROUP)
        consumer.subscribe(AONE_APPCENTER_APP_TOPIC, AONE_APPCENTER_APP_TOPIC_ADD)
        consumer.consumeMessageBatchMaxSize = 3
        consumer.registerMessageListener(aoneAppMessageListener)
        consumer.start()
        return consumer
    }

    @Bean(name = ["skylineMetaqConsumer"])
    fun skylineMetaqConsumer(): MetaPushConsumer {
        val consumer = MetaPushConsumer(AQUAMAN_SKYLINE_ITEM_OPERATE_CONSUMER_GROUP)
        consumer.subscribe(SKYLINE_ITEM_OPERATE_TOPIC, SKYLINE_ITEM_OPERATE_TOPIC_SUBEXPRESSION_APP_GROUP)
        consumer.consumeMessageBatchMaxSize = 3
        consumer.registerMessageListener(skylineMessageListener)
        consumer.start()
        return consumer
    }

    @Bean(name = ["aoneServerlessAppAuthMetaqConsumer"])
    fun aoneServerlessAppAuth():MetaPushConsumer {
        val consumer = MetaPushConsumer(AQUAMAN_APPCENTER_SERVERLESS_AUTH_CONSUMER_GROUP)
        consumer.subscribe(AONE_APPCENTER_SERVERLESS_AUTH_TOPIC, AONE_APPCENTER_SERVERLESS_AUTH_TOPIC_TAG_MERGE)
        consumer.consumeMessageBatchMaxSize = 3
        consumer.registerMessageListener(serverlessBaseAppAuthMessageListener)
        consumer.start()
        return consumer
    }

    companion object {
        private const val AONE_APPCENTER_APP_TOPIC = "AONE_APPCENTER_APP_NOTIFY"
        private const val AONE_APPCENTER_APP_TOPIC_ADD = "add"
        private const val AQUAMAN_EVENT_CONSUMER_GROUP = "CID_AQUAMAN_EVENT_CONSUMER_GROUP"

        private const val SKYLINE_ITEM_OPERATE_TOPIC = "skyline_item_operate"
        private const val SKYLINE_ITEM_OPERATE_TOPIC_SUBEXPRESSION_APP_GROUP = "app_group"
        private const val AQUAMAN_SKYLINE_ITEM_OPERATE_CONSUMER_GROUP = "CID_AQUAMAN_SKYLINE_ITEM_OPERATE_CONSUMER_GROUP"

        private const val AONE_APPCENTER_SERVERLESS_AUTH_TOPIC = "AONE_SERVERLESS_RUNTIME_GRANT_NOTIFY"
        private const val AONE_APPCENTER_SERVERLESS_AUTH_TOPIC_TAG_MERGE = "merge"
        private const val AQUAMAN_APPCENTER_SERVERLESS_AUTH_CONSUMER_GROUP = "CID_AQUAMAN_SERVERLESS_AUTH_EVENT_CONSUMER_GROUP"
    }
}