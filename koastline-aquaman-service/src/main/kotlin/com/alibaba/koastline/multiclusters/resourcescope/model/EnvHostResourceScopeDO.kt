package com.alibaba.koastline.multiclusters.resourcescope.model

import java.time.Instant
import java.util.Date

/**
 * @author:    <EMAIL>
 * @date:    2025/3/7 3:48 PM
 */
data class EnvHostResourceScopeDO(
    val id: Long,
    val appName: String,
    val currentEnvStackId: String,
    val baseEnvStackId: String,
    val resourceScope: HostResourceScope,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

data class EnvHostResourceScopeCreateDto(
    val appName: String,
    val currentEnvStackId: String,
    val baseEnvStackId: String,
    val resourceScope: HostResourceScope,
    val creator: String,
)

data class HostResourceScope(
    val target: HostResourceScopeTarget,
    val appGroupScopes: List<AppGroupScope>,
)

data class AppGroupScope(
    val appGroupName: String,
    val restrictions: List<AppGroupScopeRestriction>
)

data class AppGroupScopeRestriction(
    val unit: String,
    val site: String? = null,
)

enum class HostResourceScopeTarget {
    /**
     * 基于环境
     */
    ENV,

    /**
     * 基于分组
     */
    APP_GROUP
}