package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.ProtocolUpgradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectProtocolNotFoundException
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.io.File

@Component
class FeatureProtocolLoadService {

    @Autowired
    lateinit var commonProperties: CommonProperties

    @Autowired
    lateinit var protocolUpgradeProperties: ProtocolUpgradeProperties

    @Autowired
    lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var matchScopeService: MatchScopeService


    val cache = mutableMapOf<String, String>()
    fun getFeatureProtocol(
        protocol: ResourceObjectFeatureProtocol,
        workloadMetadataConstraint: WorkloadMetadataConstraint? = null
    ): String {
        if (!commonProperties.contains(
                IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                protocol.resourceObjectFeatureKey
            )
        ) {
            return protocol.patch
        }
        val protocolMeta = setProtocolUpgradeGray(
            ProtocolMeta(
                protocol = protocol,
            ), workloadMetadataConstraint
        )
        if (cache.containsKey(protocolMeta.buildProtocolCacheKey())) {
            return cache.get(protocolMeta.buildProtocolCacheKey())!!
        }
        val input = FeatureProtocolLoadService.javaClass.getClassLoader()
            .getResourceAsStream(protocolMeta.buildProtocolFilePath())
        if (input != null) {
            val content = input.bufferedReader().readText()
            cache.set(protocolMeta.buildProtocolCacheKey(), content)
            return content
        } else {
            throw ResourceObjectProtocolNotFoundException("${protocol.resourceObjectFeatureKey},${protocol.version},${protocol.protocol}协议文件不存在")
        }
    }

    fun setProtocolUpgradeGray(
        protocolMeta: ProtocolMeta,
        workloadMetadataConstraint: WorkloadMetadataConstraint?
    ): ProtocolMeta {
        val betaInput = FeatureProtocolLoadService.javaClass.getClassLoader()
            .getResourceAsStream(protocolMeta.buildBetaProtocolFilePath())
        if (betaInput == null) {
            return protocolMeta.copy(upgradeGray = false)
        }
        return protocolMeta.copy(
            upgradeGray = whetherInGray(workloadMetadataConstraint)
        )

    }

    fun whetherInGray(workloadMetadataConstraint: WorkloadMetadataConstraint?): Boolean {
        if (workloadMetadataConstraint == null) {
            return false
        }
        if (whetherInGrayByProductline(workloadMetadataConstraint)) {
            return true
        }
        return protocolUpgradeProperties.whetherAppStageInGray(
            workloadMetadataConstraint.appName,
            workloadMetadataConstraint.stage
        )

    }

    fun whetherInGrayByProductline(workloadMetadataConstraint: WorkloadMetadataConstraint): Boolean {
        val appInfo = appCenterApi.getAppInfoByName(workloadMetadataConstraint.appName)
        return matchScopeService.buildProductLineTransformedIds(appInfo.buId, appInfo.productFullLineIdPath)
            .firstOrNull {
                protocolUpgradeProperties.whetherProductLineStageInGray(it, workloadMetadataConstraint.stage)
            } != null
    }

    companion object {
        const val IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST = "IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST"
        const val BETA_FILE_SUFFIX = "-beta"
    }

}

data class ProtocolMeta(
    val protocol: ResourceObjectFeatureProtocol,
    val upgradeGray: Boolean = false,
) {
    fun buildProtocolCacheKey(): String {
        if (upgradeGray) {
            return buildBetaProtocolCacheKey()
        }
        return buildBaseProtocolCacheKey()
    }

    fun buildProtocolFilePath(): String {
        if (upgradeGray) {
            return buildBetaProtocolFilePath()
        }
        return buildBaseProtocolFilePath()
    }

    fun buildBaseProtocolCacheKey(): String {
        return "${protocol.resourceObjectFeatureKey}/${protocol.version}/${protocol.protocol}"
    }

    fun buildBaseProtocolFilePath(): String {
        return "featureProtocol/${buildBaseProtocolCacheKey()}.yaml"
    }

    fun buildBetaProtocolCacheKey(): String {
        return "${buildBaseProtocolCacheKey()}${FeatureProtocolLoadService.BETA_FILE_SUFFIX}"
    }

    fun buildBetaProtocolFilePath(): String {
        return "featureProtocol/${buildBetaProtocolCacheKey()}.yaml"
    }
}