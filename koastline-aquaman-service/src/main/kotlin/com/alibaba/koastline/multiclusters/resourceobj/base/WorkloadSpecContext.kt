package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint

data class WorkloadSpecContext(
    /**
     * 七元组约束
     */
    val workloadMetadataConstraint: WorkloadMetadataConstraint,
    /**
     * 环境StackId
     */
    val envStackId: String,
    /**
     * 资源对象协议
     */
    val resourceObjectProtocolEnum: ResourceObjectProtocolEnum,
)