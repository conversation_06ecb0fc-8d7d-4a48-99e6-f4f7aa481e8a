package com.alibaba.koastline.multiclusters.schedule.service.schedule.extra

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.serverlessNameFormat
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_SPEC
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_DELIMITER
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeWorkloadMetaException
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeWorkloadMetaMigrationMatchException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RuntimeScheduleService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service


@Service
class ServerlessRunningScheduleService @Autowired constructor(
    val scheduleStandardService: ScheduleStandardService,
    val runtimeWorkloadMetaService: RuntimeWorkloadMetaService,
    val skylineApi: SkylineApi,
    val apREService: ApREService
) {
    private val log by logger()

    /**
     * TODO: 计算workload前后需要轮转的7元组
     *
     */
    fun seekServerlessAppMigrationBaseApp(
        originalWorkloadMetadataConstraint: WorkloadMetadataConstraint,
        migrationToBaseApp: String,
        migrationToEnvStackId: String
    ): WorkloadMetadataConstraint {

        log.info("seekServerlessAppMigrationBaseApp: originalWorkloadMetadataConstraint:$originalWorkloadMetadataConstraint, migrationToBaseApp:$migrationToBaseApp, migrationToEnvStackId:$migrationToEnvStackId")

        val originalRuntimeId =
            requireNotNull(originalWorkloadMetadataConstraint.runtimeId) { "original runtime id not found" }

        val originalRuntimeWorkload = runtimeWorkloadMetaService.findRuntimeWorkloadMeta(originalRuntimeId)?.also {
            if (!it.matchMeRuntimeMeta(originalWorkloadMetadataConstraint, it.runtimeWorkloadId)) {
                throw RuntimeWorkloadMetaException("runtime meta not match with workload:$originalWorkloadMetadataConstraint, runtimeMeta:$it]")
            }
        } ?: let {
            // 当前为appstack基座
            return originalWorkloadMetadataConstraint
        }

        val availableRuntimes = seekAvailableRuntimes(
            originalWorkloadMetadataConstraint,
            originalRuntimeWorkload,
            migrationToBaseApp,
            migrationToEnvStackId
        )

        if (availableRuntimes.isEmpty()) {
            throw RuntimeWorkloadMetaMigrationMatchException("本次发布涉及基座轮转, 原始基座Workload:${originalWorkloadMetadataConstraint}，在新基座Workload:${migrationToBaseApp}以及环境:${migrationToEnvStackId}，未找到可用的Runtime,请联系基座管理员处理。")
        }
        if (availableRuntimes.contains(originalRuntimeId)) {
            return originalWorkloadMetadataConstraint
        }

        val migrationToRuntime = availableRuntimes.first()

        return originalWorkloadMetadataConstraint.copy(
            runtimeId = migrationToRuntime
        )
    }

    fun seekAvailableRuntimes(
        originalWorkloadMetadataConstraint: WorkloadMetadataConstraint,
        originalRuntimeWorkload: RuntimeWorkloadMeta,
        migrationToBaseApp: String,
        migrationToEnvStackId: String
    ): List<String> {
        val originalResourceSpecCode = scheduleStandardService.getServerlessRuntimeTemplate(
            serverlessBaseAppName = originalRuntimeWorkload.appName,
            appName = originalWorkloadMetadataConstraint.appName,
            resourceGroup = originalWorkloadMetadataConstraint.resourceGroup
        )
        val specCode = splitServerlessResourceSpec(originalResourceSpecCode)

        val matchRuntimes = runtimeWorkloadMetaService.listRunningWorkloadList(
            appName = migrationToBaseApp,
            envStackId = migrationToEnvStackId
        ).filter { runtimeWorkloadMeta ->
            runtimeWorkloadMeta.unit == originalWorkloadMetadataConstraint.unit
                    && runtimeWorkloadMeta.stage == originalWorkloadMetadataConstraint.stage
                    && runtimeWorkloadMeta.site == originalWorkloadMetadataConstraint.site
                    && runtimeWorkloadMeta.clusterId == originalWorkloadMetadataConstraint.clusterId
        }

        if (matchRuntimes.map { it.runtimeWorkloadId }.contains(originalWorkloadMetadataConstraint.runtimeId)) {
            return listOf(originalWorkloadMetadataConstraint.runtimeId!!)
        }

        val migrationToClusterId = originalWorkloadMetadataConstraint.clusterId
        val migrationRuntimeIds = mutableListOf<String>()

        val matchResources = apREService.listApREDetailsBySiteAndUnitAndStage(
            unit = originalWorkloadMetadataConstraint.unit,
            site = originalWorkloadMetadataConstraint.site,
            stage = originalWorkloadMetadataConstraint.stage
        ).flatMap { it.resources }.filter { it.clusterId == migrationToClusterId }

        for (runtimeGroup in matchRuntimes) {
            matchResources.flatMap { it.apRELabels }.filter {
                it.matchServerlessLabelProperties(runtimeGroup.appName)
            }.flatMap { it.apREFeatureSpecs ?: emptyList() }.forEach { apREFeatureSpec ->
                val resourceSpecCode = splitServerlessResourceSpec(apREFeatureSpec.specType ?: "")
                if (runtimeGroup.runtimeWorkloadId == apREFeatureSpec.specCode && resourceSpecCode != null && specCode == resourceSpecCode && apREFeatureSpec.status == RuntimeScheduleService.RUNTIME_STATUS_ONLINE) {
                    migrationRuntimeIds.add(checkNotNull(runtimeGroup.runtimeWorkloadId) { "spec type for runtime id not found" })
                }
            }
        }

        return migrationRuntimeIds
    }

    private fun ApRELabelDO.matchServerlessLabelProperties(baseAppName: String): Boolean {
        return this.type == SERVERLESS && this.name == APRE_LABEL_FEATURE_NAME && this.value == serverlessNameFormat(
            baseAppName
        )
    }

    /**
     * 获取
     *
     * @param specCode
     * @return
     */
    private fun splitServerlessResourceSpec(specCode: String): String? {
        val resourceSpec = specCode.split(RUNTIME_TEMPLATE_DELIMITER).lastOrNull()
        return if (resourceSpec?.contains(RUNTIME_SPEC) == false) {
            return null
        } else {
            resourceSpec
        }
    }

    private fun RuntimeWorkloadMeta.matchMeRuntimeMeta(
        constraintMeta: WorkloadMetadataConstraint,
        runtimeWorkloadIdRecord: String
    ): Boolean {
        return this.site == constraintMeta.site
                && this.stage == constraintMeta.stage
                && this.unit == constraintMeta.unit
                && this.runtimeWorkloadId == runtimeWorkloadIdRecord
    }
}