package com.alibaba.koastline.multiclusters.apre.params

enum class MatchScopeTargetTypeEnum {
    ApREDeclarationPatchData,
    ApREBindingData,
    ResourceObjectFeatureImport,
    ResourceObjectFeature,
    AppID,
    ExtraApREBindingData,
    FedTarget,
    FedPolicy,
}

inline fun checkMatchScopeTargetType(targetType: String): String {
    return MatchScopeTargetTypeEnum.valueOf(targetType).name
}