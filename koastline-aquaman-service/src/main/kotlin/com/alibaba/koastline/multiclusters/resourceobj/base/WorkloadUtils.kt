package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.common.exceptions.StatefulSetException
import java.util.*
import java.util.regex.Pattern

object WorkloadUtils {
    private const val NAMESPACE_REGEX = "[^a-zA-Z0-9]"
    fun buildNamespace(appName: String): String {
        val pattern = Pattern.compile(NAMESPACE_REGEX)
        val matcher = pattern.matcher(appName)
        val rs = matcher.replaceAll("-")
        if (appName.length != rs.length) {
            throw StatefulSetException("build namespace error")
        }
        return rs.lowercase(Locale.getDefault())
    }
}