package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.cse.models.carbonv1.rollingset.RollingSet
import com.alibaba.cse.models.carbonv1.rollingset.RollingSetSpec
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ASI_MIX_CLUSTER_NAME_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.INPLACESET_BLACK_LABEL_LIST
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.NORMANDY
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.AbstractWorkloadSpecService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.google.gson.Gson
import io.kubernetes.client.openapi.models.*
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component("RollingSetSpecService")
class RollingSetSpecService : AbstractWorkloadSpecService(),InitializingBean {

    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var workloadSpecFactory: WorkloadSpecFactory
    @Autowired
    lateinit var baseSpecService: BaseSpecService
    @Autowired
    lateinit var commonProperties: CommonProperties
    val gson = Gson()

    override fun afterPropertiesSet() {
        workloadSpecFactory.registryWorkloadSpecFacade(ResourceObjectProtocolEnum.RollingSet, this)
    }

    override fun checkSpec(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum) {
        when(resourceObjectFormatEnum) {
            ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                JsonUtils.writeValueAsString(YamlUtils.load(resourceObjectSpecStr)), RollingSet::class.java)
            else -> JsonUtils.gsonReadValue(resourceObjectSpecStr, RollingSet::class.java)
        }
    }

    override fun getBaseSpec(context: WorkloadSpecContext): Map<String, Any> {
        return cloudCmdbApi.getEnvBaselineSpec(context.envStackId, ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val rollingSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(YamlUtils.load(this)), RollingSet::class.java)
            fillAttributesToRollingSet(rollingSet, context)
            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(rollingSet))
        }
    }

    override fun postModifyBaseSpec(
        resourceObjectSpec: Map<String, Any>,
        context: WorkloadSpecContext
    ): Map<String, Any> {
        val rollingSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(resourceObjectSpec), RollingSet::class.java)
        baseSpecService.patchAffinityAndTolerations(rollingSet.spec.template)
        modifyForMixDeployCluster(rollingSet.spec.template, context.workloadMetadataConstraint)
        return YamlUtils.load(gson.toJson(rollingSet))
    }

    override fun getDeploySpec(stackPkId: String, currentResourceObjectSpecStr: String?,
                               currentResourceObjectFormatEnum: ResourceObjectFormatEnum, workloadMetadataConstraint: WorkloadMetadataConstraint): Map<String, Any> {
        checkNotNull(currentResourceObjectSpecStr)
        return cloudCmdbApi.getEnvBaselineSpecByStackPKId(stackPkId, ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME).run {
            val baseRollingSet = JsonUtils.gsonReadValue(JsonUtils.writeValueAsString(YamlUtils.load(this)), RollingSet::class.java)
            val currentRollingSet = when(currentResourceObjectFormatEnum) {
                ResourceObjectFormatEnum.YAML -> JsonUtils.gsonReadValue(
                    JsonUtils.writeValueAsString(YamlUtils.load(currentResourceObjectSpecStr)), RollingSet::class.java)
                else -> JsonUtils.gsonReadValue(currentResourceObjectSpecStr, RollingSet::class.java)
            }
            //对象转Yaml,转一层过滤无效字段
            YamlUtils.load(gson.toJson(
                buildDeploySpec(baseRollingSet, currentRollingSet)
            ))
        }
    }

    private fun buildDeploySpec(baseRollingSet: RollingSet, currentRollingSet: RollingSet): RollingSet {
        baseRollingSet.metadata.name = currentRollingSet.metadata.name
        baseRollingSet.metadata.namespace = currentRollingSet.metadata.namespace
        baseRollingSet.metadata.annotations = baseRollingSet.metadata.annotations ?.filter {
            BaseSpecService.INPLACESET_WHITE_WORKLOAD_ANNO_LIST.contains(it.key)
        }
        baseRollingSet.metadata.labels = currentRollingSet.metadata.labels?.filter {
            BaseSpecService.INPLACESET_WHITE_WORKLOAD_LABEL_LIST.contains(it.key)
        }.orEmpty() + baseRollingSet.metadata!!.labels?.filter {
            BaseSpecService.INPLACESET_WHITE_BASE_WORKLOAD_LABEL_LIST.contains(it.key)
        }.orEmpty()
        baseRollingSet.spec.template.metadata ?.let { metadata ->
            metadata.labels = metadata.labels ?.filter {
                !INPLACESET_BLACK_LABEL_LIST.contains(it.key)
            }
        }
        return baseRollingSet
    }


    private fun fillAttributesToRollingSet(rollingSet: RollingSet, context: WorkloadSpecContext) {
        setLastAppliedConfiguration(rollingSet, getLastAppliedConfiguration(rollingSet))
        setBasicInfo(rollingSet, context.workloadMetadataConstraint.appName, context)
        setRollingSetMetadata(rollingSet, context)
        baseSpecService.setPodMetadata(rollingSet.spec.template, context.workloadMetadataConstraint)
        baseSpecService.setDnsPolicy(rollingSet.spec.template.spec!!)
        baseSpecService.setDefaultAffinity(rollingSet.spec.template.spec!!, context.workloadMetadataConstraint.clusterId)
        baseSpecService.setIsolationEnvVars(rollingSet.spec.template.spec!!.containers, context.envStackId, context.workloadMetadataConstraint.stage)
    }

    /**
     * 填充基础信息
     */
    private fun setBasicInfo(rollingSet: RollingSet, appName: String, context: WorkloadSpecContext) {
        (rollingSet.metadata) .let { metadata ->
            metadata.name = baseSpecService.buildWorkloadName(appName)
            metadata.namespace = context.workloadMetadataConstraint.namespace ?: WorkloadUtils.buildNamespace(appName)
        }
    }


    /**
     * 设置workload meta data
     */
    private fun setRollingSetMetadata(rollingSet: RollingSet, context: WorkloadSpecContext) {
        (rollingSet.metadata).let { metadata ->
            //add labels
            (metadata.labels ?: mutableMapOf<String, String>().apply { metadata.labels = this }).let { labels->
                // 六元组覆盖
                labels.putAll(baseSpecService.getDefaultLabels(context.workloadMetadataConstraint))
                labels[BaseSpecService.INPLACESET_LABEL_STACKID] = labels[BaseSpecService.INPLACESET_LABEL_STACKID] ?: context.envStackId
                labels[BaseSpecService.POD_UPSTREAM_COMPONENT] = labels[BaseSpecService.POD_UPSTREAM_COMPONENT] ?: kotlin.run { NORMANDY }
            }
            //add annotations
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[BaseSpecService.DISABLE_CASCADING_DELETION] = annotations[BaseSpecService.DISABLE_CASCADING_DELETION] ?: "true"
            }
        }
    }




    private fun setLastAppliedConfiguration(rollingSet: RollingSet, lastAppliedConfiguration: String) {
        (rollingSet.metadata).let { metadata ->
            (metadata.annotations ?: mutableMapOf<String, String>().apply { metadata.annotations = this }).let { annotations ->
                annotations[BaseSpecService.LAST_APPLIED_CONFIGURATION] = lastAppliedConfiguration
            }
        }
    }

    private fun getLastAppliedConfiguration(rollingSet: RollingSet): String {
        val lastAppliedConfigurationStr = gson.toJson(RollingSet(
            // TODO 动态值或者状态值不保留，只保留静态的
            spec = RollingSetSpec(
                template = rollingSet.spec.template,
                paused = null,
                healthCheckerConfig = rollingSet.spec.healthCheckerConfig,
                signature = null,
                rollingStrategy = null,
                strategy = null,
                subrsBlackList = null,
                subrsLatestVersionRatio = null,
                subrsRatioVersion = null,
                version = null,
                selector = null,
            ),
            metadata = V1ObjectMeta()
        )
        )
        val lastAppliedConfigurationSts = gson.fromJson(lastAppliedConfigurationStr, RollingSet::class.java).apply {
            this.spec.template.metadata?.labels?.let { labels ->
                BaseSpecService.TEMPLATE_LABEL_BLACK_LIST.forEach {
                    labels.remove(it)
                }
            }
        }
        return gson.toJson(lastAppliedConfigurationSts)
    }


    /**
     * 离在线混部分时扩容场景,更新Label & Annotation
     */
    private fun modifyForMixDeployCluster(podTemplateSpec: V1PodTemplateSpec, workloadMetadataConstraint: WorkloadMetadataConstraint) {
        if (!commonProperties.contains(ASI_MIX_CLUSTER_NAME_LIST_CONFIG, workloadMetadataConstraint.clusterId)) {
            return
        }
        //添加annotation
        podTemplateSpec.metadata!!.annotations!!.let {
            it[BaseSpecService.SKIP_KUBELET_ADMISSION] = "[\"cpu\",\"memory\",\"alibabacloud.com/acu\"]"
        }
        //添加label
        podTemplateSpec.metadata!!.labels!!.let {
            it[BaseSpecService.TIMESHARING_PROMOTION_TYPE] = "trade"
        }
    }


    companion object {
        private const val ROLLINGSET_API_VERSION = "carbon.taobao.com/v1"
        private const val ROLLINGSET_KIND_NAME = "RollingSet"


        const val ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME = "rollingSet"
    }
}