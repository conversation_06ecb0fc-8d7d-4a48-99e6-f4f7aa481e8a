package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.attorney.ApREAttorneyService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.attorney.ExtraApREBindingDataService
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import com.alibaba.koastline.multiclusters.apre.base.MetadataService
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.formatUnit
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.matchDeclarationMetadata
import com.alibaba.koastline.multiclusters.apre.common.ApREDefaultFeatureService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.GroupAuthorizedDeclarationsDto
import com.alibaba.koastline.multiclusters.apre.model.DeclaredLocation
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchApREFeatureSpec
import com.alibaba.koastline.multiclusters.apre.model.MatchApRELabel
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.MixApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREBaseDetailsUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRECreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ResourcePoolCreateOrUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSourceEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.getStage
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelSpecException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREMergeBindingException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREMultipleDiscernException
import com.alibaba.koastline.multiclusters.common.exceptions.ApRENotFoundException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREUniqueExistMetadataConstraintException
import com.alibaba.koastline.multiclusters.common.exceptions.ManagedClusterNotExistException
import com.alibaba.koastline.multiclusters.common.exceptions.MetadataException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.common.utils.TraceUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.config.RegionPropertiesConfig
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ClusterEnvironmentConfigRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ClusterEnvironmentRepo
import com.alibaba.koastline.multiclusters.data.dao.env.EnvLevelStageMappingDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.KManagedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataOfSiteRepo
import com.alibaba.koastline.multiclusters.data.utils.DataValidateUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.DEFAULT
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingData
import com.alibaba.koastline.multiclusters.external.HcrmApi
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*


/**
 * ApRE和及其基础服务
 * 其中ApRE概念:
 * 1.BaseApRE -> 指的是裸露的ApRE 只有最基础的ApRE属性 不附加其关联属性
 * 2.ApREBaseDetails -> 指的是ApRE及其描述ApRE本身的描述资源
 * 3.ApREDetails&ApREFullDetails -> 指的是ApRE所有数据（包括关联绑定的资源）
 * 不同返回资源的查询时间不同和代价不同&函数命名请按照相关概念进行命名
 *
 */
@Component
class ApREService(
    val objectMapper: ObjectMapper,
) {
    val log by logger()

    @Autowired
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo

    @Autowired
    lateinit var kManagedClusterRepo: KManagedClusterRepo

    @Autowired
    lateinit var apRELabelService: ApRELabelService

    @Autowired
    lateinit var clusterEnvironmentRepo: ClusterEnvironmentRepo

    @Autowired
    lateinit var regionPropertiesConfig: RegionPropertiesConfig

    @Autowired
    lateinit var clusterEnvironmentConfigRepo: ClusterEnvironmentConfigRepo

    @Autowired
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService

    @Autowired
    lateinit var apREBindingService: ApREBindingService

    @Autowired
    lateinit var apREDeedService: ApREDeedService

    @Autowired
    lateinit var envLevelStageMappingDataRepo: EnvLevelStageMappingDataRepo

    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    @Autowired
    lateinit var metadataService: MetadataService

    @Autowired
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService

    @Autowired
    lateinit var apREResourceGroupBindingService: ApREResourceGroupBindingService

    @Autowired
    lateinit var defaultClusterService: DefaultClusterService

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var apRELabelDefinitionService: ApRELabelDefineService

    @Autowired
    lateinit var apREFeatureSpecService: ApREFeatureSpecService

    @Autowired
    lateinit var apREDefaultFeatureService: ApREDefaultFeatureService

    @Autowired
    lateinit var metadataOfSiteRepo: MetadataOfSiteRepo

    @Autowired
    lateinit var attorneyService: ApREAttorneyService

    @Autowired
    lateinit var extraApREBindingDataService: ExtraApREBindingDataService

    @Autowired
    lateinit var envLevelStageMappingService: EnvLevelStageMappingService

    @Autowired
    lateinit var hcrmApi: HcrmApi

    /**
     * 根据运行时环境声明诉求契约Key查找符合的集群资源
     * @param apREDeedKey 运行时环境声明诉求契约KEY
     * @return
     */
    fun queryClustersByApREDeedKey(apREDeedKey: String): ApREDeedResult {
        return queryClustersByApREDeedContent(apREDeedService.findApREDeedByKey(apREDeedKey))
    }

    fun queryClustersByApREDeedContent(apREDeedDO: ApREDeedDO): ApREDeedResult {
        val result = requireApREDeedResultByApREDeed(apREDeedDO)
        //填充集群信息
        result.matchDeclarations.forEach { matchDeclaration ->
            matchDeclaration.apres.forEach { apRE ->
                //只获取在线集群
                val resourceList = resourcePoolService.listOnlineResourceDOByManagedClusterKey(apRE.managedClusterKey)
                apRE.resources = filterResourcePoolByMatchDeclaration(matchDeclaration.declaration, apRE, resourceList)
                val identityInfo = result.deedDO.identityInfo!!
                identityInfo.nodeGroup?.run {
                    //查看是否有通过分组指定集群,可以通过分组绑定方式绑定自测集群
                    apREResourceGroupBindingService.getApREResourceGroupBindingDataByCondition(
                        checkNotNull(identityInfo.appName) {"identityInfo.appName cannot be null in queryClustersByApREDeedContent!" },
                        this,
                        apRE.runtimeEnvKey!!
                    )?.apply {
                        apRE.resources = getFilterResourceByApREBindingTerm(
                            resources = apRE.resources,
                            apREBindingTerm = this.selector
                        )
                    }
                } ?: let {
                    apRE.resources = apRE.resources.filter {
                        // 默认只返回生产集群
                        if (apRE.limitedClusterIdList.isEmpty()) {
                            it.clusterProfileNew != null && it.clusterProfileNew.useType == ClusterProfileUseTypeEnum.publish.name
                        } else {
                            apRE.limitedClusterIdList.contains(it.clusterProfileNew!!.clusterId)
                        }
                    }
                }
            }
        }
        log.info("queryClustersByApREDeedContent,apREDeedDO:{},result:{}", JsonUtils.writeValueAsString(apREDeedDO), JsonUtils.writeValueAsString(result))
        return result
    }

    /**
     * 获取可以开放的resourceList列表
     */
    fun getFilterResourceByApREBindingTerm(
        resources: List<ResourceDO>,
        apREBindingTerm: ApREBindingTerm
    ): List<ResourceDO> {
        return resources.filter {
            it.clusterProfileNew != null && isAuthorizedAccessCluster(it, apREBindingTerm)
        }
    }

    /**
     * 是否是授权的集群resource
     */
    fun isAuthorizedAccessCluster(resource: ResourceDO, apREBindingTerm: ApREBindingTerm): Boolean {
        val isIncluded = apREBindingTerm.required!!.clusters?.contains(resource.clusterProfileNew?.clusterId) ?: false
        val isAllSupported =
            apREBindingTerm.required.allClustersSupported == true || apREBindingTerm.allSupported == true
        return isAllSupported || isIncluded
    }

    /**
     * 基于匹配特性标签过滤符合的资源池
     */
    private fun filterResourcePoolByMatchDeclaration(
        declaration: Declaration?,
        apREDO: ApREDO,
        resourceList: List<ResourceDO>
    ): List<ResourceDO> {
        if (null == declaration || resourceList.isEmpty() || declaration.matchApRELabels.isNullOrEmpty()) {
            return resourceList
        }
        val filterResources = mutableMapOf<String, ResourceDO>()
        apREDO.apRELabels!!.forEach { apRELabel ->
            if (apRELabel.targetType == ApRELabelTargetTypeEnum.APRE) {
                //匹配到ApRE层级特性，代表ApRE下资源全部满足
                return resourceList
            }
            resourcePoolService.getResourcePoolByKey(apRELabel.targetKey!!)?.let { resourcePoolData ->
                resourceList.firstOrNull { resourceDO ->
                    resourceDO.clusterProfileNew!!.clusterId == resourcePoolData.clusterId
                }?.let { resourceDO ->
                    filterResources[resourcePoolData.resourcePoolKey] ?: let {
                        filterResources[resourcePoolData.resourcePoolKey] = resourceDO
                    }
                }
            }
        }
        return filterResources.values.toList()
    }

    /**
     * 返回声明需求的运行时环境,同时将声明需求保存生成唯一KEY返回
     * @param apREDeedDO 契约信息
     * @return 契约结果
     */
    fun requireApREDeedResultByApREDeedContent(apREDeedDO: ApREDeedDO): ApREDeedResult {
        apREDeedDO.identityInfo?.validate()
        val isPersistApREDeed = apREDeedDO.identityInfo?.appName != null
        // 查找以及生成契约
        val apREDeedDO = if (isPersistApREDeed) {
            apREDeedService.createApREDeedWhileNotExist(apREDeedDO)
        } else {
            apREDeedService.checkAndMakeupApREDeed(apREDeedDO)
            apREDeedDO
        }
        return requireApREDeedResultByApREDeed(apREDeedDO)
    }

    /**
     * 根据ApRE契约KEY
     * 返回声明需求的运行时环境
     * @param apREDeedKey 契约信息
     * @return 契约结果
     */
    fun requireApREDeedResultByApREDeedKey(apREDeedKey: String): ApREDeedResult {
        // 查找契约
        val apREDeedDO = apREDeedService.findApREDeedByKey(apREDeedKey)
        return requireApREDeedResultByApREDeed(apREDeedDO)
    }

    fun requireOnlyDeclarationOfApREDeedResultByApREDeedKey(apREDeedKey: String): ApREDeedResult {
        // 查找契约
        val apREDeedDO = apREDeedService.findApREDeedByKey(apREDeedKey)
        return ApREDeedResult(
            deedDO = apREDeedDO,
            matchDeclarations = listOf(),
            apREDeclarationPatchDatas = apREDeclarationPatchService.querySiteBalanceApREDeclarationPatchList(apREDeedDO)
        )
    }

    /**
     * 根据契约声明获取符合的ApRE
     * @param apREDeedDO 声明
     * @return 声明资源
     */
    fun requireApREDeedResultByApREDeed(apREDeedDO: ApREDeedDO): ApREDeedResult {
        val stages = listStageByEnvLevel(apREDeedDO.identityInfo!!.envLevel)
        if (stages.isEmpty()) {
            return ApREDeedResult(apREDeedDO, listOf(), listOf())
        }
        if (stages.size == 1) {
            //补充下资源用途声明
            apREDeedDO.declarations = apREDeedDO.declarations!!.map {
                it.copy(stage = it.stage?.toNullIfBlank() ?: stages[0])
            }.toMutableList()
        }
        // 依据契约身份信息查找当前符合的ApRE
        val apREBindingDataList = apREBindingService.getApREBindingDataByIdentityInfo(apREDeedDO.identityInfo)
        if (apREBindingDataList.isEmpty()) {
            return ApREDeedResult(apREDeedDO, listOf(), listOf())
        }
        // 匹配符合契约声明的运行时环境详情
        val matchDeclarations = mutableListOf<MatchDeclaration>()
        apREDeedDO.declarations!!.forEach { declaration ->
            val apres = mutableMapOf<String, ApREDO>()
            // 针对用途&四元组声明做批量过滤
            val availableRuntimeEnvKeyList = apREBindingDataList.map { it.runtimeEnvKey }.distinct().run {
                if (this.isEmpty()) {
                    emptyList<Long>()
                } else {
                    appRuntimeEnvironmentDataRepo.findByRuntimeEnvKeyList(this).filter { apRE ->
                        stages.contains(apRE.stage)
                                && matchDeclarationMetadata(declaration.region, apRE.region)
                                && matchDeclarationMetadata(declaration.az, apRE.az)
                                && matchDeclarationMetadata(declaration.stage, apRE.stage)
                                && matchDeclarationMetadata(declaration.unit, apRE.unit)
                    }.map { it.runtimeEnvKey }
                }
            }

            apREBindingDataList.forEach { apREBindingData ->
                if (availableRuntimeEnvKeyList.contains(apREBindingData.runtimeEnvKey)) {
                    //针对同一ApRE的多重授权取权限并集
                    mergeBindingApRE(
                        sourceApRE = apres[apREBindingData.runtimeEnvKey],
                        targetApREBinding = apREBindingData,
                        declaration = declaration,
                        exemptFilterTypeList = getIgnoreApRELabelTypeList()
                    )?.let {
                        apres[apREBindingData.runtimeEnvKey] = it
                    }
                }
            }

            matchDeclarations.add(
                MatchDeclaration(
                    declaration.id!!,
                    apres.values.toList(),
                    declaration.weight,
                    declaration
                )
            )
        }

        // 对 extraApREBindingData 进行额外过滤
        val extraScopeAttorneyMap = extraApREBindingDataService.getScopeAttorneyByIdentityInfo(
            identityInfo = apREDeedDO.identityInfo
        )
        val filterMatchDeclarations = maskScopeAttorney(extraScopeAttorneyMap, matchDeclarations)
        return ApREDeedResult(
            apREDeedDO,
            filterMatchDeclarations,
            apREDeclarationPatchService.querySiteBalanceApREDeclarationPatchList(apREDeedDO)
                .map { apREDeclarationPatchDataDO ->
                    makeUpRegionToApREDeclarationPatch(apREDeclarationPatchDataDO)
                }
        )
    }


    /**
     * 补充region信息
     */
    private fun makeUpRegionToApREDeclarationPatch(apREDeclarationPatchDataDO: ApREDeclarationPatchDataDO): ApREDeclarationPatchDataDO {
        return apREDeclarationPatchDataDO.copy(
            declarationPatch = apREDeclarationPatchDataDO.declarationPatch.copy(
                patchItems = apREDeclarationPatchDataDO.declarationPatch.patchItems.map { patchItem ->
                    if (patchItem.region.isNullOrBlank() && !patchItem.site.isNullOrBlank()) {
                        patchItem.copy(region = metadataOfSiteRepo.findBySite(patchItem.site)?.region)
                    } else {
                        patchItem
                    }
                }
            )
        )
    }

    /**
     * ApREBindingData 无需过滤的标签列表 这些类型的标签将会在 ExtraApREBindingData 中进行范围授权过滤
     */
    fun getIgnoreApRELabelTypeList(): List<ApRELabelType> {
        return listOf(
            SERVERLESS
        )
    }

    /**
     * 对特性开放资源进行更细粒度的资源授权过滤
     * 例如 serverless 标签在 mergeBindingApRE -> matchApRELabels 中对 value 为 serverless 为前缀的属性进行全部开放
     * 但只有被授权serverless 基座应用的才能使用授权部分 serverless 特性 1.过滤开放标签 2.过滤featureSpec
     * 前者为特性授权 后者为特性范围授权
     */
    fun maskScopeAttorney(
        extraScopeAttorneyMap: Map<ExtraApREBindingData, MatchScopeDataDO>,
        matchDeclarations: MutableList<MatchDeclaration>
    ): MutableList<MatchDeclaration> {
        val newMatchDeclarations = mutableListOf<MatchDeclaration>()
        val extraScopeAttorneyMapByType =
            extraScopeAttorneyMap.map { it.key }.groupBy { it.type.name }
        matchDeclarations.forEach { matchDeclaration ->
            val newApREs = mutableListOf<ApREDO>()
            matchDeclaration.apres.forEach { apRE ->
                val originLabels = apRE.apRELabels
                val newApRE = apRE.copy(
                    apRELabels = extraApREBindingDataService.featureByScopeAttorneySelector(
                        originApRELabels = originLabels ?: emptyList(),
                        extraScopeAttorneyMapByType = extraScopeAttorneyMapByType,
                        masksStrategyMap = extraApREBindingDataService.getMaskStrategy(),
                        minLabelMaskMap = extraApREBindingDataService.getMinLabelMask()
                    )
                )
                // 当没有 ApRELabel 时候，说明没有特性被筛选到 因此过滤此 ApRE
                if (!newApRE.apRELabels.isNullOrEmpty()) {
                    newApREs.add(newApRE)
                }
            }
            newMatchDeclarations.add(
                matchDeclaration.copy(
                    apres = newApREs
                )
            )
        }
        return newMatchDeclarations
    }

    /**
     * 根据应用和分组查询声明和授权信息，包含 hcrm 中的声明
     * 无声明时， 返回的 {@link GroupAuthorizedDeclarationsDto#declaredLocations} 为空列表
     * @param appName
     * @param resourceGroup
     * @param envLevel
     * @param serverlessBaseAppName null 表示普通应用, 非 null 表示 serverless 应用
     * @param serverlessRuntimeTemplate
     * @return 分组声明与授权信息
     */
    fun requireGroupAuthorizedDeclarations(appName: String, resourceGroup: String,
                                           envLevel: String, serverlessBaseAppName: String? = null,
                                           serverlessRuntimeTemplate: String? = null): GroupAuthorizedDeclarationsDto {
        val apREDeedDO = requireApREDeedDOByResourceGroup(resourceGroup) ?: ApREDeedDO(
            identityInfo = IdentityInfo(
                envLevel = envLevel,
                appName = appName,
                nodeGroup = resourceGroup
            ),
            declarations = mutableListOf()
        )
        val hrmDeclarations = hcrmApi.listUnitizationUnitLocation(appName, resourceGroup).map {
            Declaration(az = it.site, unit = it.unit)
        }
        val mergedDeclarations = distinctAddServerlessLabel(((apREDeedDO.declarations ?: listOf()) + hrmDeclarations),
            serverlessBaseAppName, serverlessRuntimeTemplate)
        if (mergedDeclarations.isEmpty()) {
            // 两边都没有声明
            return GroupAuthorizedDeclarationsDto(resourceGroup = resourceGroup, declaredLocations = listOf())
        }

        val mergedDeedDO = apREDeedDO.copy(declarations = mergedDeclarations.toMutableList())
        val matchDeclarations = requireApREDeedResultByApREDeed(mergedDeedDO).matchDeclarations
        val declaredLocations = matchDeclarations
            .filter {
                 it.declaration?.unit != null && it.declaration.az != null
            }
            .map {
                DeclaredLocation(unit = it.declaration!!.unit!!, az = it.declaration.az!!, authorized = it.apres.isNotEmpty())
            }
        return GroupAuthorizedDeclarationsDto(resourceGroup = resourceGroup, declaredLocations = declaredLocations)
    }

    private fun distinctAddServerlessLabel(declarations: List<Declaration>, serverlessBaseAppName: String?,
                                                serverlessRuntimeTemplate: String?) =
        declarations
            .distinctBy { it.unit + "_" + it.az }
            .map {
                if (serverlessBaseAppName != null) {
                    it.copy(matchApRELabels = listOf(
                        MatchApRELabel(
                            name = APRE_LABEL_FEATURE_NAME,
                            value = RUNTIME_TEMPLATE_PREFIX + serverlessBaseAppName,
                            matchApREFeatureSpecs = listOf(
                                MatchApREFeatureSpec(
                                    specType = serverlessRuntimeTemplate
                                )
                            )
                        )
                    ))
                } else {
                    it
                }
            }

    /**
     * 针对固定声明/临时声明分组获取最终决策后的ApRE(附加SRE切面后的最终决策)
     * @param mixApREDeedDOList 混合声明列表
     * @return 声明资源
     */
    fun requireApREDeedResultByMixApREDeedBatch(mixApREDeedDOList: List<MixApREDeedDO>): List<ApREDeedResult> {
        if (mixApREDeedDOList.isEmpty()) {
            return emptyList()
        }
        // 并发处理
        val apREDeedResultList = mutableListOf<ApREDeedResult>()
        runBlocking {
            val jobs = mutableListOf<Job>()
            mixApREDeedDOList.forEach { mixApREDeedDO ->
                val job = launch(Dispatchers.Default) {
                    val apREDeedResult = requireApREDeedResultByMixApREDeed(mixApREDeedDO)
                    synchronized(apREDeedResultList) {
                        apREDeedResultList.add(apREDeedResult)
                    }
                }
                jobs.add(job)
            }
            jobs.joinAll()
        }
        return apREDeedResultList
    }

    /**
     * 根据分组获得 apre, 并且补充均衡策略和 region 信息
     */
    fun requireApREDeedDOByResourceGroupWithRegion(resourceGroup: String): ApREDeedDO? {
        return requireApREDeedDOByResourceGroup(resourceGroup)?.let { deed ->
            deed.declarations = deed.declarations?.map {
                if (it.region.isNullOrEmpty() && !it.az.isNullOrEmpty()) {
                    it.copy(region = metadataOfSiteRepo.findBySite(it.az)?.region)
                } else {
                    it
                }
            }?.toMutableList()
            deed
        }
    }

    fun requireApREDeedDOByResourceGroup(resourceGroup: String): ApREDeedDO? {
        return apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup)?.run {
            apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(
                apREDeedService.findApREDeedByKey(this.apREDeedKey).run {
                    //兼容identityInfo分组名缺失问题
                    if (this.identityInfo != null) {
                        this.copy(identityInfo = this.identityInfo.copy(nodeGroup = resourceGroup))
                    } else this
                }
            )
        }
    }

    fun requireApREDeedResultByMixApREDeed(mixApREDeedDO: MixApREDeedDO): ApREDeedResult {
        val apREDeedDO =
            requireApREDeedDOByResourceGroup(mixApREDeedDO.identityInfo.nodeGroup) ?:
            run {
                ApREDeedDO(
                    identityInfo = IdentityInfo(
                        envLevel = mixApREDeedDO.identityInfo.envLevel,
                        appName = mixApREDeedDO.identityInfo.appName,
                        nodeGroup = mixApREDeedDO.identityInfo.nodeGroup
                    ),
                    declarations = mixApREDeedDO.declarations.run {
                        if (mixApREDeedDO.declarations.isEmpty()) {
                            mutableListOf(Declaration())
                        } else {
                            mixApREDeedDO.declarations
                        }
                    }
                )
            }
        return requireApREDeedResultByApREDeed(apREDeedDO)
    }

    /**
     * 合并针对相同ApRE的不同授权范围,上述情况下返回对授权范围做合并结果
     * @param sourceApRE 输入的源ApRE
     * @param targetApREBinding 授权过滤使用的ApREBindingData
     * @param declaration 切分的Declaration
     * @param exemptFilterTypeList 免过滤的标签列表
     */
    private fun mergeBindingApRE(
        sourceApRE: ApREDO?,
        targetApREBinding: ApREBindingData,
        declaration: Declaration,
        exemptFilterTypeList: List<ApRELabelType> = emptyList()
    ): ApREDO? {
        sourceApRE?.let {
            if (sourceApRE.runtimeEnvKey != targetApREBinding.runtimeEnvKey) {
                throw ApREMergeBindingException("it's different runtime key.")
            }
        }
        val targetApRE = findCombinedApREBaseDetailByKey(
            targetApREBinding.runtimeEnvKey,
            targetApREBinding.apREBindingTerm,
            exemptFilterTypeList
        )?.run {
            if (this.apRELabels.isNullOrEmpty()) {
                return sourceApRE
            }
            val (isMatched, matchLabels) = matchApRELabels(declaration.matchApRELabels, this.apRELabels)
            if (!isMatched) {
                //特性不匹配 直接返回原计算结果 不做添加 不做修改
                return sourceApRE
            }
            this.copy(
                apRELabels = matchLabels,
                limitedClusterIdList = getLimitedClusterIdList(targetApREBinding.apREBindingTerm)
            )
        } ?: let {
            return sourceApRE
        }

        if (sourceApRE == null || sourceApRE.apRELabels.isNullOrEmpty()) {
            return targetApRE
        }
        // 合并
        val mergeApRELabels = mergeApRELabels(
            originApRELabels = sourceApRE.apRELabels,
            increaseApRELabels = targetApRE.apRELabels!!
        )
        return sourceApRE.copy(
            apRELabels = mergeApRELabels,
            limitedClusterIdList = assembleLimitedClusterIdList(sourceApRE, targetApRE)
        )
    }

    fun assembleLimitedClusterIdList(originApRE: ApREDO, increaseApRE: ApREDO): List<String> {
        return if (originApRE.limitedClusterIdList.isEmpty() || increaseApRE.limitedClusterIdList.isEmpty()) emptyList() else mutableListOf<String>().apply {
            this.addAll(originApRE.limitedClusterIdList)
            this.addAll(increaseApRE.limitedClusterIdList)
        }.distinct()
    }

    /**
     * 归并同一个ApRE下的不filter结果
     */
    fun mergeApRELabels(originApRELabels: List<ApRELabelDO>, increaseApRELabels: List<ApRELabelDO>): List<ApRELabelDO> {
        val mergeApRELabels = mutableListOf<ApRELabelDO>()
        increaseApRELabels.forEach { targetApRELabel ->
            originApRELabels.firstOrNull { sourceApRELabel ->
                sourceApRELabel.apRELabelKey == targetApRELabel.apRELabelKey
            }?.let { sourceApRELabel ->
                // 两者做进一步规格合并
                mergeApRELabels.add(apREFeatureSpecService.mergeApREFeatureSpecs(sourceApRELabel, targetApRELabel))
            } ?: let {
                mergeApRELabels.add(targetApRELabel)
            }
        }
        // 加入originApRELabels非重Label
        originApRELabels.forEach { sourceApRELabel ->
            mergeApRELabels.firstOrNull { mergeApRELabel ->
                sourceApRELabel.apRELabelKey == mergeApRELabel.apRELabelKey
            } ?: let {
                mergeApRELabels.add(sourceApRELabel)
            }
        }
        return mergeApRELabels
    }

    private fun getLimitedClusterIdList(apREBindingTerm: ApREBindingTerm?): List<String> {
        apREBindingTerm?.let { apREBindingTerm ->
            if (apREBindingTerm.allSupported == true) {
                return emptyList()
            }
            apREBindingTerm.required?.let { required ->
                if (required.allClustersSupported == true) {
                    return emptyList()
                }
                return required.clusters ?: emptyList()
            }
        }
        return emptyList()
    }

    /**
     * 通过关键字批量查询全量的ApRE Details
     *
     * @param unit
     * @param site
     * @param stage
     * @return
     */
    fun listApREDetailsBySiteAndUnitAndStage(
        unit: String,
        site: String,
        stage: String,
    ): List<ApREDO> {
        return fillApREDetails(
            apREList = appRuntimeEnvironmentDataRepo.listBySiteAndStageAndUnit(
                unit = unit, stage = stage, site = site
            )
        )
    }

    /**
     * 通过关键字批量查询ApRE Details
     */
    fun listApREDetailsByProperties(
        unit: String?,
        site: String?,
        stage: String?,
        status: String?,
        keyWords: String?,
        pageNumber: Int,
        pageSize: Int,
    ): PageData<ApREDO> {
        val apREPageData = listBaseApREByProperties(
            unit = unit, site = site, stage = stage, status = status,
            keyWords = keyWords, pageNumber = pageNumber, pageSize = pageSize
        )

        val apREList =
            checkNotNull(apREPageData.data) { "apRE data list cannot be empty in listApREDetailsByProperties" }
        val dataTotalNumber = checkNotNull(apREPageData.totalCount) { "apREPageData totalCount cannot be null " }

        return PageData(
            pageSize = pageSize,
            pageNumber = pageNumber,
            totalCount = dataTotalNumber,
            data = fillApREDetails(apREList)
        )
    }

    /**
     * 按照ApRE keys 找到apRE详细的details
     *
     * @param apREKeys
     * @return
     */
    fun listApREDetailsByApREKeys(
        apREKeys: List<String>
    ):List<ApREDO>{
        return fillApREDetails(listAppRuntimeEnvironmentDataByKeys(apREKeys))
    }

    /**
     * 查询AppRuntimeEnvironmentData的其他详细信息并进行组装
     *当apRE查询的数据为空的时提前结束查询 此函数存在多个myBatis in 操作 不允许查询emptyList
     * @param apREList
     * @return
     */
    fun fillApREDetails(apREList: List<AppRuntimeEnvironmentData>): List<ApREDO> {
        if (apREList.isEmpty()) {
            return emptyList()
        }
        val metaDataMap: Map<String, MutableMap<String, String>> = apREList.associate {
            val map = it.metaData?.let { metaData ->
                objectMapper.readValue<MutableMap<String, String>>(metaData)
            } ?: mutableMapOf()
            it.runtimeEnvKey to map
        }

        // 添加每一个 ApRE 的环境 config annotation
        clusterEnvironmentConfigRepo.queryClusterEnvironmentConfigByClusterEnvKeyList(
            apREList.map { it.runtimeEnvKey }
        ).forEach {
            metaDataMap[it.clusterEnvKey]?.put("annotations", it.annotations)
        }

        // 查询每一个ApRE对应使用的集群及关联到的 resource pool 的特性 apRERuntimeKey -> (Map<ClusterId, List<ApRELabel>>)
        val apREResourcePoolWithLabelMap = mutableMapOf<String, Map<ResourcePoolDataDO, List<ApRELabelDO>>>()

        apREList.forEach { apRE ->
            val resourcePoolList = resourcePoolService.listByManagedClusterKey(apRE.managedClusterKey)
            val resourcePoolWithLabelsMap = mutableMapOf<ResourcePoolDataDO, List<ApRELabelDO>>()
            resourcePoolList.forEach { resourcePool ->
                val labels = apRELabelService.findApRELabelByTarget(
                    resourcePool.resourcePoolKey, ApRELabelTargetTypeEnum.RESOURCE_POOL.name
                )
                resourcePoolWithLabelsMap[resourcePool] = labels
            }
            apREResourcePoolWithLabelMap[apRE.runtimeEnvKey] = resourcePoolWithLabelsMap
        }

        // 查找 ApRE 对应 ApREBindingData 的授权范围
        val apREScopeMap = mutableMapOf<AppRuntimeEnvironmentData, List<Attorney>>()
        apREList.forEach { apRE ->
            apREScopeMap[apRE] = attorneyService.listAttorneyByApREKey(apRE.runtimeEnvKey)
        }

        // compose ApREDO
        return apREList.map {
            val apRESelfLabels = apRELabelService.findApRELabelByTargetWithDefaultLabel(
                it.runtimeEnvKey,
                ApRELabelTargetTypeEnum.APRE.name
            )
            //注入title
            apRELabelDefinitionService.fillApRELabelTitles(apRESelfLabels)

            val resources = mutableListOf<ResourceDO>()
            apREResourcePoolWithLabelMap[it.runtimeEnvKey]!!.forEach { (resourcePool, apRELabels) ->
                resources.add(
                    ResourceDO(
                        clusterId = resourcePool.clusterId,
                        apRELabels = apRELabels,
                        resourcePoolKey = resourcePool.resourcePoolKey
                    )
                )
            }

            val attorneyScope = checkNotNull(apREScopeMap[it])
            ApREDO(
                id = it.id,
                runtimeEnvKey = it.runtimeEnvKey,
                name = it.name,
                creator = it.creator,
                managedClusterKey = it.managedClusterKey,
                region = it.region,
                regionName = it.region,
                az = it.az,
                azName = it.az,
                stage = it.stage,
                unit = it.unit,
                status = it.status,
                metaData = metaDataMap[it.runtimeEnvKey],
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                isDeleted = it.isDeleted,
                apRELabels = apRESelfLabels,
                resources = resources,
                attorneys = attorneyScope
            )
        }
    }

    /**
     * 按照条件查询所有的ApRE环境
     * 不查询details
     */
    fun listBaseApREByProperties(
        unit: String?,
        site: String?,
        stage: String?,
        status: String?,
        keyWords: String?,
        pageNumber: Int,
        pageSize: Int,
    ): PageData<AppRuntimeEnvironmentData> {
        check(DataValidateUtils.hasNotNullProperty(mutableListOf(unit, site, stage, status, keyWords))) {
            "list filter conditions should be more than 0 conditions at least!"
        }
        val page: Page<AppRuntimeEnvironmentData> =
            PageHelper.startPage<AppRuntimeEnvironmentData>(pageNumber, pageSize, "gmt_modified DESC")
                .doSelectPage {
                    appRuntimeEnvironmentDataRepo.listByConditions(
                        unit = unit, site = site, stage = stage, keyWords = keyWords,
                        status = status
                    )
                }
        return PageData.transformFrom(page)
    }

    fun listApREByMetadataConstraint(region: String, az: String, stage: String, unit: String): List<ApREDO> {
        return appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region, az, stage, unit).map {
            convert(it)
        }
    }

    /**
     * 不查询资源&ApRELabels
     *
     * @param runtimeEnvKey
     * @return
     */
    fun findApREBaseDetails(runtimeEnvKey: String): ApREDO? {
        val appRuntimeEnvironmentData = appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey) ?: let {
            return null
        }
        val metaData = appRuntimeEnvironmentData.metaData?.run {
            objectMapper.readValue<MutableMap<String, String>>(appRuntimeEnvironmentData.metaData!!)
        } ?: run { mutableMapOf() }
        clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(runtimeEnvKey)?.let {
            metaData.put("annotations", it.annotations)
        }

        return ApREDO(
            id = appRuntimeEnvironmentData.id,
            runtimeEnvKey = appRuntimeEnvironmentData.runtimeEnvKey,
            name = appRuntimeEnvironmentData.name,
            creator = appRuntimeEnvironmentData.creator,
            managedClusterKey = appRuntimeEnvironmentData.managedClusterKey,
            region = appRuntimeEnvironmentData.region,
            regionName = appRuntimeEnvironmentData.region,
            az = appRuntimeEnvironmentData.az,
            azName = appRuntimeEnvironmentData.az,
            stage = appRuntimeEnvironmentData.stage,
            unit = appRuntimeEnvironmentData.unit,
            status = appRuntimeEnvironmentData.status,
            metaData = metaData,
            gmtCreate = appRuntimeEnvironmentData.gmtCreate,
            gmtModified = appRuntimeEnvironmentData.gmtModified,
            isDeleted = appRuntimeEnvironmentData.isDeleted
        )
    }

    /**
     * 根据运行时环境KEY查询详情
     */
    fun findApREDetailByKey(runtimeEnvKey: String): ApREDO? {
        val baseApRE = findApREBaseDetails(runtimeEnvKey = runtimeEnvKey) ?: return null
        val resourcePoolList = resourcePoolService.listByManagedClusterKey(baseApRE.managedClusterKey)
        val resourceList = resourcePoolList.map {
            val resourcePoolApRELabels = apRELabelService.findApRELabelByTarget(
                targetKey = it.resourcePoolKey,
                targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name
            )
            apRELabelDefinitionService.fillApRELabelTitles(resourcePoolApRELabels)
            ResourceDO(
                clusterId = it.clusterId,
                resourcePoolKey = it.resourcePoolKey,
                apRELabels = resourcePoolApRELabels
            )
        }
        return baseApRE.copy(
            apRELabels = apRELabelService.findApRELabelByTargetWithDefaultLabel(
                baseApRE.runtimeEnvKey!!,
                ApRELabelTargetTypeEnum.APRE.name
            ), resources = resourceList
        )
    }

    /**
     * 根据运行时环境KEY查询详情
     * 将Resource特性统一Patch到ApRE（Resource同时保留），已合并的ApRE视角对外提供特性服务
     * 可以使用免特性过滤
     * @param runtimeEnvKey apRE key
     * @param apREBindingTerm 过滤的bindingData
     * @param exemptFilterTypeList 无需过滤的特性类型
     */
    fun findCombinedApREBaseDetailByKey(
        runtimeEnvKey: String,
        apREBindingTerm: ApREBindingTerm?,
        exemptFilterTypeList: List<ApRELabelType> = emptyList()
    ): ApREDO? {
        var apREDO = findApREBaseDetails(runtimeEnvKey) ?: return null
        apREDO = apREDO.copy(
            apRELabels = apRELabelService.findApRELabelByRuntimeEnvKeyWithResourcePoolAndDefault(
                apREDO.runtimeEnvKey!!
            )
        )
        if (apREDO.apRELabels.isNullOrEmpty() || apREBindingTerm == null) {
            //Label为空直接返回，实际这种ApRE在ApREDeed解析中会直接被上层过滤（不能提供任何特性服务）
            return apREDO
        }
        //首先先是找到ApREDO 然后进行对apRE的切割
        return maskLabelOfApRE(apREDO = apREDO, apREBindingTerm = apREBindingTerm, exemptFilterTypeList = exemptFilterTypeList)
    }

    /**
     * 使用ApREBindingData对ApRE进行过滤
     *
     * @param apREDO
     * @param apREBindingTerm
     * @param exemptFilterTypeList
     * @return
     */
    fun maskApRE(
        apREDO: ApREDO,
        apREBindingTerm: ApREBindingTerm?,
        exemptFilterTypeList: List<ApRELabelType> = emptyList()
    ): ApREDO {
        if (apREBindingTerm == null) {
            return apREDO
        }
        val masks = listOf(
            this::maskLabelOfApRE, this::maskResourceOfApRE
        )
        var finalApRE = apREDO
        for (mask in masks) {
            finalApRE = mask(finalApRE, apREBindingTerm, exemptFilterTypeList)
        }
        return finalApRE
    }

    /**
     * 过滤ApRE其下的一级注入的特性标签
     *
     * @param apREDO
     * @param apREBindingTerm
     * @param exemptFilterTypeList
     * @return
     */
    fun maskLabelOfApRE(apREDO: ApREDO, apREBindingTerm: ApREBindingTerm, exemptFilterTypeList: List<ApRELabelType> = emptyList()):ApREDO{
        if (apREBindingTerm.allSupported == true) {
            //mask直接为全局开放，不过滤特性标签&资源
            return apREDO
        }
        var finalApREDO = apREDO
        apREBindingTerm.required?.let { required ->
            // 按照selector过滤
            if (required.allLabelSupported != true) {
                finalApREDO = if (required.apRELabelSelectorTerms.isNullOrEmpty()) {
                    apREDO.copy(apRELabels = emptyList())
                } else {
                    val filterApRELabels = mutableListOf<ApRELabelDO>()
                    apREDO.apRELabels?.forEach { apRELabel ->
                        if (exemptFilterTypeList.contains(apRELabel.type)) {
                            filterApRELabels.add(apRELabel)
                            return@forEach
                        }
                        apRELabelService.filterByApREBindingTerm(apRELabel, required.apRELabelSelectorTerms)?.let {
                            filterApRELabels.add(it)
                        }
                    }
                    apREDO.copy(apRELabels = filterApRELabels)
                }
            }
        }
        return finalApREDO
    }

    /**
     * 过滤ApRE绑定的资源 集群&集群标签
     *
     * @param apREDO
     * @param apREBindingTerm
     * @param exemptFilterTypeList
     * @return
     */
    fun maskResourceOfApRE(
        apREDO: ApREDO,
        apREBindingTerm: ApREBindingTerm,
        exemptFilterTypeList: List<ApRELabelType> = emptyList()
    ): ApREDO {
        if (apREBindingTerm.allSupported == true) {
            //mask直接为全局开放，不过滤特性标签&资源
            return apREDO
        }
        var finalApREDO = apREDO
        apREBindingTerm.required?.let { required ->
            // 按照selector过滤
            if (required.allClustersSupported != true) {
                finalApREDO = if (required.clusters.isNullOrEmpty()) {
                    apREDO.copy(resources = emptyList())
                } else {
                    //1.找到准入(授权的集群)
                    val admissionCluster = apREBindingTerm.required.clusters
                    val filterResourceCluster = apREDO.resources.filter { admissionCluster!!.contains(it.clusterId) }
                    finalApREDO = apREDO.copy(resources = filterResourceCluster)
                    //2.找到授权的集群标签
                    val filterResourceLabel = mutableListOf<ResourceDO>()
                    finalApREDO.resources.forEach { resource ->
                        val filterApRELabels = mutableListOf<ApRELabelDO>()
                        resource.apRELabels.forEach { apRELabel ->
                            if (exemptFilterTypeList.contains(apRELabel.type)) {
                                filterApRELabels.add(apRELabel)
                                return@forEach
                            }
                            apREBindingTerm.required.apRELabelSelectorTerms?.let { selectorTerms ->
                                apRELabelService.filterByApREBindingTerm(apRELabel, selectorTerms)?.let {
                                    filterApRELabels.add(it)
                                }
                            }
                        }
                        filterResourceLabel.add(resource.copy(apRELabels = filterApRELabels))
                    }
                    apREDO.copy(resources = filterResourceLabel)
                }
            }
        }
        return finalApREDO
    }

    fun findApRELabelByTargetWithDefaultLabel(targetKey: String, targetType: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        // 查询ApRELabel 按照类别
        apRELabelDOs.addAll(apRELabelService.findApRELabelByTarget(targetKey, targetType))
        val apRELabelValues = apRELabelDOs.map { it.value }
        // 添加默认导入的特性&规格
        apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage().filter {
            !apRELabelValues.contains(it.code)
        }.forEach {
            apRELabelDOs.add(
                ApRELabelDO(
                    id = it.id,
                    targetKey = targetKey,
                    name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                    value = it.code,
                    gmtCreate = it.gmtCreate,
                    gmtModified = it.gmtModified,
                    isDeleted = it.isDeleted,
                    apRELabelKey = it.featureKey,
                    apREFeatureSpecs = apREFeatureSpecService.convertDefaultFeatureSpec(it.defaultFeatureSpecs!!),
                    source = ApREFeatureSourceEnum.DEFAULT.name,
                    type = DEFAULT,
                    title = "${ApRELabelExt.APRE_LABEL_FEATURE_NAME}/${it.code}"
                )
            )
        }
        // 注入title
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    /**
     * 查询运行时环境信息
     */
    fun findApREByKey(runtimeEnvKey: String): ApREDO? {
        val appRuntimeEnvironmentData = appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
        return appRuntimeEnvironmentData?.let {
            convert(it)
        }
    }

    fun listAppRuntimeEnvironmentDataByKeys(runtimeEnvKeyList: List<String>): List<AppRuntimeEnvironmentData> {
        if (runtimeEnvKeyList.isEmpty()) {
            return emptyList()
        }
        return appRuntimeEnvironmentDataRepo.findByRuntimeEnvKeyList(runtimeEnvKeyList)
    }

    /**
     * 按照条件过滤出BaseApRE
     *
     * @param unit
     * @param stage
     * @param site
     * @return
     */
    fun listBaseApREBySiteAndStageAndUnit(unit: String, stage: String, site: String): List<ApREDO> {
        return appRuntimeEnvironmentDataRepo.listBySiteAndStageAndUnit(
            unit = unit, site = site, stage = stage
        ).map {
            convert(it)
        }
    }

    /**
     * 按照元数据条件过滤资源池
     */
    fun listResourcePoolByMetadata(unit:String, stage: String, site: String, clusterId: String): List<ResourcePoolDataDO> {
        return appRuntimeEnvironmentDataRepo.listBySiteAndStageAndUnit(
            unit = unit,
            stage = stage,
            site = site
        ).flatMap { apRE ->
            resourcePoolService.listByManagedClusterKey(apRE.managedClusterKey).filter { resourcePool ->
                resourcePool.clusterId == clusterId
            }
        }
    }

    fun requireUniqueApREBySiteAndStageAndUnit(
        unit: String, site: String, stage: String
    ): ApREDO {
        return listApREDetailsBySiteAndUnitAndStage(
            unit = unit, site = site, stage = stage
        ).run {
            if (this.isEmpty())
                throw ApRENotFoundException()
            else if (this.size > 1)
                throw ApREMultipleDiscernException()
            else
                first()
        }
    }

    /**
     * 查询 ApRE 所有相关的 ApRELabels
     * 包含 ApRE 自身 ApRELabel 和 resourcePool Labels
     * 查询特性标签详情，附带特性规格；
     * 如果特性规格不存在，则加载缺省特性规格
     * @param runtimeEnvKey 环境资源
     */
    fun findApRELabelFromApREAndResourcePoolWithDefault(runtimeEnvKey: String): List<ApRELabelDO> {
        val apRELabelDOs = mutableListOf<ApRELabelDO>()
        val apRELabelValues = mutableListOf<String>()
        // 添加ApRE self Label
        apRELabelService.findApRELabelByTarget(runtimeEnvKey, ApRELabelTargetTypeEnum.APRE.name).forEach {
            apRELabelDOs.add(it)
            apRELabelValues.add(it.value)
        }
        // 添加ApRE Resource Pool Label
        resourcePoolService.findApRERelativeResourcePoolLabelsByRuntimeEnvKey(runtimeEnvKey).forEach {
            apRELabelDOs.add(it)
            apRELabelValues.add(it.value)
        }
        // 添加默认导入的特性&规格
        apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage().filter {
            !apRELabelValues.contains(it.code)
        }.forEach {
            apRELabelDOs.add(
                ApRELabelDO(
                    id = it.id,
                    targetKey = runtimeEnvKey,
                    name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                    value = it.code,
                    gmtCreate = it.gmtCreate,
                    gmtModified = it.gmtModified,
                    isDeleted = it.isDeleted,
                    apRELabelKey = it.featureKey,
                    apREFeatureSpecs = apREFeatureSpecService.convertDefaultFeatureSpec(it.defaultFeatureSpecs!!),
                    source = ApREFeatureSourceEnum.DEFAULT.name,
                    targetType = ApRELabelTargetTypeEnum.APRE,
                    type = DEFAULT
                )
            )
        }
        // 注入title
        apRELabelDefinitionService.fillApRELabelTitles(apRELabelDOs)
        return apRELabelDOs
    }

    /**
     * 创建运行时环境
     */
    @Transactional
    fun createApREBaseDetails(apRECreateReqDto: ApRECreateReqDto): ApREDO {
        log.info("createApRE:$apRECreateReqDto")
        // params validation
        kManagedClusterRepo.findKManagedClusterByManagedClusterKey(
            apRECreateReqDto.managedClusterKey
        ) ?: throw ManagedClusterNotExistException()
        // 元数据格式化
        val region = apRECreateReqDto.region.lowercase()
        val az = apRECreateReqDto.az.lowercase()
        val unit = formatUnit(apRECreateReqDto.unit)
        val stage = apRECreateReqDto.stage.name
        // 暂时限定同四元组只能创建一个ApRE
        appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region, az, stage, unit!!).let {
            if (it.isNotEmpty()) throw ApREUniqueExistMetadataConstraintException(region, az, stage, unit)
        }
        metadataService.checkMetadataConstraintWithError(region, az, unit, stage)
        // insert a new environment
        val runtimeEnvKey = KeyGenerator.generateAlphanumericKey(ENVIRONMENT_KEY_LENGTH)
        val runtimeEnvData = AppRuntimeEnvironmentData(
            id = null,
            runtimeEnvKey = runtimeEnvKey,
            name = apRECreateReqDto.name,
            creator = apRECreateReqDto.creator,
            managedClusterKey = apRECreateReqDto.managedClusterKey,
            region = region,
            az = az,
            stage = stage,
            unit = unit,
            status = apRECreateReqDto.status.name,
            metaData = objectMapper.writeValueAsString(apRECreateReqDto.metaData),
            gmtCreate = Date(Instant.now().toEpochMilli()),
            gmtModified = Date(Instant.now().toEpochMilli()),
            modifier = apRECreateReqDto.creator,
        )
        appRuntimeEnvironmentDataRepo.insert(runtimeEnvData).let {
            if (it == 0) throw ApREException("create runtime environment causes exception")
        }
        //级联创建label及spec
        apRECreateReqDto.apRELabelList.let { originalApRELabelList ->
            originalApRELabelList.forEach {
                apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(
                    it.copy(
                        targetKey = runtimeEnvKey,
                        targetType = ApRELabelTargetTypeEnum.APRE.name
                    )
                )
            }
        }
        return checkNotNull(findApREByKey(runtimeEnvKey))
    }

    /**
     * 创建运行时环境以及相关伴生对象
     */
    @Transactional
    fun createApREAndRelativeObjects(apREAndRelativeCreateReqDto: ApREAndRelativeCreateReqDto) {
        log.info("createApREAndRelativeObjects:$apREAndRelativeCreateReqDto")
        val site = apREAndRelativeCreateReqDto.apREBaseCreateReqDto.az
        val region = metadataService.getMetadataOfSite(site)?.region
            ?: throw MetadataException("cannot find metadata of site: $site")
        val creator = apREAndRelativeCreateReqDto.apREBaseCreateReqDto.creator
        //TODO:校验绑定的集群应该和ApRE同属于同一个region
        //创建管控面
        val kManagedCluster = defaultClusterService.createSimpleKManageCluster(
            region = region, status = "created"
        )
        //创建伴生 resource Pools 并按照 resource label 绑定特性
        apREAndRelativeCreateReqDto.clusterSelector.map {
            it.mapToResourcePoolCreateReqDto(creator = creator, managedClusterKey = kManagedCluster.managedClusterKey)
        }.forEach {
            resourcePoolService.createResourcePoolIgnoreWhileExistWithLabel(it)
        }
        //创建运行时环境
        createApREBaseDetails(
            apRECreateReqDto = apREAndRelativeCreateReqDto.apREBaseCreateReqDto.mapToApRECreateReqDto(
                kManageClusterKey = kManagedCluster.managedClusterKey,
                metaData = emptyMap(),
                region = region
            )
        )
        return
    }

    /**
     * 更新ApRE以及级联刷新ApRESelfLabel
     * 注意：终态覆盖 仅限console使用
     * @param apREBaseDetailsUpdateReqDto
     * @return
     */
    @Transactional
    fun updateApREBaseDetails(apREBaseDetailsUpdateReqDto: ApREBaseDetailsUpdateReqDto): ApREDO {
        val runtimeEnvKey = apREBaseDetailsUpdateReqDto.runtimeEnvKey
        val newName = apREBaseDetailsUpdateReqDto.name
        val newStatus = apREBaseDetailsUpdateReqDto.status
        val modifier = apREBaseDetailsUpdateReqDto.modifier
        log.info("updateBaseApRE:runtimeEnvKey:$runtimeEnvKey, name:$newName, status:$newStatus, modifier:$modifier")
        val simpleApRE = updateApREBaseProperties(
            runtimeEnvKey = runtimeEnvKey, name = newName, modifier = modifier, status = newStatus
        )
        //删除历史ApRELabel
        apRELabelService.deleteApRELabelByTargetAndType(
            targetType = ApRELabelTargetTypeEnum.APRE.name,
            targetKey = runtimeEnvKey,
            type = ApRELabelType.CONSOLE
        )
        //更新&创建新的ApRE label
        apREBaseDetailsUpdateReqDto.apRELabelList.forEach {
            apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(it)
        }
        return simpleApRE
    }

    /**
     * 更新ApRE
     *
     * @param runtimeEnvKey
     * @param name
     * @param modifier
     * @param status
     * @return
     */
    fun updateApREBaseProperties(runtimeEnvKey: String, name: String? = null, modifier: String, status: ApREStatusEnum): ApREDO {
        log.info("updateApRE:runtimeEnvKey:$runtimeEnvKey, name:$name, status:$status, modifier:$modifier")
        val apRE = appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            ?: throw ApRENotFoundException()

        appRuntimeEnvironmentDataRepo.updateNameAndStatusByRuntimeEnvKey(
            runtimeEnvKey = runtimeEnvKey,
            name = name ?: apRE.name,
            status = status.name,
            modifier = modifier
        ).let {
            if (it == 0) throw ApREException("apRE update error")
        }
        return checkNotNull(findApREByKey(runtimeEnvKey))
    }

    /**
     * 更新运行时环境及相关的伴生对象
     * 部分属性进行更新 包含如下：
     * 1.apRE name
     * 2.status apRE status
     * 3.clusterSelector 筛选集群 以及对应集群的选定的ApRE Label
     * 4.apRELabelList 终态的 apRE label list
     * 备注: 历史补充数据 即 ApREBindingData 数据中的授权数据 是不在修改比较的动作内的
     */
    @Transactional
    fun updateApREAndRelativeObjects(apREAndRelativeUpdateReqDto: ApREAndRelativeUpdateReqDto) {
        log.info("updateApREAndRelativeObjects:$apREAndRelativeUpdateReqDto")
        // 验证历史数据是否存在 已经 KManageCluster 是否存在
        val runtimeEnvKey = apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.runtimeEnvKey
        val modifier = apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.modifier
        val apRE = findApREByKey(runtimeEnvKey) ?: throw ApRENotFoundException()
        defaultClusterService.kManagedClusterRepo.findKManagedClusterByManagedClusterKey(apRE.managedClusterKey)
            ?: ManagedClusterNotExistException(apRE.managedClusterKey)

        // 管理终态resource pool 对比存量的ApRE Resource Pool
        val existedResourcePools = resourcePoolService.listByManagedClusterKey(apRE.managedClusterKey)
        val finalRelativeClusters = apREAndRelativeUpdateReqDto.clusterSelector.map { it.clusterId }
        // 删除非终态的 ResourcePool 以及其附属资源
        val toDeletedResourcePool = existedResourcePools.filter { !finalRelativeClusters.contains(it.clusterId) }
        toDeletedResourcePool.forEach {
            resourcePoolService.deleteWithLabelsByResourcePoolKeyWhileCheck(it.resourcePoolKey, modifier)
        }
        // 更新&创建终态存在 resource Pool 以及更新其对应的 ApRE label 以绑定特性
        apREAndRelativeUpdateReqDto.clusterSelector.forEach { clusterWithFeatureSelector ->
            resourcePoolService.updateResourcePoolIgnoreWhileExistWithLabel(
                ResourcePoolCreateOrUpdateReqDto(
                    clusterId = clusterWithFeatureSelector.clusterId,
                    managedClusterKey = apRE.managedClusterKey,
                    creator = modifier,
                    apRELabelList = clusterWithFeatureSelector.clusterFeatureLabel,
                )
            )
        }
        // 更新ApRE
        updateApREBaseDetails(apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto)
        return
    }

    fun delAppRuntimeEnvironmentByRuntimeEnvKey(runtimeEnvKey: String) {
        //TODO 检测是否存在apre label
        appRuntimeEnvironmentDataRepo.delByRuntimeEnvKey(runtimeEnvKey).let {
            if (it == 0) throw ApREException("nonexistent runtime environment")
        }
    }

    fun physicalDelAppRuntimeEnvironmentByRuntimeEnvKey(runtimeEnvKey: String) {
        //TODO 检测是否存在apre label
        appRuntimeEnvironmentDataRepo.physicalDelByRuntimeEnvKey(runtimeEnvKey).let {
            if (it == 0) throw ApREException("nonexistent runtime environment")
        }
    }

    /**
     * Serverless RT由三部分组成：基座应用名、软件包版本（默认为common）、资源规格
     * 以serverless/为前缀
     */
    fun generateServerlessRuntimeTemplateName(
        runtimeBaseApp: String,
        packageVersion: String?,
        cpu: String,
        memory: String
    ): String {
        val runtimeBaseAppPrefix =
            if (runtimeBaseApp.startsWith(RUNTIME_TEMPLATE_PREFIX))
                runtimeBaseApp
            else
                "${RUNTIME_TEMPLATE_PREFIX}${runtimeBaseApp}"
        return "${runtimeBaseAppPrefix}${RUNTIME_TEMPLATE_DELIMITER}${packageVersion ?: "common"}${RUNTIME_TEMPLATE_DELIMITER}${RUNTIME_SPEC}${cpu}-${
            memory.run {
                //移除单位     
                this.replace("Gi", "").replace("G", "")
            }
        }"
    }

    /**
     * ！！初始化数据，禁止擅自调用
     * 从集群环境同步运行时环境数据
     */
    fun syncClusterEnvToApRE() {
        log.info("${TraceUtils.getTraceId()},sync cluster env to app runtime env")
        val clusterEnvs = clusterEnvironmentRepo.listAllClusterEnvironments()
        log.info("${TraceUtils.getTraceId()},sync cluster,clusterEnvs size = ${clusterEnvs.size}")
        clusterEnvs.forEach clusterEnvLoop@{ clusterEnv ->
            val region = clusterEnv.region
            val az = clusterEnv.az.lowercase()
            val envTags = objectMapper.readValue<MutableMap<String, String>>(clusterEnv.envLabels)
            val envLevel = envTags["envLevel"]
            val stage = envTags["stage"]
            val unit = envTags["unit"]
            if (!STANDARD_STAGE_ENV_LEVEL_LIST.contains(envLevel)
                || !STANDARD_REGION_AZ_LIST.containsKey(region)
                || !STANDARD_REGION_AZ_LIST[region]!!.contains(az)
            ) {
                log.info("${TraceUtils.getTraceId()},sync cluster,notValid metadata cluster = $clusterEnv")
                return@clusterEnvLoop
            }
            appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(clusterEnv.clusterEnvKey!!)?.let {
                log.info("${TraceUtils.getTraceId()},sync cluster,repeated key cluster = $clusterEnv")
                return@clusterEnvLoop
            }
            appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region, az, stage!!, unit!!).isNotEmpty()
                .let {
                    if (it) {
                        log.info("${TraceUtils.getTraceId()},sync cluster,repeated metadata cluster = $clusterEnv")
                        return@clusterEnvLoop
                    }
                }
            val apRE = AppRuntimeEnvironmentData(
                null,
                clusterEnv.clusterEnvKey!!,
                clusterEnv.clusterEnvName,
                if (clusterEnv.clusterEnvCreator.isNullOrBlank()) "" else clusterEnv.clusterEnvCreator!!,
                clusterEnv.managedClusterKey,
                region,
                az,
                stage,
                unit,
                ApREStatusEnum.ONLINE.name,
                null,
                clusterEnv.gmtCreate,
                clusterEnv.gmtModified,
                Constants.IS_NOT_DELETED,

                )
            log.info("${TraceUtils.getTraceId()},insert apRE = $apRE")
            metadataService.checkMetadataConstraintWithError(apRE.region, apRE.az, apRE.unit, apRE.stage)
            appRuntimeEnvironmentDataRepo.insert(apRE)
        }
    }

    /**
     * 判断声明特性标签->当前运行时环境标签是否匹配
     * @param matchApRELabels 声明匹配的标签，多个标签匹配关系为："与",暂时只支持匹配一项标签
     * @return first:是否匹配;second:返回匹配的标签
     */
    private fun matchApRELabels(
        matchApRELabels: List<MatchApRELabel>?,
        apRELabels: List<ApRELabelDO>?
    ): Pair<Boolean, List<ApRELabelDO>?> {
        if (matchApRELabels.isNullOrEmpty()) {
            return Pair(true, apRELabels?.filter { apRELabelDO ->
                //如果没有指定匹配标签，默认过滤Serverless标签
                apRELabelDO.type != SERVERLESS
            })
        }
        if (apRELabels.isNullOrEmpty()) {
            return Pair(false, apRELabels)
        }
        if (matchApRELabels.size > 1) {
            throw ApRELabelException("系统暂时只支持至多一项标签声明匹配,当前标签声明:${matchApRELabels}")
        }
        val filterApRELabelList = mutableListOf<ApRELabelDO>()
        apRELabels.filter { apRELabel ->
            matchApRELabels[0].name ?.run { apRELabel.name == this } ?: true
                    && matchApRELabels[0].value ?.run { apRELabel.value.startsWith(this) } ?:true
        }.forEach { apRELabel ->
            matchApREFeatureSpecs(matchApRELabels[0].matchApREFeatureSpecs, apRELabel.apREFeatureSpecs).let {
                if (it.first) {
                    filterApRELabelList.add(
                        apRELabel.copy(
                            apREFeatureSpecs = it.second
                        )
                    )
                }
            }
        }
        return Pair(filterApRELabelList.isNotEmpty(), filterApRELabelList)
    }

    /**
     * 判断声明的特性规格->当前运行时环境特性规格是否匹配
     * @param matchApREFeatureSpecs 声明匹配的特性规格，多个规格匹配关系为："或",暂时只支持匹配一项规格
     * @return first:是否匹配;second:返回匹配的规格
     */
    private fun matchApREFeatureSpecs(
        matchApREFeatureSpecs: List<MatchApREFeatureSpec>?,
        apREFeatureSpecs: List<ApREFeatureSpecDO>?
    ): Pair<Boolean, List<ApREFeatureSpecDO>?> {
        if (matchApREFeatureSpecs.isNullOrEmpty()) {
            return Pair(true, apREFeatureSpecs)
        }
        if (apREFeatureSpecs.isNullOrEmpty()) {
            return Pair(false, apREFeatureSpecs)
        }
        if (matchApREFeatureSpecs.size > 1) {
            throw ApRELabelSpecException("系统暂时只支持至多一项规格声明匹配,当前规格声明:${matchApREFeatureSpecs}")
        }
        apREFeatureSpecs.filter { apREFeatureSpec ->
            matchDeclarationMetadata(matchApREFeatureSpecs[0].id, apREFeatureSpec.id!!)
                    && matchDeclarationMetadata(matchApREFeatureSpecs[0].specCode, apREFeatureSpec.specCode)
                    && matchDeclarationMetadata(matchApREFeatureSpecs[0].specType, apREFeatureSpec.specType)
                    && matchApREFeatureSpecLabels(matchApREFeatureSpecs[0].matchFeatureSpecLabels, apREFeatureSpec.labels)
        }.let {
            return Pair(it.isNotEmpty(), it)
        }
    }

    private fun matchApREFeatureSpecLabels(matchFeatureSpecLabels: Map<String, String>?, featureSpecLabels: String?): Boolean {
        if (matchFeatureSpecLabels.isNullOrEmpty()) {
            return true
        }
        if (null == featureSpecLabels) {
            return false
        }
        val featureSpecLabelsMap = JsonUtils.readValue(featureSpecLabels, Map::class.java)
        matchFeatureSpecLabels.forEach { (key, value) ->
            if (featureSpecLabelsMap[key] != value) {
                return false
            }
        }
        return true
    }


    private fun convert(appRuntimeEnvironmentData: AppRuntimeEnvironmentData): ApREDO {
        return ApREDO(
            appRuntimeEnvironmentData.id,
            appRuntimeEnvironmentData.runtimeEnvKey,
            appRuntimeEnvironmentData.name,
            appRuntimeEnvironmentData.creator,
            appRuntimeEnvironmentData.managedClusterKey,
            appRuntimeEnvironmentData.region,
            appRuntimeEnvironmentData.az,
            appRuntimeEnvironmentData.stage,
            appRuntimeEnvironmentData.unit,
            appRuntimeEnvironmentData.status,
            appRuntimeEnvironmentData.metaData?.run {
                objectMapper.readValue<MutableMap<String, String>>(
                    appRuntimeEnvironmentData.metaData!!
                )
            } ?: run { null },
            appRuntimeEnvironmentData.gmtCreate,
            appRuntimeEnvironmentData.gmtModified,
            appRuntimeEnvironmentData.isDeleted,
            null,
            emptyList()
        )
    }

    private fun listStageByEnvLevel(envLevel: String): List<String> {
        envLevelStageMappingDataRepo.queryByEnvLevel(
            envLevelStageMappingService.getStandardEnvLevel(envLevel)
        ).map { it.stage }.let { stages ->
            if (stages.isNotEmpty()) {
                return stages
            }
            // 使用环境级别数据补充用途（如果环境级别在MetadataStageEnum枚举范围内）
            getStage(envLevel)?.let {
                return listOf(it)
            }
        }
        return emptyList()
    }

    companion object {
        const val ENVIRONMENT_KEY_LENGTH = 32
        const val AONE_PRODUCTLINE = "aone-productline"
        const val RUNTIME_TEMPLATE_DELIMITER: String = "$$"
        const val RUNTIME_TEMPLATE_PREFIX: String = "serverless/"
        const val RUNTIME_SPEC = "SPEC:"
        val STANDARD_REGION_AZ_LIST = mapOf(
            "ap-southeast-1" to listOf("id137", "os30", "sg52"),
            "cn-beijing" to listOf("cm12", "ng152", "ng185", "nm125", "nm89", "nu150"),
            "cn-shanghai" to listOf("ea118", "ea119", "eg169", "eo166", "et15", "et2", "et93", "eu126", "eu13"),
            "cn-shenzhen" to listOf("sa128", "sm92", "so158", "st3", "st4", "su121"),
            "cn-zhangjiakou" to listOf("na61", "na610", "na620", "na63"),
            "rus-west-1" to listOf("ru151"),
            "us-east-1" to listOf("us44", "us68", "oc27", "oc270")
        )
        val STANDARD_STAGE_ENV_LEVEL_LIST = listOf("production", "spe", "staging", "daily")
    }
}