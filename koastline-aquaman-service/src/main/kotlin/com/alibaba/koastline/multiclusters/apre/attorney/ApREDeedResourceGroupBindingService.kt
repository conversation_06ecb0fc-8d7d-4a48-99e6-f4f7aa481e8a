package com.alibaba.koastline.multiclusters.apre.attorney

import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedResourceGroupMultiBindingException
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.ApREDeedResourceGroupDataRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.external.SkylineApi
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*

@Component
class ApREDeedResourceGroupBindingService {
    val log by logger()
    @Autowired
    lateinit var apREDeedResourceGroupDataRepo: ApREDeedResourceGroupDataRepo
    @Autowired
    lateinit var skylineApi: SkylineApi
    @Autowired
    lateinit var apREDeedService: ApREDeedService

    /**
     * 保存分组&运行时环境声明绑定关系，如果存在则删除重新添加
     */
    @Transactional
    fun save(appName: String, resourceGroup: String, apREDeedKeys: List<String>) {
        if (apREDeedKeys.size > 1) {
            //暂时只允许一分组绑定一声明，产品侧做限定，后期视业务使用情况看是否放开
            throw ApREDeedResourceGroupMultiBindingException(resourceGroup)
        }
        apREDeedResourceGroupDataRepo.deleteByResourceGroup(resourceGroup)
        val now = Date(Instant.now().toEpochMilli())
        apREDeedKeys.forEach {
            apREDeedResourceGroupDataRepo.insert(
                ApREDeedResourceGroupBindingData(
                    null, it, resourceGroup, appName, now, now
                )
            )
        }
    }

    /**
     * 创建分组&运行时环境声明绑定关系,存在即忽略
     */
    @Transactional
    fun createIgnoreWhileExist(appName: String, resourceGroup: String, apREDeedKey: String) {
        getByResourceGroup(resourceGroup) ?.let {
            if (it.appName == appName && it.resourceGroup == resourceGroup && it.apREDeedKey == apREDeedKey) {
                return
            }
            throw ApREDeedResourceGroupMultiBindingException(resourceGroup)
        }
        val now = Date(Instant.now().toEpochMilli())
        apREDeedResourceGroupDataRepo.insert(
            ApREDeedResourceGroupBindingData(
                null, apREDeedKey, resourceGroup, appName, now, now
            )
        )
    }

    /**
     * 保存分组&运行时环境声明绑定关系，如果存在则删除重新添加
     */
    @Transactional
    fun createOrUpdateWhileExist(appName: String, resourceGroup: String, apREDeedKey: String) {
        apREDeedResourceGroupDataRepo.deleteByResourceGroup(resourceGroup)
        createIgnoreWhileExist(appName, resourceGroup, apREDeedKey)
    }

    /**
     * 删除分组&运行时环境声明绑定关系
     */
    @Transactional
    fun delete(appName: String, resourceGroup: String, apREDeedKey: String) {
        apREDeedResourceGroupDataRepo.queryByResourceGroup(resourceGroup).firstOrNull {
            it.appName == appName && it.resourceGroup == resourceGroup && it.apREDeedKey == apREDeedKey
        } ?.let {
            apREDeedResourceGroupDataRepo.deleteById(it.id!!)
        }
    }

    /**
     * 获取分组绑定的运行时环境声明
     */
    fun getByResourceGroup(resourceGroup : String): ApREDeedResourceGroupBindingData? {
        val apREDeedResourceGroupBindingDataList = apREDeedResourceGroupDataRepo.queryByResourceGroup(resourceGroup)
        if (apREDeedResourceGroupBindingDataList.size > 1) {
            //创建入口已做保护，查询部分防止脏数据
            throw ApREDeedResourceGroupMultiBindingException(resourceGroup)
        }
        return apREDeedResourceGroupBindingDataList.firstOrNull()
    }

    /**
     * 根据环境获取声明，默认一环境一分组约束
     */
    fun getApREDeedKeyByEnvStackId(envStackId: String): String {
        val resourceGroupList = skylineApi.listResourceGroupConfigByEnvStackId(envStackId)
        if (resourceGroupList.isEmpty()) {
            throw BizException("该环境:${envStackId}未配置资源分组")
        }
        if (resourceGroupList.size > 1) {
            throw BizException("该环境:${envStackId}配置多个资源分组:${resourceGroupList}")
        }
        getByResourceGroup(resourceGroupList[0]) ?.let {
            return it.apREDeedKey
        } ?:let {
            throw BizException("该环境:${envStackId}未配置资源声明")
        }
    }


    /**
     * 根据分组获取资源声明
     */
    fun getApREDeedByResourceGroup(resourceGroup: String): Declaration? {
        getByResourceGroup(resourceGroup) ?.let {
            return apREDeedService.findApREDeedByKey(it.apREDeedKey).declarations ?.firstOrNull()
        }
        return null
    }
}