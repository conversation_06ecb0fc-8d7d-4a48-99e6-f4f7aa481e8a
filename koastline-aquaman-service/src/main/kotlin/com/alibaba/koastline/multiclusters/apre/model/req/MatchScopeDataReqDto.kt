package com.alibaba.koastline.multiclusters.apre.model.req

import com.alibaba.koastline.multiclusters.apre.model.Exclusion
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("匹配范围定义")
@JsonIgnoreProperties(ignoreUnknown = true)
data class MatchScopeDataReqDto (
    @ApiModelProperty("范围限定ID")
    val externalId: String,
    @ApiModelProperty("范围限定类型")
    val externalType: String,
    @ApiModelProperty("排除范围")
    val exclusions: List<Exclusion>? = null,
    @ApiModelProperty("限定条件")
    val restrictions: List<Restriction>? = null
) {
    // TODO 应该要做一下externalId的合法性校验的
    fun buildMatchScopeDataDO(targetId: Long? = null, targetType: String, creator: String): MatchScopeDataDO {
        return MatchScopeDataDO(
            targetId = targetId,
            targetType = targetType,
            externalId = externalId,
            externalType = externalType,
            exclusions = exclusions,
            restrictions = restrictions,
            creator = creator,
            modifier = creator
        )
    }

    fun validate(): MatchScopeDataReqDto {
        require(externalId.isNotBlank()) { "externalId must be non-blank" }
        require(externalType.isNotBlank()) { "externalType must be non-blank" }
        exclusions?.forEach { it.validate() }
        restrictions?.forEach { it.validate() }
        return this
    }
}