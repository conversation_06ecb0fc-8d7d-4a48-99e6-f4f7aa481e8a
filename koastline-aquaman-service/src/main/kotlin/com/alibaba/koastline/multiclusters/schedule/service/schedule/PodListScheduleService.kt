package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.resourcescope.EnvHostResourceScopeService
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.ScheduleFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * @author:    <EMAIL>
 * @description:  运行态资源PodList计算服务
 * @date:    2023/12/5 11:37 AM
 */
@Component(value = "PodListScheduleService")
class PodListScheduleService : ScheduleFacade, InitializingBean {
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var runningStateScheduleService: RunningStateScheduleService
    @Autowired
    lateinit var envHostResourceScopeService: EnvHostResourceScopeService
    @Autowired
    lateinit var envHostWorkloadMetaService: EnvHostWorkloadMetaService
    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        if (isBetaHostEnv(content)) {
            return ScheduleResult(
                computeBetaHostRunningWorkloadList(content)
            )
        }
        return ScheduleResult(
            runningStateScheduleService.doSchedule(content).workloadExpectedStates.filter {
                match(it.workloadMetadataConstraint, content.scheduleRequestParam ?.scheduleEnvType)
            }
        )
    }

    /**
     * 计算Beta主机环境运行时Workload列表
     */
    private fun computeBetaHostRunningWorkloadList(content: ScheduleRequestContent): List<WorkloadExpectedState> {
        val envHostWorkloadMetaList = envHostWorkloadMetaService.listByEnvStackId(content.resourceScope.envStackId!!)
        val envHostResourceScope = envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)!!
        return runningStateScheduleService.doSchedule(content.copy(
            resourceScope = content.resourceScope.copy(envStackId = envHostResourceScope.baseEnvStackId)
        )).workloadExpectedStates.filter {workloadExpectedState ->
            envHostWorkloadMetaService.match(workloadExpectedState, envHostWorkloadMetaList)
                    && envHostWorkloadMetaService.match(workloadExpectedState, envHostResourceScope)
        }
    }

    private fun isBetaHostEnv(content: ScheduleRequestContent): Boolean {
        return content.resourceScope.envStackId?.let { envHostResourceScopeService.findByCurrentEnvStackId(it) != null } ?: false
    }

    private fun match(workloadMetadataConstraint: WorkloadMetadataConstraint, scheduleEnvType : ScheduleEnvType?): Boolean {
        return scheduleEnvType ?.let {
            return when(it) {
                ASI -> workloadMetadataConstraint.runtimeId.isNullOrEmpty()
                SERVERLESS_APP -> !workloadMetadataConstraint.runtimeId.isNullOrEmpty()
                else -> true
            }
        } ?: true
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.POD_LIST_SCHEDULE, this)
    }

}
