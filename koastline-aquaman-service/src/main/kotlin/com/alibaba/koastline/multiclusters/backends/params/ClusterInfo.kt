package com.alibaba.koastline.multiclusters.backends.params

/**
 * <AUTHOR>
 */
data class ClusterInfo(
        val clusterId: String,
        val clusterName: String?=null,
        val region: String,
        val site: String?= null
)

data class ManagedClusterInfo(
        var clusterId: String,
        val clusterName: String,
        val region: String,
        val namespace: String,
        val quota: String? = "false",
        val gateway: GatewayConfig?=null
)

data class ClusterEnvInfo(
        val clusterId: String,
        val clusterName: String,
        val region: String,
        val namespace: String,
        val gateway: GatewayConfig?=null,
        val envMeta: EnvMeta?=null
)

data class GatewayConfig(
        val host: String,
        val port: Long?=80,
        val schema: String? = "http",
        val templateVersion: String
)

data class EnvMeta(
        val envLevel: String,
        val region: String,
        val az: String,
        val unit: String? = null,
        val stage: String,
        val externalId: String,
        val externalType: String
)

data class AoneEnvInfo(
        val envLevel: String,
        val region: String,
        val regionName: String,
        val az: String,
        val azName: String,
        val stage: String,
        val unit: String,
        val unitName: String,
        val aoneEnvLevel: String,
        val clusterId: String? = null
)

data class AoneEnvRawInfo(
        val env_level: String,
        val env_region: String,
        val env_region_name: String,
        val az: String,
        val az_name: String,
        val stage: String,
        val unit: String,
        val unit_name: String,
        val aone_env_level: String
)

data class AoneEnvInfoDTO(
        val envLevel: String,
        val region: String,
        val regionName: String,
        val az: String,
        val azName: String,
        val stage: String,
        val unit: String,
        val unitName: String,
        var clusterId: String,
        val clusterName: String,
        val sysNamespace: String
)