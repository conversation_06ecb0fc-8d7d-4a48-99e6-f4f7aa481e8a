package com.alibaba.koastline.multiclusters.apre.model.req

import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.vo.env.FeatureSpecDefinition
import java.time.Instant
import java.util.*

data class FeatureSpecConditionQueryReq(
    val pageSize: Int,
    val pageNumber: Int,
    val title: String? = null,
    val specCode: String? = null,
    val scope: String? = null,
    val status: String? = null,
    val refKey: String? = null
)

data class FeatureSpecDefinitionCreateReq(
    val refKey: String,
    val title: String,
    val scope: String,
    val specType: String = "",
    val specCode: String,
    val sourceType: String = "",
    val sourceId: String = "",
    val versionType: String = "",
    val versionId: String = "",
    val annotations: String = "{}",
    val creator: String,
    val status: String
) {
    fun validate() {
        require(scope.isNotBlank()) { "scope cannot be blank" }
        require(title.isNotBlank()) { "title cannot be blank" }
        require(refKey.isNotBlank()) { "refKey cannot be blank" }
        require(status.isNotBlank()) { "status cannot be blank" }
        require(specCode.isNotBlank()) { "status cannot be blank" }
        require(creator.isNotBlank()) { "creator cannot be blank" }
        require(JsonUtils.isValidateJsonString(annotations))
    }

    fun convertToFeatureSpecDefinition(): FeatureSpecDefinition {
        validate()
        return FeatureSpecDefinition(
            refKey = this.refKey,
            title = this.title,
            scope = this.scope,
            specType = this.specType,
            specCode = this.specCode,
            sourceId = this.sourceId,
            versionId = this.versionId,
            versionType = this.versionType,
            creator = this.creator,
            modifier = this.creator,
            status = this.status,
            annotations = this.annotations,
            gmtModified = Date(Instant.now().toEpochMilli()),
            gmtCreate = Date(Instant.now().toEpochMilli())
        )
    }
}

data class FeatureSpecDefinitionUpdateReq(
    val featureSpecDefinitionId: String,
    val title: String,
    val scope: String,
    val sourceType: String = "",
    val sourceId: String = "",
    val versionType: String = "",
    val versionId: String = "",
    val annotations: String = "{}",
    val modifier: String,
    val status: String
) {

    fun validate() {
        require(scope.isNotBlank()) { "scope cannot be blank" }
        require(featureSpecDefinitionId.isNotBlank()) { "featureSpecDefinitionId cannot be blank" }
        require(title.isNotBlank()) { "title cannot be blank" }
        require(status.isNotBlank()) { "status cannot be blank" }
        require(modifier.isNotBlank()) { "modifier cannot be blank" }
        require(JsonUtils.isValidateJsonString(annotations))
    }

    fun mergeToNewFeatureSpecDefinition(originalFeatureSpecDefinition: FeatureSpecDefinition): FeatureSpecDefinition {
        validate()
        return originalFeatureSpecDefinition.copy(
            title = this.title,
            scope = this.scope,
            sourceId = this.sourceId,
            sourceType = this.sourceType,
            versionId = this.versionId,
            versionType = this.versionType,
            status = this.status,
            annotations = this.annotations,
            gmtModified = Date(Instant.now().toEpochMilli())
        )
    }
}
