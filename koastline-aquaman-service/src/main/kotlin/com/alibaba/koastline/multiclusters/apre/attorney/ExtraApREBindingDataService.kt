package com.alibaba.koastline.multiclusters.apre.attorney

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService.Companion.BU_PRODUCT_LINE_SPLITTER
import com.alibaba.koastline.multiclusters.apre.isServerlessLabel
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ExternalAndProperties
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.RuntimeProperties
import com.alibaba.koastline.multiclusters.apre.model.ServerlessAttorney
import com.alibaba.koastline.multiclusters.apre.model.ServerlessAttorneyGroup
import com.alibaba.koastline.multiclusters.apre.model.ServerlessEnvType
import com.alibaba.koastline.multiclusters.apre.model.req.CreateServerlessAttorneyDto
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.ExtraApREBindingDataRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingDataType
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingDataType.SERVERLESS_BASE
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*

@Service
class ExtraApREBindingDataService @Autowired constructor(
    val objectMapper: ObjectMapper,
    val extraApREBindingDataRepo: ExtraApREBindingDataRepo,
    val matchScopeService: MatchScopeService
) {
    val log by logger()

    /**
     * 获取 全局授权 / 产品线授权 / 自己的resource的授权
     * 最后给出所有的特性开放标签
     * 需要给定携带的二级过滤器
     */
    fun getScopeAttorneyByIdentityInfo(
        identityInfo: IdentityInfo
    ): Map<ExtraApREBindingData, MatchScopeDataDO> {
        val matchScopeDataList = matchScopeService.findMatchScopesByTargetAndExternalForApp(
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            appName = identityInfo.appName,
            resourceGroupList = identityInfo.nodeGroup?.let { listOf(it) } ?: emptyList(),
            buId = identityInfo.productLineIdPath?.run { this.substringBefore(BU_PRODUCT_LINE_SPLITTER) },
            productFullLineIdPath = identityInfo.productLineIdPath?.run { this.substringAfter(BU_PRODUCT_LINE_SPLITTER) },
        )
        val targetIds = matchScopeDataList.map {
            it.targetId!!
        }.distinct()
        if (targetIds.isEmpty()) {
            return emptyMap()
        }
        val extraBindingDataList = extraApREBindingDataRepo.findByIds(targetIds)
        return extraBindingDataList.associateWith { extraApREBindingData -> matchScopeDataList.find { extraApREBindingData.id == it.targetId }!! }
    }

    /**
     * 查找关于某一类资源对于某一个授权范围的全量授权
     */
    fun findScopeAttorneyByExternal(
        externalId: String, externalType: MatchScopeExternalTypeEnum, extraApREBindingDataType: ExtraApREBindingDataType
    ): Map<ExtraApREBindingData, MatchScopeData> {
        val matchScopeDataList = matchScopeService.matchScopeDataRepo.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            externalId = externalId,
            externalType = externalType.name
        )
        if (matchScopeDataList.isEmpty()) {
            return emptyMap()
        }
        val extraApREBindingDataList =
            extraApREBindingDataRepo.findByIds(matchScopeDataList.map { it.targetId }).filter {
                it.type == extraApREBindingDataType
            }
        return extraApREBindingDataList.associateWith { extraApREBindingData -> matchScopeDataList.first { extraApREBindingData.id == it.targetId } }
    }

    /**
     * console使用 keyWords是targetId的前缀匹配的关键词语
     */
    fun listServerlessAttorneyByProperties(
        pageNumber: Int,
        pageSize: Int,
        keyWords: String? = null,
    ): PageData<ServerlessAttorneyGroup> {
        // 逻辑先groupBy 基座应用分组 在对每一个基座应用来查询授权的范围
        val groupCount = extraApREBindingDataRepo.findScopeGroupCountByType(keyWords = keyWords, type = SERVERLESS_BASE.name)
        val total = groupCount.size

        val emptyData = PageData<ServerlessAttorneyGroup>(
            pageNumber = pageNumber,
            pageSize = pageSize,
            totalCount = total.toLong(),
            data = emptyList()
        )

        val beginGroupRow = pageSize * (pageNumber - 1) + 1
        var endGroupRow = pageSize * pageNumber

        if (beginGroupRow > total) {
            return emptyData
        }

        if (endGroupRow > total) {
            endGroupRow = total
        }

        val targetIds = groupCount.filterIndexed { index, _ ->
            index + 1 in beginGroupRow..endGroupRow
        }.map { it.groupName }


        val extraApREBindingDataList = extraApREBindingDataRepo.findByTargetIds(targetIds)

        val matchScopeList = matchScopeService.matchScopeDataRepo.findByTargetIdsAndTargetType(
            targetIds = extraApREBindingDataList.map { it.id!! },
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name
        )

        return PageData(
            pageNumber = pageNumber,
            pageSize = pageSize,
            totalCount = total.toLong(),
            data = buildServerlessAttorneyGroup(
                serverlessBaseApps = targetIds,
                extraApREBindingDataList = extraApREBindingDataList,
                matchScopeList = matchScopeList
            )
        )
    }


    fun buildServerlessAttorneyGroup(
        serverlessBaseApps: List<String>,
        extraApREBindingDataList: List<ExtraApREBindingData>,
        matchScopeList: List<MatchScopeData>
    ): List<ServerlessAttorneyGroup> {
        require(matchScopeList.size == extraApREBindingDataList.size) { "size of matchScopeList should keep same size to extraApREBindingDataList" }
        val serverlessAttorneyGroupList = mutableListOf<ServerlessAttorneyGroup>()
        serverlessBaseApps.forEach { serverlessApp ->
            val subServerlessAttorneyList =
                extraApREBindingDataList.filter { it.targetId == serverlessApp }.map { extraApREBindingData ->
                    val ms = matchScopeList.find { it.targetId == extraApREBindingData.id }!!
                    extraApREBindingData.map2ServerlessAttorney(ms)
                }

            serverlessAttorneyGroupList.add(
                ServerlessAttorneyGroup.transformServerlessAttorneyList(subServerlessAttorneyList)
            )
        }
        return serverlessAttorneyGroupList
    }


    fun findByServerlessAttorneyByExternal(
        externalId: String, externalType: MatchScopeExternalTypeEnum
    ): List<ServerlessAttorney> {
        val attorneyScopeMap = findScopeAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
            extraApREBindingDataType = SERVERLESS_BASE
        )
        return attorneyScopeMap.map { (extraApREBindingData, ms) ->
            extraApREBindingData.map2ServerlessAttorney(ms)
        }
    }

    @Transactional
    fun batchCreateServerlessAttorneyWhileExisted2Update(
        createServerlessAttorneyDtoList: List<CreateServerlessAttorneyDto>
    ): List<ServerlessAttorney> {
        createServerlessAttorneyDtoList.forEach {
            it.validate()
        }
        val result = mutableListOf<ServerlessAttorney>()
        createServerlessAttorneyDtoList.forEach { createServerlessAttorneyDto ->
            result.add(
                createServerlessAttorneyWhileExisted2Update(
                    serverlessBaseApp = createServerlessAttorneyDto.serverlessBaseApp,
                    externalId = createServerlessAttorneyDto.externalId,
                    externalType = createServerlessAttorneyDto.externalType,
                    properties = createServerlessAttorneyDto.properties,
                    description = createServerlessAttorneyDto.description,
                    creator = createServerlessAttorneyDto.creator
                )
            )
        }
        return result
    }

    @Transactional
    fun createServerlessAttorneyWhileExisted2Update(
        serverlessBaseApp: String,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum,
        properties: RuntimeProperties,
        description: String = "",
        creator: String
    ): ServerlessAttorney {
        val attorneys = findByServerlessAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
        )
        val attorney = attorneys.find { it.serverlessBaseApp == externalId }

        attorney?.let {
            if (propertiesHasCovered(origin = it.externalAndProperties.runtimeProperties, new = properties)) {
                throw ApREException("授权已经创建成功")
            }
            updateServerlessAttorney(
                serverlessBaseApp = serverlessBaseApp,
                externalId = externalId,
                externalType = externalType,
                description = description,
                modifier = creator,
                properties = properties
            )
        } ?: let { //不存在就创建新的 ApREBindingData
            val newExtraApREBindingData = ExtraApREBindingData(
                type = SERVERLESS_BASE,
                gmtModified = Date(Instant.now().toEpochMilli()),
                gmtCreate = Date(Instant.now().toEpochMilli()),
                creator = creator,
                modifier = creator,
                isDeleted = "N",
                targetId = serverlessBaseApp,
                description = description,
                properties = objectMapper.writeValueAsString(properties)
            )
            extraApREBindingDataRepo.insert(
                newExtraApREBindingData
            ).let {
                if (it <= 0) throw ApREException(
                    "insert extraApREBindingData with error, errorData is :type $SERVERLESS_BASE targetId $serverlessBaseApp properties ${
                        objectMapper.writeValueAsString(
                            description
                        )
                    }"
                )
            }
            //插入matchScopeData
            matchScopeService.createMatchScopeIgnoreWhileExist(
                MatchScopeDataDO(
                    targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                    targetId = newExtraApREBindingData.id,
                    externalType = externalType.name,
                    externalId = externalId,
                    creator = creator,
                    modifier = creator
                )
            )
        }
        log.info("create new svl base app auth, svl base app:$serverlessBaseApp,  externalId:$externalId, externalType:$externalType, properties:$properties, description:$description")
        return findByServerlessAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
        ).find { it.serverlessBaseApp == serverlessBaseApp }!!
    }

    @Transactional
    fun updateServerlessAttorney(
        serverlessBaseApp: String,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum,
        properties: RuntimeProperties,
        description: String,
        modifier: String
    ): ServerlessAttorney {
        val existedServerlessExtraApREBindingData = findScopeAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
            extraApREBindingDataType = SERVERLESS_BASE
        ).map { it.key }.find { it.targetId == serverlessBaseApp }
            ?: throw ApREException("未找到Serverless基座应用授权, 授权范围:$externalId, 授权基座应用:$serverlessBaseApp")

        extraApREBindingDataRepo.updateExtraApREBindingData(
            existedServerlessExtraApREBindingData.copy(
                gmtModified = Date(Instant.now().toEpochMilli()),
                modifier = modifier,
                properties = objectMapper.writeValueAsString(properties),
                description = description
            )
        ).let {
            if (it <= 0) throw ApREException("updateServerlessAttorney with error coordinate: serverlessBaseApp: $serverlessBaseApp externalId: $externalId externalType:$externalType")
        }

        log.info("update svl base app auth, svl base app:$serverlessBaseApp,  externalId:$externalId, externalType:$externalType, properties:$properties, description:$description")
        return findByServerlessAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
        ).find { it.serverlessBaseApp == serverlessBaseApp }!!
    }

    @Transactional
    fun deleteServerlessAttorney(
        serverlessBaseApp: String,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum,
        modifier: String
    ) {
        val matchScopeDataList = matchScopeService.matchScopeDataRepo.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            externalId = externalId,
            externalType = externalType.name
        )

        val extraApREBindingDataList = extraApREBindingDataRepo.findByTargetId(targetId = serverlessBaseApp)

        // 找到即删除存量的ServerlessApp授权 取其中的交集interSec
        matchScopeDataList.find { matchScopeData ->
            extraApREBindingDataList.map { it.id }.contains(matchScopeData.targetId)
        }?.let { matchScopeData ->
            matchScopeService.deleteMatchScopeById(matchScopeData.id!!, modifier)
            extraApREBindingDataRepo.deleteById(matchScopeData.targetId).let {
                if (it <= 0) throw ApREException("delete with extraApREBindingData error, extraApREBindingDataId:${matchScopeData.targetId}")
            }
        }
        log.info("delete svl base app auth, svl base app:$serverlessBaseApp,  externalId:$externalId, externalType:$externalType")
    }

    fun mergeServerlessProperties(
        origin: RuntimeProperties,
        new: RuntimeProperties
    ): RuntimeProperties {
        return RuntimeProperties(
            admissionEnvList = (origin.admissionEnvList + new.admissionEnvList).distinct(),
            allRuntimeSupport = origin.allRuntimeSupport || new.allRuntimeSupport
        )
    }

    fun propertiesHasCovered(origin: RuntimeProperties, new: RuntimeProperties): Boolean {
        if (!origin.admissionEnvList.containsAll(new.admissionEnvList))
            return false
        if (!origin.allRuntimeSupport)
            return false
        return true
    }

    fun ExtraApREBindingData.map2ServerlessAttorney(matchScopeData: MatchScopeData): ServerlessAttorney {
        return ServerlessAttorney(
            serverlessBaseApp = this.targetId,
            externalAndProperties = ExternalAndProperties(
                runtimeProperties = objectMapper.readValue(this.properties),
                externalId = matchScopeData.externalId,
                externalType = MatchScopeExternalTypeEnum.valueOf(matchScopeData.externalType),
                description = this.description,
                creator = this.creator,
                modifier = this.modifier,
                gmtCreate = this.gmtCreate,
                gmtModified = this.gmtModified
            )
        )
    }

    /**
     * 挑选所有被授权授权范围 使用前需要merge存量的ExtraApREBindingData
     * 先按照资源分类归并所有 ExtraApREBindingData 找到最大Mask
     * 再使用最大mask对label进行过滤
     */
    fun featureByScopeAttorneySelector(
        originApRELabels: List<ApRELabelDO>,
        extraScopeAttorneyMapByType: Map<String, List<ExtraApREBindingData>>,
        minLabelMaskMap: Map<String, (List<ExtraApREBindingData>?) -> List<ExtraApREBindingData>?>,
        masksStrategyMap: Map<String, (List<ApRELabelDO>, List<ExtraApREBindingData>?) -> List<ApRELabelDO>>
    ): List<ApRELabelDO> {
        require(minLabelMaskMap.size == masksStrategyMap.size) { "masks should keep same size with masksStrategies " }
        var leftLabels = originApRELabels
        minLabelMaskMap.forEach { (resourceType, mask) ->
            val attorneys = mask.invoke(extraScopeAttorneyMapByType[resourceType])
            masksStrategyMap[resourceType]?.let {
                leftLabels = it.invoke(leftLabels, attorneys)
            }
        }
        return leftLabels
    }

    /**
     *  mask策略list
     */
    fun getMaskStrategy(): Map<String, (List<ApRELabelDO>, List<ExtraApREBindingData>?) -> List<ApRELabelDO>> {
        return mutableMapOf(
            SERVERLESS_BASE.name to this::serverlessMask
        )
    }

    /**
     * 最小maskList策略
     */
    fun getMinLabelMask(): Map<String, (List<ExtraApREBindingData>?) -> List<ExtraApREBindingData>?> {
        return mutableMapOf(
            SERVERLESS_BASE.name to this::mergeServerlessExtraApREBindingData
        )
    }

    /**
     * 合并ExtraApREBindingData找到小Mask
     */
    fun mergeServerlessExtraApREBindingData(extraApREBindingDataList: List<ExtraApREBindingData>?): List<ExtraApREBindingData>? {
        if (extraApREBindingDataList.isNullOrEmpty()) {
            return null
        }
        val attorneyByTargetGroup = extraApREBindingDataList.groupBy {
            it.targetId
        }
        return attorneyByTargetGroup.map { (_, attorneys) ->
            val properties = attorneys.map { objectMapper.readValue<RuntimeProperties>(it.properties) }
            val newProperties = RuntimeProperties(
                allRuntimeSupport = properties.map { it.allRuntimeSupport }.fold(false) { acc, b -> acc || b },
                admissionEnvList = properties.map { it.admissionEnvList }.flatten().distinct()
            )
            attorneys.first().copy(
                properties = objectMapper.writeValueAsString(newProperties)
            )
        }
    }

    /**
     * 过滤serverless label
     */
    fun serverlessMask(
        leftApRELabels: List<ApRELabelDO>,
        attorneys: List<ExtraApREBindingData>?
    ): List<ApRELabelDO> {
        val newLabels = mutableListOf<ApRELabelDO>()
        leftApRELabels.forEach { label ->
            var newLabel = label
            if (newLabel.isServerlessLabel()) {
                val target = label.value
                // 没有授权直接去处特性 attorney
                if (attorneys.isNullOrEmpty())
                    return@forEach
                val extraApREBindingData = attorneys.find { it.targetId == target } ?: let {
                    return@forEach
                }
                val runtimeProperties =
                    objectMapper.readValue<RuntimeProperties>(extraApREBindingData.properties)

                if (!runtimeProperties.admissionEnvList.contains(ServerlessEnvType.TESTING)) {
                    val newFeatureSpec = label.apREFeatureSpecs?.filter { it.scope != "test" }
                    newLabel = newLabel.copy(
                        apREFeatureSpecs = newFeatureSpec
                    )
                }
            }
            newLabels.add(newLabel)
        }
        return newLabels
    }
}