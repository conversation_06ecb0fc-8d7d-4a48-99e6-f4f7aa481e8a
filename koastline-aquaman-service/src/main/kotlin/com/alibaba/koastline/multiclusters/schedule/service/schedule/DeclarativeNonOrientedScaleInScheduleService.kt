package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractNonOrientedScaleInScheduleRefactor
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component(value = "DeclarativeNonOrientedScaleInScheduleService")
class DeclarativeNonOrientedScaleInScheduleService @Autowired constructor(
    var scheduleServiceFactory: ScheduleServiceFactory
) : AbstractNonOrientedScaleInScheduleRefactor(), InitializingBean {

    override fun getApREDeed(content: ScheduleRequestContent): ApREDeedDO {
        return scheduleStandardService.getAssembledApREDeedByDeclarative(content)
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.DECLARATIVE_NON_ORIENTED_SCALE_IN_SCHEDULE, this)
    }

    override fun isDeclarative(): Boolean {
        return true
    }
}