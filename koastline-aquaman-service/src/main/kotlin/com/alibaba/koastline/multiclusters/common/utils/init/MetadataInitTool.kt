package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import okhttp3.OkHttpClient
import java.io.File

object MetadataInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()
    fun init() {
        val userName = "admin"
        val secret = "test123"
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("init/metadata_init_data.txt")!!.file
        val lines: List<String> = File(file).readLines()

        lines.forEach { line ->
            line.split("\t").let {
                val site = it[0]
                val stage = it[1]
                val unit = it[2]
                print("$site,$stage,$unit")
                println(
                    HttpClientUtils.httpPost(
                        "https://pre-aquaman.koastline.alibaba-inc.com/apis/metadata/v4/metadataConstraint/create",
                        mapOf(
                            "site" to site,
                            "stage" to stage,
                            "unit" to unit
                        ),
                        AuthorizationAttribute(
                            userName,
                            secret
                        )
                    )
                )
            }
        }
        println("finish init data.")
    }
}

fun main(args: Array<String>) {
    MetadataInitTool.init()
}