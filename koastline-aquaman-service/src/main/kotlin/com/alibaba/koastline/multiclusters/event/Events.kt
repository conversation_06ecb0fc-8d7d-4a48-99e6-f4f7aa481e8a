package com.alibaba.koastline.multiclusters.event

import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import java.util.Date

/**
 *
 * Date: 2023-04-18 Time: 15:38
 *
 * <AUTHOR>
 */

/**
 * 使用 CloudEvent 兼容格式
 */
data class Event<T>(
    val id: String,
    val source: String,
    val type: String,
    val data: T,
    val time: Date = Date(),
) {
    fun key() = "${source}$${id}"
}

object EventSources {
    const val RESOURCE_SPEC = "/resource-pec"
}

object EventTypes {
    const val RESOURCE_SPEC_CHANGE = "resource.spec.change"
}

data class ResourceSpecChangeEventPayload(
    val appName: String? = null,
    val resourceGroup: String? = null,
    val resourceSpec: ResourceSpec,
    val employeeId: String,
) {
    fun buildEvent(): Event<ResourceSpecChangeEventPayload> {
        require(appName != null || resourceGroup != null) {
            "appName and resourceGroup cannot be both null"
        }
        return Event(
            id = "${appName ?: ""}#${resourceGroup ?: ""}#${System.currentTimeMillis()}",
            source = EventSources.RESOURCE_SPEC,
            type = EventTypes.RESOURCE_SPEC_CHANGE,
            data = this
        )
    }
}