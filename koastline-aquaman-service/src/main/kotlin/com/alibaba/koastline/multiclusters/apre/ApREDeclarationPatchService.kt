package com.alibaba.koastline.multiclusters.apre


import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREDeclarationPatchCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_CLUSTER
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_SITE
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeclarationPatchException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeclarationPatchNotExistException
import com.alibaba.koastline.multiclusters.common.exceptions.MatchScopeDataException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.env.ApREDeclarationPatchDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.MatchScopeDataRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeclarationPatchData
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Component
class ApREDeclarationPatchService(val objectMapper: ObjectMapper) {
    val log by logger()

    @Autowired
    lateinit var apREDeclarationPatchDataRepo: ApREDeclarationPatchDataRepo

    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var matchScopeDataRepo: MatchScopeDataRepo

    @Autowired
    lateinit var skylineApi: SkylineApi

    @Transactional
    fun createApREDeclarationPatchWithMatchScope(apREDeclarationPatchCreateReqDto: ApREDeclarationPatchCreateReqDto) {
        apREDeclarationPatchCreateReqDto.validate()
        //包含相同定义，相同范围的均衡策略，不允许重复添加
        checkDuplicate(apREDeclarationPatchCreateReqDto)
        val apREDeclarationPatchData = ApREDeclarationPatchData(
            null,
            balanceType = checkNotNull(apREDeclarationPatchCreateReqDto.balanceType) { "type must not be null." }.name,
            unit = apREDeclarationPatchCreateReqDto.unit,
            stage = apREDeclarationPatchCreateReqDto.stage,
            site = apREDeclarationPatchCreateReqDto.site,
            declarationPatch = objectMapper.writeValueAsString(apREDeclarationPatchCreateReqDto.declarationPatch),
            creator = apREDeclarationPatchCreateReqDto.creator,
            modifier = apREDeclarationPatchCreateReqDto.creator
        )
        apREDeclarationPatchDataRepo.insert(apREDeclarationPatchData)
        apREDeclarationPatchCreateReqDto.matchScopeDataReqDtoList?.forEach { matchScopeDataReqDto ->
            matchScopeService.createMatchScopeIgnoreWhileExist(
                matchScopeDataReqDto.buildMatchScopeDataDO(
                    targetId = apREDeclarationPatchData.id!!,
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                    creator = apREDeclarationPatchCreateReqDto.creator
                )
            )
        }
    }

    private fun checkDuplicate(apREDeclarationPatchCreateReqDto: ApREDeclarationPatchCreateReqDto) {
        apREDeclarationPatchCreateReqDto.matchScopeDataReqDtoList?.forEach { matchScopeDataReqDto ->
            matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                externalType = matchScopeDataReqDto.externalType,
                externalId = matchScopeDataReqDto.externalId
            ).map { apREDeclarationPatchDataRepo.findById(it.targetId) }.firstOrNull {
                it != null && apREDeclarationPatchCreateReqDto.matches(it)
            }?.let {
                throw ApREDeclarationPatchException(
                    "重复的均衡策略配置，apREDeclarationPatchCreateReqDto:${
                        JsonUtils.writeValueAsString(
                            apREDeclarationPatchCreateReqDto
                        )
                    }"
                )
            }
        }
    }

    /**
     * 更新ApREDeclarationPatchData中的DeclarationPatch属性
     *
     * @param id
     * @param modifier
     * @param declarationPatch
     */
    fun updateApREDeclarationPatchById(id: Long, modifier: String, declarationPatch: DeclarationPatch) {
        declarationPatch.validate()
        apREDeclarationPatchDataRepo.findById(id) ?: throw ApREDeclarationPatchNotExistException(id)
        apREDeclarationPatchDataRepo.updateDeclarationPatchById(
            id = id,
            modifier = modifier,
            declarationPatch = objectMapper.writeValueAsString(declarationPatch)
        )
    }

    @Transactional
    fun deleteApREDeclarationPatchWithMatchScope(id: Long, modifier: String) {
        matchScopeService.deleteMatchScopeByTarget(
            targetId = id,
            targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
            modifier = modifier
        )
        apREDeclarationPatchDataRepo.deleteById(id = id, modifier = modifier)
    }

    fun findApREDeclarationPatchById(id: Long): ApREDeclarationPatchDataDO? {
        return apREDeclarationPatchDataRepo.findById(id)?.let {
            toApREDeclarationPatchDataDO(
                it,
                matchScopeService.listByTarget(id, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name)
            )
        }
    }

    /**
     * 查询关于应用继承的所有均衡规则
     *
     * @param stage
     * @param unit
     * @param externalId 生效范围
     * @param externalType 生效类型
     * @return
     */
    fun listApREDeclarationPatchWithMatchScopeByProperties(
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        balanceType: ApREDeclarationPatchType? = null,
        includeScopeType: IncludeScopeType,
        jsonSelector: String? = null,
        externalId: String,
        externalType: String,
        pageSize: Int,
        pageNumber: Int
    ): PageData<ApREDeclarationPatchDataDO> {
        val matScopeDataList = matchScopeService.filterMatchScopeDataByIncludeScopeType(
            externalType = externalType,
            externalId = externalId,
            includeScopeType = includeScopeType
        )

        if (matScopeDataList.isEmpty()) {
            return PageData.zeroPage(pageSize = pageSize, pageNumber = pageNumber)
        }

        val apREDeclarationPatchPageData = listApREDeclarationPatchByProperties(
            stage = stage,
            unit = unit,
            site = site,
            balanceType = balanceType,
            ids = matScopeDataList.map { it.targetId!! },
            pageSize = pageSize,
            pageNumber = pageNumber,
            jsonSelector = jsonSelector
        )
        return apREDeclarationPatchPageData.map { pageData -> pageData.copy(matchScopeDataDOs = matScopeDataList.filter { ms -> ms.targetId == pageData.id }) }
    }


    /**
     * 按照condition查询所有数据 不进行分页查询
     */
    fun listApREDeclarationPatchWithMatchScopeByCondition(
        region: String?,
        stage: String,
        unit: String,
    ): List<ApREDeclarationPatchDataDO> {
        return apREDeclarationPatchDataRepo.findByCondition(
            region = region,
            stage = stage,
            unit = unit,
            balanceType = BALANCE_SITE.name,
        ).map {
            toApREDeclarationPatchDataDO(
                it,
                matchScopeService.listByTarget(it.id!!, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name)
            )
        }
    }

    /**
     * 基于声明查询不同的均衡策略 并且返回对应授权的范围
     *
     * @param keyWords
     * @param region
     * @param stage
     * @param unit
     * @param site
     * @param ids
     * @param balanceType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listApREDeclarationPatchWithMatchScopeByProperties(
        keyWords: String? = null,
        region: String? = null,
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        ids: List<Long>? = null,
        balanceType: ApREDeclarationPatchType? = null,
        jsonSelector: String? = null,
        pageSize: Int,
        pageNumber: Int
    ): PageData<ApREDeclarationPatchDataDO> {
        val pageData = listApREDeclarationPatchByProperties(
            keyWords = keyWords,
            region = region,
            stage = stage,
            unit = unit,
            site = site,
            ids = ids,
            balanceType = balanceType,
            pageSize = pageSize,
            pageNumber = pageNumber,
            jsonSelector = jsonSelector
        ).map { pageData ->
            pageData.copy(
                matchScopeDataDOs = matchScopeService.listByTarget(
                    targetId = pageData.id!!,
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name
                )
            )
        }
        return pageData
    }

    /**
     * 基于声明查询不同的均衡策略
     *
     * @param keyWords
     * @param region
     * @param stage
     * @param unit
     * @param site
     * @param ids
     * @param balanceType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listApREDeclarationPatchByProperties(
        keyWords: String? = null,
        region: String? = null,
        stage: String? = null,
        unit: String? = null,
        site: String? = null,
        ids: List<Long>? = null,
        balanceType: ApREDeclarationPatchType? = null,
        jsonSelector: String? = null,
        pageSize: Int,
        pageNumber: Int
    ): PageData<ApREDeclarationPatchDataDO> {

        ids?.let {
            // 提前结束查询 id ids为空回抛出动态SQL异常
            if (it.isEmpty()) {
                return PageData.zeroPage(pageSize = pageSize, pageNumber = pageNumber)
            }
        }
        val page: Page<ApREDeclarationPatchData> =
            PageHelper.startPage<ApREDeclarationPatchData>(pageNumber, pageSize, "gmt_modified DESC")
                .doSelectPage {
                    apREDeclarationPatchDataRepo.findByCondition(
                        unit = unit,
                        stage = stage,
                        keyWords = keyWords,
                        region = region,
                        balanceType = balanceType?.name,
                        ids = ids,
                        site = site,
                        jsonSelector = jsonSelector
                    )
                }

        val pageData = PageData.transformFrom(page)
        val apREDeclarationPatchDataList = checkNotNull(pageData.data)
        return PageData(
            pageNumber = pageData.pageNumber,
            pageSize = pageData.pageSize,
            totalCount = pageData.totalCount,
            data = apREDeclarationPatchDataList.map {
                toApREDeclarationPatchDataDO(it, emptyList())
            }
        )
    }

    /**
     * 针对ApRE契约声明查询可用的补充条款约束定义
     */
    fun querySiteBalanceApREDeclarationPatchList(apREDeedDO: ApREDeedDO): List<ApREDeclarationPatchDataDO> {
        apREDeedDO.identityInfo!!.appName ?: return emptyList()
        val declaration = fetchPatchDeclaration(apREDeedDO) ?: return emptyList()
        return queryApREDeclarationPatchDataByAppNameAndResourceGroup(
            apREDeedDO.identityInfo!!.appName!!,
            apREDeedDO.identityInfo.nodeGroup,
            BALANCE_SITE.name
        ).filter {
            MetadataUtils.matchDeclarationMetadata(declaration.unit, it.unit) &&
                    MetadataUtils.matchDeclarationMetadata(declaration.stage, it.stage)
        }
    }

    /**
     * 针对ApRE契约声明附加补充条款定义
     */
    fun fillSiteBalanceApREDeclarationPatchForApREDeed(apREDeedDO: ApREDeedDO): ApREDeedDO {
        checkNotNull(apREDeedDO.identityInfo?.appName){"identityInfo.appName must not be null in fillSiteBalanceApREDeclarationPatchForApREDeed"}
        val declaration = fetchPatchDeclaration(apREDeedDO) ?: return apREDeedDO
        //弱声明模式下，如果未找到资源声明切面策略，则抛异常
        val apREDeclarationPatchDataDO =
            getSiteBalanceApREDeclarationPatchData(apREDeedDO.identityInfo!!.appName!!, apREDeedDO.identityInfo.nodeGroup,
                checkNotNull(declaration.stage) { "附件站点均衡策略,声明参数stage不能为空。" },
                checkNotNull(declaration.unit) { "附件站点均衡策略,声明参数unit不能为空。" })
                ?: throw ApREDeclarationPatchException("未找到SRE资源声明补偿策略，请联系SRE配置，apREDeed:${apREDeedDO}")
        val patchDeclarations = mutableListOf<Declaration>()
        apREDeclarationPatchDataDO.declarationPatch.patchItems.forEach {
            patchDeclarations.add(Declaration(
                UUID.randomUUID().toString(),
                it.region?.run { this } ?: kotlin.run { declaration.region },
                it.site,
                declaration.stage,
                declaration.unit,
                declaration.matchApRELabels,
                it.weight
            )
            )
        }
        return apREDeedDO.copy(declarations = patchDeclarations)
    }

    /**
     * 查询资源路由切面策略，权限范围只返回优先级最高的一项
     */
    fun queryApREDeclarationPatchDataByAppNameAndResourceGroup(
        appName: String,
        resourceGroup: String?,
        balanceType: String
    ): List<ApREDeclarationPatchDataDO> {
        val patchDataMatchScopeMap = mutableMapOf<String, MutableList<MatchScopeDataDO>>()
        val apREDeclarationPatchDataIds = mutableListOf<Long>()
        matchScopeService.findMatchScopesByTargetAndExternalForApp(
            MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName,
            resourceGroup?.let { listOf(it) } ?: emptyList()
        ).forEach { matchScopeDataDO ->
            apREDeclarationPatchDataIds.add(matchScopeDataDO.targetId!!)
            patchDataMatchScopeMap[matchScopeDataDO.targetId.toString()]?.add(matchScopeDataDO) ?: let {
                patchDataMatchScopeMap[matchScopeDataDO.targetId.toString()] = mutableListOf(matchScopeDataDO)
            }
        }
        if (apREDeclarationPatchDataIds.isEmpty()) {
            return emptyList()
        }
        // 只保留优先级最高的MatchScopeData
        val topPriorityPatchDataIdentityMap = mutableMapOf<String, Pair<ApREDeclarationPatchData, MatchScopeDataDO>>()
        apREDeclarationPatchDataRepo.findByIds(apREDeclarationPatchDataIds.distinct()).filter {
            balanceType == it.balanceType
        }.forEach { apREDeclarationPatchData ->
            patchDataMatchScopeMap[apREDeclarationPatchData.id.toString()]?.let { matchScopeDataList ->
                computePriorMatchScopeDataByPatchDataIdentity(
                    apREDeclarationPatchData,
                    topPriorityPatchDataIdentityMap,
                    matchScopeDataList
                )
            }
        }
        return topPriorityPatchDataIdentityMap.values.map {
            toApREDeclarationPatchDataDO(it.first, listOf(it.second))
        }
    }

    fun getSiteBalanceApREDeclarationPatchData(
        appName: String,
        resourceGroup: String?,
        stage: String,
        unit: String
    ): ApREDeclarationPatchDataDO? {
        val apREDeclarationPatchDataDOs = queryApREDeclarationPatchDataByAppNameAndResourceGroup(
            appName,
            resourceGroup,
            BALANCE_SITE.name
        ).filter {
            unit == it.unit && stage == it.stage
        }
        if (apREDeclarationPatchDataDOs.size > 1) {
            throw ApREDeclarationPatchException("重复的均衡策略授权，请求参数:{appName:${appName}, resourceGroup: ${resourceGroup}, stage: ${stage}, unit :${unit}},均衡策略:${apREDeclarationPatchDataDOs}")
        }
        return apREDeclarationPatchDataDOs.elementAtOrNull(0)
    }

    fun getClusterBalanceApREDeclarationPatchData(
        appName: String,
        resourceGroup: String? = null,
        stage: String,
        unit: String,
        site: String
    ): ApREDeclarationPatchDataDO? {
        val apREDeclarationPatchDataDOs = queryApREDeclarationPatchDataByAppNameAndResourceGroup(
            appName,
            resourceGroup,
            BALANCE_CLUSTER.name
        ).filter {
            unit == it.unit && stage == it.stage && site == it.site
        }
        if (apREDeclarationPatchDataDOs.size > 1) {
            throw ApREDeclarationPatchException("重复的均衡策略授权，请求参数:{appName:${appName}, resourceGroup: ${resourceGroup}, stage: ${stage}, unit :${unit}, site : ${site}},均衡策略:${apREDeclarationPatchDataDOs}")
        }
        return apREDeclarationPatchDataDOs.elementAtOrNull(0)
    }

    /**
     * 基于均衡数据身份计算最优范围匹配
     */
    private fun computePriorMatchScopeDataByPatchDataIdentity(
        apREDeclarationPatchData: ApREDeclarationPatchData,
        patchDataIdentityMatchScopeMap: MutableMap<String, Pair<ApREDeclarationPatchData, MatchScopeDataDO>>,
        currentMatchScopeDataList: List<MatchScopeDataDO>
    ) {
        val currentPriorMatchScopeData = matchScopeService.getPriorMatchScopeData(currentMatchScopeDataList) ?: return
        patchDataIdentityMatchScopeMap[apREDeclarationPatchData.identity()]?.second?.let { originalMatchScopeData ->
            when (matchScopeService.compareScopePriority(
                originalMatchScopeData.externalType, originalMatchScopeData.externalId,
                currentPriorMatchScopeData.externalType, currentPriorMatchScopeData.externalId
            )) {
                1 -> return
                0 -> throw MatchScopeDataException("重复的均衡策略授权，identity:${apREDeclarationPatchData.identity()}, 冲突授权范围：${originalMatchScopeData.externalType}#${originalMatchScopeData.externalId}")
                -1 -> patchDataIdentityMatchScopeMap.put(
                    apREDeclarationPatchData.identity(),
                    Pair(apREDeclarationPatchData, currentPriorMatchScopeData)
                )

                else -> return
            }
        } ?: patchDataIdentityMatchScopeMap.put(
            apREDeclarationPatchData.identity(),
            Pair(apREDeclarationPatchData, currentPriorMatchScopeData)
        )
    }

    private fun toApREDeclarationPatchDataDO(
        apREDeclarationPatchData: ApREDeclarationPatchData,
        matchScopeDataDOList: List<MatchScopeDataDO>
    ): ApREDeclarationPatchDataDO {
        return ApREDeclarationPatchDataDO(
            apREDeclarationPatchData.id,
            apREDeclarationPatchData.balanceType,
            apREDeclarationPatchData.region,
            apREDeclarationPatchData.unit,
            apREDeclarationPatchData.stage,
            apREDeclarationPatchData.site,
            objectMapper.readValue(apREDeclarationPatchData.declarationPatch),
            apREDeclarationPatchData.creator,
            apREDeclarationPatchData.modifier,
            apREDeclarationPatchData.priority,
            apREDeclarationPatchData.gmtCreate,
            apREDeclarationPatchData.gmtModified,
            apREDeclarationPatchData.isDeleted,
            matchScopeDataDOList
        )
    }

    /**
     * 获取需要Patch的声明
     */
    private fun fetchPatchDeclaration(apREDeedDO: ApREDeedDO): Declaration? {
        if (apREDeedDO.declarations.isNullOrEmpty() || apREDeedDO.declarations!!.size > 1) {
            return null
        }
        val declaration = apREDeedDO.declarations!![0]
        if (!declaration.az.isNullOrEmpty()) {
            return null
        }
        return declaration
    }
}

/**
 * 特性均衡的feat balance
 *
 * @property includeScopeType 是否是继承类授权
 * @property apREDeclaration 部署策略
 * @property existedFeatBalanceList 已经加入特性均衡策略的特性
 * @property extraAppendFeatBalanceList 可以加入特性均衡策略的特性
 */
data class FeatBalanceApREDeclaration(
    val includeScopeType: IncludeScopeType? = null,
    val apREDeclaration: ApREDeclarationPatchDataDO,
    val existedFeatBalanceList: List<FeatBalance>,
    val extraAppendFeatBalanceList: List<ClusterLabel> = emptyList()
)

/**
 * 特性均衡描述
 * @property clusterLabelList
 * @property weight
 * @property effected
 */
data class FeatBalance(
    val clusterLabelList: List<ClusterLabel>,
    val weight: Int,
    val effected: Boolean? = null
)

/**
 * 集群均衡的clusterBalanceApREDeclaration
 *
 * @property includeScopeType
 * @property apREDeclaration
 * @property existedClusterBalanceList
 * @property extraAppendClusterBalanceList
 */
data class ClusterBalanceApREDeclaration(
    val includeScopeType: IncludeScopeType? = null,
    val apREDeclaration: ApREDeclarationPatchDataDO,
    val existedClusterBalanceList: List<ClusterBalance>,
    val extraAppendClusterBalanceList: List<String> = emptyList()
)

/**
 * 集群均衡特性
 *
 * @property clusterList
 * @property weight
 * @property effected
 */
data class ClusterBalance(
    val clusterList: List<String>,
    val weight: Int,
    val effected: Boolean? = null
)


/**
 * 集群均衡的clusterBalanceApREDeclaration
 *
 * @property includeScopeType
 * @property apREDeclaration
 * @property existedSiteBalanceList
 * @property extraAppendSiteBalanceList
 */
data class SiteBalanceApREDeclaration(
    val includeScopeType: IncludeScopeType? = null,
    val apREDeclaration: ApREDeclarationPatchDataDO,
    val existedSiteBalanceList: List<SiteBalance>,
    val extraAppendSiteBalanceList: List<String> = emptyList()
)

/**
 * 集群均衡特性
 *
 * @property site
 * @property weight
 * @property effected
 */
data class SiteBalance(
    val site: String,
    val weight: Int,
    val effected: Boolean? = null
)