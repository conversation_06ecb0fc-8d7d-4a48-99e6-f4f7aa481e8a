package com.alibaba.koastline.multiclusters.common.utils

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.kubernetes.client.openapi.JSON
import java.io.StringWriter
import java.io.Writer
import java.util.*
import javax.json.Json
import javax.json.JsonValue
import javax.json.JsonWriterFactory
import javax.json.stream.JsonGenerator

object JsonUtils {
    private val gson = JSON()
    private val objectMapper = ObjectMapper()
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS)
        .disable(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS)
        .registerKotlinModule()
        .registerModule(JavaTimeModule())
        .registerModule(JodaModule())
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .configure(JsonParser.Feature.AUTO_CLOSE_SOURCE, true)
        .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
        .setSerializationInclusion(JsonInclude.Include.NON_NULL)

    inline fun <reified T> objectTypeReference() = object : TypeReference<T>() {}

    fun isValidateJsonString(jsonString: String): Boolean {
        return try {
            val mapper = ObjectMapper()
            mapper.readTree(jsonString)
            true
        } catch (e: Exception) {
            false
        }
    }

    fun <T> readValue(value: String, valueType: Class<T>): T {
        return objectMapper.readValue(value, valueType)
    }

    fun <T> gsonReadValue(value: String, valueType: Class<T>): T {
        return gson.deserialize(value, valueType)
    }

    fun <T> readValue(value: String, valueType: TypeReference<T>): T {
        return objectMapper.readValue(value, valueType)
    }

    fun <T> readListValue(value: String, valueType: TypeReference<List<T>>): List<T> {
        return objectMapper.readValue(value, valueType)
    }

    fun readTree(json: String): JsonNode {
        return objectMapper.readTree(json)
    }

    fun value2Tree(value: Any): JsonNode {
        return objectMapper.valueToTree(value)
    }

    fun writeValueAsString(value: Any): String {
        return objectMapper.writeValueAsString(value)
    }

    fun gsonWriteValueAsString(value: Any): String {
        return gson.serialize(value)
    }

    fun getObjectMapper(): ObjectMapper {
        return objectMapper
    }

    /**
     * to fast json string
     */
    fun <T : Any> T.toJson(): String = com.alibaba.fastjson.JSON.toJSONString(this)

    /*
    pring JsonValue as pretty formatted json string
     */
    fun format(json: JsonValue?): String? {
        val stringWriter = StringWriter()
        prettyPrint(json, stringWriter)
        return stringWriter.toString()
    }

    private fun prettyPrint(json: JsonValue?, writer: Writer) {
        val config: Map<String, Any?> = Collections.singletonMap(JsonGenerator.PRETTY_PRINTING, true)
        val writerFactory: JsonWriterFactory = Json.createWriterFactory(config)
        writerFactory.createWriter(writer).use { jsonWriter -> jsonWriter.write(json) }
    }
}
