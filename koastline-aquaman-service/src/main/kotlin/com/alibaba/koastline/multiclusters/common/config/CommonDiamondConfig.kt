package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.boot.diamond.listener.DataIdListener
import com.alibaba.koastline.multiclusters.common.logger
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.taobao.diamond.client.Diamond
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

@Component
class CommonDiamondConfig (val commonProperties: CommonProperties): DataIdListener {
    val log by logger()
    override fun getDiamondUrl(): String {
        return "diamond://koastline-aquaman/${GROUP_ID}/${DATA_ID}"
    }

    override fun valueChanged(newResource: Resource?) {
        commonProperties.properties = buildProperties()
        log.info("value changed, new commonProperties: ${commonProperties.properties}")
    }

    companion object {
        private const val DATA_ID = "koasltine-aquaman-common"
        private const val GROUP_ID = "DEFAULT_GROUP"

        fun buildProperties(): Map<String, List<String>> {
            val config = Diamond.getConfig(DATA_ID, GROUP_ID, 5000)
            val objectMapper = ObjectMapper()
            return objectMapper.readValue(config)
        }
    }
}