package com.alibaba.koastline.multiclusters.schedule.model

import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("调度请求")
data class ScheduleRequestContent(
    val resourceScope: ResourceScope,
    val declarationData: DeclarationData? = null,
    val scheduleType: ScheduleType,
    val scheduleRequestParam: ScheduleRequestParam? = null,
    val includeClusterInfoToResult: Boolean = true,
    val dryRun: Boolean? = false,
){
    constructor(resourceScope: ResourceScope, scheduleType: ScheduleType): this(
        resourceScope, null, scheduleType, null,false
    )
    constructor(resourceScope: ResourceScope, declarationData: DeclarationData, scheduleType: ScheduleType): this(
        resourceScope, declarationData, scheduleType, null,false
    )
    constructor(resourceScope: ResourceScope, scheduleType: ScheduleType, scheduleRequestParam: ScheduleRequestParam): this(
        resourceScope, null, scheduleType, scheduleRequestParam,false
    )
}

@ApiModel("资源操作范围")
data class ResourceScope(
    @ApiModelProperty("应用名")
    val appName: String,
    @ApiModelProperty("环境StackId")
    val envStackId: String? = null,
    @ApiModelProperty("分组名")
    val resourceGroup: String? = null,
    @ApiModelProperty("资源SN列表")
    val resourceSNList: List<String> = emptyList()
)

@ApiModel("声明内容(面向声明态操作)，apREDeedKey和declaration二选一")
data class DeclarationData(
    @ApiModelProperty("ApRE声明契约KEY")
    val apREDeedKey: String? = null,
    @ApiModelProperty("ApRE声明")
    val declaration: OrientedDeclaration? = null
){
    fun validate() {
        require(apREDeedKey == null && declaration != null || apREDeedKey != null && declaration == null){
            "illegal DeclarationData, must choose one of apREDeedKey or declaration"
        }
    }
}

data class ScheduleRequestParam (
    @ApiModelProperty("操作副本数")
    val replicas: Int? = null,
    @ApiModelProperty("发布环境类型")
    val scheduleEnvType : ScheduleEnvType = ScheduleEnvType.ASI,
    @ApiModelProperty("是否为Serverless")
    val serverless : Boolean = false,
    @ApiModelProperty("Serverless Runtime Template")
    val serverlessRuntimeTemplate : String? = null,
    @ApiModelProperty("Serverless App Name")
    val serverlessBaseAppName: String? = null,
    @ApiModelProperty("环境StackPkId")
    val envStackPkId: String? = null,
)

@ApiModel("调度对象类型")
data class ScheduleType(
    val schedulePattern: SchedulePatternEnum,
    val scene: SceneEnum
) {
    override fun toString(): String {
        return "${schedulePattern.name}&${scene.name}"
    }
}

@ApiModel("调度模式枚举，范围：[DECLARATIVE,NON_DECLARATIVE]")
enum class SchedulePatternEnum{
    @ApiModelProperty("声明式")
    DECLARATIVE,
    @ApiModelProperty("非声明式")
    NON_DECLARATIVE
}

@ApiModel("场景枚举，范围：[SCALE_OUT,SPECIFIED_SCALE_IN,NON_SPECIFIED_SCALE_IN,RESTART,REPLACE,DEPLOY,LIST_POD]")
enum class SceneEnum{
    @ApiModelProperty("扩容")
    SCALE_OUT,
    @ApiModelProperty("定向缩容")
    ORIENTED_SCALE_IN,
    @ApiModelProperty("非定向缩容")
    NON_ORIENTED_SCALE_IN,
    @ApiModelProperty("重启")
    RESTART,
    @ApiModelProperty("置换")
    REPLACE,
    @ApiModelProperty("发布")
    DEPLOY,
    @ApiModelProperty("资源列表")
    LIST_POD
}

data class OrientedDeclaration(
    @ApiModelProperty("区域",required = false)
    @JsonProperty("region")
    val region: String? = null,
    @ApiModelProperty("站点",required = true)
    @JsonProperty("site")
    val site: String,
    @ApiModelProperty("用途",required = true)
    @JsonProperty("stage")
    val stage: String,
    @ApiModelProperty("单元",required = true)
    @JsonProperty("unit")
    val unit: String,
    @ApiModelProperty("特性匹配")
    @JsonProperty("matchApRELabels")
    val matchApRELabels: List<OrientedMatchApRELabel> = emptyList()
)

data class OrientedMatchApRELabel(
    @ApiModelProperty("特性名称",required = true)
    @JsonProperty("name")
    val name: String,
    @ApiModelProperty("特性值",required = true)
    @JsonProperty("value")
    val value: String,
    @ApiModelProperty("规格匹配")
    @JsonProperty("matchApREFeatureSpecs")
    val matchApREFeatureSpecs: List<OrientedMatchApREFeatureSpec>? = null
)

data class OrientedMatchApREFeatureSpec(
    @ApiModelProperty("规格代码")
    @JsonProperty("specCode")
    val specCode: String,
    @ApiModelProperty("规格类型")
    @JsonProperty("specType")
    val specType: String?,
    @ApiModelProperty("规格匹配标签")
    @JsonProperty("matchFeatureSpecLabels")
    val matchFeatureSpecLabels: Map<String, String>?
)