package com.alibaba.koastline.multiclusters.appenv.model

import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.ParameterChecker
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus

/**
 * <AUTHOR>
 */
data class ClusterProfile(
        val clusterId: String,
        val clusterName: String,
        val clusterProvider: String,
        val clusterType: String,
        val clusterExternalId: String? = null,
        val clusterMetaData: ClusterMetaData,
        val status: String = ClusterStatus.ONLINE.status,
        val useType: String = ClusterProfileUseTypeEnum.publish.name,
)

data class ClusterMetaData(
        val gatewayConfigs: List<GatewayConfig?>,
        val region: String,
        val site: String,
        val clusterAnnotations: MutableMap<String, String>,
        val clusterLabels: Map<String?, String?>
)

data class GatewayConfig(
        val schema: String,
        val host: String,
        val port: Long,
        val pathPrefix: String? = "k8s-api/v1",
        val k8sNamespace: String? = "cse-default",
        //val templateVersion: String? = "v2",
        val annotations: MutableMap<String,String>
){
    constructor(schema: String, host: String, port: Long, k8sNamespace: String, annotations: MutableMap<String, String>)
            : this(schema = schema, host = host, port = port, pathPrefix = "k8s-api/v1", k8sNamespace = k8sNamespace, annotations = annotations)
}


data class ClusterInstanceDto(
        val clusterId: String,
        val clusterName: String,
        val clusterProvider: String,
        val clusterType: String,
        val region: String,
        val status: String,
        val site: String? = "",
        val annotations: String? = null,
        val useType: String,
        val operator: String
) {
        fun validate(){
                ParameterChecker.isNotBlank(
                        mapOf(
                                "clusterId" to clusterId,
                                "clusterName" to clusterName,
                                "clusterProvider" to clusterProvider,
                                "clusterType" to clusterType,
                                "region" to region,
                                "status" to status,
                                "useType" to useType
                        ), "ClusterInstanceDto"
                )
                ClusterProfileUseTypeEnum.valueOf(useType)
        }
}