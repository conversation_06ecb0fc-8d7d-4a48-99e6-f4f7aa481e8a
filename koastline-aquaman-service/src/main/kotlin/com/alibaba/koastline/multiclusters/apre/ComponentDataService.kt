package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.apre.model.ComponentDataDO
import com.alibaba.koastline.multiclusters.apre.params.checkComponentRefObject
import com.alibaba.koastline.multiclusters.common.exceptions.ComponentDataUniqueExistException
import com.alibaba.koastline.multiclusters.common.exceptions.CreateDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ModifyDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ParamMustNotBeNullException
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.ComponentRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ComponentData
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.*

@Component
class ComponentDataService(val objectMapper: ObjectMapper) {
    @Autowired
    lateinit var componentRepo: ComponentRepo

    fun createComponentData(componentDataDO: ComponentDataDO): ComponentDataDO {
        val now = Date(Instant.now().toEpochMilli())
        componentRepo.findByRefAndCode(componentDataDO.refObjectType,
            componentDataDO.refObjectId, componentDataDO.code) ?.let {
            throw ComponentDataUniqueExistException(componentDataDO.code, componentDataDO.refObjectType, componentDataDO.refObjectId)
        }
        val componentData = ComponentData(
            null,
            componentDataDO.code.toNullIfBlank() ?:let { throw ParamMustNotBeNullException("code") },
            componentDataDO.refObjectId,
            checkComponentRefObject(componentDataDO.refObjectType),
            objectMapper.writeValueAsString(componentDataDO.annotations),
            now,
            now,
            IS_NOT_DELETED
        )
        componentRepo.insert(
            componentData
        ).let {
            if (it == 0) throw CreateDataException(componentData)
        }
        return getComponentDataById(componentData.id!!)!!
    }

    fun updateComponentData(id: Long,annotations: Map<String, String>) {
        componentRepo.updateById(id, objectMapper.writeValueAsString(annotations)).let {
            if (it == 0) throw ModifyDataException("id = $id", annotations)
        }
    }

    fun deleteComponentDataById(id: Long) {
        componentRepo.deleteById(id)
    }

    fun getComponentDataById(id: Long): ComponentDataDO? {
        return componentRepo.findById(id) ?.run {
            convert(this)
        }
    }

    fun listComponentDataByRef(refObjectType: String, refObjectId: String): List<ComponentDataDO> {
        val componentDataDOList = mutableListOf<ComponentDataDO>()
        componentRepo.findByRef(refObjectType, refObjectId).forEach {
            componentDataDOList.add(
                convert(it)
            )
        }
        return componentDataDOList
    }

    fun listComponentDataByRefAndCode(refObjectType: String, refObjectId: String, codeList: List<String>): List<ComponentDataDO> {
        val componentDataDOList = mutableListOf<ComponentDataDO>()
        if (codeList.isEmpty()) {
            return componentDataDOList
        }
        componentRepo.findByRefAndCodeList(refObjectType, refObjectId, codeList).forEach {
            componentDataDOList.add(
                convert(it)
            )
        }
        return componentDataDOList
    }

    private fun convert(componentData: ComponentData): ComponentDataDO {
        return ComponentDataDO(
            componentData.id,
            componentData.code,
            componentData.refObjectId,
            componentData.refObjectType,
            objectMapper.readValue<Map<String,String>>(componentData.annotations),
            componentData.gmtCreate,
            componentData.gmtModified,
            componentData.isDeleted
        )
    }
}