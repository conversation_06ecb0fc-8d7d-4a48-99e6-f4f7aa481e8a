package com.alibaba.koastline.multiclusters.fed

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.FedPolicyException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.dao.fed.FedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureImportRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import com.alibaba.koastline.multiclusters.external.FedConfigApi
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyDetailsPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.model.FeatureImportPatchDO
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolExtEnum
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class FedClusterAssistant @Autowired constructor(
    val fedClusterRepo: FedClusterRepo,
    val matchScopeService: MatchScopeService,
    val resourceObjectFeatureImportRepo: ResourceObjectFeatureImportRepo,
    val fedConfigApi: FedConfigApi,
    val resourceObjectFeatureService: ResourceObjectFeatureService
) {

    val log by logger()

    fun listFedClusterByProperties(
        pageNumber: Int, pageSize: Int,
        envNameKeyWords: String? = null,
        region: String? = null,
        status: String? = null,
        ids: List<Long>? = null
    ): PageData<FedCluster> {
        val page: Page<FedCluster> =
            PageHelper.startPage<FedCluster>(
                pageNumber,
                pageSize,
                "gmt_modified DESC"
            )
                .doSelectPage {
                    fedClusterRepo.listByProperties(
                        keyWords = envNameKeyWords,
                        region = region,
                        status = status,
                        ids = ids?.ifEmpty { null },
                    )
                }
        return PageData.transformFrom(page)
    }

    fun updateFedTarget(
        fedCluster: FedCluster,
        modifier: String,
        updateParams: Map<String, Any>,
        updateCondition: (Map<String, Any>) -> Boolean,
    ) {
        val targetInputParamId = matchScopeService.listByTargetTypeAndExternal(
            externalId = fedCluster.id.toString(),
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
            targetType = MatchScopeTargetTypeEnum.FedTarget.name
        ).map {
            it.targetId!!
        }.first()

        val featureImport = checkNotNull(resourceObjectFeatureImportRepo.findById(targetInputParamId)) {
            "featureImport by id:${targetInputParamId} not found!"
        }
        val params = YamlUtils.load(featureImport.paramMap!!)
        if (updateCondition(params)) {
            val finalParamsYaml = YamlUtils.dump(
                mutableMapOf<String, Any>().let {
                    it.putAll(params)
                    it.putAll(updateParams)
                    it
                }
            )
            resourceObjectFeatureImportRepo.updateById(
                id = targetInputParamId,
                modifier = modifier,
                paramMap = finalParamsYaml,
                featureImport.status,
                featureImport.version
            )
            val fedTargetCrd = assembleCrd(
                inputParamsIds = listOf(checkNotNull(featureImport.id) {
                    "id cannot be null at existed data!"
                }),
            )
            log.info("update fed policy with fedCluster:$fedCluster")
            fedConfigApi.createOrUpdateFedTarget(fedTargetCrd.first().patch)
        }
    }

    fun assembleCrd(
        inputParamsIds: List<Long>,
        version: String? = null,
        systemInputParams: Map<String, Any> = emptyMap(),
        mapFeatureImportParamMap: ((List<ResourceObjectFeatureImport>) -> List<ResourceObjectFeatureImport>)? = null
    ): List<FeatureImportPatchDO> {
        val importParams = if (inputParamsIds.isEmpty()) {
            emptyList()
        } else {
            val originalFeatureImports = resourceObjectFeatureImportRepo.findByIdList(inputParamsIds)
            if(mapFeatureImportParamMap != null){
                mapFeatureImportParamMap(originalFeatureImports)
            }else {
                originalFeatureImports
            }
        }

        return resourceObjectFeatureService.assembleSpecByFeatureImport(
            resourceObjectFeatureImportList = importParams,
            protocol = ResourceObjectProtocolExtEnum.FedCluster.name,
            version = version, systemInputParams = systemInputParams,
            //注入处理的函数
            extraTemplateFunc = mapOf("FreemarkerUtils" to FreemarkerUtils)
        )
    }


}