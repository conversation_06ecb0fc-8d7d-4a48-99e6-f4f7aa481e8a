package com.alibaba.koastline.multiclusters.resourceobj.params

enum class UserLabelType (
    val code: String
) {
    /**
     * 资源规格，形如4-8-60
     */
    MODEL(code = "model"),
    /**
     * GPU数量
     */
    GPU_COUNT(code = "gpuCount"),
    /**
     * 操作系统
     */
    OS(code = "os"),
    /**
     * 模板
     */
    TEMPLATE(code = "template"),
    /**
     * 是否启动
     */
    STARTUP(code = "startup"),

    /**
     ********** 业务属性 ************
     */
    /**
     * 是否启动
     */
    SCENE(code = "scene"),

    /**
     * 多云账号uid
     */
    N_CLOUD_ACCOUNT("nCloudAccount"),

    /**
     * 多云 vSwitch tag
     */
    N_CLOUD_VSW_TAG("nCloudVswTag"),
}

inline fun toUserLabelType(code: String): UserLabelType? {
    return UserLabelType.values().firstOrNull {
        it.code == code
    }
}

enum class UserLabelExternalType {
    /**
     * 产品线
     */
    AONE_PRODUCTLINE,
    /**
     * 应用
     */
    APPLICATION,
    /**
     * 分组
     */
    RESOURCE_GROUP,
    /**
     * 环境
     */
    ENV_STACK,
    /**
     * GPU
     */
    GPU_PRODUCT,
    /**
     * 资源账号
     */
    RESOURCE_ACCOUNT,
}

const val HALO_RUNTIME_TYPE_LABEL_VALUE: String = "HALO"
const val SCHEDULE_ENV_TYPE_LABEL_NAME: String = "SCHEDULE_ENV_TYPE"