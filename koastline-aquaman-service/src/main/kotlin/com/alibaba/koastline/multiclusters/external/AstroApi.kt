package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.AstroException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.SignUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.AppUserLabel
import com.alibaba.koastline.multiclusters.external.model.AstroAppUserLabelQuery
import com.alibaba.koastline.multiclusters.external.model.AstroResult
import com.alibaba.koastline.multiclusters.external.model.CommonQueryResult
import com.alibaba.koastline.multiclusters.external.params.AppUserLabelNameEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class AstroApi(val objectMapper: ObjectMapper) {
    @Value("\${astro.host}")
    lateinit var astroHost: String
    @Value("\${astro.account}")
    lateinit var astroAccount: String
    @Value("\${astro.access.key}")
    lateinit var astroAccessKey: String

    @ExternalCall(SYS_CALLED)
    fun getResourceSpec(appName: String): ResourceSpec {
        val url = "${astroHost}/openapi/${astroAccount}/astroCommonQuery.json"
        val params = mapOf(
            "param" to objectMapper.writeValueAsString(AstroAppUserLabelQuery(condition = "appName=${appName}")),
            "_sign" to SignUtils.getAstroToken(astroAccessKey)
        )
        val rs = objectMapper.readValue<AstroResult<CommonQueryResult<AppUserLabel>>>(HttpClientUtils.httpGet(url, params))
        if (!rs.success) {
            throw AstroException("call astro error,msg:${rs.message},url:${url},params:${params}")
        }
        rs.data ?.result ?.let { appUserLabels ->
            appUserLabels.firstOrNull {
                it.labelName == AppUserLabelNameEnum.model.name
            } ?.let { modelLabel ->
                val modelProperties = modelLabel.labelValue.split("-")
                appUserLabels.firstOrNull { gpuLabel ->
                    gpuLabel.labelName == AppUserLabelNameEnum.gpuCount.name
                } ?.let {
                    return ResourceSpec(modelProperties[0], "${modelProperties[1]}", "${modelProperties[2]}", it.labelValue)
                } ?:let {
                    return ResourceSpec(modelProperties[0], "${modelProperties[1]}", "${modelProperties[2]}")
                }
            }
        }
        throw AstroException("未找到应用：${appName}规格.")
    }

    companion object {
        const val SYS_CALLED = "astro"
    }
}