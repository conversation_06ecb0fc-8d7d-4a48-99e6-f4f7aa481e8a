package com.alibaba.koastline.multiclusters.schedule.exception

import com.alibaba.koastline.multiclusters.common.exceptions.BizException

/**
 * <AUTHOR>
 */
class ScheduleException(
    errorMsg: String? = null,
    errorCode: Int? = BASE
): BizException(errorMsg, errorCode) {
    companion object{
        const val BASE = 1000
        const val INVALID_PARAM = 1001
        const val DO_NOT_FOUND_CLUSTER_ROUTER = 1002
        const val SCHEDULE_RESULT_IS_WRONG = 1003
        const val SCHEDULE_COMPUTE_DECLARATION_EXPECTED_REPLICAS_IS_WRONG = 1004

        const val SCHEDULE_SCALE_IN_REPLICAS_MORE_THAN_RUNNING_NUM = 2001
        const val SCHEDULE_SCALE_IN_RUNNING_RESOURCE_MATCH_DECLARATION_ERROR = 2002
        const val SCHEDULE_SCALE_IN_REPLICAS_MATCH_DECLARATION_ERROR = 2003
    }
}

class ScheduleFilterException(
    errorMsg: String? = null,
    errorCode: Int
): BizException(errorMsg, errorCode) {
    companion object{
        const val RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE = 2000
    }
}
