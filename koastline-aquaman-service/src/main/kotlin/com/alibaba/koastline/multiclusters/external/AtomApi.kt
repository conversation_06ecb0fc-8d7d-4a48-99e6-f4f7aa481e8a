package com.alibaba.koastline.multiclusters.external

import com.alibaba.atomcore.facade.gamma.AstroLabelApi
import com.alibaba.atomcore.facade.gamma.label.SigmaConfigMapApi
import com.alibaba.atomcore.facade.gamma.strategy.StrategyApi
import com.alibaba.atomcore.facade.param.StrategyParam
import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.common.exceptions.CallExternalSysException
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class AtomApi {
    @Autowired
    lateinit var strategyApi: StrategyApi

    @Autowired
    lateinit var astroLabelApi: AstroLabelApi

    @Autowired
    lateinit var sigmaConfigMapApi: SigmaConfigMapApi

    @ExternalCall(SYS_CALLED)
    fun getBaselineSpec(appName: String, resourceGroup: String?, site: String?, unit: String?): ResourceSpec {
        val strategyParam = StrategyParam().apply {
            this.app = appName
            this.nodeGroup = resourceGroup
            this.site = site
            this.cell = unit
        }
        val resp = strategyApi.queryStrategy(strategyParam)
        if (!resp.isSuccess) {
            throw CallExternalSysException(SYS_CALLED, resp.message, null)
        }
        val modelProperties = resp.data.model.split("-")
        return ResourceSpec(
            modelProperties[0],
            modelProperties[1],
            modelProperties[2],
            resp.data.gpuCount
        )
    }

    @ExternalCall(AstroApi.SYS_CALLED)
    fun updateAppLabel(appName: String, label: String, value: String?) {
        astroLabelApi.updateAppLabel(appName, label, value)
    }

    @ExternalCall(AstroApi.SYS_CALLED)
    fun queryAppLabel(appName: String): List<Map<String, Any>> {
        val resp = astroLabelApi.queryAppLabel(appName)
        if (!resp.isSuccess) {
            throw CallExternalSysException(SYS_CALLED, resp.message, null)
        }
        return resp.data
    }

    @ExternalCall(SYS_CALLED)
    fun getSigmaConfigMap(appName: String, groupName: String?, idc: String?, unit: String?, env: String?): String {
        val resp = sigmaConfigMapApi.getSigmaConfigMap(
            appName,
            groupName,
            idc,
            unit,
            env
        )
        if (!resp.isSuccess) {
            throw CallExternalSysException(SYS_CALLED, resp.message, null)
        }
        return resp.data
    }

    @ExternalCall(SYS_CALLED)
    fun queryStrategy(
        strategyParam: StrategyParam
    ): StrategiesResultVO {
        val resp = strategyApi.queryStrategy(strategyParam)
        if (!resp.isSuccess) {
            throw CallExternalSysException(SYS_CALLED, resp.message, null)
        }
        return resp.data
    }

    companion object {
        const val SYS_CALLED = "atom"
    }
}