package com.alibaba.koastline.multiclusters.schedule.service

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.stereotype.Component

@Component
class ScheduleFilterServiceFactory {
    
    internal fun registryScheduleFilterService(scheduleFilterFacade: ScheduleFilterFacade) {
        scheduleFilterFacadeMap[scheduleFilterFacade.javaClass.name] = scheduleFilterFacade
    }

    fun getScheduleFilterServiceBuilder(): ScheduleFilterServiceBuilder {
        return ScheduleFilterServiceBuilder(this)
    }

    fun getScheduleFilterFacade(scheduleFilterFacadeClass: Class<out ScheduleFilterFacade>): ScheduleFilterFacade {
        return scheduleFilterFacadeMap[scheduleFilterFacadeClass.name]!!
    }

    companion object {
        val scheduleFilterFacadeMap = mutableMapOf<String, ScheduleFilterFacade>()
    }

    class ScheduleFilterServiceBuilder(private val scheduleFilterServiceFactory: ScheduleFilterServiceFactory) {
        private val filterProcessorList = mutableListOf<ScheduleFilterFacade>()

        fun addFilterProcessor(scheduleFilterFacadeClass: Class<out ScheduleFilterFacade>): ScheduleFilterServiceBuilder {
            filterProcessorList.add(scheduleFilterServiceFactory.getScheduleFilterFacade(scheduleFilterFacadeClass))
            return this
        }

        fun doScheduleFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
            if (filterProcessorList.isEmpty()) {
                return matchDeclaration
            }
            var filterMatchDeclaration = matchDeclaration
            filterProcessorList.forEach {
                filterMatchDeclaration = it.doScheduleFilter(filterMatchDeclaration, content)
            }
            return filterMatchDeclaration
        }
    }
}