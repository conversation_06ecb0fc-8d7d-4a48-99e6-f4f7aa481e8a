package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import okhttp3.OkHttpClient
import java.io.File

object ApREResourceInitTool {
    val objectMapper = ObjectMapper()
    val client: OkHttpClient = OkHttpClient().newBuilder().build()
    private fun loadSiteRegionMappingData():Map<String, String> {
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("init/region_site_mapping_data.txt")!!.file
        val lines: List<String> = File(file).readLines()
        val map = mutableMapOf<String, String>()
        lines.forEach { line ->
            line.split("\t").let { data ->
                map.put(data[1], data[0])
            }
        }
        return map
    }
    fun init() {
        val userName = "admin"
        val secret = "test123"
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("init/apre_resource_init_data.txt")!!.file
        val lines: List<String> = File(file).readLines()
        val siteRegionMapping = loadSiteRegionMappingData()
        lines.forEach { line ->
            line.split("\t").let { data ->
                /**
                 * 样例：
                 * na610	PUBLISH	CENTER_UNIT.center	asi_zjk_core_a02$232850,asi_zjk_pai_b$2698,asi_zjk_mvap_a$3346,asi_zjk_core_a01$80008,asi_zjk_cse_a$4538,asi_zjk_shenma_b$11922,asi_zjk_dsw_b01$14483	349845
                 */
                val site = data[0]
                val stage = data[1]
                val unit = data[2]
                val region = siteRegionMapping[site] ?:let {
                    throw RuntimeException("${site}找不到region")
                }
                var clusterNames = data[3].split(",").map {
                    it.split("$")[0]
                }
                println("$site,$stage,$unit,$clusterNames")
                var apREDOListData = objectMapper.readValue<Map<String,Any>>(HttpClientUtils.httpPost(
                    "https://pre-aquaman.koastline.alibaba-inc.com/apis/apre/v4/listApREByMetadataConstraint", mapOf(
                        "region" to region,
                        "az" to site,
                        "stage" to stage,
                        "unit" to unit
                    ), AuthorizationAttribute(
                        userName,
                        secret
                    )
                ))
                val managedClusterKeys = (apREDOListData["data"] as List<Map<String,String>>).map {
                    it["managedClusterKey"]
                }
                if (managedClusterKeys.size != 1) {
                    if (managedClusterKeys.size == 0) {
                        print("未找到匹配的ApRE,忽略")
                        return@forEach
                    }
                    throw RuntimeException("apre num error. size = ${managedClusterKeys.size}")
                }
                clusterNames.forEach { clusterName ->
                    print("$region,$site,$stage,$unit,$clusterName")
                    println(
                        HttpClientUtils.httpPost(
                            "https://pre-aquaman.koastline.alibaba-inc.com/apis/apre/v4/resourcePool/createWithClusterName",
                            mapOf(
                                "managedClusterKey" to managedClusterKeys[0]!!,
                                "clusterName" to clusterName
                            ),
                            AuthorizationAttribute(
                                "admin",
                                "WUTALLdbmMnEelB6"
                            )
                        )
                    )
                }
            }
        }
        println("finish init data.")
    }
}

fun main(args: Array<String>) {
    ApREResourceInitTool.init()
}