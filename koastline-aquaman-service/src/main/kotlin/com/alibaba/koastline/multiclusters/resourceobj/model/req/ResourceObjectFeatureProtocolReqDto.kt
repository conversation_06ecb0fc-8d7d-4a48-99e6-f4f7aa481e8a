package com.alibaba.koastline.multiclusters.resourceobj.model.req

import com.alibaba.koastline.multiclusters.data.vo.env.DEFAULT_VERSION
import com.alibaba.koastline.multiclusters.resourceobj.model.PatchStrategyDefinition
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolExtEnum
import io.swagger.annotations.ApiModelProperty

data class ResourceObjectFeatureProtocolCreateReqDto(
    @ApiModelProperty("资源对象特性Key", required = true)
    val resourceObjectFeatureKey: String,
    @ApiModelProperty("协议", required = true)
    val protocol: ResourceObjectProtocolEnum,
    @ApiModelProperty("模板", required = true)
    val patch: String,
    @ApiModelProperty("创建人", required = true)
    val creator: String,
    @ApiModelProperty("策略", required = false)
    val strategy: PatchStrategyDefinition? = null,
    @ApiModelProperty("版本号", required = false)
    val version: String = DEFAULT_VERSION,
)

data class ResourceObjectFeatureProtocolUpdateReqDto(
    val id: Long,
    @ApiModelProperty("模板", required = true)
    val patch: String,
    @ApiModelProperty("修改人", required = true)
    val modifier: String,
    @ApiModelProperty("策略", required = false)
    val strategy: PatchStrategyDefinition? = null,
)

data class ResourceObjectFeatureProtocolExtCreateReqDto(
    @ApiModelProperty("资源对象特性Key", required = true)
    val resourceObjectFeatureKey: String,
    @ApiModelProperty("协议", required = true)
    val protocol: ResourceObjectProtocolExtEnum,
    @ApiModelProperty("模板", required = true)
    val patch: String,
    @ApiModelProperty("创建人", required = true)
    val creator: String,
    @ApiModelProperty("策略", required = false)
    val strategy: PatchStrategyDefinition? = null
)