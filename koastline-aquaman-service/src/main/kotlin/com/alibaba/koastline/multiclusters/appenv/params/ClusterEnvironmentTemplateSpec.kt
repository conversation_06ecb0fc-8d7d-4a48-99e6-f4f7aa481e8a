package com.alibaba.koastline.multiclusters.appenv.params

import com.alibaba.koastline.multiclusters.appenv.model.GatewayConfig

/**
 * <AUTHOR>
 * initializes a set of cluster environments according to envLevels and default environment meta data;
 * envLevels are a set of predefined devops stage, such as daily, gray, spe, production and so on.
 */
data class ClusterEnvironmentTemplateSpec(
        val envLevels: MutableSet<EnvLevel>,
        val defaultClusterEnvironmentsMeta: EnvironmentSelectorMeta
)

data class ManagedClusterWithEnvironmentsTemplateSpec(
        val clusterId: String,
        val clusterName: String?=null,
        val region: String,
        val namespace: String,
        val gatewayConfig: GatewayConfig,
        val clusterEnvironmentTemplateSpec: ClusterEnvironmentTemplateSpec
)

data class EnvironmentSelectorMeta(
        val region: String,
        val az: String,
        val envMeta: MutableMap<String, String>
)

enum class EnvLevel(val formalName: String) {
    Daily("daily"),
    Staging("staging"), 
    Production("production"), 
    Project("project"), 
    Spe("spe"), 
    Gray("gray"), 
    IsolationStaging("isolation-staging"), 
    TestingTrunk("test-trunk")
}