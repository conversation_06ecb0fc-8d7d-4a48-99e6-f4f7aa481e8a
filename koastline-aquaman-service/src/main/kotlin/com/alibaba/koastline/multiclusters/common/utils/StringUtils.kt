package com.alibaba.koastline.multiclusters.common.utils

import java.math.BigInteger
import java.security.MessageDigest

inline fun String.toNullIfEmpty() = this.ifEmpty { null }

inline fun String.toNullIfBlank() = this.ifBlank { null }

inline fun String?.toBlankIfNull() = this ?: ""

object ParameterChecker {
    fun isNotBlank(params: Map<String, Any>, clazz: String) {
        params.forEach { (key, value) ->
            if (value is String) {
                require(value.isNotBlank()) { "$key should not be blank in $clazz" }
            }
        }
    }
}

inline fun String.md5() = this.run {
    val md = MessageDigest.getInstance("MD5")
    val digest = md.digest(this.toByteArray(Charsets.UTF_8))
    BigInteger(1, digest).toString(16).padStart(32, '0')
}