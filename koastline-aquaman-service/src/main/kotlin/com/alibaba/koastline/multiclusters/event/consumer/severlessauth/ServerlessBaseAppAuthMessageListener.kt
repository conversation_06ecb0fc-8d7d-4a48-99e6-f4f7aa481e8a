package com.alibaba.koastline.multiclusters.event.consumer.severlessauth

import com.alibaba.koastline.multiclusters.apre.AoneServerlessAuthResonate
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.rocketmq.client.consumer.listener.ConsumeOrderlyContext
import com.alibaba.rocketmq.client.consumer.listener.ConsumeOrderlyStatus
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerOrderly
import com.alibaba.rocketmq.common.message.MessageExt
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class ServerlessBaseAppAuthMessageListener @Autowired constructor(
    val aoneServerlessAuthResonate: AoneServerlessAuthResonate
) : MessageListenerOrderly {
    val log by logger()

    override fun consumeMessage(msgs: MutableList<MessageExt>, context: ConsumeOrderlyContext): ConsumeOrderlyStatus {
        log.info("Receive appCenter new event: $msgs")
        msgs.forEach { messageExt ->
            processMessageExt(messageExt)
        }
        return ConsumeOrderlyStatus.SUCCESS
    }

    private fun processMessageExt(messageExt: MessageExt) {
        try {
            val admissionChangeOrder = JsonUtils.readValue(String(messageExt.body), AdmissionChangeRequest::class.java)
            aoneServerlessAuthResonate.resonateAoneServerlessAuth(admissionChangeOrder)
        } catch (e: Exception) {
            log.error("AppCenter event consumer with error, msg:${e.message}", e)
            throw e
        }
    }
}