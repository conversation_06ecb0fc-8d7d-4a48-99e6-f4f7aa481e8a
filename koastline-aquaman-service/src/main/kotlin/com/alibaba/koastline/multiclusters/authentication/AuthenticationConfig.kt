package com.alibaba.koastline.multiclusters.authentication

import com.alibaba.koastline.multiclusters.authentication.models.UserAuthenticationDetailService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.NoOpPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder

/**
 * <AUTHOR>  
 */
@Configuration
@Order(value = Ordered.HIGHEST_PRECEDENCE)
class AuthenticationConfig : WebSecurityConfigurerAdapter() {
    @Autowired
    fun passwordEncoder(): PasswordEncoder = NoOpPasswordEncoder.getInstance()

    @Autowired
    lateinit var aquamanUserDetailsService: UserAuthenticationDetailService

    override fun configure(http: HttpSecurity) {
        http.regexMatcher(".*/api(s)?/.*").sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .and().csrf().disable()
                .authorizeRequests().anyRequest().authenticated()
                .and().httpBasic()
    }

    override fun configure(auth: AuthenticationManagerBuilder) {
        auth.run { userDetailsService(aquamanUserDetailsService).passwordEncoder(passwordEncoder())
        }
    }
}