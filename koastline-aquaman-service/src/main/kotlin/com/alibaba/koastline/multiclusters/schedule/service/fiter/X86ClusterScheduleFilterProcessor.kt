package com.alibaba.koastline.multiclusters.schedule.service.fiter

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.AbstractScheduleFilterFacade
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 扩容调度计算服务
 * 功能：
 * 缺省调度X86集群
 */
@Component
class X86ClusterScheduleFilterProcessor : AbstractScheduleFilterFacade(), ScheduleFilterFacade, InitializingBean {
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory

    override fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
        if (matchDeclaration.apres.isNullOrEmpty()) {
            return matchDeclaration
        }
        matchDeclaration.apres.forEach {apRE ->
            apRE.resources = apRE.resources.filter { resource ->
                !isArmCluster(resource)
            }
        }
        return matchDeclaration
    }

    private fun isArmCluster(resource: ResourceDO): Boolean {
        return resource.apRELabels.firstOrNull {apRELabel ->
            apRELabel.name == ApRELabelExt.APRE_LABEL_NAME_CUSTOMER_MACHINE_TYPE && apRELabel.value == ApRELabelExt.APRE_LABEL_VALUE_ARM
        }?.let { true } ?: false
    }

    override fun afterPropertiesSet() {
        scheduleFilterServiceFactory.registryScheduleFilterService(this)
    }
}