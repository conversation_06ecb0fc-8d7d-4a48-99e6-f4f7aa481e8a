package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchApRELabel
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataOfSiteRepo
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractNonOrientedScaleInScheduleRefactor
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component(value = "NonDeclarativeNonOrientedScaleInScheduleService")
class NonDeclarativeNonOrientedScaleInScheduleService @Autowired constructor(
    var scheduleServiceFactory: ScheduleServiceFactory,
    var apREService: ApREService,
    var metadataOfSiteRepo: MetadataOfSiteRepo
) : AbstractNonOrientedScaleInScheduleRefactor(), InitializingBean {
    override fun getApREDeed(content: ScheduleRequestContent): ApREDeedDO {
        return ApREDeedDO(
            identityInfo = IdentityInfo(
                envLevel = content.declarationData!!.declaration!!.stage,
                appName = content.resourceScope.appName,
                envStackId = content.resourceScope.envStackId,
                nodeGroup = content.resourceScope.resourceGroup
            ),
            declarations = mutableListOf(
                Declaration(
                    region = content.declarationData!!.declaration!!.region ?.apply {
                        metadataOfSiteRepo.findBySite(content.declarationData.declaration!!.site)!!.region
                    },
                    az = content.declarationData.declaration!!.site,
                    unit = content.declarationData.declaration.unit,
                    stage = content.declarationData.declaration.stage,
                    matchApRELabels = if (checkNotNull(content.scheduleRequestParam).serverless) {
                        scheduleStandardService.getServerlessMatchApRELabelList(
                            serverlessRuntimeTemplate = checkNotNull(content.scheduleRequestParam.serverlessRuntimeTemplate),
                            resourceGroup = content.resourceScope.resourceGroup
                        )
                    } else {
                        content.declarationData.declaration.matchApRELabels ?.map {
                            MatchApRELabel(name = it.name, value = it.value)
                        }
                    }
                )
            )
        )
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.NON_DECLARATIVE_NON_ORIENTED_SCALE_IN_SCHEDULE, this)
    }

    override fun isDeclarative(): Boolean {
        return false
    }
}