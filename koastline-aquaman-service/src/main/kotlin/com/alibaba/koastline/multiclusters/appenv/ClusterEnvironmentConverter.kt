package com.alibaba.koastline.multiclusters.appenv

import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentData
import com.fasterxml.jackson.module.kotlin.readValue

/**
 * <AUTHOR>
 */
object ClusterEnvironmentConverter {

    private var objectMapper = ObjectMapperFactory.newTolerant()

    fun toClusterEnvironment(clusterEnvironmentData: ClusterEnvironmentData, clusterId: String, clusterName: String, clusterProvider: String, clusterType: String): ClusterEnvironment {
        val envTags = objectMapper.readValue<MutableMap<String, String>>(clusterEnvironmentData.envLabels)
        return ClusterEnvironment(
                clusterEnvironmentData.clusterEnvKey!!,
                "",
                "",
                clusterEnvironmentData.region,
                clusterEnvironmentData.az,
                envTags,
                clusterId,
                clusterName
        )
    }
}