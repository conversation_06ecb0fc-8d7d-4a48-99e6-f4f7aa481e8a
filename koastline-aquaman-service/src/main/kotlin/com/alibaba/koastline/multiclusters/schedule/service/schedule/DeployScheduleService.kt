package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.StackServerlessBaseAppBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypePriority
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.TESTING_ENV_COMMON_APP_NAME
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostResourceScopeService
import com.alibaba.koastline.multiclusters.runtime.RuntimeService
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.DeclarationData
import com.alibaba.koastline.multiclusters.schedule.model.OrientedDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.RUNTIME
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeclarativeScaleOutScheduleService.Companion.RESOURCE_GROUP_USAGE_TYPE_DAILY
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.ScheduleFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component(value = "DeployScheduleService")
class DeployScheduleService : ScheduleFacade, InitializingBean {
    @Autowired
    lateinit var scheduleServiceFactory: ScheduleServiceFactory
    @Autowired
    lateinit var runningStateScheduleService: RunningStateScheduleService
    @Autowired
    lateinit var declarativeScaleOutScheduleService: DeclarativeScaleOutScheduleService
    @Autowired
    lateinit var nonDeclarativeScaleOutScheduleService: NonDeclarativeScaleOutScheduleService
    @Autowired
    lateinit var skylineApi: SkylineApi
    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService
    @Autowired
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService
    @Autowired
    lateinit var apREService: ApREService
    @Autowired
    lateinit var envLevelStageMappingService: EnvLevelStageMappingService
    @Autowired
    lateinit var resourcePoolService: ResourcePoolService
    @Autowired
    lateinit var cloudCmdbApi: CloudCmdbApi
    @Autowired
    lateinit var userLabelBaseService: UserLabelBaseService
    @Autowired
    lateinit var stackServerlessBaseAppBindingService: StackServerlessBaseAppBindingService
    @Autowired
    lateinit var envHostResourceScopeService: EnvHostResourceScopeService
    @Autowired
    lateinit var deployHostScheduleService: DeployHostScheduleService

    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        //主机发布调度服务
        content.resourceScope.envStackId?.let {
            envHostResourceScopeService.findByCurrentEnvStackId(it)?.let {
                return deployHostScheduleService.doSchedule(content)
            }
        }
        val scheduleRequestParam = checkNotNull(content.scheduleRequestParam)
        val scheduleResult = runningStateScheduleService.doSchedule(content)
        if (scheduleRequestParam.scheduleEnvType == ASI ||
                scheduleRequestParam.scheduleEnvType == RUNTIME) {
            return scheduleResult
        }
        if (scheduleRequestParam.scheduleEnvType == SERVERLESS_APP) {
            return computeServerlessScheduleResult(scheduleResult, content)
        }
        throw ScheduleException("未支持的协议:${scheduleRequestParam.scheduleEnvType}")
    }

    /**
     * 计算Serverless调度结果
     */
    private fun computeServerlessScheduleResult(runningScheduleResult: ScheduleResult, content: ScheduleRequestContent): ScheduleResult {
        val resourceGroupList = mutableListOf<String>()
        content.resourceScope.resourceGroup ?.toNullIfBlank() ?.let { resourceGroup ->
            resourceGroupList.add(resourceGroup)
        } ?:let {
            resourceGroupList.addAll(skylineApi.getEnvStackResourceGroupScopeByStackId(content.resourceScope.envStackId!!))
        }
        if (resourceGroupList.isEmpty()) {
            return runningScheduleResult
        }

        //默认每个分组必须有一台Serverless POD
        val scaleOutWorkloadExpectedStates = mutableListOf <WorkloadExpectedState>()
        resourceGroupList.forEach { resourceGroup ->
            runningScheduleResult.workloadExpectedStates.firstOrNull {
                it.workloadMetadataConstraint.resourceGroup == resourceGroup && !it.workloadMetadataConstraint.runtimeId.isNullOrBlank()
            } ?:let {
                if(isServerlessEnv(content.resourceScope.envStackId)) {
                    //分组下未有serverless pod，则扩容一台
                    computeServerlessScaleOut(content, resourceGroup, runningScheduleResult.workloadExpectedStates).let {
                        scaleOutWorkloadExpectedStates.addAll(it.workloadExpectedStates)
                    }
                }
            }
        }
        return runningScheduleResult.copy(
            workloadExpectedStates = runningScheduleResult.workloadExpectedStates.toMutableList().apply {
                this.addAll(scaleOutWorkloadExpectedStates)
            }
        )
    }

    /**
     * 是否为Serverless环境
     */
    private fun isServerlessEnv(envStackId: String?): Boolean {
        envStackId?.run {
            userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = envStackId,
                externalType = UserLabelExternalType.ENV_STACK.name,
                labelName = "SERVERLESS_STATUS"
            )?.let { return true }
            stackServerlessBaseAppBindingService.getStackServerlessBaseAppBindingData(envStackId)?.let {
                return true
            }
        }
        return false
    }


    /**
     * 判断分组是否存在声明，如存在则走固定声明式扩容；如不存在则走临时声明式扩容；
     * @param content 调度上下文参数
     * @param resourceGroup 分组名
     * @param runningWorkloadExpectedStates 运行态计算workload结果
     * @return serverless扩容调度计算workload结果
     */
    private fun computeServerlessScaleOut(content: ScheduleRequestContent, resourceGroup: String, runningWorkloadExpectedStates: List<WorkloadExpectedState>): ScheduleResult {
        // 从发布版本中获取基座信息
        val serverlessBaseAppInfo = content.scheduleRequestParam ?.envStackPkId ?.run {
            cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(this)
        }
        val serverlessRuntimeTemplate = scheduleStandardService.getServerlessRuntimeTemplate(
            serverlessBaseAppName = content.scheduleRequestParam?.serverlessBaseAppName ?: serverlessBaseAppInfo ?.baseAppName,
            appName = content.resourceScope.appName,
            resourceGroup = resourceGroup,
            envStackId = content.resourceScope.envStackId
        )
        if (hasDeclaration(resourceGroup)) {
            //固定声明式扩容调度
            return declarativeScaleOutScheduleService.doSchedule(content.copy(
                resourceScope = ResourceScope(
                    appName = content.resourceScope.appName,
                    envStackId = content.resourceScope.envStackId,
                    resourceGroup = resourceGroup
                ),
                scheduleRequestParam = checkNotNull(content.scheduleRequestParam).copy(
                    replicas = 1,
                    serverless = true,
                    serverlessRuntimeTemplate = serverlessRuntimeTemplate
                )
            ))
        }
        //如果没有资源且没有分组声明，则返回空调度结果
        if (runningWorkloadExpectedStates.isEmpty()) {
            return ScheduleResult(workloadExpectedStates = emptyList())
        }
        //临时声明式扩容,组装声明契约
        val apREDeed = ApREDeedDO(
            identityInfo = IdentityInfo(
                envLevel = getStandardStage(checkNotNull(content.resourceScope.envStackId){"需要指定环境StackId以计算用途。"}) ,
                appName = content.resourceScope.appName,
                nodeGroup = content.resourceScope.resourceGroup
            ),
            declarations = mutableListOf(
                Declaration(
                    matchApRELabels = scheduleStandardService.getServerlessMatchApRELabelList(
                        serverlessRuntimeTemplate = serverlessRuntimeTemplate,
                        envStackId = content.resourceScope.envStackId
                    )
                )
            )
        )
        val apres = apREService.requireApREDeedResultByApREDeedContent(apREDeed).matchDeclarations[0].apres
        //查找匹配的最大数量Workload的四元组
        selectMaxMatchWorkload(runningWorkloadExpectedStates, apres) ?.let {
            //临时声明式扩容调度
            return nonDeclarativeScaleOutScheduleService.doSchedule(content.copy(
                resourceScope = ResourceScope(
                    appName = content.resourceScope.appName,
                    envStackId = content.resourceScope.envStackId,
                    resourceGroup = resourceGroup
                ),
                declarationData = DeclarationData(
                    declaration = OrientedDeclaration(
                        site = it.workloadMetadataConstraint.site, unit = it.workloadMetadataConstraint.unit, stage = it.workloadMetadataConstraint.stage
                    )
                ),
                scheduleRequestParam = checkNotNull(content.scheduleRequestParam).copy(
                    replicas = 1,
                    serverless = true,
                    serverlessRuntimeTemplate = serverlessRuntimeTemplate
                )
            ))
        } ?:let {
            throw ScheduleException("分组:${resourceGroup}基于运行态资源三元组[机房、用途、单元]:${
                runningWorkloadExpectedStates.map { it.workloadMetadataConstraint.toThreeTuplesMetadataConstraintString() }
            },未找到对应的Runtime:${serverlessRuntimeTemplate},请联系基座应用Owner创建对应Runtime并授权ServerlessApp.")
        }
    }


    private fun hasDeclaration(resourceGroup: String): Boolean {
        if (apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup) != null) {
            return true
        }
        val appGroup = skylineApi.getAppGroup(resourceGroup)
        //测试分组默认有声明
        return appGroup.isTestingAppGroup()
    }

    private fun selectMaxMatchWorkload(runningWorkloadExpectedStates: List<WorkloadExpectedState>, apres: List<ApREDO>): WorkloadExpectedState? {
        return runningWorkloadExpectedStates.filter {
            contains(apres, it.workloadMetadataConstraint)
        }.maxByOrNull {
            it.params[ScheduleResultParamConstants.RESOURCE_NUM]!!.toInt()
        }
    }

    private fun contains(apres: List<ApREDO>, runningWorkload: WorkloadMetadataConstraint): Boolean {
        apres.firstOrNull {
            it.az == runningWorkload.site && it.unit == runningWorkload.unit && it.stage == runningWorkload.stage
        } ?.let {
            return true
        }
        return false
    }

    /**
     * 环境级别做标准化处理
     */
    private fun getStandardStage(envStackId: String): String {
        return envLevelStageMappingService.getStandardEnvLevel(skylineApi.getEnvStackEnvLevelByStackId(envStackId)).run {
            val stageList = envLevelStageMappingService.listStageByEnvLevel(this)
            when(stageList.size) {
                0 -> throw ScheduleException("未配置环境级别[${this}]到用途的映射关系，请联系平台管理员配置。")
                1 -> return stageList[0]
                else -> throw ScheduleException("环境级别[${this}]对应多个用途[${stageList}]映射，请联系平台管理员配置。")
            }
        }
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.SERVERLESS_MIX_DEPLOY_SCHEDULE, this)
    }
}