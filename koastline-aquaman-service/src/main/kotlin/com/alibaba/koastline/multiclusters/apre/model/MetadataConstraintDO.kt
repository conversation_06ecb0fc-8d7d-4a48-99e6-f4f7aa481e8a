package com.alibaba.koastline.multiclusters.apre.model

import com.fasterxml.jackson.annotation.JsonProperty
import java.util.*

data class MetadataConstraintDO(
    val id: Long?,
    val site: String,
    val unit: String,
    val stage: String,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date?,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String?,
    val creator:String? = null,
    val modifier:String? = null
)
