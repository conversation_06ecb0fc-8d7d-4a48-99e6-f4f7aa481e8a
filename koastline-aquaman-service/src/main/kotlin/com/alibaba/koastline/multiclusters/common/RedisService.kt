package com.alibaba.koastline.multiclusters.common

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.taobao.eagleeye.redis.clients.jedis.Jedis
import com.taobao.eagleeye.redis.clients.jedis.JedisPool
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 */
@Component
class RedisService {
    @Autowired
    lateinit var jedisPool: JedisPool
    @Autowired
    lateinit var commonProperties: CommonProperties

    @ExternalCall(SYS_CALLED)
    fun getValue(key: String): String? {
        if (!commonProperties.keepCache()) {
            return null
        }
        var jedis: Jedis? = null
        try {
            jedis = jedisPool.resource
            return jedis.get(key)
        } finally {
            jedis ?.close()
        }
    }

    @ExternalCall(SYS_CALLED)
    fun setValue(key: String, value: String) {
        if (!commonProperties.keepCache()) {
            return
        }
        var jedis: Jedis? = null
        try {
            jedis = jedisPool.resource
            jedis.set(key, value)
        } finally {
            jedis ?.close()
        }
    }

    @ExternalCall(SYS_CALLED)
    fun deleteValue(key: String) {
        if (!commonProperties.keepCache()) {
            return
        }
        var jedis: Jedis? = null
        try {
            jedis = jedisPool.resource
            jedis.del(key)
        } finally {
            jedis ?.close()
        }
    }
    companion object{
        const val SYS_CALLED = "redis"
    }
}