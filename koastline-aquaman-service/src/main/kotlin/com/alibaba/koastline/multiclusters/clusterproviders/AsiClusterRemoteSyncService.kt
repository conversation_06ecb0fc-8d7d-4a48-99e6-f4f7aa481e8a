package com.alibaba.koastline.multiclusters.clusterproviders

import com.alibaba.koastline.multiclusters.common.logger
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Component
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * <AUTHOR>
 */
@Component
class AsiClusterRemoteSyncService(val objectMapper: ObjectMapper): AbstactClusterRemoteSynService<String, JsonNode?>() {

    val log by logger()

    @Value("\${asiops.endpoint}")
    lateinit var endpoint: String

    @Value("\${asiops.accesskey}")
    lateinit var accesskey: String

    @Value("\${asiops.accesssecret}")
    lateinit var accessSecret: String

    private fun buildHttpRequestUrl(url: String, values: MutableMap<String, String>): String {
        values["accessKeyId"] = accesskey
        values["timestamp"] = getAuthTimestamp()
        val query = buildCanonicalQueryString(values)
        val signature = hmacSha1Str(buildCanonicalQueryString(values), accessSecret)
        values["signature"] = signature
        return "$endpoint$url?$query&signature=$signature"
    }

    private fun buildCanonicalQueryString(values: MutableMap<String, String>): String{
        val sortedValues = values.toSortedMap()
        val paramsList = arrayListOf<String>()
        sortedValues.forEach { key, value ->
            run {
                if (key != "signature") {
                    paramsList.add("$key=$value")
                }
            }
        }
        return paramsList.joinToString(separator = "&")
    }

    private fun getAuthTimestamp(): String {
        return System.currentTimeMillis().toString()
    }

    private fun hmacSha1Str(data: String, key: String): String {
        val signingKey = SecretKeySpec(key.toByteArray(), HMAC_SHA1_ALGORITHM)
        val mac: Mac = Mac.getInstance(HMAC_SHA1_ALGORITHM)
        mac.init(signingKey)
        return toHexString(mac.doFinal(data.toByteArray()))!!
    }

    private fun toHexString(bytes: ByteArray): String? {
        val formatter = Formatter()
        for (b in bytes) {
            formatter.format("%02x", b)
        }
        return formatter.toString()
    }

    fun get(apiPath: String, params: MutableMap<String, String>): JsonNode? {
        val url = buildHttpRequestUrl(apiPath, params)
        val request = HttpEntity<String>(getBasicHttpHeader())
        log.info("the assembled full url: $url")
        val responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, String::class.java)
        return objectMapper.readTree(responseEntity.body)
    }

    companion object {
        const val HMAC_SHA1_ALGORITHM = "HmacSHA1";
    }

}