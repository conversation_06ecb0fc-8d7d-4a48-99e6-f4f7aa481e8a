package com.alibaba.koastline.multiclusters.external.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import org.glassfish.jersey.internal.util.collection.StringIgnoreCaseKeyComparator

data class CmdbCloudResp<T> (
    val success: Boolean,
    val code : Int,
    val msg: String? = null,
    val data: T? = null
)

data class CmdbCloudPageResp<T> (
    val success: Boolean,
    val code : Int,
    val msg: String? = null,
    val data: PageResult<T> ?= null
)

data class PageResult<T>(
    val list: List<T>,
    @JsonProperty("pageInfoVO")
    val pageInfo: PageInfo
)

data class PageInfo(
    val currentPage: Int,
    val pageSite: Int,
    val hasNext: Boolean,
    val total: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CmdbStack(
    val id: String,
    val categoryId: String,
    val name : String,
    val version: Int,
    val state: String,
    val status: String,
    val appName: String,
    val envType: String,
    @JsonProperty("box")
    val boxList: List<CmdbBox>? = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CmdbBox(
    val id: String,
    val categoryId: String,
    val name : String,
    val version: Int,
    val srcId: String?,
    @JsonProperty("services")
    val serviceList: List<CmdbService>? = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CmdbService(
    val id: String,
    val categoryId: String,
    val name: String,
    val version: Int,
    val type: String,
    val targetStatus: String,
    @JsonProperty("attribute")
    val attributeList: List<CmdbAttribute> = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CmdbAttribute(
    val id: String,
    val name: String,
    val type: String,
    val value: String,
    val valueType: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CmdbStackConfig(
    val id: String,
    val categoryId: String,
    val name : String,
    val version: Int,
    val state: String,
    val status: String,
    val appName: String,
    val envType: String,
    val armorySchema: String?,
    val stackVariable: String,
    val attribute: Map<String, List<CmdbAttribute>>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class StackVariable(
    val name: String,
    val type: String? = null, //昆仑存在type为空的脏数据
    val value: String,
    val valueType: String
)

/**
 * 绑定的Serverless基座应用信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ServerlessBaseAppInfo(
    /**
     * 基座应用名
     */
    val baseAppName: String,
    /**
     * 基座环境StackId
     */
    val baseEnvStackId: String
)


