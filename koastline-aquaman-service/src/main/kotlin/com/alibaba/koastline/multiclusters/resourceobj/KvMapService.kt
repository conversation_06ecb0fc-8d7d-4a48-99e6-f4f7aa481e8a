package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.data.dao.resourceobj.KvMapRepo
import com.alibaba.koastline.multiclusters.data.vo.env.KvMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class KvMapService {


    @Autowired
    lateinit var kvMapRepo: KvMapRepo

    fun createKvMapIgnoreWhenExist(kvMap: KvMap) {
        kvMapRepo.findByTypeAndKeyAndValue(
            kvMap.type,
            kvMap.keyName,
            kvMap.value
        ) ?: let {
            kvMapRepo.insert(kvMap)
        }
    }

    fun deleteKvMapByTypeAndKeyAndValue(type: String, keyName: String, value: String) {
        kvMapRepo.findByTypeAndKeyAndValue(
            type,
            keyName,
            value
        ) ?. let {
            kvMapRepo.deleteById(it.id!!)
        }
    }

    fun getValuesAsListByTypeAndKey(type: String, keyName: String): List<String> {
        return kvMapRepo.findByTypeAndKey(
            type,
            keyName,
        ).map {
            it.value
        }
    }


}

enum class KvMapTypeEnum {

    APPNAME_2_GROUPNAME,
}