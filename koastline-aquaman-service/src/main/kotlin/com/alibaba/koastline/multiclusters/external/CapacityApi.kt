package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.formatShortUnit
import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.CapacityException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.SignUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.CapacityResp
import com.alibaba.koastline.multiclusters.external.model.VpaCurrentSpec
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class CapacityApi(val objectMapper: ObjectMapper) {
    val log by logger()
    @Value("\${capacity.host}")
    lateinit var host: String
    @Value("\${capacity.account}")
    lateinit var account: String
    @Value("\${capacity.access.key}")
    lateinit var accessKey: String
    @Autowired
    lateinit var externalCallDowngradeProperties: ExternalCallDowngradeProperties

    /**
     * 查询Vpa-Cpu规格
     * @param cpuBaselineSpec 单位:M
     * @return vpa cpu规格，单位：M
     */
    @ExternalCall(SYS_CALLED)
    fun queryVpaCurrentSpec(appName: String, resourceGroup: String?, site: String?, unit: String?, stage: String?, cpuBaselineSpec: Int) : Int {
        if (externalCallDowngradeProperties.isDowngrade(SYS_CALLED, "queryVpaCurrentSpec")) {
            log.info("$SYS_CALLED queryVpaCurrentSpec downgrade.")
            return cpuBaselineSpec
        }
        val url = "${host}/openapi/${account}/queryVpaCurrentSpec"
        val params = mutableMapOf(
            "appName" to appName,
            "baselineSpec" to JsonUtils.writeValueAsString(
                mapOf( "cpu" to cpuBaselineSpec)
            )
        ).apply {
            resourceGroup ?.toNullIfBlank() ?.let { this["appGroup"] = it }
            site ?.toNullIfBlank() ?.let { this["site"] = it }
            unit ?.toNullIfBlank() ?.let { this["unit"] = formatShortUnit(it) }
            stage ?.toNullIfBlank() ?.let { this["env"] = it }
        }
        addSignToParams(params)
        val rs = HttpClientUtils.httpPost(url, params, null)
        val capacityResp = objectMapper.readValue<CapacityResp<List<VpaCurrentSpec>>>(rs)
        if (!capacityResp.success) {
            throw CapacityException(capacityResp.msg)
        }
        if (capacityResp.data.isNullOrEmpty()) {
            /**
             * 没有设置vpa
             */
            return cpuBaselineSpec
        }
        return capacityResp.data[0]?.spec?.cpu ?:cpuBaselineSpec
    }

    private fun addSignToParams(params: MutableMap<String, String>) {
        val timeStamp = SignUtils.getISOTimestamp();
        // 将时间戳和签名附加到 http 请求参数中
        params["_signature"] = SignUtils.getCapacityToken(account, accessKey, timeStamp, params);
        params["_timestamp"] = timeStamp;
    }

    companion object {
        const val SYS_CALLED = "capacity"
    }
}