package com.alibaba.koastline.multiclusters.authentication.models

import org.springframework.security.core.GrantedAuthority

/**
 * <AUTHOR>
 */
class AquamanAuthority(private val authority: Authority): GrantedAuthority {
    override fun getAuthority(): String {
        return authority.name
    }
}

enum class Authority {
    // admin
    ADMIN_ROOT_RIGHT,
    // platform user
    DEVOPS_SYSTEM_RIGHT,
    // cluster sre
    CLUSTER_SRE_RIGHT,
    // eg. crablet
    CLUSTER_AGENT
}