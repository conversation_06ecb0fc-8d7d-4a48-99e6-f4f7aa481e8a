package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import okhttp3.OkHttpClient
import java.io.File

object MetadataSiteInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()
    fun init() {
        val userName = "admin"
        val secret = "test123"
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("init/region_site_mapping_data.txt")!!.file
        val lines: List<String> = File(file).readLines()

        lines.forEach { line ->
            line.split("\t").let {
                val region = it[0]
                val site = it[1]
                println("$region,$site")
                println(
                    HttpClientUtils.httpPost(
                        "https://pre-aquaman.koastline.alibaba-inc.com/apis/metadata/v4/metadata/site/create",
                        mapOf(
                            "site" to site,
                            "region" to region
                        ),
                        AuthorizationAttribute(
                            userName,
                            secret
                        )
                    )
                )
            }
        }
        println("finish init data.")
    }
}

fun main(args: Array<String>) {
    MetadataSiteInitTool.init()
}