package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.HcrmException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.HcrmApiExt
import com.alibaba.koastline.multiclusters.external.model.HcrmApiResponse
import com.alibaba.koastline.multiclusters.external.model.HcrmUnitInfo
import com.alibaba.koastline.multiclusters.external.model.UnitLocation
import com.alibaba.koastline.multiclusters.external.model.UnitGroup
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * date 2024/4/15 11:53
 */
@Component
class HcrmApi(
    @Value("\${hcrm.host}")
    val host: String,
    @Value("\${hcrm.account}")
    val account: String,
    @Value("\${hcrm.access.key}")
    val accessKey: String,
) {
    val log by logger()

    @ExternalCall(SYS_CALLED)
    fun listUnitizationUnitGroup(appName: String, appGroupName: String): List<UnitGroup> {
        require(appName.isNotBlank()) { "appName must be non-blank" }
        require(appGroupName.isNotBlank()) { "groupName must be non-blank" }
        val rs = HttpClientUtils.httpGetWithHeaders(
            url = host + HCRM_GROP_QUERY_UNIT_INFO_API,
            headers = HcrmApiExt.buildApiHeadersWithSign(account, accessKey),
            params = mapOf(
                "appName" to appName,
                "nodeGroup" to appGroupName
            )
        )
        val apiResponse = JsonUtils.readValue(rs, object : TypeReference<HcrmApiResponse<List<HcrmUnitInfo>>>() {})
        if (!apiResponse.isSuccess()) {
            throw HcrmException("$SYS_CALLED listUnitizationUnitGroup failed, appName:$appName, appGroupName: $appGroupName")
        }
        return apiResponse.data
            ?.filter { VALID_UNIT_STATUS.equals(it.status, ignoreCase = true) }
            ?.map {hcrmUnitInfo ->
            UnitGroup(
                unitType = hcrmUnitInfo.unitType,
                description = hcrmUnitInfo.description!!,
                unitLocationList = hcrmUnitInfo.unitLocation.run {
                    JsonUtils.readValue(this, object : TypeReference<List<UnitLocation>>() {})
                }
            )
        } ?: emptyList()
    }
    @ExternalCall(SYS_CALLED)
    fun listUnitizationUnitLocation(appName: String, appGroupName: String): List<UnitLocation> {
        return listUnitizationUnitGroup(
            appName = appName,
            appGroupName = appGroupName,
        ).map { it.unitLocationList }.flatten()
    }

    companion object {
        const val HCRM_GROP_QUERY_UNIT_INFO_API = "/api/unit/getUnitInfoByAppNameAndGroup"
        const val VALID_UNIT_STATUS = "effected"
        const val SYS_CALLED = "hcrm"
    }
}