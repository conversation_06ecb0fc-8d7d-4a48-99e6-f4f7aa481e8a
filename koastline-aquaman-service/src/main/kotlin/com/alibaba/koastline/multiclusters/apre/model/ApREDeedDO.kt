package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.apre.params.ApREConstants
import com.alibaba.koastline.multiclusters.resourceobj.model.req.TraitModifyReqDto
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModelProperty
import javax.validation.Valid
import javax.validation.constraints.NotBlank

@JsonIgnoreProperties(ignoreUnknown = true)
data class ApREDeedDO(
    @JsonProperty("key")
    var key: String? = null,
    @field: Valid
    @JsonProperty("identityInfo")
    val identityInfo: IdentityInfo? = null,
    @JsonProperty("declarations")
    var declarations: MutableList<Declaration>? = null
) {
    init {
        this.key = if (key.isNullOrBlank()) null else key!!.trim()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class IdentityInfo(
    @field: NotBlank
    @JsonProperty("envLevel")
    val envLevel: String,
    /**
     * 可选，如果产品线不存在，则用应用名反查
     */
    @JsonProperty("productLineIdPath")
    val productLineIdPath: String? = null,
    @JsonProperty("appName")
    val appName: String? = null,
    @JsonProperty("envId")
    val envId: String? = null,
    @JsonProperty("envStackId")
    val envStackId: String? = null,
    @JsonProperty("nodeGroup")
    val nodeGroup: String? = null
){
    fun validate(): IdentityInfo {
        appName ?: checkNotNull(productLineIdPath) { "productLineIdPath must be non-blank while appName is null" }
        return this
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class MixApREDeedDO(
    @field: Valid
    @JsonProperty("identityInfo")
    val identityInfo: ShortIdentityInfo,
    @ApiModelProperty("资源声明，临时声明分组需要填写")
    @JsonProperty("declarations")
    var declarations: MutableList<Declaration> = mutableListOf()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ShortIdentityInfo(
    @ApiModelProperty("环境级别")
    @field: NotBlank
    @JsonProperty("envLevel")
    val envLevel: String,
    @ApiModelProperty("应用名")
    @field: NotBlank
    @JsonProperty("appName")
    val appName: String,
    @ApiModelProperty("分组名")
    @field: NotBlank
    @JsonProperty("nodeGroup")
    val nodeGroup: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Declaration(
    @JsonProperty("id")
    var id: String? = ApREConstants.APRE_DEED_DECLARATION_UNDEFINED_ID,
    @ApiModelProperty("区域")
    @JsonProperty("region")
    val region: String? = null,
    @ApiModelProperty("站点")
    @JsonProperty("az")
    val az: String? = null,
    @ApiModelProperty("用途")
    @JsonProperty("stage")
    val stage: String? = null,
    @ApiModelProperty("单元")
    @JsonProperty("unit")
    val unit: String? = null,
    @ApiModelProperty("特性匹配")
    @JsonProperty("matchApRELabels")
    val matchApRELabels: List<MatchApRELabel>? = null,
    @ApiModelProperty("权重,默认：1")
    @JsonProperty("weight")
    val weight: Int = 1
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MatchApRELabel(
    @ApiModelProperty("特性名称")
    @JsonProperty("name")
    val name: String?,
    /**
     * 匹配的标签值，支持前缀匹配,例如：value="serverless/",可以匹配到"serverless/\*"
     */
    @ApiModelProperty("特性值")
    @JsonProperty("value")
    val value: String?,
    @ApiModelProperty("规格匹配")
    @JsonProperty("matchApREFeatureSpecs")
    val matchApREFeatureSpecs: List<MatchApREFeatureSpec>? = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MatchApREFeatureSpec(
    @ApiModelProperty("规格ID")
    @JsonProperty("id")
    val id: Long? = null,
    @ApiModelProperty("规格代码")
    @JsonProperty("specCode")
    val specCode: String? = null,
    @ApiModelProperty("规格类型")
    @JsonProperty("specType")
    val specType: String? = null,
    @ApiModelProperty("规格匹配标签")
    @JsonProperty("matchFeatureSpecLabels")
    val matchFeatureSpecLabels: Map<String, String>? = null
)

