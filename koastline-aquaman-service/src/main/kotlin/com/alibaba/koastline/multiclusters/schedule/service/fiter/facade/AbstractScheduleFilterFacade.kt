package com.alibaba.koastline.multiclusters.schedule.service.fiter.facade

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent

abstract class AbstractScheduleFilterFacade : ScheduleFilterFacade {

    abstract fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration

    override fun doScheduleFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
//        pre(matchDeclaration, content)
        val matchDeclaration = doFilter(matchDeclaration, content)
//        post(matchDeclaration, content)
        return matchDeclaration
    }

    private fun pre(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent) {
        TODO("Not yet implemented")
    }

    private fun post(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent) {
        TODO("Not yet implemented")
    }
}