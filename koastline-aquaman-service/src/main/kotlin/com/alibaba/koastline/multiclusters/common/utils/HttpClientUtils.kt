package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.koastline.multiclusters.common.logger
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody


object HttpClientUtils {
    private val log by logger()
    private val client: OkHttpClient = OkHttpClient().newBuilder().build()
    fun httpGet(url: String, params: Map<String, String>? = null, authorizationAttribute: AuthorizationAttribute? = null): String {
        val httpBuilder =  url.toHttpUrl().newBuilder()
        params ?.forEach { (key, value) ->
            httpBuilder.addQueryParameter(key, value)
        }
        val request = Request.Builder()
            .url(httpBuilder.build()).apply {
                authorizationAttribute ?.let {
                    this.addHeader("Authorization", Credentials.basic(authorizationAttribute.userName, authorizationAttribute.secret))
                }
            }.get().build()
        val response = client.newCall(request).execute()
        return response.body!!.string()
    }

    fun httpGetWithHeaders(url: String, params: Map<String, String>?, authorizationAttribute: AuthorizationAttribute? = null, headers: Map<String, String>?): String {
        val httpBuilder =  url.toHttpUrl().newBuilder()
        params ?.forEach { (key, value) ->
            httpBuilder.addQueryParameter(key, value)
        }
        val request = Request.Builder()
            .url(httpBuilder.build()).apply {
                authorizationAttribute ?.let {
                    this.addHeader("Authorization", Credentials.basic(authorizationAttribute.userName, authorizationAttribute.secret))
                }
                headers ?.forEach { (k, v) ->
                    this.addHeader(k, v)
                }
            }.get().build()
        val response = client.newCall(request).execute()
        return response.body!!.string()
    }

    fun httpPost(url:String, params: Map<String, String>, requestBodyStr: String, authorizationAttribute: AuthorizationAttribute?): String {
        val requestBodyBuilder = MultipartBody.Builder()
        params.forEach { (t, u) ->
            requestBodyBuilder.addFormDataPart(t,u)
        }
        requestBodyBuilder.addPart(requestBodyStr.toRequestBody("application/json".toMediaTypeOrNull()))
        val requestBuilder = Request.Builder()
        authorizationAttribute ?.run {
            requestBuilder.addHeader("Authorization", Credentials.basic(userName, secret))
        }
        requestBuilder.url(url)
        .post(
            requestBodyBuilder.build()
        ).build()
        val response = client.newCall(requestBuilder.build()).execute()
        return response.body!!.string()
    }

    fun httpPost(url:String, requestBodyStr: String, authorizationAttribute: AuthorizationAttribute?): String {
        val requestBuilder = Request.Builder()
        authorizationAttribute ?.run {
            requestBuilder.addHeader("Authorization", Credentials.basic(userName, secret))
        }
        requestBuilder.url(url)
            .post(
                requestBodyStr.toRequestBody("application/json".toMediaTypeOrNull())
            ).build()
        val response = client.newCall(requestBuilder.build()).execute()
        return response.body!!.string()
    }


    fun httpPost(url:String, params: Map<String, String>, authorizationAttribute: AuthorizationAttribute?): String {
        val requestBodyBuilder = FormBody.Builder()
        params.forEach { (t, u) ->
            requestBodyBuilder.add(t,u)
        }
        val requestBuilder = Request.Builder()
        authorizationAttribute ?.run {
            requestBuilder.addHeader("Authorization", Credentials.basic(userName, secret))
        }
        requestBuilder.url(url)
            .post(
                requestBodyBuilder.build()
            ).build()
        val response = client.newCall(requestBuilder.build()).execute()
        return response.body!!.string()
    }
}

data class AuthorizationAttribute (
    val userName: String,
    val secret: String
)
