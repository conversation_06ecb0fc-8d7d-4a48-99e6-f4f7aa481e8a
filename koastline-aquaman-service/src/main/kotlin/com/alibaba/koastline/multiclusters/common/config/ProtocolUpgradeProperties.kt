package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration


@Configuration
@RefreshScope
class ProtocolUpgradeProperties {
    /*
{
    "PROTOCOL_UPGRADE_APP_STAGE_GRAY": {
        "normandy-test-app4": [
            "DAILY",
            "PRE_PUBLISH",
            "GRAY",
            "SMALLFLOW",
            "PUBLISH"
        ],
        "appstack-qianyionline": [
            "DAILY",
            "PRE_PUBLISH",
            "GRA<PERSON>",
            "SMALLFLOW",
            "PUBLISH"
        ]
    },
    "PROTOCOL_UPGRADE_PRODUCTLINE_STAGE_GRAY": {
        "24369": [
            "DAILY",
            "PRE_PUBLISH",
            "GRAY",
            "SMALLFLOW",
            "PUBLISH"
        ],
        "alibaba": [
            "DAILY",
            "PRE_PUBLISH",
            "GRAY",
            "SMALLFLOW",
            "PUBLISH"
        ]
    }
}
     */

    var properties = ProtocolUpgradeDiamondConfig.buildProperties()

    fun whetherAppStageInGray(appName: String, stage: String): Boolean {
        return properties[PROTOCOL_UPGRADE_APP_STAGE_GRAY]?.get(appName)?.contains(stage) ?: false

    }

    fun whetherProductLineStageInGray(productLineId: String, stage: String): Boolean {
        return properties[PROTOCOL_UPGRADE_PRODUCTLINE_STAGE_GRAY]?.get(productLineId)?.contains(stage) ?: false

    }

    companion object {
        const val PROTOCOL_UPGRADE_APP_STAGE_GRAY = "PROTOCOL_UPGRADE_APP_STAGE_GRAY"
        const val PROTOCOL_UPGRADE_PRODUCTLINE_STAGE_GRAY = "PROTOCOL_UPGRADE_PRODUCTLINE_STAGE_GRAY"
    }
}