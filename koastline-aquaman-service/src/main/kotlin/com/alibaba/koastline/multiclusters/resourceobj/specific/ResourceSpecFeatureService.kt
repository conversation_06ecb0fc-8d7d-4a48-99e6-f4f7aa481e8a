package com.alibaba.koastline.multiclusters.resourceobj.specific

import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.common.RedisService
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.constants.CachePrefixConstants
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureImportRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImportWithMatchScope
import com.alibaba.koastline.multiclusters.event.EventProducer
import com.alibaba.koastline.multiclusters.event.ResourceSpecChangeEventPayload
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceGroupCpuModelResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceGroupGpuModelResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.req.ResourceObjectFeatureImportCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

/**
 * <AUTHOR>
 */
@Component
class ResourceSpecFeatureService {
    @Autowired
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService
    @Autowired
    lateinit var resourceObjectFeatureImportRepo: ResourceObjectFeatureImportRepo
    @Autowired
    lateinit var eventProducer: EventProducer
    @Autowired
    lateinit var commonProperties: CommonProperties
    @Autowired
    lateinit var redisService: RedisService

    @Transactional
    fun createOrUpdateResourceSpec(matchScopeExternalType: String, matchScopeExternalId: String, resourceSpec: ResourceSpec, employeeId: String): ResourceSpec {
        checkResourceSpec(resourceSpec)
        val resourceSpecGot =
            createOrUpdateResourceSpecInner(matchScopeExternalType, matchScopeExternalId, resourceSpec.run {
                resourceSpec.copy(gpu = rectifyGpuProperty(resourceSpec.gpu))
            }, employeeId)

        // send event
        eventProducer.sendIgnoreError(
            ResourceSpecChangeEventPayload(
                appName = if (matchScopeExternalType == MatchScopeExternalTypeEnum.APPLICATION.name) {
                    matchScopeExternalId
                } else null,
                resourceGroup = if (matchScopeExternalType == MatchScopeExternalTypeEnum.RESOURCE_GROUP.name) {
                    matchScopeExternalId
                } else null,
                resourceSpec,
                employeeId
            ).buildEvent()
        )

        return resourceSpecGot.apply {
            redisService.setValue(buildResourceSpecFeatureKey(
                matchScopeExternalType = matchScopeExternalType,
                matchScopeExternalId = matchScopeExternalId
            ), JsonUtils.writeValueAsString(this))
        }
    }

    @Transactional
    fun createOrUpdateResourceSpecForCpuModelByResourceGroup(resourceGroupCpuModelResourceSpec: ResourceGroupCpuModelResourceSpec) {
        val resourceSpec = getResourceSpecByMatchScope(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroupCpuModelResourceSpec.resourceGroup,
        )
        createOrUpdateResourceSpec(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroupCpuModelResourceSpec.resourceGroup,
            resourceSpec = ResourceSpec(
                cpu = resourceGroupCpuModelResourceSpec.resourceSpec.cpu,
                memory = resourceGroupCpuModelResourceSpec.resourceSpec.memory,
                disk = resourceGroupCpuModelResourceSpec.resourceSpec.disk,
                gpu = resourceSpec?.gpu,
            ),
            employeeId = resourceGroupCpuModelResourceSpec.employeeId,
        )
    }

    @Transactional
    fun deleteResourceSpecForCpuModelByResourceGroup(resourceGroup: String, modifier: String) {
        getResourceSpecByMatchScope(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroup,
        ) ?.gpu ?.let {
            throw ResourceObjectException("存在分组GPU资源规格配置，不允许直接删除分组规格。")
        }
        deleteResourceSpecByMatchScope(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroup,
            modifier = modifier,
        )
    }

    @Transactional
    fun createOrUpdateResourceSpecForGpuModelByResourceGroup(resourceGroupGpuModelResourceSpec: ResourceGroupGpuModelResourceSpec) {
        val resourceSpec = getResourceSpecByMatchScope(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroupGpuModelResourceSpec.resourceGroup,
        )
        require(resourceSpec != null) {"resourceSpec must not be null."}
        createOrUpdateResourceSpec(
            matchScopeExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            matchScopeExternalId = resourceGroupGpuModelResourceSpec.resourceGroup,
            resourceSpec = ResourceSpec(
                cpu = resourceSpec.cpu,
                memory = resourceSpec.memory,
                disk = resourceSpec.disk,
                gpu = resourceGroupGpuModelResourceSpec.resourceSpec.gpu,
            ),
            employeeId = resourceGroupGpuModelResourceSpec.employeeId,
        )
    }

    /**
     * gpu规格为"0"时，设置为空（为兼容当前normandy-ui传入为gpu = 0的场景）
     */
    private fun rectifyGpuProperty(gpu: String?): String? {
        gpu ?.toNullIfBlank() ?.let {
            if (it == "0") {
                return null
            } else {
                return gpu
            }
        }
        return null
    }

    private fun createOrUpdateResourceSpecInner(matchScopeExternalType: String, matchScopeExternalId: String, resourceSpec: ResourceSpec, employeeId: String): ResourceSpec {
        val paramMap = mapOf(
            "resources" to mapOf(
                "requests" to mapOf(
                    "cpu" to resourceSpec.cpu,
                    "memory" to resourceSpec.memory,
                    "disk" to resourceSpec.disk,
                    "gpu" to resourceSpec.gpu ?.toNullIfBlank()
                ),
                "limits" to mapOf(
                    "cpu" to resourceSpec.cpu,
                    "memory" to resourceSpec.memory,
                    "disk" to resourceSpec.disk,
                    "gpu" to resourceSpec.gpu ?.toNullIfBlank()
                )
            )
        )
        resourceObjectFeatureService.getFeatureImportByMatchScopeAndFeatureKey(matchScopeExternalType, matchScopeExternalId, RESOURCE_SPEC_FEATURE_KEY) ?.let {
            //删除原配置，作为操作日志记录
            resourceObjectFeatureService.deleteFeatureImportById(it.id, employeeId)
        }
        resourceObjectFeatureService.createFeatureImport(
            ResourceObjectFeatureImportCreateReqDto(
                paramMap = paramMap,
                creator = employeeId,
                resourceObjectFeatureKey = RESOURCE_SPEC_FEATURE_KEY,
                matchScopeDataList = listOf(
                    MatchScopeDataReqDto(
                        externalId = matchScopeExternalId,
                        externalType = matchScopeExternalType
                    )
                )
            )
        )
        return resourceSpec
    }

    fun deleteResourceSpecByMatchScope(matchScopeExternalType: String, matchScopeExternalId: String, modifier: String) {
        resourceObjectFeatureService.getFeatureImportByMatchScopeAndFeatureKey(
            matchScopeExternalType = matchScopeExternalType,
            matchScopeExternalId = matchScopeExternalId,
            resourceObjectFeatureKey = RESOURCE_SPEC_FEATURE_KEY
        ) ?.let {
            resourceObjectFeatureService.deleteFeatureImportById(
                id = it.id!!,
                modifier = modifier,
            )
        }
        redisService.deleteValue(buildResourceSpecFeatureKey(
            matchScopeExternalType = matchScopeExternalType,
            matchScopeExternalId = matchScopeExternalId
        ))
    }

    fun getResourceSpecByMatchScope(matchScopeExternalType: String, matchScopeExternalId: String): ResourceSpec? {
        redisService.getValue(buildResourceSpecFeatureKey(
            matchScopeExternalType = matchScopeExternalType,
            matchScopeExternalId = matchScopeExternalId
        )) ?.let {
            return JsonUtils.readValue(it, ResourceSpec::class.java)
        }
        resourceObjectFeatureImportRepo.findByMatchScopeAndFeatureKey(matchScopeExternalType, matchScopeExternalId, RESOURCE_SPEC_FEATURE_KEY) ?.let {
            it.paramMap ?.toNullIfBlank() ?.let { paramMapStr ->
                val paramMap = YamlUtils.load(paramMapStr)
                val resourcesParamMap = (paramMap["resources"]!! as Map<String,String>)["limits"] as Map<String,String>
                return ResourceSpec(
                    resourcesParamMap["cpu"]!!,
                    resourcesParamMap["memory"]!!,
                    resourcesParamMap["disk"]!!,
                    resourcesParamMap["gpu"]
                ).apply {
                    redisService.setValue(buildResourceSpecFeatureKey(
                        matchScopeExternalType = matchScopeExternalType,
                        matchScopeExternalId = matchScopeExternalId
                    ), JsonUtils.writeValueAsString(this))
                }
            }
        }
        return null
    }

    fun getFeatureImportByFeatureKeyAndResouceGroup(
        featureKey: String,
        resourceGroup: String
    ): ResourceObjectFeatureImport? {
        return resourceObjectFeatureImportRepo.findByMatchScopeAndFeatureKey(
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            resourceGroup,
            featureKey
        )
    }

    fun getFeatureImportByFeatureKeyAndResouceGroups(
        featureKey: String,
        resourceGroups: List<String>
    ): List<ResourceObjectFeatureImportWithMatchScope> {
        return resourceObjectFeatureImportRepo.findByMatchScopeAndFeatureKeyBatch(
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            resourceGroups,
            featureKey
        )
    }

    /**
     * 规格配置转换为字节单位
     */
    fun formatResourceSpecUnit(paramMap: String?): String? {
        return paramMap?.run { YamlUtils.load(paramMap) }?.run {
            val requests = (this["resources"]!! as Map<String, String>)["requests"] as Map<String, String>
            val limits = (this["resources"]!! as Map<String, String>)["limits"] as Map<String, String>
            mapOf(
                "resources" to mapOf(
                    "requests" to mapOf(
                        "cpu" to convertComputeResourceSpecValue(requests["cpu"]),
                        "memory" to convertStoreResourceSpecValue(requests["memory"]),
                        "disk" to convertStoreResourceSpecValue(requests["disk"]),
                        "gpu" to convertComputeResourceSpecValue(requests["gpu"])
                    ),
                    "limits" to mapOf(
                        "cpu" to convertComputeResourceSpecValue(limits["cpu"]),
                        "memory" to convertStoreResourceSpecValue(limits["memory"]),
                        "disk" to convertStoreResourceSpecValue(limits["disk"]),
                        "gpu" to convertComputeResourceSpecValue(requests["gpu"])
                    )
                )
            )
        } ?.run {
            YamlUtils.dump(this)
        }
    }

    fun formatDiskUnitToHumanReadable(paramMap: String?): String? {
        return paramMap?.run { YamlUtils.load(paramMap) }?.run {
            val requests = (this["resources"]!! as Map<String, String>)["requests"] as Map<String, String>
            val limits = (this["resources"]!! as Map<String, String>)["limits"] as Map<String, String>
            mapOf(
                "resources" to mapOf(
                    "requests" to mapOf(
                        "cpu" to requests["cpu"],
                        "memory" to requests["memory"],
                        "disk" to convertStoreResourceSpecValueToMb(requests["disk"]!!),
                        "gpu" to requests["gpu"]
                    ),
                    "limits" to mapOf(
                        "cpu" to limits["cpu"],
                        "memory" to limits["memory"],
                        "disk" to convertStoreResourceSpecValueToMb(limits["disk"]!!),
                        "gpu" to requests["gpu"]
                    )
                )
            )
        } ?.run {
            YamlUtils.dump(this)
        }
    }

    /**
     * 转换memory、disk等存储资源spec数值为byte单位
     * K，M，G，以1000为换算标准的。
     * Ki，Mi，Gi，以1024为换算标准的
     */
    fun convertStoreResourceSpecValue(specValue: String?): String? {
        specValue?.let {
            STORE_RESOURCE_SPEC_UNIT_MAP.keys.firstOrNull {
                specValue.endsWith(it, true)
            } ?.let {
                return specValue.substring(0, specValue.length - it.length).toBigDecimal().multiply(
                    STORE_RESOURCE_SPEC_UNIT_MAP[it]!!).toString()
            } ?:let {
                return specValue.toBigDecimal().multiply(
                    STORE_RESOURCE_SPEC_UNIT_MAP["Gi"]!!).toString()
            }
        }
        return specValue
    }

    /**
     * 转换cpu、gpu等计算资源spec数值
     * m,以1/1000为换算标准的
     */
    fun convertComputeResourceSpecValue(specValue: String?): String? {
        specValue ?.let {
            COMPUTE_RESOURCE_SPEC_UNIT_MAP.keys.firstOrNull {
                specValue.endsWith(it, true)
            } ?.let {
                return specValue.substring(0, specValue.length - it.length).toBigDecimal().divide(
                    COMPUTE_RESOURCE_SPEC_UNIT_MAP[it]!!).toString()
            }
        }
        return specValue
    }

    /**
     * 校验:预转换下资源规格
     */
    private fun checkResourceSpec(resourceSpec: ResourceSpec) {
        try {
            convertComputeResourceSpecValue(resourceSpec.cpu) ?.toNullIfBlank() ?:let {
                throw ResourceObjectException("资源cpu不能为空")
            }
            convertStoreResourceSpecValue(resourceSpec.memory) ?.toNullIfBlank() ?:let {
                throw ResourceObjectException("资源memory不能为空")
            }
            convertStoreResourceSpecValue(resourceSpec.disk) ?.toNullIfBlank() ?:let {
                throw ResourceObjectException("资源disk不能为空")
            }
            convertComputeResourceSpecValue(resourceSpec.gpu)
        } catch (e: Exception) {
            throw ResourceObjectException("资源规格不合法,${resourceSpec}")
        }
    }

    private fun buildResourceSpecFeatureKey(matchScopeExternalType: String, matchScopeExternalId: String): String {
        return "${CachePrefixConstants.RESOURCE_OBJECT_RESOURCE_SPEC}-${matchScopeExternalType}-${matchScopeExternalId}"
    }

    companion object {
        private val STORE_RESOURCE_SPEC_UNIT_MAP = mapOf(
            "K" to BigDecimal(1000 * 1000),
            "M" to BigDecimal(1000 * 1000),
            "G" to BigDecimal(1000 * 1000 * 1000),
            "Ki" to BigDecimal(1024),
            "Mi" to BigDecimal(1024 * 1024),
            "Gi" to BigDecimal(1024 * 1024 * 1024)
        )
        private val COMPUTE_RESOURCE_SPEC_UNIT_MAP = mapOf(
            "c" to BigDecimal(1),
            "m" to BigDecimal(1000)
        )

        fun convertStoreResourceSpecValueToMb(specValue: String): String {
            return (specValue.toBigDecimal().divide(STORE_RESOURCE_SPEC_UNIT_MAP["Mi"]).toInt()).toString() + "M"
        }
    }
}