package com.alibaba.koastline.multiclusters.apre.common

import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils.matchDeclarationMetadata
import com.alibaba.koastline.multiclusters.apre.model.Exclusion
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.alibaba.koastline.multiclusters.apre.model.equalsIgnoreOrder
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.ALL
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.INHERIT
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.SELF_DEF
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.APPLICATION
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.RESOURCE_GROUP
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.ENV_STACKID
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypePriority
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.checkMatchScopeExternalType
import com.alibaba.koastline.multiclusters.apre.params.checkMatchScopeTargetType
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.MatchScopeDataException
import com.alibaba.koastline.multiclusters.common.exceptions.SkylineBizException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.MatchScopeDataRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.KvMap
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeature
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.resourceobj.KvMapService
import com.alibaba.koastline.multiclusters.resourceobj.KvMapTypeEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*
import java.util.regex.Matcher
import java.util.regex.Pattern


/**
 * 匹配范围服务
 * target代表服务对象
 * external代表外部范围目标，如：BU、产品线、应用、分组等{@link MatchScopeExternalTypeEnum}
 */
@Component
class MatchScopeService(val objectMapper: ObjectMapper) {
    @Autowired
    lateinit var matchScopeDataRepo: MatchScopeDataRepo

    @Autowired
    lateinit var appCenterClient: AppCenterApi

    @Autowired
    lateinit var skylineApi: SkylineApi

    @Autowired
    lateinit var kvMapService: KvMapService

    val log by logger()

    /**
     * 判断一个生效范围是不是另一个生效范围的子生效范围
     *
     * @param goalExternalId 当前目标生效范围
     * @param goalExternalType 当前目标生效范围类型
     * @param externalId 代入比较的生效范围
     * @param externalType 代入比较的生效范围类型
     * @return 结果小于0 代表属于子集 等于0 是同一个范围 大于1是不属于
     */
    fun compareExternalScopeBelongs2(
        goalExternalId: String,
        goalExternalType: MatchScopeExternalTypeEnum,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum
    ): Int {
        // 减枝判断法
        // 优先剪枝非同级别判断
        if (MatchScopeExternalTypePriority[goalExternalType.name]!! < MatchScopeExternalTypePriority[externalType.name]!!) {
            return 1
        }

        // 同级别范围比较
        if (goalExternalType == externalType) {
            return compareSamePriorityExternalScope(
                goalExternalId = goalExternalId,
                goalExternalType = goalExternalType,
                externalId = externalId,
                externalType = externalType
            )
        }

        // 这里存在 { A:AONE_PRODUCTLINE, B:(APPLICATION, AONE_PRODUCTLINE) } 两种情况
        val appName = if(goalExternalType == APPLICATION) goalExternalId else skylineApi.getAppGroup(goalExternalId).appName
        val appInfo = appCenterClient.getAppInfoByName(appName)
        val productLinePath = getProductLinePath(appInfo)

        if (goalExternalType == APPLICATION && externalType == AONE_PRODUCTLINE) {
            if (productLinePath.startsWith(externalId) || externalId == ALIBABA_GROUP) {
                return -1
            }
        }

        if (goalExternalType == RESOURCE_GROUP) {
            if (externalType == APPLICATION && appName == externalId) {
                return -1
            }
            if (externalType == AONE_PRODUCTLINE && (productLinePath.startsWith(externalId) || externalId == ALIBABA_GROUP)) {
                return -1
            }
        }
        return 1
    }

    fun compareSamePriorityExternalScope(
        goalExternalId: String,
        goalExternalType: MatchScopeExternalTypeEnum,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum
    ): Int {
        require(goalExternalType == externalType) {
            "goal external type must be same with external type"
        }
        if (goalExternalId == externalId) {
            return 0
        } else {
            if (goalExternalType == AONE_PRODUCTLINE && (goalExternalId.startsWith(externalId) || externalId == ALIBABA_GROUP)) {
                return -1
            }
            return 1
        }
    }

    /**
     * external 范围类型范围需要比 goalExternalId 大
     * @param goalExternalId
     * @param goalExternalType
     * @param externalId
     * @param externalType
     * @return
     */
    fun getIncludeType(
        goalExternalId: String,
        goalExternalType: MatchScopeExternalTypeEnum,
        externalId: String,
        externalType: MatchScopeExternalTypeEnum
    ): IncludeScopeType {
        // 校验生效范围是否合法
        require(goalExternalId.isNotBlank() && externalId.isNotBlank()) {
            "externalId of matchScopeDataDO in getIncludeType cannot be blank"
        }
        if (goalExternalType == AONE_PRODUCTLINE && externalId != ALIBABA_GROUP) {
            checkProductLineIsLegal(goalExternalId)
        }
        if (externalType == AONE_PRODUCTLINE && externalId != ALIBABA_GROUP) {
            checkProductLineIsLegal(externalId)
        }

        val isBelongs2 = compareExternalScopeBelongs2(
            goalExternalId = goalExternalId,
            goalExternalType = goalExternalType,
            externalId = externalId,
            externalType = externalType
        )

        // 前者是后者的子范围 属于继承关系
        if (isBelongs2 == -1) {
            return INHERIT
        }

        // 前者和后者是等价范围关系 属于自定
        if (isBelongs2 == 0) {
            return SELF_DEF
        }

        if(goalExternalType == APPLICATION && externalType == RESOURCE_GROUP){
            check(skylineApi.getAppGroup(externalId).appName == goalExternalId){
                "RESOURCE_GROUP:$goalExternalId need belong to APPLICATION:$goalExternalType, but actually does not!"
            }
            return SELF_DEF
        }

        throw ApREException("overstep the boundary")
    }


    /**
     * 获取过滤的matchScopeData
     *
     * @param externalType
     * @param externalId
     * @param includeScopeType
     * @return
     * 返回策略
     * 1.产品线：查询到自己和alibaba
     * 2.应用:查询到自己和上级产品线和alibaba
     * 3.分组：查询到自己和上级应用&产品线&alibaba
     */
    fun filterMatchScopeDataByIncludeScopeType(
        externalType: String,
        externalId: String,
        includeScopeType: IncludeScopeType
    ): List<MatchScopeDataDO> {
        val matScopeDataList = when (MatchScopeExternalTypeEnum.valueOf(externalType)) {
            APPLICATION -> {
                val appGroup =
                    skylineApi.getAppGroupsByAppName(appName = externalId, includeType = listOf(SkylineApi.PRODUCT_LINE_STAGE))
                        .map { it.name }
                findMatchScopesByTargetAndExternalForApp(
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                    appName = externalId,
                    resourceGroupList = appGroup
                )
            }

            RESOURCE_GROUP -> {
                val appGroup = listOf(externalId)
                val appName = skylineApi.getAppGroup(externalId).appName
                findMatchScopesByTargetAndExternalForApp(
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                    appName = appName,
                    resourceGroupList = appGroup
                )
            }

            AONE_PRODUCTLINE -> {
                findMatchScopesByTargetAndExternalForProductLineOrBu(
                    productLinePath = externalId,
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                )
            }

            else ->{
                throw MatchScopeDataException("unSupported MatchScopeExternalTypeEnum")
            }
        }
        // 过滤指定范围的matchScopeData SELF_DEF 指向自己的分组和旗下的所有分组规则, INHERIT 指向产品线特性规则 ALL 指向所有特性规则

        val filterMs = when (includeScopeType) {
            SELF_DEF -> {
                when (MatchScopeExternalTypeEnum.valueOf(externalType)) {
                    APPLICATION -> matScopeDataList.filter { it.externalType != AONE_PRODUCTLINE.name }
                    RESOURCE_GROUP -> matScopeDataList.filter { it.externalType == RESOURCE_GROUP.name }
                    AONE_PRODUCTLINE -> matScopeDataList.filter { it.externalId == externalId && it.externalType == AONE_PRODUCTLINE.name }
                    else ->{
                        throw MatchScopeDataException("unSupported MatchScopeExternalTypeEnum")
                    }
                }
            }

            INHERIT -> {
                when (MatchScopeExternalTypeEnum.valueOf(externalType)) {
                    APPLICATION -> matScopeDataList.filter { it.externalType == AONE_PRODUCTLINE.name }
                    RESOURCE_GROUP -> matScopeDataList.filter { it.externalType != RESOURCE_GROUP.name }
                    AONE_PRODUCTLINE -> matScopeDataList.filter { it.externalId != externalId && it.externalType == AONE_PRODUCTLINE.name }
                    else ->{
                        throw MatchScopeDataException("unSupported MatchScopeExternalTypeEnum")
                    }
                }
            }

            ALL -> matScopeDataList
        }
        return filterMs
    }


    /**
     * 采用目标范围获取其绑定关系
     *
     * @param targetId
     * @param targetType
     * @return
     */
    fun listByTarget(targetId: Long, targetType: String): List<MatchScopeDataDO> {
        return matchScopeDataRepo.findByTarget(targetId, targetType).map {
            convertMatchScopeData(it)
        }
    }

    /**
     * 按照productLinePrefix进行搜索对应BU以及旗下的产品线或者某个产品线以及旗下的子产品线的所有matchScopeData数据
     *
     * @param productLinePrefix
     * @param targetType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @Transactional
    fun listByProductLinePrefixExternalScope(
        productLinePrefix: String?,
        targetType: String,
        pageSize: Int,
        pageNumber: Int
    ): PageData<List<MatchScopeDataDO>> {
        require(pageNumber > 0) { "page number cannot less than 0" }
        require(pageSize > 0) { "page size cannot be less than 0" }

        val groupCount = if (productLinePrefix != null) {
            val (bu, productLineId) = extractBuAndProductLine(productLinePrefix)
            if (productLineId != null) {
                matchScopeDataRepo.countByTargetTypeAndExternalGroupWithPrefix(
                    targetType = targetType,
                    externalType = AONE_PRODUCTLINE.name,
                    externalIdPrefix = productLinePrefix,
                    externalId = bu.toString(),
                )
            } else {
                matchScopeDataRepo.countByTargetTypeAndExternalGroupWithPrefix(
                    targetType = targetType,
                    externalType = AONE_PRODUCTLINE.name,
                    externalIdPrefix = "$bu#",
                    externalId = bu.toString(),
                )
            }
        } else {
            matchScopeDataRepo.countByTargetTypeAndExternalGroupWithPrefix(
                targetType = targetType,
                externalType = AONE_PRODUCTLINE.name,
                externalIdPrefix = null,
                externalId = null,
            )
        }
        val total = groupCount.count()

        val pageData = PageData<List<MatchScopeDataDO>>(
            pageNumber = pageNumber,
            pageSize = pageSize,
            totalCount = total.toLong(),
            data = emptyList()
        )

        val beginGroupRow = pageSize * (pageNumber - 1) + 1
        var endGroupRow = pageSize * pageNumber

        if (beginGroupRow > total) {
            return pageData
        }

        if (endGroupRow > total) {
            endGroupRow = total
        }

        val externalIds = groupCount.filterIndexed { index, _ ->
            index + 1 in beginGroupRow..endGroupRow
        }.map { it.groupName }

        val matchScopeList = matchScopeDataRepo.findByTargetTypeAndExternalIds(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            externalType = AONE_PRODUCTLINE.name,
            externalIds = externalIds
        ).map { convertMatchScopeData(it) }.groupBy { it.externalId }.map { it.value }

        check((endGroupRow - beginGroupRow + 1) == matchScopeList.size) {
            "size of matchScopeList should keep with (endGroupRow - beginGroupRow +1) "
        }

        pageData.data = matchScopeList
        return pageData
    }

    /**
     * 使用targetType和external对matchScopeData
     *
     * @param targetType
     * @param externalId
     * @param externalType
     * @return
     */
    fun listByTargetTypeAndExternal(
        targetType: String,
        externalId: String,
        externalType: String
    ): List<MatchScopeDataDO> {
        return matchScopeDataRepo.listByTargetTypeAndExternal(
            targetType = targetType,
            externalType = externalType,
            externalId = externalId,
        ).map {
            convertMatchScopeData(it)
        }
    }

    /**
     * 通过target和external找matchScope
     *
     * @param targetType
     * @param targetId
     * @param externalId
     * @param externalType
     * @return
     */
    fun findByTargetAndExternal(
        targetType: String,
        targetId: Long,
        externalId: String,
        externalType: String,
    ): MatchScopeDataDO? {
        return matchScopeDataRepo.findByExternalAndTarget(
            targetType = targetType,
            targetId = targetId,
            externalType = externalType,
            externalId = externalId
        )?.let {
            convertMatchScopeData(it)
        }
    }

    /**
     * 查找应用级别的匹配信息 全量查询 会把自己还有自己任意父级别的产品线的授权都返回
     *
     * @param targetType
     * @param appName
     * @param resourceGroupList
     * @return
     */
    fun findMatchScopesByTargetAndExternalForApp(
        targetType: String,
        appName: String? = null,
        resourceGroupList: List<String> = emptyList(),
        envStackId: String? = null,
        buId: String? = null,
        productFullLineIdPath: String? = null,
        clusterId: String? = null,
    ): List<MatchScopeDataDO> {
        val matchScopeDataList = mutableListOf<MatchScopeDataDO>()

        val appInfo = appName?.run {
            val appInfo = appCenterClient.getAppInfoByName(appName)
            // 添加基于应用限定范围
            matchScopeDataList.addAll(
                matchScopeDataRepo.listByTargetTypeAndExternal(
                    targetType = targetType,
                    externalType = APPLICATION.name,
                    externalId = appName
                ).map {
                    convertMatchScopeData(it)
                }
            )
            // 添加基于BU或产品线限定范围
            matchScopeDataList.addAll(
                findMatchScopesByTargetAndExternalForProductLine(
                    buId = appInfo.buId,
                    productFullLineIdPath = appInfo.productFullLineIdPath,
                    targetType = targetType
                )
            )
            appInfo
        }
        appInfo?:let {
            checkNotNull(buId){"buId is must not be null while appName is null in findMatchScopesByTargetAndExternalForApp"}
            checkNotNull(productFullLineIdPath){"productFullLineIdPath is must not be null while appName is null in findMatchScopesByTargetAndExternalForApp"}
            matchScopeDataList.addAll(
                findMatchScopesByTargetAndExternalForProductLine(
                    buId = buId.toLong(),
                    productFullLineIdPath = productFullLineIdPath,
                    targetType = targetType
                )
            )
        }

        // 添加基于应用限定范围
        envStackId?.run {
            matchScopeDataList.addAll(
                matchScopeDataRepo.listByTargetTypeAndExternal(
                    targetType = targetType,
                    externalType = ENV_STACKID.name,
                    externalId = this
                ).map {
                    convertMatchScopeData(it)
                }
            )
        }

        // 添加基于应用分组范围
        if (resourceGroupList.isNotEmpty()) {
            matchScopeDataList.addAll(
                matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(
                    targetType = targetType,
                    externalType = RESOURCE_GROUP.name,
                    externalIds = resourceGroupList
                ).map {
                    convertMatchScopeData(it)
                }
            )
        }

        return matchScopeDataList.filter {
            !isMsExcluded(
                ms = it,
                productFullLineIdPath = appInfo ?.run { "${this.buId}$BU_PRODUCT_LINE_SPLITTER${this.productFullLineIdPath}" } ?:run {
                    "${buId}$BU_PRODUCT_LINE_SPLITTER${productFullLineIdPath}"
                },
                appName = appName,
                clusterId = clusterId,
            )
        }
    }

    fun findMatchScopesForVersionOut(
        appName: String,
        envStackId: String? = null
    ): List<MatchScopeDataDO> {
        val matchScopeDataList = mutableListOf<MatchScopeDataDO>()
        val targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
        matchScopeDataList.addAll(
            matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = targetType,
                externalType = APPLICATION.name,
                externalId = appName
            ).map {
                convertMatchScopeData(it)
            }
        )

        envStackId?.run {
            matchScopeDataList.addAll(
                matchScopeDataRepo.listByTargetTypeAndExternal(
                    targetType = targetType,
                    externalType = ENV_STACKID.name,
                    externalId = this
                ).map {
                    convertMatchScopeData(it)
                }
            )
        }

        /*
        system inject for all
         */
        matchScopeDataList.addAll(
            matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = targetType,
                externalType = AONE_PRODUCTLINE.name,
                externalId = ALIBABA_GROUP
            ).map {
                convertMatchScopeData(it)
            }
        )

        return matchScopeDataList
    }

    fun findMatchScopesByAppNameAndResourceGroupsForResourceObjectFeatureImport(
        appName: String,
        resourceGroupList: List<String>
    ): List<MatchScopeDataDO> {
        val matchScopeDataList = mutableListOf<MatchScopeDataDO>()
        val targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name
        val appInfo = appCenterClient.getAppInfoByName(appName)
        matchScopeDataList.addAll(
            matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = targetType,
                externalType = APPLICATION.name,
                externalId = appName
            ).map {
                convertMatchScopeData(it)
            }
        )
        matchScopeDataList.addAll(
            findMatchScopesByTargetAndExternalForProductLine(
                buId = appInfo.buId,
                productFullLineIdPath = appInfo.productFullLineIdPath,
                targetType = targetType
            )
        )

        if (resourceGroupList.isNotEmpty()) {
            matchScopeDataList.addAll(
                matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(
                    targetType = targetType,
                    externalType = RESOURCE_GROUP.name,
                    externalIds = resourceGroupList
                ).map {
                    convertMatchScopeData(it)
                }
            )
        }
        return matchScopeDataList.filter {
            !isMsExcluded(
                ms = it,
                productFullLineIdPath = appInfo.getProductLinePath(),
                appName = appName
            )
        }
    }

    fun findMatchScopesByTargetAndExternalForProductLineOrBu(
        productLinePath: String,
        targetType: String
    ): List<MatchScopeDataDO> {
        val (bu, productFullLineIdPath) = extractBuAndProductLine(productLinePath)
        if (productFullLineIdPath != null) {
            return findMatchScopesByTargetAndExternalForProductLine(
                buId = bu,
                productFullLineIdPath = productFullLineIdPath,
                targetType = targetType
            )
        }

        val transformedIds = listOf(
            productLinePath, ALIBABA_GROUP
        )
        return matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(
            targetType = targetType,
            externalType = AONE_PRODUCTLINE.name,
            externalIds = transformedIds
        ).map {
            convertMatchScopeData(it)
        }.filter {
            !isMsExcluded(
                ms = it,
                productFullLineIdPath = productLinePath
            )
        }
    }

    /**
     * 查询productLine相关的MatchScopeData
     * 只查询产品线 不查询BU
     *
     * @param buId
     * @param productFullLineIdPath
     * @param targetType
     * @return
     */
    fun findMatchScopesByTargetAndExternalForProductLine(
        buId: Long,
        productFullLineIdPath: String,
        targetType: String
    ): List<MatchScopeDataDO> {
        checkProductLineIsLegal("$buId$BU_PRODUCT_LINE_SPLITTER$productFullLineIdPath")
        val transformedIds = buildProductLineTransformedIds(buId, productFullLineIdPath)
        return if (transformedIds.isNotEmpty()) {
            return matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(
                targetType = targetType,
                externalType = AONE_PRODUCTLINE.name,
                externalIds = transformedIds
            ).map {
                convertMatchScopeData(it)
            }.filter {
                !isMsExcluded(
                    ms = it,
                    productFullLineIdPath = "${buId}$BU_PRODUCT_LINE_SPLITTER${productFullLineIdPath}"
                )
            }
        } else emptyList()
    }

    fun checkProductLineIsLegal(productLinePath: String) {
        val validPattern: Pattern = Pattern.compile("^\\d+(#\\d+(_\\d+)*)?$")
        val matcher: Matcher = validPattern.matcher(productLinePath)
        if (!matcher.matches()) {
            throw ApREException("illegal productLine full path:$productLinePath")
        }
    }

    /**
     * TODO
     *
     * @param matchScopeDataDO
     */
    fun createMatchScopeIgnoreWhileExist(matchScopeDataDO: MatchScopeDataDO) {
        saveAppName2ResourceGroup(matchScopeDataDO)
        log.info("createMatchScopeIgnoreWhileExist: matchScopeDataDO:$matchScopeDataDO")
        checkMatchScopeExternalType(matchScopeDataDO.externalType)
        checkMatchScopeTargetType(matchScopeDataDO.targetType!!)
        matchScopeDataRepo.findByExternalAndTargetAndRestrictionsExclusions(
            matchScopeDataDO.externalType,
            matchScopeDataDO.externalId,
            matchScopeDataDO.targetType,
            matchScopeDataDO.targetId!!,
            if (matchScopeDataDO.restrictions.isNullOrEmpty()) null else objectMapper.writeValueAsString(
                matchScopeDataDO.restrictions
            ),
            if (matchScopeDataDO.exclusions.isNullOrEmpty()) null else objectMapper.writeValueAsString(
                matchScopeDataDO.exclusions
            ),
        ) ?: let {
            matchScopeDataRepo.insert(
                MatchScopeData(
                    null,
                    matchScopeDataDO.targetId,
                    matchScopeDataDO.targetType,
                    matchScopeDataDO.externalId,
                    matchScopeDataDO.externalType,
                    if (matchScopeDataDO.exclusions.isNullOrEmpty()) null else objectMapper.writeValueAsString(
                        matchScopeDataDO.exclusions
                    ),
                    if (matchScopeDataDO.restrictions.isNullOrEmpty()) null else objectMapper.writeValueAsString(
                        matchScopeDataDO.restrictions
                    ),
                    matchScopeDataDO.creator,
                    matchScopeDataDO.modifier
                )
            )
        }
    }

    fun saveAppName2ResourceGroup(matchScopeDataDO: MatchScopeDataDO) {
        if (matchScopeDataDO.targetType != MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name) {
            return
        }
        if (matchScopeDataDO.externalType != MatchScopeExternalTypeEnum.RESOURCE_GROUP.name) {
            return
        }
        val group = matchScopeDataDO.externalId
        val appName: String
        try {
            appName = getAppNameByGroupNameFromSkyline(group)
        } catch (e: SkylineBizException) {
            log.warn("get appName by groupName from skyline failed, groupName: $group, error: ${e.message}")
            return
        }
        saveAppNameToGroupName(group, appName)
    }

    fun deleteAppName2ResourceGroup(matchScopeDataDO: MatchScopeDataDO) {
        if (matchScopeDataDO.targetType != MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name) {
            return
        }
        if (matchScopeDataDO.externalType != MatchScopeExternalTypeEnum.RESOURCE_GROUP.name) {
            return
        }
        if (listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                externalId = matchScopeDataDO.externalId,
                externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            ).isNotEmpty()
        ) {
            return
        }
        val group = matchScopeDataDO.externalId
        val appName: String
        try {
            appName = getAppNameByGroupNameFromSkyline(group)
        } catch (e: SkylineBizException) {
            log.warn("get appName by groupName from skyline failed, groupName: $group, error: ${e.message}")
            return
        }
        deleteAppNameToGroupName(group, appName)
    }

    fun getAppNameByGroupNameFromSkyline(groupName: String): String {
        return skylineApi.getAppGroup(groupName).appName
    }

    @Transactional
    fun saveAppNameToGroupName(groupName: String, appName: String) {
        kvMapService.createKvMapIgnoreWhenExist(
            KvMap(
                type = KvMapTypeEnum.APPNAME_2_GROUPNAME.name,
                keyName = appName,
                value = groupName,
                creator = "appName2GroupName",
                modifier = "appName2GroupName",
            )
        )
    }
    @Transactional
    fun deleteAppNameToGroupName(groupName: String, appName: String) {
        kvMapService.deleteKvMapByTypeAndKeyAndValue(
            type = KvMapTypeEnum.APPNAME_2_GROUPNAME.name,
            keyName = appName,
            value = groupName,
        )
    }

    /**
     * 更新 MatchScope 当不存在时进行创建操作操作
     *
     * @param matchScopeDataDO
     */
    @Transactional
    fun updateMatchScope(matchScopeDataDO: MatchScopeDataDO) {
        log.info("updateMatchScope: matchScopeDataDO:$matchScopeDataDO")
        requireNotNull(matchScopeDataDO.targetId) { "MatchScopeData target id cannot be null" }
        requireNotNull(matchScopeDataDO.targetType) { "MatchScopeData target type cannot be null" }
        print(matchScopeDataDO)
        val toUpdateMatchScopeData = MatchScopeData(
            id = matchScopeDataDO.id,
            targetId = matchScopeDataDO.targetId,
            targetType = matchScopeDataDO.targetType,
            externalId = matchScopeDataDO.externalId,
            externalType = matchScopeDataDO.externalType,
            exclusions = objectMapper.writeValueAsString(matchScopeDataDO.exclusions),
            creator = matchScopeDataDO.creator,
            modifier = matchScopeDataDO.modifier,
            gmtCreate = matchScopeDataDO.gmtCreate ?: Date(Instant.now().toEpochMilli()),
            gmtModified = Date(Instant.now().toEpochMilli()),
            restrictions = null,
        )
        // id 不存在时 或者 matchScope 不存时 进行创建操作
        matchScopeDataDO.id ?: let {
            throw ApREException("cannot find matchScopeData: id: matchScopeDataDO.id")
        }
        val matchScopeId = checkNotNull(matchScopeDataDO.id)
        matchScopeDataRepo.findById(matchScopeId).let { originMatchScope ->
            if(originMatchScope == null){
                throw ApREException("cannot find matchScopeData: id: matchScopeDataDO.id")
            }
            matchScopeDataRepo.updateTargetAndExternalById(
                toUpdateMatchScopeData.copy(
                    creator = originMatchScope.creator
                )
            )
        }
    }

    fun deleteMatchScopeByTarget(targetId: Long, targetType: String, modifier: String) {
        log.info("deleteMatchScopeByTarget: targetId:$targetId, targetType:$targetType, modifier:$modifier")
        matchScopeDataRepo.deleteByTarget(targetId, targetType, modifier)
    }

    fun deleteMatchScopeByIds(idList: List<Long>, modifier: String) {
        log.info("deleteMatchScopeByIds: idList:$idList")
        matchScopeDataRepo.deleteByIds(idList, modifier)
    }

    fun deleteMatchScopeById(id: Long, modifier: String) {
        matchScopeDataRepo.deleteById(id, modifier)
    }

    fun deleteMatchScope(matchScope: MatchScopeDataDO, modifier: String) {
        matchScopeDataRepo.deleteById(checkNotNull(matchScope.id), modifier)
        deleteAppName2ResourceGroup(matchScope)
    }

    /**
     * 根据匹配范围和特性导入key查询满足条件的所有匹配范围
     */
    private fun queryMatchScopesByExternalAndResourceObjectFeatureKey(
        matchScopeData: MatchScopeDataReqDto, featureKey: String
    ): List<MatchScopeDataDO> {
        return matchScopeDataRepo.findByExternalAndResourceObjectFeatureKey(
            matchScopeData.externalType, matchScopeData.externalId, featureKey
        )
            .map(this::convertMatchScopeData)
            .filter { matchScope ->
                (matchScopeData.restrictions ?: listOf()).equalsIgnoreOrder(matchScope.restrictions ?: listOf())
            }
    }

  fun findFeatureMatchScopeByResourceObjectFeatureKey(
      feature: ResourceObjectFeature,
      appInfo: AppInfo,
  ): List<MatchScopeDataDO> {
      val matchScopeDataList = mutableListOf<MatchScopeDataDO>()
      matchScopeDataList.addAll(
          matchScopeDataRepo.findFeatureByExternalAndResourceObjectFeatureKey(
              APPLICATION.name,
              feature.resourceObjectFeatureKey,
          ).map {
              convertMatchScopeData(it)
          }
      )
      matchScopeDataList.addAll(
          findMatchScopesByTargetAndExternalForProductLine(
              buId = appInfo.buId,
              productFullLineIdPath = appInfo.productFullLineIdPath,
              targetType = MatchScopeTargetTypeEnum.ResourceObjectFeature.name,
          ).filter { matchScopeDataDO ->
              matchScopeDataDO.targetId == feature.id
          }
      )
      return matchScopeDataList
  }

    /**
     * 根据匹配范围和特性导入key查询满足条件的匹配范围。
     * 注意这里对于 match scope 是精确匹配，即 externalId、externalType、restrictions 完全相同
     */
    fun findMatchScopeByExternalAndResourceObjectFeatureKey(
        matchScopeData: MatchScopeDataReqDto, featureKey: String
    ): MatchScopeDataDO? {
        val matchScopes = queryMatchScopesByExternalAndResourceObjectFeatureKey(matchScopeData, featureKey)
        if (matchScopes.size > 1) {
            throw MatchScopeDataException("预期最多存在 1 条满足条件的匹配范围，实际查询得到${matchScopes.size}条")
        }
        return matchScopes.getOrNull(0)
    }

    /**
     * 产品线级别ms存在对产品线级别/应用级别进行剔除
     * 当测试产品线时候 不传递appName
     *
     * @param ms
     * @param productFullLineIdPath
     * @param appName
     * @return
     */
    fun isMsExcluded(
        ms: MatchScopeDataDO, productFullLineIdPath: String, appName: String? = null, clusterId: String? = null
    ): Boolean {
        if (ms.exclusions.isNullOrEmpty()) {
            return false
        }
        val exclusions = ms.exclusions
        return exclusions.any { exclusion ->
            exclusion.validate()
            when (exclusion.externalType) {
                MatchScopeExternalTypeEnum.CLUSTER_ID.name -> {
                    if (clusterId == exclusion.externalId) return true
                }
                AONE_PRODUCTLINE.name -> {
                    if (productFullLineIdPath.startsWith(exclusion.externalId)) return true
                }

                APPLICATION.name -> {
                    if (appName == exclusion.externalId) return true
                }

                RESOURCE_GROUP.name -> {
                    if (ms.externalId == exclusion.externalId) return true
                }
            }
            false
        }
    }

    /**
     * 是否为限定条件内的
     */
    fun isMatchScopeRestricted(
        restrictions: List<Restriction>?,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String,
        systemInputParams: Map<String, Any>? = null,
    ): Boolean {
        if (restrictions.isNullOrEmpty()) {
            return true
        }
        return restrictions.firstOrNull { restriction ->
            isMatchMetadata(restriction, workloadMetadataConstraint, envStackId, systemInputParams)
        } != null
    }

    /**
     * 处理产品线Path为多级，如
     * 输入产品线：3#4_5_6
     * 输出结果:[3,3#4,3#4_5,3#4_5_6] 还会添加全局产品线范围 alibaba
     */
    fun buildProductLineTransformedIds(buId: Long, productFullLineIdPath: String): List<String> {
        val transformedIds = arrayListOf<String>()
        transformedIds.add(ALIBABA_GROUP)
        transformedIds.add(buId.toString())
        productFullLineIdPath.split(PRODUCT_LINE_SPLITTER).let {
            val productLineTransformeds = arrayListOf<String>()
            productLineTransformeds.add(it[0])
            for (i in 1 until it.size) {
                productLineTransformeds.add(productLineTransformeds[i - 1] + PRODUCT_LINE_SPLITTER + it[i])
            }
            for (i in productLineTransformeds.indices) {
                transformedIds.add("$buId$BU_PRODUCT_LINE_SPLITTER${productLineTransformeds[i]}")
            }
        }
        return transformedIds
    }

    /**
     * 抽取BU层面的序号 检查
     *
     * @return 返回BU和产品线自身的序号
     * 例如case -> 4#1_2_3 -> pair(4,4#1_2_3), 3 -> pair(3,null),
     */
    fun extractBuAndProductLine(productLinePath: String): Pair<Long, String?> {
        checkProductLineIsLegal(productLinePath)
        if (!productLinePath.contains(BU_PRODUCT_LINE_SPLITTER)) {
            return Pair(productLinePath.toLong(), null)
        }
        val productLineSet = productLinePath.split(BU_PRODUCT_LINE_SPLITTER)
        check(productLineSet.size == 2) {
            "illegal productFullLineIdPath"
        }
        return Pair(productLineSet[0].toLong(), productLineSet[1])
    }

    /**
     * 比较范围优先级
     */
    fun compareScopePriority(
        externalType1: String,
        externalId1: String,
        externalType2: String,
        externalId2: String
    ): Int {
        (MatchScopeExternalTypePriority[externalType1]!! - MatchScopeExternalTypePriority[externalType2]!!).let {
            if (it > 0) return 1
            if (it < 0) return -1
            if (externalType1 != AONE_PRODUCTLINE.name) {
                return 0
            }
        }
        if (externalId1 == ALIBABA_GROUP && externalId2 == ALIBABA_GROUP) {
            return 0;
        }
        // 全局范围配置优先级最低
        if (externalId1 == ALIBABA_GROUP) {
            return -1
        }
        if (externalId2 == ALIBABA_GROUP) {
            return 1
        }
        // 产品线比较长度，产品线范围越小优先级越高
        (externalId1.length - externalId2.length).let {
            if (it > 0) return 1
            if (it < 0) return -1
            return 0
        }
    }

    /**
     * 比较限定条件优先级
     * 按照上述优先级及积分进行计算,积分高者优先级高
     */
    fun compareRestrictionPriority(
        restrictionList1: List<Restriction>?,
        restrictionList2: List<Restriction>?,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String
    ): Int {
        val score1 = computeRestrictionScore(restrictionList1, workloadMetadataConstraint, envStackId)
        val score2 = computeRestrictionScore(restrictionList2, workloadMetadataConstraint, envStackId)
        return when {
            score1 > score2 -> 1
            score1 < score2 -> -1
            else -> 0
        }
    }

    /**
     * 计算符合的限定条件最大积分
     */
    private fun computeRestrictionScore(
        restrictionList: List<Restriction>?,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String
    ): Int {
        if (restrictionList.isNullOrEmpty()) {
            return 0
        }
        return restrictionList.filter { restriction ->
            isMatchMetadata(restriction, workloadMetadataConstraint, envStackId)
        }.maxOfOrNull { restriction -> restriction.getScore() } ?: 0
    }

    /**
     * 限定条件&Workload元数据是否匹配 涉及：站点、单元、用途、集群
     *
     * @param restriction
     * @param workloadMetadataConstraint
     * @param envStackId
     * @return
     */
    private fun isMatchMetadata(
        restriction: Restriction,
        workloadMetadataConstraint: WorkloadMetadataConstraint,
        envStackId: String,
        systemInputParams: Map<String, Any>? = null,
    ): Boolean {
        val workloadMetadataConstraintMatch = matchDeclarationMetadata(restriction.site, workloadMetadataConstraint.site)
                && matchDeclarationMetadata(restriction.unit, workloadMetadataConstraint.unit)
                && matchDeclarationMetadata(restriction.stage, workloadMetadataConstraint.stage)
                && (restriction.clusterIdList.isEmpty() || restriction.clusterIdList.contains(workloadMetadataConstraint.clusterId))
                && matchDeclarationMetadata(restriction.envStackId, envStackId)
        var restrictionLabelMatch = true
        restriction.extendedLabels?.forEach { key, value ->
            restrictionLabelMatch = restrictionLabelMatch && (systemInputParams != null && getNestedMapValueByPath(YamlUtils.load(JsonUtils.writeValueAsString(systemInputParams)), key) == value)
        }
        return workloadMetadataConstraintMatch && restrictionLabelMatch
    }

  fun isMatchRestrictionExtendedLabels(
    restriction: Restriction,
    systemInputParams: Map<String, Any>? = null,
  ): Boolean {
    var restrictionLabelMatch = true
    restriction.extendedLabels?.forEach { key, value ->
      restrictionLabelMatch = restrictionLabelMatch && (systemInputParams != null && getNestedMapValueByPath(
        YamlUtils.load(
          JsonUtils.writeValueAsString(systemInputParams)
        ), key
      ) == value)
    }
    return restrictionLabelMatch
  }

    fun getNestedMapValueByPath(map: Map<String, Any>?, path: String): String? {
        val keys = path.split(".")
        var value = map
        for (i in 0 until keys.size - 1) {
            value = value?.get(keys[i]) as? Map<String, Any>

        }
        return value?.get(keys.last()) as? String
    }

    /**
     * 获取优先级最高的
     *
     * @param matchScopeDataDOList
     * @return
     */
    fun getPriorMatchScopeData(matchScopeDataDOList: List<MatchScopeDataDO>): MatchScopeDataDO? {
        if (matchScopeDataDOList.isEmpty()) {
            return null
        }
        var prior: MatchScopeDataDO? = null
        matchScopeDataDOList.forEach { matchScopeDataDO ->
            prior?.let {
                if (compareScopePriority(
                        it.externalType,
                        it.externalId,
                        matchScopeDataDO.externalType,
                        matchScopeDataDO.externalId
                    ) < 0
                ) {
                    prior = matchScopeDataDO
                }
            } ?: let {
                prior = matchScopeDataDO
            }
        }
        return prior
    }

    fun convertMatchScopeData(matchScopeData: MatchScopeData): MatchScopeDataDO {
        return MatchScopeDataDO(
            matchScopeData.id,
            matchScopeData.targetId,
            matchScopeData.targetType,
            matchScopeData.externalId,
            matchScopeData.externalType,
            matchScopeData.exclusions?.toNullIfBlank()?.run {
                objectMapper.readValue<MutableList<Exclusion>>(this)
            },
            matchScopeData.restrictions?.toNullIfBlank()?.run {
                objectMapper.readValue<MutableList<Restriction>>(this)
            },
            matchScopeData.creator,
            matchScopeData.modifier,
            matchScopeData.gmtCreate,
            matchScopeData.gmtModified,
            matchScopeData.isDeleted
        )
    }

    /**
     * 获得app的全量产品线路径
     *
     * @param appInfo
     * @return
     */
    private fun getProductLinePath(appInfo: AppInfo): String {
        val buId = appInfo.buId
        val productLineFullPath = appInfo.productFullLineIdPath
        val productLinePath = "${buId}$BU_PRODUCT_LINE_SPLITTER$productLineFullPath"
        checkProductLineIsLegal(productLinePath)
        return productLinePath
    }

    companion object {
        const val ALIBABA_GROUP = "alibaba" // alibaba group
        const val BU_PRODUCT_LINE_SPLITTER = "#"
        const val PRODUCT_LINE_SPLITTER = "_"
    }
}