package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.external.NormandyGropApi
import com.alibaba.koastline.multiclusters.external.model.GpuResourceMappingDTO
import com.alibaba.koastline.multiclusters.resourceobj.hook.GpuAffinitySpecFeatureImportHook.Companion.FEATURE_KEY
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.GPU_AFFINITY_SPEC_FEATURE_KEY
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Duration

@Service
@FeatureImportHookMeta(FEATURE_KEY)
class GpuAffinitySpecFeatureImportHook: FeatureImportHook {
    @Autowired
    lateinit var normandyGropApi: NormandyGropApi

    private val gpuCardModelMappingCache = CacheBuilder.newBuilder()
        .maximumSize(100)
        // refresh 触发缓存更新，同时仍然返回旧值
        .refreshAfterWrite(Duration.ofHours(2))
        .build(object : CacheLoader<String, Map<String, String>>() {
            override fun load(key: String): Map<String, String> {
                require(key == GPU_RESOURCE_MAPPING_CACHE_KEY) {
                    "unsupported cache key $key"
                }
                return normandyGropApi.queryGpuResourceMapping()
                    .filter { it.gpuQuotaName != it.gpuDetailCardModel }
                    .associateBy(GpuResourceMappingDTO::gpuQuotaName, GpuResourceMappingDTO::gpuDetailCardModel)
            }
        })

    override fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport {
        check(featureImport.resourceObjectFeatureKey == FEATURE_KEY) {
            "unexpected feature key ${featureImport.resourceObjectFeatureKey}, expect $FEATURE_KEY"
        }

        if (featureImport.paramMap.isNullOrEmpty()) {
            return featureImport
        }

        val gpuCardModelMapping = gpuCardModelMappingCache.get(GPU_RESOURCE_MAPPING_CACHE_KEY)

        val paramMap = YamlUtils.load(featureImport.paramMap!!)
        val paramProcessed = paramMap.mapValues { (key, value) ->
            if (key == "gpu") {
                val gpu = value as Map<String, Any>
                gpu.mapValues { (gpuKey, gpuValue) ->
                    if (gpuKey == "models") {
                        val detailCardModels = mutableListOf<String>()
                        (gpuValue as List<String>).forEach { gpuQuotaName ->
                            val detailCardList = gpuCardModelMapping[gpuQuotaName]
                            if (detailCardList != null) {
                                detailCardModels.addAll(
                                    StringUtils.split(detailCardList, ',').map { it.trim() }
                                )
                            } else {
                                detailCardModels.add(gpuQuotaName)
                            }
                        }
                        detailCardModels
                    } else {
                        gpuValue
                    }
                }
            } else {
                value
            }
        }


        val processedImport = featureImport.copy(
            paramMap = YamlUtils.dump(paramProcessed)
        )
        return processedImport
    }

    companion object {
        const val FEATURE_KEY = GPU_AFFINITY_SPEC_FEATURE_KEY
        private const val GPU_RESOURCE_MAPPING_CACHE_KEY = "GPU_RESOURCE_MAPPING_CACHE_KEY"
    }
}