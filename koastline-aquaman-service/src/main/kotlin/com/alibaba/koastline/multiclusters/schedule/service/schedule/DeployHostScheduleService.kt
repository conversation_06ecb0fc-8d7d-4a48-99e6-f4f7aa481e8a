package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostResourceScopeService
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostWorkloadMetaService
import com.alibaba.koastline.multiclusters.resourcescope.model.AppGroupScope
import com.alibaba.koastline.multiclusters.resourcescope.model.AppGroupScopeRestriction
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeDO
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostWorkloadMetaCreateDto
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostWorkloadMetaDO
import com.alibaba.koastline.multiclusters.resourcescope.model.HostResourceScopeTarget
import com.alibaba.koastline.multiclusters.schedule.model.DeployMode
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants.Companion.RESOURCE_NUM
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.ScheduleFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * @author:    <EMAIL>
 * @description:  主机发布调度服务
 * @date:    2025/3/10 9:53 AM
 */
@Component(value = "DeployHostScheduleService")
class DeployHostScheduleService : ScheduleFacade {
    @Autowired
    lateinit var envHostResourceScopeService: EnvHostResourceScopeService

    @Autowired
    lateinit var envHostWorkloadMetaService: EnvHostWorkloadMetaService

    @Autowired
    lateinit var runningStateScheduleService: RunningStateScheduleService
    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        val currentEnvStackId = checkNotNull(content.resourceScope.envStackId)
        val envHostResourceScope = envHostResourceScopeService.findByCurrentEnvStackId(currentEnvStackId)
            ?: throw BizException("envHostResourceScope not found, currentEnvStackId: $currentEnvStackId")
        val envHostWorkloadMetaList = envHostWorkloadMetaService.listByEnvStackId(currentEnvStackId)
        val filteredBaseRunningStateWorkloadList = matchBaseRunningStateWorkloadWithEnvHostResourceScope(
            envHostResourceScope = envHostResourceScope,
            baseRunningStateWorkloadList = getBaseRunningStateWorkloadList(
                content = content,
                baseEnvStackId = envHostResourceScope.baseEnvStackId
            )
        )
        return ScheduleResult(
            deployMode = DeployMode.HOST,
            workloadExpectedStates = computeScheduleWorkloadList(
                filteredBaseRunningStateWorkloadList = filteredBaseRunningStateWorkloadList,
                envHostWorkloadMetaList = envHostWorkloadMetaList,
            ).apply {
                if (content.dryRun != true) {
                    envHostWorkloadMetaService.deleteByEnvStackId(
                        envStackId = currentEnvStackId,
                    )
                    this.forEach {
                        envHostWorkloadMetaService.create(
                            EnvHostWorkloadMetaCreateDto(
                                envStackId = currentEnvStackId,
                                appName = it.workloadMetadataConstraint.appName,
                                resourceGroup = it.workloadMetadataConstraint.resourceGroup,
                                unit = it.workloadMetadataConstraint.unit,
                                site = it.workloadMetadataConstraint.site,
                                stage = it.workloadMetadataConstraint.stage,
                                clusterId = it.workloadMetadataConstraint.clusterId,
                                creator = "ADMIN",
                            )
                        )
                    }
                }
            }
        )
    }

    /**
     * 基于过滤&归并的基准环境运行时Workload列表（MAP每个KEY代表Beta的一个维度，最终选择一个Workload）
     * 与当前Beta的Workload列表进行匹配，优先在上述维度内选择已有的BetaWorkload，如果没有则随机返回一个
     */
    private fun computeScheduleWorkloadList(
        filteredBaseRunningStateWorkloadList: Map<String, List<WorkloadExpectedState>>,
        envHostWorkloadMetaList: List<EnvHostWorkloadMetaDO>,
    ): List<WorkloadExpectedState> {
        val scheduleWorkloadExpectedStateList = mutableListOf<WorkloadExpectedState>()
        for (key in filteredBaseRunningStateWorkloadList.keys) {
            filteredBaseRunningStateWorkloadList[key]!!.firstOrNull { workloadExpectedState ->
                envHostWorkloadMetaService.match(workloadExpectedState, envHostWorkloadMetaList)
            }?.let {
                scheduleWorkloadExpectedStateList.add(it)
            } ?: let {
                scheduleWorkloadExpectedStateList.add(
                    filteredBaseRunningStateWorkloadList[key]!!.sortedByDescending { it.params[RESOURCE_NUM]?.toInt()?: 0 }[0]
                )
            }

        }
        return scheduleWorkloadExpectedStateList
    }

    /**
     * 通过Beta环境的资源配置，匹配基准环境当前运行时符合的Workload列表
     */
    private fun matchBaseRunningStateWorkloadWithEnvHostResourceScope(
        envHostResourceScope: EnvHostResourceScopeDO,
        baseRunningStateWorkloadList: List<WorkloadExpectedState>
    ): Map<String, List<WorkloadExpectedState>> {
        if (envHostResourceScope.resourceScope.target == HostResourceScopeTarget.ENV) {
            // 基于基准环境的环境粒度指定一台
            return baseRunningStateWorkloadList.ifEmpty {
                throw BizException("Beta环境配置的基准正式环境缺少资源，请现在正式环境扩容资源后再针对Beta环境进行发布，Beta环境StackId:${envHostResourceScope.currentEnvStackId}")
            }.run {
                mapOf(HostResourceScopeTarget.ENV.name to this)
            }
        }
        envHostResourceScope.resourceScope.appGroupScopes.ifEmpty {
            throw BizException("Beta环境配置主机资源来自正式环境分组，但是缺少对应的分组配置，Beta环境StackId:${envHostResourceScope.currentEnvStackId}")
        }
        val filteredBaseRunningStateWorkloadMap = mutableMapOf<String, List<WorkloadExpectedState>>()
        envHostResourceScope.resourceScope.appGroupScopes.forEach { appGroupScope ->
            filteredBaseRunningStateWorkloadMap.putAll(
                matchBaseRunningStateWorkloadWithAppGroupScope(
                    appGroupScope = appGroupScope,
                    baseRunningStateWorkloadList = baseRunningStateWorkloadList,
                    envHostResourceScope.currentEnvStackId
                )
            )
        }
        return filteredBaseRunningStateWorkloadMap
    }

    private fun matchBaseRunningStateWorkloadWithAppGroupScope(
        appGroupScope: AppGroupScope,
        baseRunningStateWorkloadList: List<WorkloadExpectedState>,
        currentEnvStackId: String
    ): Map<String, List<WorkloadExpectedState>> {
        return baseRunningStateWorkloadList.filter {
            it.workloadMetadataConstraint.resourceGroup == appGroupScope.appGroupName
        }.ifEmpty {
            throw BizException("Beta环境配置主机资源来自正式环境分组${appGroupScope.appGroupName}，但是缺少对应的分组机器资源，Beta环境StackId:${currentEnvStackId}")
        }.run {
            if (appGroupScope.restrictions.isEmpty()) {
                mapOf("${HostResourceScopeTarget.APP_GROUP.name}-${appGroupScope}" to this)
            } else {
                /**
                 * 基于分组限定条件（单元、机房）计算匹配的Workload列表
                 */
                val filteredBaseRunningStateWorkloadMap = mutableMapOf<String, List<WorkloadExpectedState>>()
                appGroupScope.restrictions.forEach { appGroupScopeRestriction ->
                    filteredBaseRunningStateWorkloadMap[toAppGroupScopeRestrictionString(
                        appGroupScope,
                        appGroupScopeRestriction
                    )] =
                        this.filter {
                            it.workloadMetadataConstraint.unit == appGroupScopeRestriction.unit &&
                                    (appGroupScopeRestriction.site == null || it.workloadMetadataConstraint.site == appGroupScopeRestriction.site)
                        }.ifEmpty {
                            throw BizException("Beta环境配置主机资源来自正式环境分组${appGroupScope.appGroupName}-${appGroupScopeRestriction.unit}-${appGroupScopeRestriction.site}，但是缺少对应的分组&单元&机房机器资源，Beta环境StackId:${currentEnvStackId}")
                        }
                }
                filteredBaseRunningStateWorkloadMap
            }
        }
    }

    private fun toAppGroupScopeRestrictionString(
        appGroupScope: AppGroupScope,
        appGroupScopeRestriction: AppGroupScopeRestriction
    ): String {
        return "${appGroupScope.appGroupName}-${appGroupScopeRestriction.unit}-${appGroupScopeRestriction.site}"
    }

    /**
     * 获取基准环境当前运行时Workload列表
     */
    private fun getBaseRunningStateWorkloadList(
        content: ScheduleRequestContent,
        baseEnvStackId: String
    ): List<WorkloadExpectedState> {
        return runningStateScheduleService.doSchedule(
            content.copy(
                resourceScope = content.resourceScope.copy(envStackId = baseEnvStackId)
            )
        ).workloadExpectedStates.filter {
            it.workloadMetadataConstraint.stage == MetadataStageEnum.PUBLISH.name
        }
    }
}