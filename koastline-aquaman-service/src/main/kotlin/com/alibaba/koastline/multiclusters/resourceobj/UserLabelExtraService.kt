package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.common.config.UserLabelExternalDowngradeProperties
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.AtomApi
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelType
import org.springframework.stereotype.Component

@Component
class UserLabelExtraService(
    val atomApi: AtomApi,
    val userLabelExternalDowngradeProperties: UserLabelExternalDowngradeProperties
) {
    val log by logger()
    /**
     * 暂时双写原外部系统元数据
     */
    fun updateExtraLabel(userLabelCreateReqDto: UserLabelCreateReqDto) {
        if (userLabelExternalDowngradeProperties.isDowngrade(userLabelCreateReqDto.labelName)) {
            log.info("用户标签:${userLabelCreateReqDto.labelName}双写降级,userLabelCreateReqDto:${userLabelCreateReqDto}")
            return
        }
        if (userLabelCreateReqDto.externalType == UserLabelExternalType.APPLICATION) {
            log.info("用户标签:${userLabelCreateReqDto.labelName}双写,userLabelCreateReqDto:${userLabelCreateReqDto}")
            when (userLabelCreateReqDto.labelName) {
                //更新model
                UserLabelType.MODEL.code, UserLabelType.OS.code, UserLabelType.STARTUP.code, UserLabelType.TEMPLATE.code
                    -> atomApi.updateAppLabel(userLabelCreateReqDto.externalId, userLabelCreateReqDto.labelName, userLabelCreateReqDto.labelValue)
                //更新gpu,适配atom功能，gpu <=0 时删除gpu label
                UserLabelType.GPU_COUNT.code -> atomApi.updateAppLabel(userLabelCreateReqDto.externalId, userLabelCreateReqDto.labelName, userLabelCreateReqDto.labelValue ?.toNullIfBlank() ?: "0")
            }
        }
    }
}