package com.alibaba.koastline.multiclusters.resourceobj.model

import com.fasterxml.jackson.annotation.JsonProperty
import java.util.*

data class ResourceObjectFeatureFrontDO(
    /**
     * ID
     */
    val id: Long,
    /**
     * 资源对象特性Key
     */
    @JsonProperty("traitKey")
    val resourceObjectFeatureKey: String,
    /**
     * 标题
     */
    val title: String,
    /**
     * 使用范围,DEPLOY、SCALE_OUT,可多选，以逗号分隔
     */
    val useScope: List<ResourceObjectFeatureUseScope>,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String = "N",
    val feasibleProtocols: String,
    val type: String,
    val effectiveStage: String,
    val jsonSchema: String?,
    val displayTheme: String,
    val version: String,
    val submitters: String,
)

data class ResourceObjectFeatureUseScope(
    /**
     * 场景
     */
    val scene: ResourceObjectSceneEnum,
    /**
     * 构建类型
     */
    val buildType: ResourceObjectBuildTypeEnum
)
