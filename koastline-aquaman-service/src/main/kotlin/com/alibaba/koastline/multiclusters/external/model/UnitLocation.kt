package com.alibaba.koastline.multiclusters.external.model

import com.fasterxml.jackson.annotation.JsonAlias

/**
 * <AUTHOR>
 * date 2024/4/15 11:54
 */

data class UnitGroup(
    /**
     * 单元类型
     */
    val unitType: String,
    /**
     * 单元描述
     */
    val description: String,
    /**
     * 单元坐标列表
     */
    val unitLocationList: List<UnitLocation>,
)

data class UnitLocation(
    @field:JsonAlias("idc")
    val site: String,
    @field:JsonAlias("unit")
    val unit: String,
    @field:JsonAlias("trade")
    val unitType: String,
    @field:JsonAlias("isEcs")
    val ecs: String
) {
    fun isEcs() = ecs.toBoolean()
}

data class HcrmUnitInfo(
    val description: String?,
    val empId: String?,
    val empNick: String?,
    val remark: String?,
    /**
     * 需要过滤掉EFFECTED的status
     */
    val status: String,
    val unitLocation: String,
    val unitType: String,
)