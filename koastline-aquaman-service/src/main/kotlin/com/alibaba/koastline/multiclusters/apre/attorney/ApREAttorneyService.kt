package com.alibaba.koastline.multiclusters.apre.attorney

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService.Companion.BU_PRODUCT_LINE_SPLITTER
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.AttorneyApRE
import com.alibaba.koastline.multiclusters.apre.model.AttorneyScope
import com.alibaba.koastline.multiclusters.apre.model.Exclusion
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.INHERIT
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.SELF_DEF
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.APPLICATION
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.valueOf
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREAttorneyException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREBindingDataException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.CoordinateUtils
import com.alibaba.koastline.multiclusters.data.vo.PageData
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ApREAttorneyService {
    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var apREBindingService: ApREBindingService

    @Autowired
    lateinit var apREService: ApREService

    @Autowired
    lateinit var apRELabelService: ApRELabelService


    val log by logger()

    /**
     * 获得单个ApRE旗下的所有授权
     * Attorney需要保证ApREBindingData & MatchScopeData
     *
     * @param apREKey
     * @return
     */
    fun listAttorneyByApREKey(apREKey: String): List<Attorney> {
        val attorneyList = mutableListOf<Attorney>()
        apREBindingService.listApREBindingDataByApREKey(apREKey).forEach { apREBindingData ->
            matchScopeService.listByTarget(
                targetId = apREBindingData.id!!,
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
            ).firstOrNull()?.let {
                attorneyList.add(
                    Attorney(
                        apREBindingData = apREBindingData,
                        matchScopeData = it
                    )
                )
            }
        }
        return attorneyList
    }

    /**
     * 获取单个的attorney
     *
     * @param apREBindingDataId
     * @return
     */
    fun findAttorneyByApREBindingDataId(apREBindingDataId: Long): Attorney? {
        return apREBindingService.findApREBindingData(apREBindingDataId)?.let { apREBindingData ->
            matchScopeService.listByTarget(
                targetId = apREBindingData.id!!,
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
            ).firstOrNull()?.let { ms ->
                Attorney(
                    apREBindingData = apREBindingData, matchScopeData = ms
                )
            }
        }
    }


    /**
     * 创建授权ApRE授权
     *
     * @param attorney
     */
    @Transactional
    fun createAttorney(attorney: Attorney) {
        val (apREBindingData, matchScopeData) = attorney
        val existedApREBindingDataList = apREBindingService.listApREBindingDataByApREKey(apREBindingData.runtimeEnvKey)
        existedApREBindingDataList.forEach { apREBindingData ->
            // 检测是否存在授权
            matchScopeService.findByTargetAndExternal(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                targetId = apREBindingData.id!!,
                externalId = matchScopeData.externalId,
                externalType = matchScopeData.externalType
            )?.let {
                throw ApREBindingDataException("重复授权行为，请检查授权动作")
            }
        }
        // 创建apREBindingData 并且配套创建MatchScopeData
        matchScopeService.createMatchScopeIgnoreWhileExist(
            matchScopeData.copy(
                targetId = apREBindingService.createApREBindingData(apREBindingData).id,
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            )
        )
    }

    /**
     * 批量创建ApRE授权
     *
     * @param attorneyList
     */
    @Transactional
    fun batchCreateAttorney(attorneyList: List<Attorney>) {
        require(attorneyList.all {
            it.matchScopeData.targetType == MatchScopeTargetTypeEnum.ApREBindingData.name
        }) { "targetType of matchScopeData should be ApREBindingData" }
        attorneyList.forEach { attorneyCreateDto ->
            createAttorney(attorneyCreateDto)
        }
    }

    /**
     * 指定坐标开放授权
     * 注:范围自动授权是开放所有特性&集群
     *
     * @param creator
     * @param unit
     * @param site
     * @param stage
     * @param externalId
     * @param externalTypeEnum
     */
    @Transactional
    fun createAttorneyApREByCoordinate(
        creator: String,
        unit: String,
        site: String,
        stage: String,
        externalId: String,
        externalTypeEnum: MatchScopeExternalTypeEnum
    ) {
        log.info("createAttorneyApREByCoordinate creator:$creator, unit:$unit, site:$site, stage:$stage, externalId:$externalId, externalTypeEnum:$externalTypeEnum")

        val apREs = apREService.listBaseApREBySiteAndStageAndUnit(unit = unit, site = site, stage = stage)

        val matchScopeDataList = matchScopeService.listByTargetTypeAndExternal(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            externalId = externalId,
            externalType = externalTypeEnum.name
        )

        val attorneyApREKeyList =
            apREBindingService.listApREBindingDataByIdList(matchScopeDataList.map { it.targetId!! })
                .map { it.runtimeEnvKey }
        apREs.filter { !attorneyApREKeyList.contains(it.runtimeEnvKey) }.forEach { apRE ->
            createAttorney(
                Attorney(
                    apREBindingData = ApREBindingData(
                        id = null,
                        runtimeEnvKey = apRE.runtimeEnvKey!!,
                        apREBindingTerm = ApREBindingTerm(allSupported = true)
                    ),
                    matchScopeData = MatchScopeDataDO(
                        targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                        externalId = externalId, externalType = externalTypeEnum.name,
                        creator = creator, modifier = creator
                    )
                )
            )
        }
    }

    /**
     * 展示不同类型的授权情况
     *
     * @param site
     * @param stage
     * @param unit
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun findAttorneyScopeByProperties(
        site: String? = null,
        stage: String? = null,
        unit: String? = null,
        externalId: String,
        externalType: String,
        includeScopeType: IncludeScopeType,
        pageSize: Int,
        pageNumber: Int
    ): AttorneyScope {
        CoordinateUtils.coordinatesPropertiesValidate(
            stage = stage,
            site = site,
            unit = unit,
            externalId = externalId,
            externalType = externalType
        )

        val filterMatchScopeList = getFilterMatchScopeDataListForListAttorney(
            externalId = externalId, externalType = valueOf(externalType), includeScopeType = includeScopeType
        )

        if (filterMatchScopeList.isEmpty()) {
            return AttorneyScope(
                externalId = externalId,
                externalType = externalType,
                pageData = PageData.zeroPage(pageSize = pageSize, pageNumber = pageNumber)
            )
        }

        val pageData = apREBindingService.listApREBindingDataByProperties(
            ids = filterMatchScopeList.map { it.targetId!! }, site = site, unit = unit, stage = stage,
            pageNumber = pageNumber,
            pageSize = pageSize
        )

        val attorneys = pageData.data!!.map { apREBindingData ->
            Attorney(
                apREBindingData = apREBindingData,
                //这个由ms作为index来查找 能保证一定会有ms能对应上apREBindingData
                matchScopeData = filterMatchScopeList.first { it.targetId == apREBindingData.id })
        }

        val attorneyApREs = getAttorneyApREs(
            attorneys = attorneys,
            goalExternalId = externalId,
            goalExternalType = valueOf(externalType)
        )

        check(attorneyApREs.size == attorneys.size) {
            "attorney size should key size same with size of attorneyApREs!"
        }

        return AttorneyScope(
            externalType = externalType, externalId = externalId,
            pageData = PageData.of(
                pageSize = pageSize,
                pageNumber = pageNumber,
                totalCount = pageData.totalCount,
                data = attorneyApREs
            )
        )
    }

    /**
     * 按照类型对Attorney类型进行预过滤
     *
     * @param externalId
     * @param externalType
     * @param includeScopeType
     * @return
     */
    private fun getFilterMatchScopeDataListForListAttorney(
        externalId: String,
        externalType: MatchScopeExternalTypeEnum,
        includeScopeType: IncludeScopeType
    ): List<MatchScopeDataDO> {
        return when (externalType) {
            APPLICATION -> {
                val msList = matchScopeService.findMatchScopesByTargetAndExternalForApp(
                    targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                    appName = externalId
                )
                when (includeScopeType) {
                    SELF_DEF -> {
                        msList.filter { it.externalType == APPLICATION.name }
                    }

                    INHERIT -> {
                        msList.filter { it.externalType == AONE_PRODUCTLINE.name }
                    }

                    else -> {
                        msList
                    }
                }
            }

            AONE_PRODUCTLINE -> {
                val buId = externalId.split(BU_PRODUCT_LINE_SPLITTER).first().toLong()
                val productFullLineIdPath = externalId.substringAfter(BU_PRODUCT_LINE_SPLITTER)
                val msList = matchScopeService.findMatchScopesByTargetAndExternalForProductLine(
                    targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                    buId = buId, productFullLineIdPath = productFullLineIdPath
                )
                when (includeScopeType) {
                    SELF_DEF -> {
                        msList.filter { it.externalId == externalId }
                    }

                    INHERIT -> {
                        msList.filter { it.externalId != externalId }
                    }

                    else -> {
                        msList
                    }
                }
            }

            else -> {
                throw ApREAttorneyException("listAttorneyByProperties only application & aone-product-line")
            }
        }
    }

    /**
     * 获取对应Attorney类型的AttorneyApRE
     *
     * @param attorneys
     * @return
     */
    fun getAttorneyApREs(
        attorneys: List<Attorney>,
        goalExternalId: String,
        goalExternalType: MatchScopeExternalTypeEnum
    ): List<AttorneyApRE> {
        val maskedApREsMap =
            apREService.listApREDetailsByApREKeys(attorneys.map { it.apREBindingData.runtimeEnvKey }).map { apRE ->
                apREService.maskApRE(
                    apRE,
                    attorneys.first { apRE.runtimeEnvKey == it.apREBindingData.runtimeEnvKey }
                        .apREBindingData.apREBindingTerm
                )
            }.associateBy { it.runtimeEnvKey!! }

        return attorneys.mapNotNull { attorney ->
            val apREKey = attorney.apREBindingData.runtimeEnvKey
            val apRE = maskedApREsMap[apREKey]
            if (apRE != null) {
                AttorneyApRE(
                    apREKey = apREKey,
                    apREName = apRE.name ?: "${apRE.stage}-${apRE.unit}-${apRE.az}",
                    unit = apRE.unit,
                    site = apRE.az,
                    stage = apRE.stage,
                    attorney = attorney,
                    supportedCluster = apRE.resources.map { it.clusterId!! },
                    supportedFeatureLabel = (apRE.resources.flatMap { it.apRELabels } + (apRE.apRELabels
                        ?: emptyList())).distinctBy {
                        "${it.name}${it.value}${it.type}"
                    },
                    includeScopeType = matchScopeService.getIncludeType(
                        goalExternalId = goalExternalId,
                        goalExternalType = goalExternalType,
                        externalId = attorney.matchScopeData.externalId,
                        externalType = MatchScopeExternalTypeEnum.valueOf(attorney.matchScopeData.externalType)
                    )
                )
            } else {
                null
            }
        }
    }

    /**
     * 取消授权
     * 1.如果是当前如果是同级别的授权 直接删除授权范围就可以
     * 2.如果当前非同级别的授权 需要将其排除在授权范围内并更新
     *
     * @param apREBindingDataId
     * @param externalId
     * @param externalType
     * @param modifier
     */
    fun cancelAttorney(
        apREBindingDataId: Long,
        externalId: String,
        externalType: String,
        modifier: String
    ) {
        // 查询到这个externalType
        findAttorneyByApREBindingDataId(apREBindingDataId = apREBindingDataId)?.let { attorney: Attorney ->
            val isBelong2 = matchScopeService.compareExternalScopeBelongs2(
                goalExternalId = externalId,
                goalExternalType = valueOf(externalType),
                externalId = attorney.matchScopeData.externalId,
                externalType = valueOf(attorney.matchScopeData.externalType)
            )
            if(isBelong2  == 0){
                // 同级别match上 删除
                apREBindingService.deleteApREBindingDataById(id = apREBindingDataId, modifier = modifier)
            }else if(isBelong2 < 0){
                // 属于子范围 加入exclusion中
                val toAddExclusion = Exclusion(externalId = externalId, externalType = externalType)
                val newExclusionList = (attorney.matchScopeData.exclusions?: emptyList()) + toAddExclusion
                matchScopeService.updateMatchScope(attorney.matchScopeData.copy(
                    exclusions = newExclusionList
                ))
            }else {
                throw ApREException("取消授权动作超出授权范围 externalId:$externalId, externalType:$externalType, apREBindingDataId:$apREBindingDataId")
            }
        }
    }
}
