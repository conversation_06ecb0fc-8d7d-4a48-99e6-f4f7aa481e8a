package com.alibaba.koastline.multiclusters.apre.attorney

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService.Companion.BU_PRODUCT_LINE_SPLITTER
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ClusterBindingInfoNotFoundException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.ClusterBindingRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentBindingData
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.pagehelper.PageHelper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*

/**
 * 只涉及基础ApREBindingData操作
 * 授权相关为扩展service<ApREAttorneyService>
 *
 * @property objectMapper
 */
@Component
class ApREBindingService(val objectMapper: ObjectMapper) {
    @Autowired
    lateinit var matchScopeService: MatchScopeService

    @Autowired
    lateinit var clusterBindingRepo: ClusterBindingRepo

    val log by logger()

    /**
     * 根据IdentityInfo获取授权的运行时环境以及选择器
     * 注：
     * 1.运行时环境可能已删除或待下线
     * 2.可能存在重复的运行时环境授权，需要在查询运行时环境时合并选择器
     *
     * @param identityInfo
     * @return
     */
    fun getApREBindingDataByIdentityInfo(identityInfo: IdentityInfo): List<ApREBindingData> {
        val apREBindingDataList = mutableListOf<ApREBindingData>()
        //获取授权数据
        matchScopeService.findMatchScopesByTargetAndExternalForApp(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            appName = identityInfo.appName,
            resourceGroupList = identityInfo.nodeGroup?.let { listOf(it) } ?: emptyList(),
            buId = identityInfo.productLineIdPath?.run { this.substringBefore(BU_PRODUCT_LINE_SPLITTER) },
            productFullLineIdPath = identityInfo.productLineIdPath?.run { this.substringAfter(BU_PRODUCT_LINE_SPLITTER) }
        ).map {
            it.targetId!!
        }.distinct().let {
            if (it.isNotEmpty()) {
                clusterBindingRepo.listByIdList(it).forEach { envBindingInfo ->
                    apREBindingDataList.add(buildApREBindingData(envBindingInfo))
                }
            }
        }
        return apREBindingDataList
    }

    /**
     * 通过apREBindingDataId来查找apREBindingData
     *
     * @param id
     * @return
     */
    fun findApREBindingData(id:Long):ApREBindingData? {
        return clusterBindingRepo.findById(id)?.let {
            buildApREBindingData(it)
        }
    }

    /**
     * 使用ApREBindingData的ids进行批量查询
     *
     * @param ids
     * @return
     */
    fun listApREBindingDataByIdList(ids: List<Long>): List<ApREBindingData> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        return clusterBindingRepo.listByIdList(ids).map {
            buildApREBindingData(it)
        }
    }

    /**
     * 按照条件进行过滤
     *
     * @param ids
     * @param pageSize
     * @param pageNumber
     * @return
     */
    fun listApREBindingDataByProperties(
        site: String? = null,
        unit: String? = null,
        stage: String? = null,
        ids: List<Long>,
        pageSize: Int,
        pageNumber: Int
    ): PageData<ApREBindingData> {
        return PageData.transformFrom(
            PageHelper.startPage<ClusterEnvironmentBindingData>(pageNumber, pageSize, "gmt_modified DESC")
                .doSelectPage<ClusterEnvironmentBindingData> {
                    clusterBindingRepo.listLegalBindingDataByIdList(
                        idList = ids,
                        site = site,
                        unit = unit,
                        stage = stage
                    )
                }).map { buildApREBindingData(it) }
    }

    /**
     * 获得单个ApRE旗下的所有ApREBindingData
     *
     * @param apREKey
     * @return
     */
    fun listApREBindingDataByApREKey(apREKey: String): List<ApREBindingData> {
        return clusterBindingRepo.listClusterBindingData(apREKey).map {
            buildApREBindingData(it)
        }
    }

    /**
     * 创建ApREBindingData 并更新id
     *
     * @param apREBindingData
     * @return
     */
    fun createApREBindingData(apREBindingData: ApREBindingData): ApREBindingData {
        // 创建授权内容范围
        val clusterEnvBindingData = buildEnvBindingInfo(apREBindingData)
        clusterBindingRepo.createClusterBindingData(clusterEnvBindingData)
        return apREBindingData.copy(
            id = checkNotNull(clusterEnvBindingData.id) { "target id cannot be null after insert!" }
        )
    }

    /**
     * 更新ApREBindingData中的授权指定范围
     *
     * @param apREBindingDataId
     * @param selector
     */
    fun updateApREBindingSelectorData(apREBindingDataId: Long, selector: ApREBindingTerm?) {
        clusterBindingRepo.findById(apREBindingDataId) ?: let {
            throw ClusterBindingInfoNotFoundException()
        }
        clusterBindingRepo.updateClusterBindingSelectorDataById(apREBindingDataId, selector?.run {
            objectMapper.writeValueAsString(this)
        })
    }

    /**
     * 删除一个ApRE旗下所有的授权
     *
     * @param apREKey
     * @param modifier
     */
    @Transactional
    fun deleteApREBindingDataByApREKey(apREKey: String, modifier: String) {
        log.info("deleteApREBindingDataByApREKey: runtimeEnvKey:$apREKey")
        val apREBindingDataIdList = clusterBindingRepo.listClusterBindingDataByClusterEnvKey(apREKey).map { it.id!! }
        if (apREBindingDataIdList.isNotEmpty()) {
            apREBindingDataIdList.forEach {
                matchScopeService.deleteMatchScopeByTarget(
                    targetId = it,
                    targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                    modifier
                )
            }
        }
        clusterBindingRepo.deleteClusterBindingDataByClusterEnvKey(apREKey)
    }

    /**
     * 删除ApREBindingData并级联删除对应的MatchScopeData
     *
     * @param id
     * @param modifier
     */
    @Transactional
    fun deleteApREBindingDataById(id: Long, modifier: String) {
        log.info("deleteApREBindingDataById: id:$id")
        clusterBindingRepo.deleteClusterBindingDataById(id)
        matchScopeService.deleteMatchScopeByTarget(
            targetId = id,
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            modifier
        )
    }

    /**
     * 数据库层对象转换成ApREBindingData
     *
     * @param envBindingInfo
     * @return
     */
    private fun buildApREBindingData(envBindingInfo: ClusterEnvironmentBindingData): ApREBindingData {
        return ApREBindingData(
            id = envBindingInfo.id,
            runtimeEnvKey = envBindingInfo.clusterEnvKey,
            apREBindingTerm = envBindingInfo.selector?.toNullIfBlank()
                ?.run { objectMapper.readValue<ApREBindingTerm>(this) }
        )
    }

    /**
     * 转换成为数据库对象ClusterEnvironmentBindingData
     *
     * @param apREBindingData
     * @return
     */
    private fun buildEnvBindingInfo(apREBindingData: ApREBindingData): ClusterEnvironmentBindingData {
        val now = Date(Instant.now().toEpochMilli())
        return ClusterEnvironmentBindingData(
            id = null,
            clusterEnvKey = apREBindingData.runtimeEnvKey,
            externalId = COMPATIBLE_CLUSTER_ENV_BINDING_DATA_EXTERNAL_ID,
            externalIdType = COMPATIBLE_CLUSTER_ENV_BINDING_DATA_EXTERNAL_TYPE,
            gmtCreate = now,
            gmtModified = now,
            selector = apREBindingData.apREBindingTerm?.run { objectMapper.writeValueAsString(this) }
        )
    }

    companion object {
        const val COMPATIBLE_CLUSTER_ENV_BINDING_DATA_EXTERNAL_ID = "-1"
        const val COMPATIBLE_CLUSTER_ENV_BINDING_DATA_EXTERNAL_TYPE = "aone-productline"
    }
}