package com.alibaba.koastline.multiclusters.fed.model.req

import com.alibaba.koastline.multiclusters.common.utils.ParameterChecker
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.util.Date

@ApiModel("创建fedPolicy")
data class FedClusterPolicyCreateReq(
    @ApiModelProperty("绑定的Fed集群id", required = true)
    val policyTargetClusterId: Long,
    @ApiModelProperty("fed policy名称", required = true)
    val policyName: String,
    @ApiModelProperty("资源环境的优先级别", required = true)
    val policyPriority: Int,
    @ApiModelProperty("被规则影响的资源类型", required = true)
    val resourceKind: String,
    @ApiModelProperty("接入规则", required = true)
    val policySpec: Map<String, Any>,
    @ApiModelProperty("创建人", required = true)
    val creator: String
){
    fun validate(){
        ParameterChecker.isNotBlank(
            mapOf(
                "policyName" to policyName,
                "resourceKind" to resourceK<PERSON>,
                "creator" to creator
            ), "FedClusterPolicyCreateReq"
        )
        require(policySpec.keys.all { it.isNotBlank() }){
            "policy spec cannot be blank!"
        }
    }
}

@ApiModel("更新fedPolicy")
data class FedClusterPolicyUpdateReq(
    @ApiModelProperty("绑定的Fed集群策略", required = true)
    val policyId: Long,
    @ApiModelProperty("资源环境的优先级别", required = true)
    val policyPriority: Int,
    @ApiModelProperty("被规则影响的资源类型", required = true)
    val resourceKind: String,
    @ApiModelProperty("接入规则", required = true)
    val policySpec: Map<String, Any>,
    @ApiModelProperty("修改人", required = true)
    val modifier: String
){
    fun validate(){
        require(resourceKind.isNotBlank()){
            "resourceKind cannot be blank"
        }
        require(policySpec.keys.all { it.isNotBlank() }){
            "policy spec cannot be blank"
        }
        require(modifier.isNotBlank()){
            "modifier cannot be blank"
        }
    }
}

@ApiModel("条件查询关联的fed集群")
data class FedClusterPolicyQueryCondition(
    @ApiModelProperty("关联的fed集群", required = true)
    val fedClusterTargetId: Long,
    @ApiModelProperty("查询的fedPolicy的关键字", required = false)
    val fedPolicyKeyWords: String? = null,
    @ApiModelProperty("policySelector的关键字map", required = false)
    val policySelectors: List<String>? = null,
    @ApiModelProperty("重载关键词list", required = false)
    val overrideKeyWords: String? = null,
    @ApiModelProperty("查询的页大小", required = true)
    val pageSize: Int,
    @ApiModelProperty("查询的页标号", required = true)
    val pageNumber: Int
){
    fun validate(){
        overrideKeyWords?.let {
            require(it.isNotBlank()){
                "overrideKeyWords cannot be blank!"
            }
        }
        fedPolicyKeyWords?.let {
            require(it.isNotBlank()){
                "fedPolicyKeyWords cannot blank!"
            }
        }
        policySelectors?.let {
            require(policySelectors.all { it.isNotBlank() }){
                "policySelectors cannot be blank"
            }
        }
    }
}

@ApiModel("查询关联的fedPolicy")
data class FedPolicy(
    @ApiModelProperty("关联的fed集群策略", required = true)
    val id: Long,
    @ApiModelProperty("关联的fed集群id", required = true)
    val fedClusterId: Long,
    @ApiModelProperty("关联的fed集群优先级", required = true)
    val fedPolicyPriority: Int,
    @ApiModelProperty("fed集群的策略名称", required = true)
    val fedPolicyName: String,
    @ApiModelProperty("关联的fed集群唯一名称", required = true)
    val fedUniqueName: String,
    @ApiModelProperty("关联的fed集群调度策略", required = true)
    val schedulePlugin: List<String>,
    @ApiModelProperty("关联的fed集群规则生效范围", required = true)
    val effectedMemberClusterList: List<String>,
    @ApiModelProperty("关联的fed集群policySelector", required = true)
    val policySelectorList: List<String>,
    @ApiModelProperty("策略的修改人", required = true)
    val modifier: String,
    @ApiModelProperty("策略的修改时间", required = true)
    val gmtModified: Date,
){
    fun validate(){
        ParameterChecker.isNotBlank(
            mapOf(
                "fedPolicyName" to fedPolicyName,
                "fedUniqueName" to fedUniqueName,
                "schedulePlugin" to schedulePlugin,
                "modifier" to modifier
            ), "FedPolicyDetails"
        )
        require(effectedMemberClusterList.all { it.isNotBlank() }){
            "effectedMemberCluster cannot be blank!"
        }
        require(policySelectorList.all { it.isNotBlank() }){
            "policySelector cannot be blank!"
        }
    }
}

@ApiModel("查询详细的fedPolicy")
data class FedPolicyDetails(
    @ApiModelProperty("id", required = true)
    val id: Long,
    @ApiModelProperty("策略的修改人", required = true)
    val paramsMap: Map<String, Any>,
    @ApiModelProperty("组装完的整体", required = true)
    val yamlStr: String,
    @ApiModelProperty("策略的修改人", required = true)
    val modifier: String,
    @ApiModelProperty("策略的修改时间", required = true)
    val gmtModified: Date,
) {
    fun validate() {
        ParameterChecker.isNotBlank(
            mapOf(
                "yamlStr" to yamlStr,
                "modifier" to modifier
            ), "FedPolicyDetails"
        )
    }
}