package com.alibaba.koastline.multiclusters.authentication.models

import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.userdetails.UserDetails

/**
 * <AUTHOR>
 */
class AquamanUserDetails(val authentication: UserAuthentication,
                         private val accessSecret: String) : UserDetails {
    override fun getAuthorities(): Collection<GrantedAuthority> {
        return authentication.aquamanAuthority.map { AquamanAuthority(it) }
    }

    override fun getPassword(): String {
        return accessSecret
    }

    override fun getUsername(): String {
        return authentication.appKey
    }

    override fun isAccountNonExpired(): Boolean = true

    override fun isAccountNonLocked(): Boolean = true

    override fun isCredentialsNonExpired(): Boolean = true

    override fun isEnabled(): Boolean = true
}