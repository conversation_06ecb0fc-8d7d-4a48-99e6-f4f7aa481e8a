package com.alibaba.koastline.multiclusters.resourcescope.model

import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.NameSpaceSplitter
import java.time.Instant
import java.util.Date

/**
 * @author:    <EMAIL>
 * @date:    2025/3/10 11:18 AM
 */
data class EnvHostWorkloadMetaDO (
    val id: Long,
    val envStackId: String,
    val appName: String,
    val resourceGroup: String,
    /**
     * 站点
     */
    val site: String,
    /**
     * 单元
     */
    val unit: String,
    /**
     * 用途
     */
    val stage: String,
    /**
     * 集群ID
     */
    val clusterId: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val creator: String,
    val modifier: String,
    val isDeleted: String = "N",
){
    fun toSixMetaWorkloadString(): String{
        return "${appName}$NameSpaceSplitter${resourceGroup}$NameSpaceSplitter${site}$NameSpaceSplitter${unit}$NameSpaceSplitter${stage}$NameSpaceSplitter${clusterId}"
    }
}

data class EnvHostWorkloadMetaCreateDto(
    val envStackId: String,
    val appName: String,
    val resourceGroup: String,
    val site: String,
    val unit: String,
    val stage: String,
    val clusterId: String,
    val creator: String,
)