package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.KEY_ALL_APRE_LABEL_DEFINITION_CACHE
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApRELabelDefinitionUniqueExistException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.dao.env.LabelDefinitionRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelDefinition
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock

@Service
class ApRELabelDefineService : CacheLoader<String, List<ApRELabelDefinition>>() {
    @Autowired
    lateinit var apRELabelDefinitionRepo: LabelDefinitionRepo
    val log by logger()
    private val allApRELabelDefinitionCache: LoadingCache<String, List<ApRELabelDefinition>> = CacheBuilder.newBuilder()
                    .maximumSize(100)
                    .expireAfterWrite(1, TimeUnit.MINUTES)
                    .build(this)


    /**
     * 创建 ApRE Label Definition
     */
    @Transactional
    fun createApRELabelDefinition(
        apRELabelDefinition: ApRELabelDefinition
    ): Boolean {
        val now = Date(Instant.now().toEpochMilli())
        apRELabelDefinitionRepo.findByTitle(apRELabelDefinition.title)?.let {
            throw ApRELabelDefinitionUniqueExistException("ApRE title 不可与存量数据重复")
        }
        apRELabelDefinitionRepo.findByNameAndValue(apRELabelDefinition.name, apRELabelDefinition.value)?.let {
            throw ApRELabelDefinitionUniqueExistException("ApRE name/value: ${apRELabelDefinition.name}/${apRELabelDefinition.value} 不可与存量数据重复")
        }
        val apRELabelDef = apRELabelDefinition.copy(
            name = apRELabelDefinition.name,
            apRELabelDefinitionId = KeyGenerator.generateAlphanumericKey(ApRELabelExt.APRE_LABEL_DEF_KEY_LENGTH),
            gmtCreate = now,
            gmtModified = now,
            isDeleted = "N",
        )
        apRELabelDefinitionRepo.insert(apRELabelDef).let {
            if (it == 0) throw ApRELabelDefinitionUniqueExistException("已经存在记录")
        }
        return true
    }

    /**
     * 更新 ApRE Label Definition
     * 不存在时候创建
     */
    @Transactional
    fun updateApRELabelDefinition(
        apRELabelDefinition: ApRELabelDefinition
    ): Boolean {
        val apRELabelDefinitionId = requireNotNull(apRELabelDefinition.apRELabelDefinitionId)
        val now = Date(Instant.now().toEpochMilli())
        apRELabelDefinitionRepo.findByLabelDefinitionId(apRELabelDefinitionId).let { originData ->
            originData?.copy(
                title = apRELabelDefinition.title,
                name = apRELabelDefinition.name,
                value = apRELabelDefinition.value,
                scope = apRELabelDefinition.scope,
                gmtModified = now,
                modifier = apRELabelDefinition.modifier
            )?.let { newData ->
                if (apRELabelDefinitionRepo.updateByLabelDefinitionId(newData) != 0) return true
            }
        }

        //不存在时候创建
        if (apRELabelDefinitionRepo.insert(apRELabelDefinition) != 0) return true

        return false
    }

    /**
     * 通过使用 id 删除
     */
    fun deleteByApRELabelDefinitionId(id: String, modifier:String){
        apRELabelDefinitionRepo.deleteByLabelDefinitionId(id, modifier)
    }

    fun getApRELabelDefinition(name: String, value: String): ApRELabelDefinition? {
        return apRELabelDefinitionRepo.findByNameAndValue(name, value)
    }

    fun getCacheApRELabelDefinition(name: String, value: String): ApRELabelDefinition? {
        try {
            return queryCachedAllApRELabelDefinitions().firstOrNull {
                it.name == name && it.value == value
            }
        }catch (e: Exception) {
            log.error("queryCachedAllApRELabelDefinitions error", e)
        }
        return apRELabelDefinitionRepo.findByNameAndValue(name = name, value = value)
    }

    /**
     * list所有的 ApRE Label Definition
     */
    fun listApRELabelDefinitionsByProperties(
        title: String?, name: String?, value: String?, pageSize: Int, pageNumber: Int
    ): PageData<ApRELabelDefinition> {
        val page: Page<ApRELabelDefinition> = PageHelper.startPage<ApRELabelDefinition>(pageNumber, pageSize)
            .doSelectPage {
                apRELabelDefinitionRepo.listLabelDefinitionsByProperties(
                    title = title,
                    name = name,
                    value = value,
                )
            }
        return PageData.transformFrom(page)
    }

    /**
     * 按照apRELabelDefinitionId查询ApRELabelDefinition
     */
    fun findApRELabelDefinitionsByApRELabelDefKey(apRELabelDefinitionId: String): ApRELabelDefinition? {
        return apRELabelDefinitionRepo.findByLabelDefinitionId(apRELabelDefinitionId)
    }

    /**
     * 批量注入ApRE Label Title
     */
    fun fillApRELabelTitles(apRELabels: List<ApRELabelDO>) {
        apRELabels.forEach {
            it.title = getCacheApRELabelDefinition(it.name, it.value)?.title ?: "${it.name}=${it.value}"
        }
    }

    /**
     * 查询所有ApRE Label Definition
     * 数量少，变更频率低
     */
    fun queryCachedAllApRELabelDefinitions(): List<ApRELabelDefinition> {
        return allApRELabelDefinitionCache.get(KEY_ALL_APRE_LABEL_DEFINITION_CACHE)
    }

    override fun load(key: String): List<ApRELabelDefinition> {
        log.info("reload all ApRE Label Definition cache,key:$key")
        if (key == KEY_ALL_APRE_LABEL_DEFINITION_CACHE) {
            return apRELabelDefinitionRepo.listAll()
        }
        return emptyList()
    }
}

object ApRELabelExt {
    const val APRE_LABEL_KEY_LENGTH = 32
    const val APRE_LABEL_DEF_KEY_LENGTH = 16
    const val APRE_LABEL_FEATURE_NAME = "alibaba/apre/feature"
    const val APRE_LABEL_FEATURE_VALUE_COMPUTE = "compute"
    const val DEFAULT_APRE_FEATURE_SPEC_TYPE_COMMON = "common"
    const val KEY_ALL_APRE_LABEL_DEFINITION_CACHE = "ALL_APRE_LABEL_DEFINITION_CACHE"

    /**
     * 机型Label名称
     */
    const val APRE_LABEL_NAME_CUSTOMER_MACHINE_TYPE = "customer/machine/type"
    /**
     * arm集群标签值
     */
    const val APRE_LABEL_VALUE_ARM ="m"

    const val SERVERLESS_PREFIX = "serverless/"

    /**
     * ApRE Label Definition 缓存过期时间
     */
    const val ALL_APRE_LABEL_DEFINITION_CACHE_EXPIRE_TIME = 60*1000L

    /**
     * 标准化serverless name名称
     *
     * @param name
     * @return
     */
    fun serverlessNameFormat(name: String?): String {
        return if (name?.startsWith(SERVERLESS_PREFIX) == true) {
            name
        } else {
            name?.let {
                "$SERVERLESS_PREFIX$name"
            } ?: SERVERLESS_PREFIX
        }
    }
}

fun ApRELabelDO.isServerlessLabel(): Boolean {
    return this.type == SERVERLESS
}