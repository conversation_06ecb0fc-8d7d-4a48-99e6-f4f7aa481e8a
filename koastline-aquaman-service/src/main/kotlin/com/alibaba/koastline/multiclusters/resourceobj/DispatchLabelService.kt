package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.koastline.multiclusters.apre.params.DispatchLabelStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.DispatchLabelTypeEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.exceptions.DispatchLabelException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.toBlankIfNull
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.DispatchLabelRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.DispatchLabelValueRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabel
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValue
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValueWithMetadata
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.AtomApi
import com.alibaba.koastline.multiclusters.resourceobj.base.CheckService
import com.alibaba.koastline.multiclusters.resourceobj.model.ConfigMapDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelSpecificValueDTO
import com.alibaba.koastline.multiclusters.resourceobj.model.DispatchLabelValueDTO
import org.apache.commons.lang.StringEscapeUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component


@Component
class DispatchLabelService {
  private val logger by logger()

  @Autowired
  lateinit var dispatchLabelRepo: DispatchLabelRepo

  @Autowired
  lateinit var dispatchLabelValueRepo: DispatchLabelValueRepo

  @Autowired
  lateinit var appCenterApi: AppCenterApi

    @Autowired
    lateinit var commonProperties: CommonProperties

    @Autowired
    lateinit var atomApi: AtomApi

    @Autowired
    lateinit var checkService: CheckService


    fun createOrUpdateDispatchLabel(dispatchLabelDTO: DispatchLabelDTO, modifer: String) {
        dispatchLabelDTO.validate()
        if (!dispatchLabelDTO.specificValue.isNullOrEmpty()) {
            dispatchLabelDTO.specificValue = StringEscapeUtils.unescapeHtml(dispatchLabelDTO.specificValue)
    }

        if (dispatchLabelDTO.status.isNullOrEmpty()) {
            dispatchLabelDTO.status = DispatchLabelStatusEnum.ONLINE.name
    }
        val configDispatchLabel = dispatchLabelRepo.findByCodeWithDeleted(dispatchLabelDTO.code)
    val newLabel = ConfigDispatchLabel(
        cnName = dispatchLabelDTO.cnName,
        description = dispatchLabelDTO.description,
        isSpecificValue = dispatchLabelDTO.isSpecificValue,
        specifiedValue = dispatchLabelDTO.specificValue,
        scope = dispatchLabelDTO.scope,
        specificResourcePool = dispatchLabelDTO.specificResourcePool,
        type = dispatchLabelDTO.type,
        submitSystem = dispatchLabelDTO.submitSystem ?: "hcrm-grop",
        status = dispatchLabelDTO.status ?: DispatchLabelStatusEnum.ONLINE.name,
      modifier = modifer,
    )
    if (configDispatchLabel != null) {
        dispatchLabelRepo.updateById(newLabel.copy(id = configDispatchLabel.id))
    } else {
        dispatchLabelRepo.insert(
            newLabel.copy(
                creator = modifer,
                code = dispatchLabelDTO.code,
                approveId = dispatchLabelDTO.approveId,
            )
        )
    }
  }

    fun syncLabel(label: DispatchLabelDTO, id: Long) {

        val saved = dispatchLabelRepo.findById(id)
    if (saved != null) {
        throw DispatchLabelException("label:id=$id is exist")
    } else {
        dispatchLabelRepo.insertWithId(
            ConfigDispatchLabel(
                id = id,
                code = label.code,
                cnName = label.cnName,
                type = label.type,
                scope = label.scope,
                specificResourcePool = label.specificResourcePool,
                isSpecificValue = label.isSpecificValue,
                specifiedValue = label.specificValue,
                status = label.status!!,
                description = label.description,
                approveId = label.approveId,
                submitSystem = label.submitSystem!!,
                creator = label.creator,
                modifier = label.modifier!!,
                isDeleted = if (label.isRemoved) 1 else 0,
            )
        )
    }
  }

    fun syncLabelValue(label: DispatchLabelValueDTO, id: Long) {

        val saved = dispatchLabelValueRepo.selectById(id)
        if (saved != null) {
            throw DispatchLabelException("labelValue:id=$id is exist")
        } else {
            dispatchLabelValueRepo.insertWithId(
                ConfigDispatchLabelValue(
                    id = id,
                    appName = label.appName,
                    labelCode = label.labelCode,
                    labelValue = label.labelValue,
                    submitSystem = label.submitSystem!!,
                    groupName = label.groupName,
                    idc = label.idc,
                    unit = label.unit,
                    env = label.env,
                    remark = label.remark,
                    creator = label.creator!!,
                    modifier = label.modifier!!,
                    isDeleted = if (label.isRemoved) 1 else 0,
                )
            )
        }
    }

  fun updateSpecifiedValueOfLabelByCode(
    configdispatchSpecificValueDTO: DispatchLabelSpecificValueDTO,
    modifer: String
  ) {
    val configDispatchLabel = dispatchLabelRepo.findByCode(configdispatchSpecificValueDTO.code)
    if (configDispatchLabel == null) {
      throw DispatchLabelException("config dispatch label ${configdispatchSpecificValueDTO.code} not found")
    }
      dispatchLabelRepo.updateById(
          configDispatchLabel.copy(
              specifiedValue = JSON.toJSONString(configdispatchSpecificValueDTO.values),
              modifier = modifer
          )
      )

  }

    fun getAllDispatchLabel(): List<ConfigDispatchLabel> {
        val list = dispatchLabelRepo.findAllLabelWithoutDeleted()
        return list

    }

  fun deleteLabelByCode(code: String, modifer: String) {

    dispatchLabelRepo.deleteByCode(code, modifer)

  }

    fun deleteLabelValueByUK(dispatchLabelValueDTO: DispatchLabelValueDTO, modifer: String) {
        val saved = dispatchLabelValueRepo.selectBy4ElementsWithDeleted(
            dispatchLabelValueDTO.appName,
            dispatchLabelValueDTO.groupName.toBlankIfNull(),
            dispatchLabelValueDTO.idc.toBlankIfNull(),
            dispatchLabelValueDTO.unit.toBlankIfNull(),
            dispatchLabelValueDTO.env.toBlankIfNull(),
            dispatchLabelValueDTO.labelCode
        )
        saved ?: throw DispatchLabelException("labelValue:${dispatchLabelValueDTO} not found")
        dispatchLabelValueRepo.deleteById(saved.id!!, modifer)

  }

  fun createOrUpdateLabelValue(dispatchLabelValueDTO: DispatchLabelValueDTO, rawModifer: String) {
    dispatchLabelValueDTO.validate()
    var modifier = rawModifer
    if (!dispatchLabelValueDTO.creator.isNullOrEmpty()) {
      modifier = dispatchLabelValueDTO.creator
    }

    var submitSystem = dispatchLabelValueDTO.submitSystem
    if (dispatchLabelValueDTO.submitSystem.isNullOrEmpty()) {
      var isUserSubmitter = !modifier.isNullOrEmpty() && (modifier.toIntOrNull() != null
              || modifier.startsWith("WB"))

      if (isUserSubmitter) {
        submitSystem = "atomcore"
      } else {
        submitSystem = modifier
      }
    }
    var save = dispatchLabelValueRepo.selectBy4ElementsWithDeleted(
      dispatchLabelValueDTO.appName,
        dispatchLabelValueDTO.groupName.toBlankIfNull(),
        dispatchLabelValueDTO.idc.toBlankIfNull(),
        dispatchLabelValueDTO.unit.toBlankIfNull(),
        dispatchLabelValueDTO.env.toBlankIfNull(),
      dispatchLabelValueDTO.labelCode,
    )
      if (save != null) {

          val newSave = save.copy(
              labelValue = dispatchLabelValueDTO.labelValue,
              remark = dispatchLabelValueDTO.remark
          )
          if (dispatchLabelValueDTO.getIsRemoved()) {
              dispatchLabelValueRepo.deleteById(newSave.id!!, modifier)
          } else {
              dispatchLabelValueRepo.updateById(
                  newSave.copy(
                      modifier = modifier
                  )
              )
          }
          return
      }

      if (dispatchLabelValueDTO.getIsRemoved()) {
          return
      }

      dispatchLabelValueRepo.insert(
          ConfigDispatchLabelValue(
              submitSystem = submitSystem!!,
              appName = dispatchLabelValueDTO.appName,
              groupName = dispatchLabelValueDTO.groupName,
              idc = dispatchLabelValueDTO.idc,
              unit = dispatchLabelValueDTO.unit,
              env = dispatchLabelValueDTO.env,
              labelCode = dispatchLabelValueDTO.labelCode,
              labelValue = dispatchLabelValueDTO.labelValue,
              remark = dispatchLabelValueDTO.remark,
              modifier = modifier,
              creator = modifier,
          )
      )



  }

    fun getSigmaConfigMap(
        appName: String,
        groupName: String? = null,
        idc: String? = null,
        unit: String? = null,
        env: String? = null,
    ): String {
        if (
            whetherFallback(appName)
        ) {
            return atomApi.getSigmaConfigMap(appName, groupName, idc, unit, env)

        }
        val labels = dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(appName)
        val data = buildData(
            labels,
            appName,
            groupName,
            idc,
            unit,
            env,
        )
        val aquamanRet = JSON.toJSONString(
            ConfigMapDTO(
                data = data
            )
        )
        if (commonProperties.contains(
                CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH,
                "false"
            ) ||
            commonProperties.contains(
                CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, appName
            )
        ) {
            return aquamanRet
        }
        val atomRet = try {
            atomApi.getSigmaConfigMap(appName, groupName, idc, unit, env)
        } catch (e: Exception) {
            return aquamanRet
        }
        val atomData = getSigmaConfigMapFromString(
            JSON.parseObject(atomRet).getString("data")
        )
        val aquamanData = getSigmaConfigMapFromString(
            JSON.toJSONString(data)
        )
        var ret = aquamanRet
        try {
            checkService.checkGetSigmaConfigMap(
                appName,
                groupName,
                idc,
                unit,
                env,
                atomData,
                aquamanData
            )
        } catch (e: Exception) {
            ret = atomRet
        }
        return ret

    }
    fun getLabelValue(
        appName: String,
        groupName: String? = null,
        idc: String? = null,
        unit: String? = null,
        env: String? = null,
    ): List<ConfigDispatchLabelValueWithMetadata> {
        return dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(appName).filter {
            groupName == null || it.groupName == groupName
        }.filter {
            idc == null || it.idc == idc
        }.filter {
            unit == null || it.unit == unit
        }.filter {
            env == null || it.env == env
        }
    }

    fun whetherFallback(appName: String) = !commonProperties.contains(
        CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, appName
    ) && !commonProperties.contains(
        CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*"
    )


  fun buildData(
    labels: List<ConfigDispatchLabelValueWithMetadata>,
    appName: String,
    groupName: String?,
    idc: String?,
    unit: String?,
    env: String?,
  ): Map<String, String> {
    if (labels.isEmpty()) {
      return emptyMap()
    }
    val labelKeyListMap = labels.groupBy { it.labelCode }
    val matchedSigmaLabelList = labelKeyListMap.values.map { sigmaLabels ->
      calculateMatchedLabel(
        sigmaLabels,
        appName,
        groupName,
        idc,
        unit,
        env,
      )
    }.filterNotNull()
      val data = mutableMapOf<String, String>()
    data.put(
      DispatchLabelTypeEnum.constraints.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.constraints, matchedSigmaLabelList)
    )
      data.put(
          DispatchLabelTypeEnum.spread.name,
          buildDispatchLabelMap(DispatchLabelTypeEnum.spread, matchedSigmaLabelList)
      )
    data.put(
      DispatchLabelTypeEnum.allocSpec.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.allocSpec, matchedSigmaLabelList)
    )
    data.put(
      DispatchLabelTypeEnum.hostConfig.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.hostConfig, matchedSigmaLabelList)
    )
    data.put(
      DispatchLabelTypeEnum.extConfig.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.extConfig, matchedSigmaLabelList)
    )
    data.put(
      DispatchLabelTypeEnum.prohibit.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.prohibit, matchedSigmaLabelList)
    )
    data.put(
      DispatchLabelTypeEnum.monopolize.name,
        buildDispatchLabelMap(DispatchLabelTypeEnum.monopolize, matchedSigmaLabelList)
    )
    return data.toMap()
  }

    private fun buildDispatchLabelMap(
    keyType: DispatchLabelTypeEnum, sigmaLabelList: List<ConfigDispatchLabelValueWithMetadata>
  ): String {
        val dataContent = mutableMapOf<String, String>()
    sigmaLabelList.filter { label ->
      label.labelType.equals(keyType.name, true)
    }.forEach { label ->
      dataContent.put(
        label.labelCode, label.labelValue
      )
    }
    return JSONObject.toJSONString(dataContent)
  }

  companion object {
    val GROUP_NAME_MATCHED: String = "groupNameMatched"
    val IDC_MATCHED: String = "idcMatched"
    val UNIT_MATCHED: String = "unitMatched"
    val ENV_MATCHED: String = "envMatched"
    val QUERY_NOT_MATCH: String = "queryNotMatch"
    val DEFAULT_WEIGHT = mapOf(
      GROUP_NAME_MATCHED to 8,
      ENV_MATCHED to 4,
      UNIT_MATCHED to 2,
      IDC_MATCHED to 1,
      QUERY_NOT_MATCH to -100,
    )
      fun getSigmaConfigMapFromString(str: String): Map<String, Map<String, String>> {
          val jsonObject = JSON.parseObject(str)
          val map = mutableMapOf<String, Map<String, String>>()
          for (key in jsonObject.keys) {
              map[key as String] = JSON.parseObject(jsonObject.getString(key), Map::class.java) as Map<String, String>
          }
          return map
      }

    fun calculateMatchedLabel(
      labels: List<ConfigDispatchLabelValueWithMetadata>,
      appName: String,
      groupName: String?,
      idc: String?,
      unit: String?,
      env: String?,
    ): ConfigDispatchLabelValueWithMetadata? {
      if (labels.isEmpty()) {
        return null
      }
      var matchScore = -1
      var matchedLabel: ConfigDispatchLabelValueWithMetadata? = null
      for (label in labels) {
        var score = 0
        score += matcher(label.groupName ?: "", groupName, GROUP_NAME_MATCHED)
        score += matcher(label.idc ?: "", idc, IDC_MATCHED)
        score += matcher(label.unit ?: "", unit, UNIT_MATCHED)
        score += matcher(label.env ?: "", env, ENV_MATCHED)
        if (score >= 0 && score > matchScore) {
          matchScore = score
          matchedLabel = label
        }
      }
      return matchedLabel
    }

    private fun matcher(dbParam: String, queryParam: String?, weightType: String): Int {
      if (dbParam.isNullOrEmpty()) {
        if (queryParam.isNullOrEmpty()) {
          return DEFAULT_WEIGHT.get(weightType)!!
        }
        return 0
      }
      if (dbParam.equals(queryParam, true)) {
        return DEFAULT_WEIGHT.get(weightType)!!
      } else {
        return DEFAULT_WEIGHT.get(QUERY_NOT_MATCH)!!
      }
    }

  }


}