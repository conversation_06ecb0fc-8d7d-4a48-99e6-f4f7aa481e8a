package com.alibaba.koastline.multiclusters.schedule.service.fiter

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.AbstractScheduleFilterFacade
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 运行态调度计算服务
 * 功能：
 * 清理无效的ApRE,包括：
 *    不存在集群资源的ApRE
 */
@Component
class ValidApREScheduleFilterProcessor : AbstractScheduleFilterFacade(), ScheduleFilterFacade, InitializingBean {
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory

    override fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
        val filterApREList = matchDeclaration.apres.filter { apRE ->
            apRE.resources.isNotEmpty()
        }
        return matchDeclaration.copy(apres = filterApREList)
    }

    override fun afterPropertiesSet() {
        scheduleFilterServiceFactory.registryScheduleFilterService(this)
    }
}