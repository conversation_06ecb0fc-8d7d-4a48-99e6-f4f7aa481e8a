package com.alibaba.koastline.multiclusters.event

import com.alibaba.koastline.multiclusters.common.exceptions.EventSendException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.rocketmq.client.producer.SendStatus.SEND_OK
import com.alibaba.rocketmq.common.message.Message
import com.fasterxml.jackson.databind.ObjectMapper
import com.taobao.metaq.client.MetaProducer
import org.springframework.stereotype.Service

/**
 *
 * Date: 2023-04-18 Time: 14:33
 *
 * <AUTHOR>
 */
@Service
class EventProducer(
    private val objectMapper: ObjectMapper,
    private val metaProducer: MetaProducer,
) {
    val log by logger()

    fun sendIgnoreError(event: Event<*>) {
        try {
            send(event)
        } catch (e: Exception) {
            log.error(e.message, e)
        }
    }

    fun send(event: Event<*>) {
        val message = Message(
            MetaProducerConfig.AQUAMAN_CONFIG_CHANGE_EVENT_TOPIC,
            event.type,
            event.key(),
            objectMapper.writeValueAsBytes(event)
        )
        log.info("[Event][${event.key()}][Payload]${objectMapper.writeValueAsString(event)}")
        val sendResult = metaProducer.send(message)
        log.info("[Event][${event.key()}][Result]$sendResult")
        if (sendResult.sendStatus != SEND_OK) {
            throw EventSendException("send event error, key: ${event.key()}, result: $sendResult")
        }
    }
}
