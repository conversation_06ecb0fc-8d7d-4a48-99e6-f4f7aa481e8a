package com.alibaba.koastline.multiclusters.appenv.model

/**
 * <AUTHOR>
 * [ClusterEnvironment] is an environment generated by koastline, providing the unit for
 * applications to be installed and run. virtual？
 * @param[envId] a unique identity for the cluster environment
 * @param[clusterEnvName] a unique name for the virtual environment, following a generation process
 * @param[envMeta] contains all metadata about the [ClusterEnvironment]
 * @param[envSpec] servers as specification of the desired behavior of the [ClusterEnvironment]
 */
data class ClusterEnvironment(val apiVersion: String,
                              val kind: EnvKind? = EnvKind.SHARED,
                              val envId: String,
                              val clusterEnvName: String,
                              val envMeta: EnvMetaData,
                              val envSpec: EnvSpec)

enum class EnvKind {
    // environment is virtually isolated, but sharing the same koastline related base
    SHARED,
    // environment is isolated in the sense that koastline base is owned by the env
    OWNED,
}

/**
 * Contains all metadata about the [ClusterEnvironment]
 * @param[clusterName]
 * @param[clusterId]
 * @param[gatewayConfig]
 * @param[providerProfile]
 * @param[labels]
 * @param[annotations]
 */
data class EnvMetaData(val clusterName: String,
                       val clusterId: String,
                       val gatewayConfig: GatewayConfig,
                       val providerProfile: Map<String, String>,
                       val labels: Map<String, String>,
                       val annotations: Map<String, String>)

/**
 * Specification of the desired behavior of the [ClusterEnvironment]
 * @param[clusterSelectors] contains a set of key-values to select a cluster
 */
data class EnvSpec(val clusterSelectors: Map<String, String>)