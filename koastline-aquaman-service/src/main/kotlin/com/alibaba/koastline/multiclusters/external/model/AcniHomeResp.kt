package com.alibaba.koastline.multiclusters.external.model

/**
 * @author: fudai.yf
 * @since: 2023/3/7
 */
data class AcniAssetRelatedK8sObject(

    /**
     * K8s Object 的 API version
     */
    val apiVersion: String,

    /**
     * K8s Object 的类型
     */
    val kind: String,

    /**
     * K8s Object 的内容
     */
    val objectContent: String

)

data class AcniRestErrorHint(

    /**
     * Http status.
     */
    val status: Int?,

    /**
     * Error message.
     */
    val message: String?,

    /**
     * Path of error resource.
     */
    val path: String?,

    /**
     * Trace id of request.
     */
    val traceId: String?
)