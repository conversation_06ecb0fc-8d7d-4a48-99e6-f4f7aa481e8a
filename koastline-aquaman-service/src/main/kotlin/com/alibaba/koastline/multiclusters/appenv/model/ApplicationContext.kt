package com.alibaba.koastline.multiclusters.appenv.model

/**
 * <AUTHOR>
 * @param[source] the source generating application context, eg. appstack
 * @param[clusterProvider] provider's name who provides the infrastructure of clusters.
 * @param[devOpsStage] the stage of an application is in, eg, test/prepublish/publish
 * @param[region] geographically the region which is covered by clusters.
 * @param[envTags] geographically locates a cluster where an environment will be created,in which the application will be run
 */
data class ApplicationContext(val appName: String,
                              val source: String,
                              val clusterProvider: String,
                              val clusterType: String,
                              val devOpsStage: String,
                              val region: String,
                              val envTags: Map<String, String>)