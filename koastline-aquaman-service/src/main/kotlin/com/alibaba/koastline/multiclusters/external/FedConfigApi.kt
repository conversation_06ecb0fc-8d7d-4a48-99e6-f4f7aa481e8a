package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.FedConfigException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.fasterxml.jackson.core.type.TypeReference
import io.kubernetes.client.openapi.models.V1LabelSelector
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class FedConfigApi {
    @Value("\${fedConfig.host}")
    lateinit var host: String

    val log by logger()

    /**
     * 创建或更新feaTargetCRD
     *
     * @param crString
     * @return
     */
    @ExternalCall(SYS_CALLED)
    fun createOrUpdateFedTarget(crString: String): String {
        // 验证crString yaml 格式是否正确
        val path = buildUrl(CREATE_OR_UPDATE_FED_TARGET_PATH)
        val fedRs = JsonUtils.readValue(
            HttpClientUtils.httpPost(
                url = path, requestBodyStr = crString,
                authorizationAttribute = null
            ), object : TypeReference<FedConfigResponse<Map<String, String>>>() {}
        )
        if (!fedRs.isSuccess()) {
            log.info("push fed target with error! errorMsg:${fedRs.message} crString:$crString")
            throw FedConfigException("push fed target with error! errorMsg:${fedRs.message}")
        }

        val uniqueName =  checkNotNull(fedRs.data[FED_UNIQUE_NAME_KEY]) {
            "missing $FED_UNIQUE_NAME_KEY in resp at createOrUpdateFedTarget action!"
        }
        log.info("create or update fed target with uniqueName:$uniqueName ,cr: $crString")
        return uniqueName
    }

    /**
     * 创建或更新feaPolicyCRD
     *
     * @param crString
     * @return
     */
    @ExternalCall(SYS_CALLED)
    fun createOrUpdateFedPolicy(crString: String): String {
        // 验证crString yaml 格式是否正确
        YamlUtils.load(crString)
        val path = buildUrl(CREATE_OR_UPDATE_FED_POLICY_PATH)
        val fedRs = JsonUtils.readValue(
            HttpClientUtils.httpPost(
                url = path, requestBodyStr = crString,
                authorizationAttribute = null
            ), object : TypeReference<FedConfigResponse<Map<String, String>>>() {}
        )
        if (!fedRs.isSuccess()) {
            log.info("push fed policy with error! errorMsg:${fedRs.message} crString:$crString")
            throw FedConfigException("push fed policy with error! errorMsg:${fedRs.message}")
        }
        val uniqueName =  checkNotNull(fedRs.data[FED_UNIQUE_NAME_KEY]) {
            "missing $FED_UNIQUE_NAME_KEY in resp at createOrUpdateFedPolicy action!"
        }
        log.info("create or update fed policy with uniqueName:$uniqueName cr: $crString")
        return uniqueName
    }

    /**
     * 删除fedPolicy策略
     *
     * @param uniquePolicyName
     */
    @ExternalCall(SYS_CALLED)
    fun deleteFedPolicy(uniquePolicyName: String, tenantClusterName: String, region: String) {
        val path = buildUrl(DELETE_FED_POLICY_PATH)
        val bodyParamsStr = JsonUtils.writeValueAsString(
            mapOf(
                "name" to uniquePolicyName,
                "tenantCluster" to tenantClusterName,
                "region" to region
            )
        )
        val fedRs = JsonUtils.readValue(
            HttpClientUtils.httpPost(
                url = path, requestBodyStr = bodyParamsStr,
                authorizationAttribute = null
            ), object : TypeReference<FedConfigResponse<Any?>>() {}
        )
        if (!fedRs.isSuccess()) {
            throw FedConfigException("delete fed policy with error! uniquePolicyName:$uniquePolicyName")
        }
        log.info("delete fed policy with uniquePolicyName: $uniquePolicyName ")
        return
    }

    private fun buildUrl(subPath: String) = "$host$BASE_PATH$subPath"

    companion object {
        // aop call name
        const val SYS_CALLED = "fed-config-api"

        // request path
        const val BASE_PATH = "/apis/v2/federation"
        const val CREATE_OR_UPDATE_FED_TARGET_PATH = "/updateFedTarget"
        const val CREATE_OR_UPDATE_FED_POLICY_PATH = "/updatePolicy"
        const val DELETE_FED_POLICY_PATH = "/deletePolicy"

        // response key words
        const val BATCH_EVICT_POD_REQ_NAME = "reqName"
        const val FED_UNIQUE_NAME_KEY = "name"
    }
}

data class FedConfigResponse<T>(
    val code: Int,
    val message: String,
    val data: T
) {
    fun isSuccess(): Boolean {
        return code == RESPONSE_SUCCESS
    }

    companion object {
        const val RESPONSE_SUCCESS = 0
        const val RESPONSE_FAIL = 1
    }
}