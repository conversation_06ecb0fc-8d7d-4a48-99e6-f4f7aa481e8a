package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.koastline.multiclusters.common.exceptions.CapacityException
import org.apache.commons.codec.digest.HmacUtils
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import org.joda.time.format.ISODateTimeFormat
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.time.Instant
import java.util.*


/**
 * <AUTHOR>
 */
object SignUtils {
    fun getAppCenterToken(accessKey: String): String {
        val dateFormat: String = SimpleDateFormat("yyMMddHH").format(Date(Instant.now().toEpochMilli()))
        return transByteArrayToHexString(encryptToByte(dateFormat + accessKey, "UTF-8"))
    }

    fun getAstroToken(accessKey: String): String {
        val dateFormat: String = SimpleDateFormat("yyMMddHH").format(Date(Instant.now().toEpochMilli()))
        return transByteArrayToHexString(encryptToByte(accessKey + dateFormat, "UTF-8"))
    }

    fun getCapacityToken(accessId: String, accessKey: String,timestamp: String, params: Map<String, String>): String {
        return CapacitySignUtils.sign(accessId, accessKey, timestamp, params)
    }

    fun getGropToken(accessKey: String, secretKey: String, timestamp: String): String {
        return transByteArrayToHexString(encryptToByte(String.format("%s::%s::%s", accessKey, timestamp, secretKey), "UTF-8"))
    }


    /**
     * timestamp in ISO8601 UTC
     */
    fun getISOTimestamp(): String {
        return DateTime.now(DateTimeZone.UTC).toString(ISODateTimeFormat.dateTimeNoMillis())
    }

    private fun encryptToByte(data: String, charset: String): ByteArray {
        val digest = MessageDigest.getInstance("MD5")
        digest.update(data.toByteArray(charset(charset)))
        return digest.digest()
    }

    private fun transByteArrayToHexString(byteArray: ByteArray): String {
        val sb = StringBuffer()
        for (i in byteArray.indices) {
            val b = byteArray[i].toInt()
            var s = Integer.toHexString(b)
            if (s.length > 2) {
                s = s.substring(s.length - 2, s.length)
            }
            if (s.length == 1) {
                s = "0$s"
            }
            sb.append(s)
        }
        return sb.toString()
    }
}
private object CapacitySignUtils {
    private const val KEY_VALUE_SEPARATOR = "="
    private const val PARAM_SEPARATOR = "&"

    private const val URL_ENCODING = "UTF-8"

    fun sign(accessId: String, accessKey: String,timestamp: String, params: Map<String, String>): String {
        val toSign: MutableMap<String, String> = HashMap(params)
        toSign["_accessId"] = accessId
        toSign["_timestamp"] = timestamp
        return HmacUtils.hmacSha1Hex(accessKey, buildCanonicalQueryString(toSign))
    }

    private fun buildCanonicalQueryString(params: Map<String, String>): String? {
        // sort params by key
        val orderedParams = TreeMap(params)

        // build canonical query string
        val encodedQueryStrings: MutableList<String> = ArrayList()
        orderedParams.forEach { (key: String?, value: String?) ->
            encodedQueryStrings.add(
                encode(key) + KEY_VALUE_SEPARATOR + encode(value)
            )
        }
        return encodedQueryStrings.joinToString(PARAM_SEPARATOR)
    }

    private fun encode(value: String?): String? {
        return try {
            if (value == null) null else URLEncoder.encode(value, URL_ENCODING)
        } catch (e: UnsupportedEncodingException) {
            throw CapacityException(e.message, e)
        }
    }
}