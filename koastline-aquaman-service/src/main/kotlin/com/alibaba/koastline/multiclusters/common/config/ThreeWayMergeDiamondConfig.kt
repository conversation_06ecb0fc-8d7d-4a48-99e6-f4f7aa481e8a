package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.boot.diamond.listener.DataIdListener
import com.alibaba.koastline.multiclusters.common.logger
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.taobao.diamond.client.Diamond
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

@Component
class ThreeWayMergeDiamondConfig(val threeWayMergeProperties: ThreeWayMergeProperties) : DataIdListener {
    val log by logger()
    override fun getDiamondUrl(): String {
        return "diamond://koastline-aquaman/${GROUP_ID}/${DATA_ID}"
    }

    override fun valueChanged(newResource: Resource?) {
        threeWayMergeProperties.properties = buildProperties()
        log.info("value changed, new threeWayMergeProperties: ${threeWayMergeProperties.properties}")
    }

    companion object {
        private const val DATA_ID = "koasltine-aquaman-three-way-merge"
        private const val GROUP_ID = "DEFAULT_GROUP"

        /*
       {
           "THREE_WAY_MERGE_APP_STAGE_LIST": {
               "testAppName": [
                   "DAILY"
               ]
           }
       }
        */
        fun buildProperties(): Map<String, Map<String, List<String>>> {
            val config = Diamond.getConfig(DATA_ID, GROUP_ID, 5000)
            val objectMapper = ObjectMapper()
            return objectMapper.readValue(config)
        }
    }
}