package com.alibaba.koastline.multiclusters.common.config

import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
class ExternalCallDowngradeProperties {
    var properties = ExternalCallDowngradeDiamondConfig.buildProperties()

    fun isDowngrade(sys: String, service: String): Boolean{
        return properties[sys] ?.get(service) ?:false
    }
}