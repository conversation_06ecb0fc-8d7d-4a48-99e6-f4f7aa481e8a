package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.event.EventProducer
import com.alibaba.koastline.multiclusters.event.ResourceSpecChangeEventPayload
import com.alibaba.koastline.multiclusters.external.AstroApi
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.model.AppResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional


@Component
class UserLabelService{
    val log by logger()
    @Autowired
    lateinit var userLabelBaseService: UserLabelBaseService
    @Autowired
    lateinit var userExtraLabelService: UserLabelExtraService
    @Autowired
    lateinit var astroApi: AstroApi
    @Autowired
    lateinit var eventProducer: EventProducer

    @Transactional
    fun createAndOverrideWhileExist(userLabelCreateReqDto: UserLabelCreateReqDto) {
        val createReqDto = userLabelCreateReqDto.validate()
        userLabelBaseService.findByExternalAndLabelWithCache(
            userLabelCreateReqDto.externalId,
            userLabelCreateReqDto.externalType.name,
            userLabelCreateReqDto.labelName
        ) ?.let {
            userLabelBaseService.deleteByExternalAndLabelWithCache(
                userLabelCreateReqDto.externalId,
                userLabelCreateReqDto.externalType.name,
                userLabelCreateReqDto.labelName,
                userLabelCreateReqDto.creator)
        }
        if (userLabelCreateReqDto.labelName == UserLabelType.GPU_COUNT.code) {
            if (userLabelCreateReqDto.labelValue.isNullOrBlank() || userLabelCreateReqDto.labelValue == "0") {
                userExtraLabelService.updateExtraLabel(createReqDto)
                return
            }
        }

        userLabelBaseService.createWithCache(createReqDto)
        // 双写更新外部系统
        userExtraLabelService.updateExtraLabel(createReqDto)

        //send event
        sendEvent(userLabelCreateReqDto)
    }

    @Transactional
    fun createOrUpdateAppResourceSpec(appResourceSpec: AppResourceSpec) {
        createAndOverrideWhileExist(userLabelCreateReqDto = UserLabelCreateReqDto(
                externalType = UserLabelExternalType.APPLICATION,
                externalId = appResourceSpec.appName,
                labelName = UserLabelType.MODEL.code,
                labelValue = appResourceSpec.resourceSpec.toModel(),
                creator = appResourceSpec.employeeId
            )
        )
        createAndOverrideWhileExist(userLabelCreateReqDto = UserLabelCreateReqDto(
                externalType = UserLabelExternalType.APPLICATION,
                externalId = appResourceSpec.appName,
                labelName = UserLabelType.GPU_COUNT.code,
                labelValue = appResourceSpec.resourceSpec.gpu,
                creator = appResourceSpec.employeeId
            )
        )
    }

    fun getAppResourceSpec(appName: String): ResourceSpec {
        return userLabelBaseService.findByExternalAndLabelWithCache(
            externalId = appName,
            externalType = UserLabelExternalType.APPLICATION.name,
            labelName = UserLabelType.MODEL.code
        ) ?.labelValue ?.run {
            ResourceSpec(model = this).copy(
                gpu = userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = appName,
                    externalType = UserLabelExternalType.APPLICATION.name,
                    UserLabelType.GPU_COUNT.code) ?.labelValue ?.toNullIfBlank() ?.run { if (this == "0") null else this}
            )
        } ?:run {
            //直接调用astro获取资源规格
            astroApi.getResourceSpec(appName)
        }
    }

    @Transactional
    fun initAppLabels(appName: String) {
        log.info("初始化应用标签，appName:${appName},labels:${INIT_APP_DEFAULT_LABELS}")
        INIT_APP_DEFAULT_LABELS.forEach { (key, value) ->
            createAndOverrideWhileExist(
                UserLabelCreateReqDto(
                    externalType = UserLabelExternalType.APPLICATION,
                    externalId = appName,
                    labelName = key,
                    labelValue = value,
                    creator = "admin_init"
                )
            )
        }
    }

    /**
     * send event
     */
    private fun sendEvent(userLabelCreateReqDto: UserLabelCreateReqDto) {
        if (userLabelCreateReqDto.labelName == UserLabelType.MODEL.code
            && userLabelCreateReqDto.externalType == UserLabelExternalType.APPLICATION) {
            require(!userLabelCreateReqDto.labelValue.isNullOrBlank()) { "labelValue must be non-blank" }
            eventProducer.sendIgnoreError(
                ResourceSpecChangeEventPayload(
                    appName = userLabelCreateReqDto.externalId,
                    resourceSpec = ResourceSpec(userLabelCreateReqDto.labelValue),
                    employeeId = userLabelCreateReqDto.creator,
                ).buildEvent()
            )
        }
    }

    companion object {
        private val INIT_APP_DEFAULT_LABELS = mapOf(
            UserLabelType.MODEL.code to "4-8-60",
            UserLabelType.OS.code to "alios7u2",
            UserLabelType.TEMPLATE.code to "3tao",
            UserLabelType.STARTUP.code to "yes"
        )
    }
}