package com.alibaba.koastline.multiclusters.external.model

data class Server(
    /**
     * hostNetwork 模式、多云等场景下，ip为空
     */
    val ip: String?,
    val sn: String,
    val appName: String,
    val resourceGroup: String,
    val site: String,
    val stage: String,
    val unit: String,
    val clusterId: String,
    val resourceSubGroup: String,
    val namespace: String,
    val clusterPoolId: String?,
    val tags: List<String> = emptyList(),
    val workloadName: String? = null
){
    fun toMetadataConstraintString(): String {
        return "${appName}&${resourceGroup}&${site}&${stage}&${unit}&${clusterId}&${resourceSubGroup}&${namespace}&${clusterPoolId}&${workloadName}"
    }
}




