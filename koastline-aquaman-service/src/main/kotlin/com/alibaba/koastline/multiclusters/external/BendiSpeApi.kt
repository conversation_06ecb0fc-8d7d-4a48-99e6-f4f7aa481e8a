package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.URLEncoderUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.ThirdJoinSpeResp
import org.springframework.stereotype.Component
import org.springframework.util.DigestUtils

/**
 * @author:    <EMAIL>
 * @description:  本地生活Spe定制化功能
 * @date:    2025/2/14 10:48 AM
 */
@Component
class BendiSpeApi {
    @ExternalCall(SYS_CALLED)
    fun isBendiApp(appName: String): Boolean {
        return queryAppJoinSpe(appName, emptyList()).joinedGray ?:false
    }

    private fun queryAppJoinSpe(appName: String, unitList: List<String>): ThirdJoinSpeResp {
        val timestamp: Long = System.currentTimeMillis()
        return HttpClientUtils.httpGet(
            url = "${BEN_DI_SPE_HOST}${URL_JOIN_SPE}",
            params = mapOf(
                "appName" to appName,
                "unit" to URLEncoderUtils.encode(JsonUtils.writeValueAsString(unitList)),
                "timestamp" to timestamp.toString(),
                "token" to buildToken(timestamp)
            )
        ).run {
            JsonUtils.readValue(this, ThirdJoinSpeResp::class.java)
        }
    }
    private fun buildToken(timestamp: Long): String {
        val message: String = SECRET + timestamp
        return DigestUtils.md5DigestAsHex(message.toByteArray())
    }
    companion object{
        private const val SYS_CALLED= "BENDI_SPE"
        private const val BEN_DI_SPE_HOST = "https://pa-oapi.alibaba-inc.com"
        private const val SECRET = "b4de89333b41327287ddf5ea16b17d56"
        private const val URL_JOIN_SPE = "/open/api/hasAppJoinSpe"
    }
}