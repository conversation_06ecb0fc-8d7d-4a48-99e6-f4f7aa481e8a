package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.URLEncoderUtils
import com.alibaba.koastline.multiclusters.external.model.ThirdJoinSpeResp
import org.springframework.stereotype.Component
import org.springframework.util.DigestUtils

/**
 * @author:    <EMAIL>
 * @description:  考拉spe定制化功能
 * @date:    2025/2/14 10:39 AM
 */
@Component
class KaolaSpeApi(
    val appCenterApi: AppCenterApi
) {
    fun isKaolaApp(appName: String): Boolean {
        return DEPLOY_TYPE_KAOLA == appCenterApi.getAppInfoByName(appName).devloyType
    }

    private fun queryAppJoinSpe(appName: String, unitList: List<String>): ThirdJoinSpeResp {
        val timestamp: Long = System.currentTimeMillis()
        return HttpClientUtils.httpGet(
            url = "${KAOLA_HOST}${URL_JOIN_SPE}",
            params = mapOf(
                "appName" to appName,
                "unit" to URLEncoderUtils.encode(JsonUtils.writeValueAsString(unitList)),
                "timestamp" to timestamp.toString(),
                "token" to buildToken(timestamp)
            )
        ).run {
            JsonUtils.readValue(this, ThirdJoinSpeResp::class.java)
        }
    }
    private fun buildToken(timestamp: Long): String {
        val message: String = SECRET + timestamp
        return DigestUtils.md5DigestAsHex(message.toByteArray())
    }
    companion object {
        const val DEPLOY_TYPE_KAOLA = "KOALA"
        private const val KAOLA_HOST = "https://km-kl.alibaba-inc.com"
        private const val SECRET = "b4de89333b41327287ddf5ea16b17d56"
        private const val URL_JOIN_SPE = "/open/api/hasAppJoinSpe"
    }
}
