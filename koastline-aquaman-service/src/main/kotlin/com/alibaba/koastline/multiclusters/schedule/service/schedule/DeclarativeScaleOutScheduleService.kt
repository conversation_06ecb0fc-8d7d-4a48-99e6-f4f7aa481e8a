package com.alibaba.koastline.multiclusters.schedule.service.schedule

import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractScaleOutScheduleFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component(value = "DeclarativeScaleOutScheduleService")
class DeclarativeScaleOutScheduleService @Autowired constructor(
    var apREService: ApREService
) : AbstractScaleOutScheduleFacade(), InitializingBean {

    override fun getApREDeedResultWithResource(content: ScheduleRequestContent): ApREDeedResult {
        return apREService.queryClustersByApREDeedContent(
            scheduleStandardService.getAssembledApREDeedByDeclarative(content)
        )
    }

    override fun afterPropertiesSet() {
        scheduleServiceFactory.registryScheduleService(
            ScheduleServiceEnum.DECLARATIVE_SCALE_OUT_SCHEDULE, this)
    }

    companion object {
        const val RESOURCE_GROUP_USAGE_TYPE_DAILY = "test"
        const val TEST_ENV_LEVEL = "daily"
        const val TEST_DEFAULT_UNIT = "CENTER_UNIT.center"
        const val TEST_STAGE = "DAILY"
    }
}