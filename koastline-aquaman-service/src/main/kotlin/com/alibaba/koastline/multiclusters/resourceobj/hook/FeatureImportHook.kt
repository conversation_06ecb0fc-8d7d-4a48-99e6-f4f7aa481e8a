package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct

interface FeatureImportHook {
    /**
     * 特性在真正渲染前的预处理
     */
    fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport
}

annotation class FeatureImportHookMeta(val featureKey: String)
annotation class FeatureImportHookMetaBatch(val featureKeys: Array<String>)

@Service
class FeatureImportHookManager(
    private val featureImportHooks: List<FeatureImportHook>
) : InitializingBean, FeatureImportHook {
    private val hooks = mutableMapOf<String, FeatureImportHook>()

    override fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport {
        return hooks[featureImport.resourceObjectFeatureKey]?.preProcess(featureImport) ?: featureImport
    }

    override fun afterPropertiesSet() {
        registryHooks()
    }

    fun registryHooks() {
        featureImportHooks.forEach {
            val meta = it::class.java.getAnnotation(FeatureImportHookMeta::class.java)
            if (meta != null) {
                hooks[meta.featureKey] = it
            }
            val metas = it::class.java.getAnnotation(FeatureImportHookMetaBatch::class.java)
            if (metas != null) {
                metas.featureKeys.forEach { key ->
                    hooks[key] = it
                }
            }
        }
    }
}