package com.alibaba.koastline.multiclusters.resourceobj.listener

import org.springframework.stereotype.Component

@Component
class ResourceObjectFeatureImportListenerFactory {

    /**
     * 注册监听器
     * @param featureKey 特性KEY
     * @param resourceObjectFeatureImportListener 监听器
     */
    fun registryListener(featureKey: String, resourceObjectFeatureImportListener: ResourceObjectFeatureImportListener) {
        listeners[featureKey] ?.let {
            it.add(resourceObjectFeatureImportListener)
        } ?:let {
            listeners[featureKey] = mutableListOf(resourceObjectFeatureImportListener)
        }
    }

    /**
     * 获取监听器
     * @param featureKey 特性KEY
     */
    fun listListener(featureKey: String): List<ResourceObjectFeatureImportListener> {
        return listeners[featureKey] ?.toList() ?: emptyList()
    }

    companion object {
        val listeners = mutableMapOf<String, MutableList<ResourceObjectFeatureImportListener>>()
    }
}