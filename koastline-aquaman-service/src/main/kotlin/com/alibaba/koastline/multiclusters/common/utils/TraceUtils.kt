package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.koastline.multiclusters.common.logger
import com.taobao.eagleeye.EagleEye
import java.net.InetAddress
import java.net.UnknownHostException

/**
 * <AUTHOR>
 */
object TraceUtils {
    val log by logger()
    private val traceId = ThreadLocal<String>()

    fun setTraceId(inputTraceId: String) {
        traceId.set(inputTraceId)
    }

    fun generateNewTraceId(): String = EagleEye.generateTraceId(getLocalHostAddress())

    fun unsetTraceId() {
        traceId.remove()
    }

    fun getTraceId(): String {
        return traceId.get() ?: EagleEye.getTraceId() ?: generateNewTraceId()
    }

    private fun getLocalHostAddress():String {
        return try {
            val localHost = InetAddress.getLocalHost()
            localHost.hostAddress
        }catch (e: UnknownHostException){
            log.warn("require localhost with exception!")
            LocalHost
        }
    }

    private const val LocalHost = "127.0.0.1"
}