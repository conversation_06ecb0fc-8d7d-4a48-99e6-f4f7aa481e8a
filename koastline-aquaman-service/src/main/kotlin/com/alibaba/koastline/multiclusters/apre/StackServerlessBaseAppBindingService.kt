package com.alibaba.koastline.multiclusters.apre

import com.alibaba.koastline.multiclusters.apre.model.ServerlessBaseAppOfStackExtraParams
import com.alibaba.koastline.multiclusters.apre.model.StackServerlessBaseAppBindingDataDO
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.dao.env.StackServerlessBaseAppBindingRepo
import com.alibaba.koastline.multiclusters.data.vo.env.StackServerlessBaseAppBindingData
import org.apache.ibatis.annotations.Param
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StackServerlessBaseAppBindingService {
    @Autowired
    lateinit var stackServerlessBaseAppBindingRepo: StackServerlessBaseAppBindingRepo

    @Transactional
    fun createOrUpdateServerlessBaseAppOfStack(serverlessBaseAppName: String, envStackId: String, employeeId: String,
                                               extraParams: ServerlessBaseAppOfStackExtraParams?
    ): Long {
        stackServerlessBaseAppBindingRepo.findByEnvStackId(envStackId) ?.let {
            stackServerlessBaseAppBindingRepo.delById(checkNotNull(it.id), employeeId)
        }
        val stackServerlessBaseAppBindingData = StackServerlessBaseAppBindingData(
            envStackId = envStackId,
            serverlessBaseAppName = serverlessBaseAppName,
            extraParams = extraParams ?.run {JsonUtils.writeValueAsString(extraParams) },
            creator = employeeId,
            modifier = employeeId
        )
        stackServerlessBaseAppBindingRepo.insert(stackServerlessBaseAppBindingData)
        return checkNotNull(stackServerlessBaseAppBindingData.id)
    }

    fun getServerlessBaseAppName(envStackId: String): String? {
        return stackServerlessBaseAppBindingRepo.findByEnvStackId(envStackId) ?.run { this.serverlessBaseAppName }
    }

    fun getStackServerlessBaseAppBindingData(envStackId: String): StackServerlessBaseAppBindingDataDO? {
        return stackServerlessBaseAppBindingRepo.findByEnvStackId(envStackId) ?.run {
            StackServerlessBaseAppBindingDataDO(
                id = this.id!!,
                envStackId = this.envStackId,
                serverlessBaseAppName = this.serverlessBaseAppName,
                extraParams = this.extraParams ?.run { JsonUtils.readValue(this, ServerlessBaseAppOfStackExtraParams::class.java) },
                creator = this.creator,
                modifier = this.modifier,
                gmtCreate = this.gmtCreate,
                gmtModified =  this.gmtModified,
                isDeleted = this.isDeleted
            )
        }
    }

    /**
     * 获取唯一的绑定声明数据，如果涉及多个，则资源数据必须相同，否则抛异常处理
     */
    fun getUniqueStackServerlessBaseAppBindingData(envStackIds: List<String>): StackServerlessBaseAppBindingDataDO? {
        val stackServerlessBaseAppBindingDataList = envStackIds.mapNotNull { envStackId ->
            getStackServerlessBaseAppBindingData(envStackId)
        }
        return when(stackServerlessBaseAppBindingDataList.size) {
            0 -> null
            1 -> stackServerlessBaseAppBindingDataList[0]
            else -> if (stackServerlessBaseAppBindingDataList.distinctBy { it.identity() } .size == 1) {
                stackServerlessBaseAppBindingDataList[0]
            } else {
                throw BizException("请调整环境Serverless资源声明配置相同,环境列表:${envStackIds.joinToString(",")}")
            }
        }
    }

    @Transactional
    fun delByEnvStackId(@Param("envStackId")  envStackId: String, @Param("modifier") modifier: String) {
        stackServerlessBaseAppBindingRepo.delByEnvStackId(envStackId, modifier).let {
            if (it == 0) throw BizException("环境[envStackId:${envStackId}]未绑定基座应用")
        }
    }
}