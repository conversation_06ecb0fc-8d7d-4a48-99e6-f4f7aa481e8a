package com.alibaba.koastline.multiclusters.config

import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
class RegionPropertiesConfig {
    var regionProperties = RegionDiamondConfig.buildRegionProperties()
}

data class RegionProperties(
        val regions: MutableMap<String, String>,
        val az: MutableMap<String, String>
)