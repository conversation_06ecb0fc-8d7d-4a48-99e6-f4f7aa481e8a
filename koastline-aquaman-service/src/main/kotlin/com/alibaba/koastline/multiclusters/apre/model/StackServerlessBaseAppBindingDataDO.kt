package com.alibaba.koastline.multiclusters.apre.model

import java.time.Instant
import java.util.*

data class StackServerlessBaseAppBindingDataDO (
    val id: Long,
    val envStackId: String,
    val serverlessBaseAppName: String,
    val extraParams: ServerlessBaseAppOfStackExtraParams?,
    val creator: String, //工号
    val modifier: String, //工号
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
) {
    fun identity(): String {
        return "$serverlessBaseAppName${extraParams ?.identity()}"
    }
}

data class ServerlessBaseAppOfStackExtraParams (
    /**
     * 基座ID
     */
    val runtimeId: String? = null,
    /**
     * 基座环境StackId
     */
    val runtimeBaseEnvStackId: String? = null
) {
    fun identity(): String {
        return "$runtimeId$runtimeBaseEnvStackId"
    }
}