package com.alibaba.koastline.multiclusters.config

import com.alibaba.boot.diamond.listener.DataIdListener
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.taobao.diamond.client.Diamond
import org.slf4j.LoggerFactory
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap
/**
 * <AUTHOR>
 */
@Component
class RegionDiamondConfig(val regionPropertiesConfig: RegionPropertiesConfig) : DataIdListener {
    override fun getDiamondUrl(): String {
        return "diamond://koastline-aquaman/DEFAULT_GROUP/koasltine-aquaman-region"
    }

    override fun valueChanged(newResource: Resource?) {
        regionPropertiesConfig.regionProperties = buildRegionProperties()
        logger.info("region properties changed as ${regionPropertiesConfig.regionProperties}")
    }

    companion object {
        private const val REGION_MAPPING_DATA_ID = "koasltine-aquaman-region"
        private const val REGION_MAPPING_GROUP_ID = "DEFAULT_GROUP"
        private val logger = LoggerFactory.getLogger("DEBUG_LOGGER")!!

        private val regionMapping = ConcurrentHashMap<String, String>()
        private val azMapping = ConcurrentHashMap<String, String>()

        fun buildRegionProperties() : RegionProperties {
            val regionMappings = Diamond.getConfig(REGION_MAPPING_DATA_ID, REGION_MAPPING_GROUP_ID, 5000)
            logger.info("region mapping $regionMappings")
            return handleRegionMappings(regionMappings)
        }

        private fun handleRegionMappings(regionMappings: String): RegionProperties {

            val objectMapper = ObjectMapper()
            logger.info("region mapping starts mapping $regionMappings")
            val regionPropertiesMap = objectMapper.readValue<MutableMap<String, MutableMap<String,String>>>(regionMappings)
            logger.info("regionPropertiesMap $regionPropertiesMap")
            val regionProperties = regionPropertiesMap["regions"]?.let { regionPropertiesMap["az"]?.let { item -> RegionProperties(it, item) } }
            logger.info("regionProperties $regionProperties")
            regionProperties?.apply {
                this.regions.let {
                    for (item in it) {
                        if(regionMapping.containsKey(item.key)) {
                            logger.info("region key duplicated ${item.key}")
                            continue
                        }
                        regionMapping[item.key] = item.value
                    }
                }
                this.az.let {
                    for (item in it) {
                        if(azMapping.containsKey(item.key)) {
                            logger.info("region key duplicated ${item.key}")
                            continue
                        }
                        azMapping[item.key] = item.value
                    }
                }
            }
            return RegionProperties(regionMapping, azMapping)
        }
    }
}
