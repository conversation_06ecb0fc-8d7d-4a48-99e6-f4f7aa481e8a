package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.AddAppToDeployGrayException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.normandy.deployment.deployapi.client.DeployApiClient
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class DeploymentApi(val objectMapper: ObjectMapper) {

    val log by logger()

    @Autowired
    lateinit var deployApiClient: DeployApiClient

    @ExternalCall(SYS_CALLED)
    fun addAppToDeployGrayList(appName: String) {
        when {
            !deployApiClient.updateResourceObjectDeployConfig(appName, "add").isSuccess() -> {
                throw AddAppToDeployGrayException("添加应用 $appName 到发布灰度失败")
            }
        }
    }

    companion object {
        const val SYS_CALLED = "deployment-api"
    }

}