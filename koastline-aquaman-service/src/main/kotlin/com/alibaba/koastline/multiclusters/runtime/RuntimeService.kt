package com.alibaba.koastline.multiclusters.runtime

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ApREService.Companion.RUNTIME_TEMPLATE_PREFIX
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeDataException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.data.dao.env.RuntimeRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeData
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.params.HALO_RUNTIME_TYPE_LABEL_VALUE
import com.alibaba.koastline.multiclusters.resourceobj.params.SCHEDULE_ENV_TYPE_LABEL_NAME
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelType
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeWorkloadAndRouteRegistryDto
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeWorkloadAndRouteUnRegistryDto
import com.alibaba.koastline.multiclusters.runtime.model.RuntimeWorkloadReqDto
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadRunningStatus.UNINSTALLED
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.RUNTIME
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractNonOrientedScaleInScheduleRefactor.Companion.METADATA_DELIMITER
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class RuntimeService {
    val log by logger()
    @Autowired
    lateinit var runtimeBaseService: RuntimeBaseService
    @Autowired
    lateinit var apRELabelService: ApRELabelService
    @Autowired
    lateinit var apRElabelSpecService: ApREFeatureSpecService
    @Autowired
    lateinit var apREService: ApREService
    @Autowired
    lateinit var resourcePoolService: ResourcePoolService
    @Autowired
    lateinit var userLabelBaseService: UserLabelBaseService
    @Autowired
    lateinit var runtimeRepo: RuntimeRepo
    @Autowired
    lateinit var skylineApi: SkylineApi

    @Transactional
    fun registryRuntimeWorkloadAndRoute(runtimeWorkloadAndRouteRegistryDto: RuntimeWorkloadAndRouteRegistryDto) {
        val runtime = checkRuntimeWorkloadAndRouteRegistryDto(runtimeWorkloadAndRouteRegistryDto)
        runtimeWorkloadAndRouteRegistryDto.runtimeWorkloadReqDtoList.forEach { runtimeWorkloadReqDto ->
            //注册RuntimeWorkload
            runtimeBaseService.registryRuntimeWorkload(RuntimeWorkloadCreateReqDto(
                runtimeKey = runtimeWorkloadAndRouteRegistryDto.runtimeKey,
                site = runtimeWorkloadReqDto.site,
                unit = runtimeWorkloadReqDto.unit,
                stage = runtimeWorkloadReqDto.stage,
                clusterId = runtimeWorkloadReqDto.clusterId,
                status = runtimeWorkloadReqDto.status,
                runningStatus = runtimeWorkloadReqDto.runningStatus,
                operator = runtimeWorkloadAndRouteRegistryDto.operator
            ))
            //注册路由
            runtimeWorkloadReqDto.routeMessage ?.let {
                buildApRELabelAndSpec(
                    runtime = runtime,
                    runtimeWorkloadReqDto = runtimeWorkloadReqDto
                ).forEach {
                    apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(it)
                }
            }
        }
    }

    @Transactional
    fun unRegistryRuntimeWorkloadAndRoute(runtimeWorkloadAndRouteUnRegistryDto: RuntimeWorkloadAndRouteUnRegistryDto) {
        val runtime = checkRuntimeWorkloadAndRouteUnRegistryDto(runtimeWorkloadAndRouteUnRegistryDto)
        runtimeWorkloadAndRouteUnRegistryDto.runtimeWorkloadUnRegistryReqDtoList.forEach { runtimeWorkloadUnRegistryReqDto ->
            RuntimeWorkloadCreateReqDto(
                runtimeKey = runtimeWorkloadAndRouteUnRegistryDto.runtimeKey,
                site = runtimeWorkloadUnRegistryReqDto.site,
                unit = runtimeWorkloadUnRegistryReqDto.unit,
                stage = runtimeWorkloadUnRegistryReqDto.stage,
                clusterId = runtimeWorkloadUnRegistryReqDto.clusterId,
                status = runtimeWorkloadUnRegistryReqDto.status,
                runningStatus = runtimeWorkloadUnRegistryReqDto.runningStatus,
                operator = runtimeWorkloadAndRouteUnRegistryDto.operator
            ).let {
                runtimeBaseService.updateRuntimeWorkloadRunningStatus(it)
                runtimeBaseService.unRegistryRuntimeWorkload(it)
            }

            //注销路由
            apREService.listResourcePoolByMetadata(
                unit = runtimeWorkloadUnRegistryReqDto.unit,
                stage = runtimeWorkloadUnRegistryReqDto.stage,
                site = runtimeWorkloadUnRegistryReqDto.site,
                clusterId = runtimeWorkloadUnRegistryReqDto.clusterId
            ).flatMap { resourcePool ->
                apRELabelService.findApRELabelByTarget(resourcePool.resourcePoolKey, RESOURCE_POOL.name).filter {
                    it.name == APRE_LABEL_FEATURE_NAME && it.value == RUNTIME_TEMPLATE_PREFIX + runtime.appName
                }
            }.forEach { apRELabel ->
                apRElabelSpecService.deleteByLabelKeyAndSpecCode(labelKey = apRELabel.apRELabelKey!!, specCode = "${runtime.runtimeKey}")
            }
        }
    }

    fun computeRuntimeScheduleResult(envStackId: String): ScheduleResult {
        if (isStandardRuntime(envStackId)) {
            //标准基座
            val resourceGroupList = skylineApi.listResourceGroupConfigByEnvStackId(envStackId)
            if (resourceGroupList.isEmpty()) {
                return ScheduleResult(workloadExpectedStates = emptyList())
            }
            val workloadExpectedStateList = mutableListOf<WorkloadExpectedState>()
            resourceGroupList.forEach { resourceGroup ->
                val runtimeData = getRuntime(resourceGroup = resourceGroup)
                workloadExpectedStateList.addAll(computeRuntimeInstalledWorkload(
                        appName = runtimeData.appName,
                        runtimeKey = runtimeData.runtimeKey
                    )
                )
            }
            return ScheduleResult(workloadExpectedStates = workloadExpectedStateList)
        }
        val runtimeData = getRuntime(envStackId = envStackId)
        return ScheduleResult( workloadExpectedStates = computeRuntimeInstalledWorkload(
                appName = runtimeData.appName,
                runtimeKey = runtimeData.runtimeKey
            )
        )
    }

    fun isStandardRuntime(envStackId: String): Boolean {
        userLabelBaseService.findByExternalAndLabelWithCache(envStackId, UserLabelExternalType.ENV_STACK.name, SCHEDULE_ENV_TYPE_LABEL_NAME) ?.let {
            return it.labelValue == ScheduleEnvType.RUNTIME.name
        }
        return false
    }

    private fun getRuntime(envStackId: String? = null, resourceGroup: String? = null): RuntimeData {
        envStackId ?.let {
            return checkNotNull(runtimeRepo.findByEnvStackId(envStackId)) {"未找到Runtime注册信息，envStackId:${envStackId}"}
        }
        resourceGroup ?.let {
            return checkNotNull(runtimeRepo.findByResourceGroupName(resourceGroup)) {"未找到Runtime注册信息，resourceGroup:${resourceGroup}"}
        }
        throw ScheduleException("调度异常：环境StackId和分组名不能同时为空.")
    }

    fun computeRuntimeInstalledWorkload(appName: String, runtimeKey: String): List<WorkloadExpectedState> {
        val runtimeData = runtimeBaseService.findRuntime(appName, runtimeKey)
        return runtimeBaseService.listInstalledWorkloadByRuntimeKey(runtimeData.appName, runtimeData.runtimeKey).map { runtimeWorkload ->
            //基座采用ASI模式处理，暂时去除 untimeId = runtimeData.runtimeKey
            WorkloadExpectedState(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    appName = runtimeData.appName,
                    resourceGroup = runtimeData.resourceGroupName,
                    unit = runtimeWorkload.unit,
                    stage = runtimeWorkload.stage,
                    site = runtimeWorkload.site,
                    clusterId = runtimeWorkload.clusterId,
                    namespace = runtimeData.appName
                ),
                clusterProfile = resourcePoolService.getClusterProfileNew(clusterId = runtimeWorkload.clusterId),
                params = emptyMap()
            )
        }
    }

    fun isHaloRuntimeSchedule(scheduleEnvType: ScheduleEnvType?, envStackId: String?): Boolean {
        if (scheduleEnvType == RUNTIME) {
            return true
        }
        envStackId ?.let { envStackId ->
            if (isStandardRuntime(envStackId)) {
                return true
            }
        }
        return false
    }

    private fun buildApRELabelAndSpec(runtime: RuntimeData, runtimeWorkloadReqDto: RuntimeWorkloadReqDto): List<ApRELabelCreateReqDto> {
        val resourcePoolDataList = apREService.listResourcePoolByMetadata(
            unit = runtimeWorkloadReqDto.unit,
            stage = runtimeWorkloadReqDto.stage,
            site = runtimeWorkloadReqDto.site,
            clusterId = runtimeWorkloadReqDto.clusterId
        )
        val routeMessage = checkNotNull(runtimeWorkloadReqDto.routeMessage)
        return resourcePoolDataList.map { resourcePool ->
            ApRELabelCreateReqDto(
                targetKey = resourcePool.resourcePoolKey,
                targetType = RESOURCE_POOL.name,
                type = ApRELabelType.SERVERLESS,
                name = APRE_LABEL_FEATURE_NAME,
                value = RUNTIME_TEMPLATE_PREFIX + runtime.appName,
                apREFeatureSpecList = listOf(ApREFeatureSpecCreateReqDto(
                    title = routeMessage.title,
                    specType = buildRuntimeSpecType(runtime),
                    specCode = "${runtime.runtimeKey}",
                    scope = routeMessage.scope,
                    status = routeMessage.status,
                    sourceId = "${runtime.appName}.${runtime.runtimeKey}",
                    sourceType = DEFAULT_RUNTIME_SOURCE_TYPE,
                    annotations = routeMessage.annotations
                ))
            )
        }
    }

    private fun checkRuntimeWorkloadAndRouteRegistryDto(runtimeWorkloadAndRouteRegistryDto: RuntimeWorkloadAndRouteRegistryDto): RuntimeData {
        val runtime = runtimeBaseService.findRuntime(
            appName = runtimeWorkloadAndRouteRegistryDto.appName,
            runtimeKey = runtimeWorkloadAndRouteRegistryDto.runtimeKey
        )
        runtimeWorkloadAndRouteRegistryDto.runtimeWorkloadReqDtoList.forEach {
            if (it.runningStatus == UNINSTALLED
                && it.routeMessage != null) {
                throw RuntimeDataException("非法的RuntimeWorkload注册数据：${runtimeWorkloadAndRouteRegistryDto.runtimeWorkloadReqDtoList}")
            }
        }
        return runtime
    }

    private fun checkRuntimeWorkloadAndRouteUnRegistryDto(runtimeWorkloadAndRouteUnRegistryDto: RuntimeWorkloadAndRouteUnRegistryDto): RuntimeData {
        val runtime = runtimeBaseService.findRuntime(
            appName = runtimeWorkloadAndRouteUnRegistryDto.appName,
            runtimeKey = runtimeWorkloadAndRouteUnRegistryDto.runtimeKey
        )
        runtimeWorkloadAndRouteUnRegistryDto.runtimeWorkloadUnRegistryReqDtoList.forEach {
            if (it.runningStatus != UNINSTALLED) {
                throw RuntimeDataException("非法的RuntimeWorkload注销数据：${runtimeWorkloadAndRouteUnRegistryDto.runtimeWorkloadUnRegistryReqDtoList}")
            }
        }
        return runtime
    }

    /**
     * 根据Runtime类型获取RT
     */
    private fun buildRuntimeSpecType(runtime: RuntimeData): String {
        userLabelBaseService.findByExternalAndLabelWithCache(
            externalId = runtime.resourceGroupName,
            externalType = UserLabelExternalType.RESOURCE_GROUP.name,
            labelName = UserLabelType.SCENE.code
        ) ?.let { userLabel ->
            if (userLabel.labelValue == HALO_RUNTIME_TYPE_LABEL_VALUE) {
                return "$RUNTIME_TEMPLATE_PREFIX${runtime.appName}$METADATA_DELIMITER${runtime.runtimeKey}"
            }
        }
        throw RuntimeDataException("其他SpecType组装待实现.")
    }

    companion object {
        const val DEFAULT_RUNTIME_SOURCE_TYPE: String = "AONE"
    }

}