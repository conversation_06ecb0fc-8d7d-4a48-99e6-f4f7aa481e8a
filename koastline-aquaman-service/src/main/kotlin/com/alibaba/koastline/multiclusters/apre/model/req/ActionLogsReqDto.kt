package com.alibaba.koastline.multiclusters.apre.model.req

import com.alibaba.koastline.multiclusters.apre.model.Exclusion
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("操作日志查询")
@JsonIgnoreProperties(ignoreUnknown = true)
data class ActionLogsReqDto(
    @ApiModelProperty("范围限定ID")
    val externalId: String,
    @ApiModelProperty("范围限定类型")
    val externalType: String,
    @ApiModelProperty("租户名称", required = false)
    val submitters: String = "SYSTEM",
) {

    fun validate(): ActionLogsReqDto {
        require(externalId.isNotBlank()) { "externalId must be non-blank" }
        require(externalType.isNotBlank()) { "externalType must be non-blank" }
        return this
    }
}