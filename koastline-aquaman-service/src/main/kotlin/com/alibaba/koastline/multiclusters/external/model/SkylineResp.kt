package com.alibaba.koastline.multiclusters.external.model

import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.fasterxml.jackson.annotation.JsonProperty

data class AppGroup(
    @JsonProperty("name")
    val name: String,
    @JsonProperty("usage_type")
    val usageType: String,
    @JsonProperty("app_name")
    val appName: String,
    @JsonProperty("original_name")
    val originalName: String? = null,
    @JsonProperty("safety_out")
    val safetyOut: Int = 0,
    val tags: List<String> = emptyList()
) {
    /**
     * 标识同类限定身份
     */
    fun whetherSafeOutOpen(): Boolean {
        return safetyOut == 1
    }

    /**
     * 是否为测试分组
     */
    fun isTestingAppGroup(): Boolean = (originalName == SkylineApi.TESTING_ENV_COMMON_APP_NAME)
}