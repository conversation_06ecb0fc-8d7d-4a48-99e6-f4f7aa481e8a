package com.alibaba.koastline.multiclusters.appenv.params

/**
 * <AUTHOR>
 */

data class ClusterEnvironment(
        val clusterEnvironmentKey: String,
        val clusterProvider: String? = null,
        val clusterType: String? = null,
        val region: String,
        val az: String,
        val envTags: MutableMap<String, String>,
        val clusterId: String? = null,
        val clusterName: String? = null) {
    constructor(clusterEnvironmentKey: String, clusterProvider: String, clusterType: String, region: String, az: String, envTags: MutableMap<String, String>): this(
            clusterEnvironmentKey, clusterProvider, clusterType, region, az, envTags, null, null
    )
}

data class ClusterEnvironmentDetails(
        val clusterEnvironmentKey: String,
        val clusterProvider: String,
        val clusterType: String,
        val region: String,
        val az: String,
        val envTags: Map<String, String>,
        val annotation: MutableMap<String, Any>? = null,
        val clusterId: String,
        val clusterName: String
)