package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.NormandyException
import com.alibaba.koastline.multiclusters.common.exceptions.NormandyGropException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.CrResourceObjectList
import com.alibaba.koastline.multiclusters.external.model.GpuResourceMappingDTO
import com.alibaba.koastline.multiclusters.external.model.NormandyResponse
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class NormandyGropApi() {
    val log by logger()

    @Value("\${normandy.grop.host}")
    lateinit var host: String


    @ExternalCall(SYS_CALLED)
    fun queryGpuResourceMapping(): List<GpuResourceMappingDTO> {
        val url = "${host}/metaData/gpuResourceMapping"

        val rs = HttpClientUtils.httpGet(url, null)
        val normandyResponse =
            JsonUtils.readValue(rs, object : TypeReference<NormandyResponse<List<GpuResourceMappingDTO>>>() {})
        if (!normandyResponse.success) {
            val errMsg =
                "${NormandyApi.SYS_CALLED} queryGpuResourceMapping failed, errMsg:${normandyResponse.message}"
            throw NormandyGropException(errMsg)
        }
        return normandyResponse.data
    }


    companion object {
        const val SYS_CALLED = "normandy-grop"
    }
}