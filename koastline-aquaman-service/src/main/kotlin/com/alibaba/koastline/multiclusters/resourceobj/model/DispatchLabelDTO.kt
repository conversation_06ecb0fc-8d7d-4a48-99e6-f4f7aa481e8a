package com.alibaba.koastline.multiclusters.resourceobj.model

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty


@ApiModel("调度路由标签")
data class DispatchLabelDTO(
  @ApiModelProperty(value = "标签定义code(唯一标识)", required = true)
  val code: String,

  @ApiModelProperty("中文名称")
  val cnName: String? = null,

  @ApiModelProperty(value = "类型", required = true)
  val type: String,

  @ApiModelProperty(value = "作用域，对应枚举值：container、nc、all", required = true)
  val scope: String,

  @ApiModelProperty("标签适用于指定资源池，字段为空表示适用于所有资源池")
  val specificResourcePool: String? = null,

  @ApiModelProperty(value = "标签的value是否可以为任意值，默认为任意值(boolean值为0)", required = true)
  val isSpecificValue: Boolean = false,

  @ApiModelProperty("状态，对应枚举值：ONLINE, OFFLINE")
  var status: String? = null,

  @ApiModelProperty("标签的描述")
  val description: String? = null,
  val approveId: String? = null,

  @ApiModelProperty("来源系统，默认hcrm")
  val submitSystem: String? = null,
  var specificValue: String? = null,
  val isRemoved: Boolean = false,
  val creator: String? = null,
  val modifier: String? = null,
) {
  fun validate(): DispatchLabelDTO {
    require(code.isNotEmpty())
    return this
  }
}

@ApiModel
data class DispatchLabelSpecificValueDTO(
  @ApiModelProperty(value = "标签定义code(唯一标识)", required = true)
  val code: String,
  @ApiModelProperty(value = "可选值Map，key为值，value为说明", required = true)
  val values: Map<String, String>,
)

@ApiModel
class DispatchLabelValueDTO(
  val id: Long? = null,

  @ApiModelProperty(value = "应用名称", required = true)
  val appName: String,
  val labelType: String? = null,

  @ApiModelProperty(value = "标签定义code(唯一标识)", required = true)
  val labelCode: String,
  val labelName: String? = null,

  @ApiModelProperty(value = "标签值", required = true)
  val labelValue: String,

  @ApiModelProperty("来源系统，默认hcrm")
  val submitSystem: String? = null,

  @ApiModelProperty("应用分组")
  val groupName: String? = null,

  @ApiModelProperty("机房")
  val idc: String? = null,

  @ApiModelProperty("单元")
  val unit: String? = null,


  @ApiModelProperty("环境")
  val env: String? = null,

  val remark: String? = null,
  val isRemoved: Boolean = false,
  val creator: String? = null,
  val modifier: String? = null,
) {

  fun getIsRemoved(): Boolean {
    return isRemoved
  }

  fun validate(): DispatchLabelValueDTO {
    require(appName.isNotEmpty())
    require(labelCode.isNotEmpty())
    return this
  }
}

data class ConfigMapDTO(
  val data: Map<String, String>? = null,
)
