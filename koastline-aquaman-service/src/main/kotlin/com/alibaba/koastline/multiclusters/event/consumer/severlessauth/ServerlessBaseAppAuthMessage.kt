package com.alibaba.koastline.multiclusters.event.consumer.severlessauth

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.MatchScopeDataException


data class AdmissionChangeRequest(
    val add: List<AdmissionScope>,
    val del: List<AdmissionScope>,
    val runtimeAppId: Int,
    val runtimeAppName: String
)

data class AdmissionScope(
    val target: Target,
    val targetId: String,
    val targetType: String
) {
    fun getMatchScopeTargetId(): String {
        val matchScopeTargetType = AdmissionTargetType.valueOf(
            targetType
        ).map2MatchScopeTargetType()
        when (matchScopeTargetType) {
            MatchScopeExternalTypeEnum.APPLICATION -> {
                return target.name
            }

            MatchScopeExternalTypeEnum.AONE_PRODUCTLINE -> {
                check(!target.fullIdPath.isNullOrBlank()) { "missing fullIdPath in admission scope" }
                return buildProductLineForAppCenter(
                    buId = checkNotNull(target.divisionId) { "missing division id in admission scope" },
                    productLineFullPath = target.fullIdPath
                )
            }
            else -> {
                throw MatchScopeDataException("unSupported to resolved matchScope type")
            }
        }
    }

    private fun buildProductLineForAppCenter(
        buId: Long, productLineFullPath: String
    ): String {
        return "${buId}${MatchScopeService.BU_PRODUCT_LINE_SPLITTER}$productLineFullPath"
    }
}

data class Target(
    val divisionId: Long? = null,
    val fullIdPath: String? = null,
    val id: Long,
    val name: String
)

enum class AdmissionTargetType {
    APPLICATION,
    PRODUCT_LINE;

    fun map2MatchScopeTargetType() = when (this) {
        APPLICATION -> MatchScopeExternalTypeEnum.APPLICATION
        PRODUCT_LINE -> MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
    }
}