package com.alibaba.koastline.multiclusters.external.model

/**
 * @author:    <EMAIL>
 * @description:  三方定制化SPE返回结果
 * @date:    2025/2/14 10:53 AM
 */
data class ThirdJoinSpeResp(
    /**
     * 是否已接入灰度
     */
    val joinedGray: Boolean? = false,

    /**
     * 应用名称
     */
    val appName: String? = null,

    /**
     * 灰度备注
     */
    val remark: String? = null,

    var appGroups: List<SpeAppGroup?>? = null
)


data class SpeAppGroup(
    /**
     * 应用线上分组名
     */
    val groupName: String? = null,

    /**
     * 应用spe环境的分组名
     */
    val speGroupName: String? = null,

    /**
     * spe分组的ip列表
     */
    val ips: List<String>? = null,

    /**
     * spe 分组 ip 数量
     */
    val speGroupIpCount: Int? = null,
)

