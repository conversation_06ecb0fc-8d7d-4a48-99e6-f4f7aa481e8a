package com.alibaba.koastline.multiclusters.schedule.service.fiter

import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.AbstractScheduleFilterFacade
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 运行态调度计算服务
 * 功能：根据集群实时负载调度，优先选择负载低的集群
 */
@Component
class ClusterLoadScheduleFilterProcessor : AbstractScheduleFilterFacade(), ScheduleFilterFacade, InitializingBean {
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory
    override fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
//        TODO("Not yet implemented")
        return matchDeclaration
    }

    override fun afterPropertiesSet() {
        scheduleFilterServiceFactory.registryScheduleFilterService(this)
    }
}