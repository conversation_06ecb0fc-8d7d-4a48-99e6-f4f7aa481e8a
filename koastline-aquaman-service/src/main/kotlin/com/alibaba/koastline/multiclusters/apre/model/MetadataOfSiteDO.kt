package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant
import java.util.*

data class MetadataOfSiteDO(
    val id: Long? = null,
    val site: String,
    val region: String,
    val creator: String? = null,
    val modifer:String? = null,
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtCreate: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val gmtModified: Date? = Date(Instant.now().toEpochMilli()),
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    val isDeleted: String? = IS_NOT_DELETED
)
