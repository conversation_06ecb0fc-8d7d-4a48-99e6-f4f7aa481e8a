package com.alibaba.koastline.multiclusters.apre.attorney

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.base.MetadataUtils
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.params.ApREConstants.APRE_DEED_DECLARATION_UNDEFINED_ID
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedMultiDeclarationException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedNotFoundException
import com.alibaba.koastline.multiclusters.common.utils.Base64Utils
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.data.dao.env.ApREDeedRepo
import com.alibaba.koastline.multiclusters.data.dao.env.MetadataOfSiteRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeed
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.security.MessageDigest
import java.time.Instant
import java.util.*

@Component
class ApREDeedService {
    @Autowired
    lateinit var apREDeedRepo: ApREDeedRepo
    @Autowired
    lateinit var metadataOfSiteRepo: MetadataOfSiteRepo

    var messageDigest: MessageDigest = MessageDigest.getInstance("MD5")

    fun findApREDeedByKey(apREDeedKey: String): ApREDeedDO {
        val originalDeedDO = Base64Utils.decode<ApREDeedDO>(
            (apREDeedRepo.findByKey(apREDeedKey) ?: throw ApREDeedNotFoundException()).content
        )
        val processedDeclarations = originalDeedDO.declarations ?.map { declaration ->
            processApREDeedDeclaration(declaration)
        }
        return ApREDeedDO(apREDeedKey, originalDeedDO.identityInfo, processedDeclarations ?.toMutableList())
    }

    /**
     *  声明加工
     */
    private fun processApREDeedDeclaration(declaration: Declaration): Declaration {
        //给声明授予一个UUID，便于后继功能处理
        if (declaration.id.isNullOrBlank() || declaration.id == APRE_DEED_DECLARATION_UNDEFINED_ID) {
            declaration.id = UUID.randomUUID().toString()
        }
        val region = declaration.az ?.run {
            metadataOfSiteRepo.findBySite(this) ?.region
        } ?: declaration.region
        return declaration.copy(region = region)
    }

    fun checkAndMakeupApREDeed(apREDeedDO: ApREDeedDO) {
        apREDeedDO.copy(
            declarations = apREDeedDO.declarations?.map {
                it.copy(
                    region = it.region?.toNullIfBlank()?.lowercase(),
                    az = it.az?.toNullIfBlank()?.lowercase(),
                    stage = it.stage?.toNullIfBlank()?.uppercase(),
                    unit = it.unit?.toNullIfBlank()?.run {
                        MetadataUtils.formatUnit(it.unit)
                    }
                )
            }?.toMutableList()
        )
        if (!apREDeedDO.declarations.isNullOrEmpty() && apREDeedDO.declarations!!.size > 1) {
            //改为一个契约限定只能包含一个声明
            throw ApREDeedMultiDeclarationException()
        }
        apREDeedDO.declarations ?.let { declarations ->
            if (declarations.isEmpty()) {
                declarations.add(
                    Declaration(APRE_DEED_DECLARATION_UNDEFINED_ID,null,null,null,null,null)
                )
            } else {
                declarations.forEach { declaration ->
                    declaration.id = APRE_DEED_DECLARATION_UNDEFINED_ID
                }
            }
        } ?:let {
            apREDeedDO.declarations = mutableListOf(
                Declaration(APRE_DEED_DECLARATION_UNDEFINED_ID,null,null,null,null,null)
            )
        }
        val content = Base64Utils.encode(apREDeedDO)
        apREDeedDO.key = Base64Utils.encode(messageDigest.digest(content.toByteArray(Charsets.UTF_8)))
    }

    /**
     * 如果契约KEY存在，则返回契约详情；否则生成契约KEY并创建契约
     * 2022.9.6修订，如下：
     *      1.一次契约只包括一个声明
     *      2.不再生成唯一的declaration.id,填充固定值"id"防止外部引用错误，待外部确认后去掉
     */
    @Transactional
    fun createApREDeedWhileNotExist(apREDeedDO: ApREDeedDO): ApREDeedDO {
        checkAndMakeupApREDeed(apREDeedDO)
        val content = Base64Utils.encode(apREDeedDO)
        val apREDeedKey = checkNotNull(apREDeedDO.key){ "apREDeedDO.key cannot be null in createApREDeedWhileNotExist!" }
        apREDeedRepo.findByKey(apREDeedKey) ?:let {
            val now = Date(Instant.now().toEpochMilli())
            val identityInfo = checkNotNull(apREDeedDO.identityInfo){ "apREDeedDO.key cannot be null in apREDeedRepo.findByKey of createApREDeedWhileNotExist!" }
            apREDeedRepo.insert(
                ApREDeed(
                null,
                    apREDeedKey,
                    identityInfo.envLevel,
                    checkNotNull(identityInfo.appName){"identityInfo.appName cannot be null in createApREDeedWhileNotExist!" },
                    identityInfo.envId,
                    identityInfo.envStackId,
                    identityInfo.nodeGroup,
                    content,
                    now,
                    now,
                    Constants.IS_NOT_DELETED
                )
            )
        }
        return apREDeedDO
    }
}