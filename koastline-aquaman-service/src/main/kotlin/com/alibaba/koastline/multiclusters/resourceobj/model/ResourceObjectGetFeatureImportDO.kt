package com.alibaba.koastline.multiclusters.resourceobj.model

import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectFeatureImportStatusEnum
import com.fasterxml.jackson.annotation.JsonProperty
import java.util.*

data class ResourceObjectGetFeatureImportDO(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 资源对象特性Key
     */
    @JsonProperty("traitKey")
    val resourceObjectFeatureKey: String,
    /**
     * 状态,@see ResourceObjectFeatureImportStatusEnum
     */
    val status: String = ResourceObjectFeatureImportStatusEnum.ENABLED.name,
    /**
     * 预置参数映射
     */
    val formData: Map<String, Any>?,
    /**
     * 创建人
     */
    val creator: String = "",
    /**
     * 修改人
     */
    val modifier: String = "",
    val gmtCreate: Date,
    val gmtModified: Date? = null,

    val isDeleted: String = "N",
    val externalId: String? = null,
    val externalType: String? = null,
    val matchScope: MatchScopeDataDO? = null,
    val version: String,

)