package com.alibaba.koastline.multiclusters.resourceobj.model

import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.annotations.ApiModelProperty
import java.io.Serializable

data class ResourceRequirementRequest(
    @ApiModelProperty("应用名", required = true)
    val appName: String,
    @ApiModelProperty("环境StackId", required = false)
    val envStackId: String? = null,
    @ApiModelProperty("分组名", required = false)
    val resourceGroup: String?,
    @ApiModelProperty("站点", required = false)
    val site: String? = null,
    @ApiModelProperty("单元", required = false)
    val unit: String? = null,
    @ApiModelProperty("用途", required = false)
    val stage: String? = null,
    @ApiModelProperty("集群ID", required = false)
    val clusterId: String? = null
)

data class FeatureImportRequest(

    @ApiModelProperty("分组名", required = true)
    val resourceGroup: String,
    @ApiModelProperty("特性", required = true)
    val featureKey: String,
)

data class FeatureImportResourceGroupBatchRequest(

  @ApiModelProperty("分组名列表", required = true)
  val resourceGroups: List<String>,
  @ApiModelProperty("特性", required = true)
  val featureKey: String,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ResourceRequirementResp(
    val resourceRequest: ResourceRequest,
    val resourceLimit: ResourceLimit,
    val resourceRequirementRequest: ResourceRequirementRequest,
    val resourceSpecOrigin: ResourceSpecOrigin,
    val cpuSpecOrigin: CpuSpecOrigin,
    val gpuCardModel: String? = null,
)

enum class ResourceSpecOrigin {
    APP, // 规格来源自应用
    RESOURCE_GROUP, // 规格来源自分组
    DAILY, // 规格来源自应用日常规格
}

enum class CpuSpecOrigin {
    VPA, // CPU 规格来源自 vpa
    NON_VPA, // CPU 规格不来源自 vpa
}

data class ResourceLimit(
    val cpu: String,
    val memory: String,
    val disk: String,
    val gpu: String? = null
)

data class ResourceRequest(
    val cpu: String,
    val memory: String,
    val disk: String,
    val gpu: String? = null
)

/**
 * 不带单位，memory、disk默认为G
 */
data class ResourceSpec(
    val cpu: String,
    val memory: String,
    val disk: String,
    val gpu: String? = null
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }

    constructor(model: String) : this(
        cpu = model.split("-").apply {
            require(this.size == 3) { "the format of the model must be similar to the style of 4-8-60" }
        }[0],
        memory = model.split("-")[1],
        disk = model.split("-")[2],
        gpu = null
    )

    fun toModel(): String {
        return "${this.cpu}-${this.memory}-${this.disk}"
    }
}

/**
 * CPU模式资源规格
 */
data class CpuModelResourceSpec(
    val cpu: String,
    val memory: String,
    val disk: String,
)

/**
 * GPU模式资源规格（特指GPU特性规格）
 */
data class GpuModelResourceSpec(
    val gpu: String,
)

/**
 * 资源分组的资源规格
 */
data class ResourceGroupResourceSpec(
    val resourceGroup: String,
    val resourceSpec: ResourceSpec,
    val employeeId: String
)

data class ResourceGroupCpuModelResourceSpec(
    val resourceGroup: String,
    val resourceSpec: CpuModelResourceSpec,
    val employeeId: String
)

data class ResourceGroupGpuModelResourceSpec(
    val resourceGroup: String,
    val resourceSpec: GpuModelResourceSpec,
    val employeeId: String
)

/**
 * 应用的资源规格
 */
data class AppResourceSpec(
    val appName: String,
    val resourceSpec: ResourceSpec,
    val employeeId: String
)