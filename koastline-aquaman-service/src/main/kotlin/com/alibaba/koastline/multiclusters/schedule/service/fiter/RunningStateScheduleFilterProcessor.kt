package com.alibaba.koastline.multiclusters.schedule.service.fiter

import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleFilterException
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleFilterException.Companion.RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.AbstractScheduleFilterFacade
import com.alibaba.koastline.multiclusters.schedule.service.fiter.facade.ScheduleFilterFacade
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 运行态调度计算服务
 * 为兼容历史功能，短时期内，如果五元组下有运行态POD的集群，则优先选择该集群调度
 * 功能：
 * if 当前资源范围内[ScheduleRequestContent.resourceScope]存在运行中的POD，则优先返回当前ApRE&Resource资源;
 *      if 授权范围内不包含运行态集群，则根据[RunningStateScheduleFilterService#THROW_EXCEPTION_WHILE_RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE] 执行
 *          true: 抛异常（为了兼容历史系统）
 *          false:返回所有授权集群
 *      else
 *          只返回运行态集群
 * else
 *      返回所有授权集群
 */
@Component
class RunningStateScheduleFilterProcessor : AbstractScheduleFilterFacade(), ScheduleFilterFacade, InitializingBean {
    @Autowired
    lateinit var scheduleFilterServiceFactory: ScheduleFilterServiceFactory
    @Autowired
    lateinit var skylineApi: SkylineApi

    override fun doFilter(matchDeclaration: MatchDeclaration, content: ScheduleRequestContent): MatchDeclaration {
        val runningClusterIdList = skylineApi.listClusterIdByResourceScopeAndDeclaration(
            content.resourceScope.envStackId,
            content.resourceScope.resourceGroup!!,
            matchDeclaration.declaration!!
        )
        if (runningClusterIdList.isNullOrEmpty()) {
            return matchDeclaration
        }
        var runningStateFilterApREList = mutableListOf<ApREDO>()
        matchDeclaration.apres.forEach { apRE ->
            apRE.resources.filter { resource ->
                runningClusterIdList.contains(resource.clusterProfileNew!!.clusterId)
            }.let { resourceList ->
                if (resourceList.isNotEmpty()) {
                    runningStateFilterApREList.add(apRE.copy(
                        resources = resourceList
                    ))
                }
            }
        }
        if (runningStateFilterApREList.isNotEmpty()) {
            return matchDeclaration.copy(
                apres = runningStateFilterApREList
            )
        }
        if (THROW_EXCEPTION_WHILE_RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE) {
            throw ScheduleFilterException("资源声明:${matchDeclaration.declaration}，存在运行中资源分布在集群${runningClusterIdList},但上述集群未在授权范围内，请联系PE授权.", RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE)
        }
        return matchDeclaration
    }

    companion object {
        const val THROW_EXCEPTION_WHILE_RUNNING_CLUSTER_NOT_IN_AUTHORITY_SCOPE = true
    }

    override fun afterPropertiesSet() {
        scheduleFilterServiceFactory.registryScheduleFilterService(this)
    }
}