package com.alibaba.koastline.multiclusters.resourceobj.params

import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum

object ResourceObjectConstants {
    const val RESOURCE_SPEC_FEATURE_KEY = "RESOURCE_SPEC"
    const val LIGHT_CONTAINER_FEATURE_KEY = "lightweight-container"
    const val ENV_LABELS_FEATURE_KEY = "ENV_LABELS"
    const val CPUSHARE_FEATURE_KEY = "CPUSHARE"
    const val SAFETY_OUT = "SAFETY_OUT"
    const val GPU_AFFINITY_SPEC_FEATURE_KEY = "GPU_AFFINITY_SPEC"

    //Fed数据存储到resourceObject
    const val FED_TARGET_KEY = "FED_TARGET_SPEC"
    const val FED_POLICY_KEY = "FED_POLICY_SPEC"

    /**
     * 规格特性修改限定范围
     */
    val RESOURCE_OBJECT_FEATURE_SCOPE = mapOf(
        RESOURCE_SPEC_FEATURE_KEY to listOf(
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
        ),
        GPU_AFFINITY_SPEC_FEATURE_KEY to listOf(
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
        )

    )
    const val METADATA_APP_NAME = "appName"
    const val METADATA_RESOURCE_GROUP = "resourceGroup"
    const val METADATA_SITE = "site"
    const val METADATA_UNIT = "unit"
    const val METADATA_STAGE = "stage"
    const val METADATA_SUBGROUP = "subgroup"
    const val METADATA_CLUSTER_ID = "clusterId"
    const val METADATA_ENVTYPE = "envType"
    const val METADATA_DEPLOY_VERSION = "tags.sunfire.com/app-deploy-version"
    const val METADATA_DEPLOY_VERSION_FOR_ROLLINGSET = "tags.sunfire.com/base-app-deploy-version"
}
