package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.AcniHomeException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.AuthorizationAttribute
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.*
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class AcniHomeApi(val objectMapper: ObjectMapper) {

    val log by logger()

    @Value("\${acnihome.host}")
    lateinit var acniHomeHost: String

    @Value("\${acnihome.app-key}")
    lateinit var acniHomeAppKey: String

    @Value("\${acnihome.app-sec}")
    lateinit var acniHomeAppSec: String

    @Autowired
    lateinit var externalCallDowngradeProperties: ExternalCallDowngradeProperties

    /**
     * 查询 Identity Asset 关联的 K8s 资源列表
     *
     * @param metadata AcniHome 调用时通用的 workload metadata
     * @param encoding K8s 资源的编码：`YAML` or `JSON`
     */
    @ExternalCall(SYS_CALLED)
    fun queryRelatedK8sObjectList(metadata: AcniWorkloadMetadata, encoding: String): List<AcniAssetRelatedK8sObject> {

        // Downgrades. Note: service param of invocation should be changed when function name is changed.
        if (externalCallDowngradeProperties.isDowngrade(SYS_CALLED, "queryRelatedK8sObjectList")) {
            log.info("$SYS_CALLED queryRelatedK8sObjectList downgrade.")
            return emptyList()
        }

        // Verifies params.
        require(encoding in AcniK8sObjectEncoding.values().map { it.name }) {
            "illegal encoding `$encoding` appears when query related K8s object list from acni-home"
        }

        // Assembles request body.
        val scopes = listOf(
            AcniAoneAppEnvironment.from(metadata),
            AcniNormandyAppGroup.from(metadata),
            AcniAppWorkloadScope.from(metadata),
        )
        val typedEncoding = AcniK8sObjectEncoding.valueOf(encoding)
        val requestBody = AcniAssetRelatedK8sObjectsQuery(
            scopes = scopes,
            clusterId = metadata.clusterId,
            namespace = metadata.namespace,
            encoding = typedEncoding
        )

        // Request service.
        val response = HttpClientUtils.httpPost(
            "$acniHomeHost$URL_LIST_RELATED_K8S_OBJECTS",
            JsonUtils.writeValueAsString(requestBody),
            AuthorizationAttribute(
                userName = acniHomeAppKey,
                secret = acniHomeAppSec
            )
        )

        // Returns objects directly when parse is successful.
        try {
            return JsonUtils.readListValue(response, JsonUtils.objectTypeReference())
        } catch (ignore: Exception) {
        }

        // Throws exception when request is failed.
        val hint = JsonUtils.readValue(response, AcniRestErrorHint::class.java)
        val errMsg =
            "查询资产关联的K8s资源失败, Status: ${hint.status}, Message:${hint.message}, TraceId:${hint.traceId}"
        throw AcniHomeException(errMsg)
    }


    @ExternalCall(SYS_CALLED)
    fun notifyAcniToInjectVolumes(appName: String) {

        val response = HttpClientUtils.httpPost(
            "$URL_NOTIIFY_ACNI_TO_INJECT_VOLUMES",
            JsonUtils.writeValueAsString(
                SyncEffectiveScopesRequest(
                    appName = appName,
                )
            ),
            null
        )
    }


    companion object {
        const val URL_LIST_RELATED_K8S_OBJECTS = "/apis/v1/public/assets/related-resources/k8s-objects/query"
        const val URL_NOTIIFY_ACNI_TO_INJECT_VOLUMES =
            "https://acni-resource-bonder.alibaba-inc.com/freeapis/v1/acni/sync-effective-scopes"
        const val SYS_CALLED = "acni-home"
    }
}