package com.alibaba.koastline.multiclusters.runtime.model

import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadRunningStatus
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeWorkloadStatus


data class RuntimeWorkloadAndRouteUnRegistryDto(
    val appName: String,
    val runtimeKey: String,
    val operator: String,
    val runtimeWorkloadUnRegistryReqDtoList: List<RuntimeWorkloadUnRegistryReqDto>
)

data class RuntimeWorkloadUnRegistryReqDto (
    val site: String,
    val stage: String,
    val unit: String,
    val clusterId: String,
    val status: RuntimeWorkloadStatus,
    val runningStatus: RuntimeWorkloadRunningStatus
)


