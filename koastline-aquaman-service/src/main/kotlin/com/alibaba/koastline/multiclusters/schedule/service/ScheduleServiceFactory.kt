package com.alibaba.koastline.multiclusters.schedule.service

import com.alibaba.koastline.multiclusters.common.exceptions.ScheduleServiceNotFoundException
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleServiceEnum
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.ScheduleFacade
import org.springframework.stereotype.Component

@Component
class ScheduleServiceFactory {

    internal fun registryScheduleService(scheduleServiceEnum: ScheduleServiceEnum, scheduleService: ScheduleFacade){
        scheduleFacades[scheduleServiceEnum.name] = scheduleService
    }

    fun getScheduleService(scheduleType: ScheduleType): ScheduleFacade {
        val scheduleService = scheduleFacades[scheduleTypeToServiceMap[scheduleType.toString()]] ?:let {
            throw ScheduleServiceNotFoundException(scheduleType.toString())
        }
        return scheduleService
    }

    fun getScheduleService(scheduleServiceEnum: ScheduleServiceEnum): ScheduleFacade {
        val scheduleService = scheduleFacades[scheduleServiceEnum.name] ?:let {
            throw ScheduleServiceNotFoundException(scheduleServiceEnum.name)
        }
        return scheduleService
    }

    companion object {
        val scheduleFacades = mutableMapOf<String, ScheduleFacade>()
        /**
         * 发布&运维场景 -> 调度服务映射
         */
        val scheduleTypeToServiceMap = mapOf(
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.ORIENTED_SCALE_IN).toString() to ScheduleServiceEnum.RUNNING_STATE_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.RESTART).toString() to ScheduleServiceEnum.RUNNING_STATE_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.DEPLOY).toString() to ScheduleServiceEnum.SERVERLESS_MIX_DEPLOY_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD).toString() to ScheduleServiceEnum.POD_LIST_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.SCALE_OUT).toString() to ScheduleServiceEnum.NON_DECLARATIVE_SCALE_OUT_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.DECLARATIVE, SceneEnum.SCALE_OUT).toString() to ScheduleServiceEnum.DECLARATIVE_SCALE_OUT_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.DECLARATIVE, SceneEnum.NON_ORIENTED_SCALE_IN).toString() to ScheduleServiceEnum.DECLARATIVE_NON_ORIENTED_SCALE_IN_SCHEDULE.name,
            ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.NON_ORIENTED_SCALE_IN).toString() to ScheduleServiceEnum.NON_DECLARATIVE_NON_ORIENTED_SCALE_IN_SCHEDULE.name
        )
    }
}