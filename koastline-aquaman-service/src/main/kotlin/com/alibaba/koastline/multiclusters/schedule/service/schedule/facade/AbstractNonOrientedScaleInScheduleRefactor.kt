package com.alibaba.koastline.multiclusters.schedule.service.schedule.facade

import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.SERVER_SERVERLESS_SURGE
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.ResourceStrategy
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyReqDto
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyResult
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraintAssemble
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.NameSpaceSplitter
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.ResultGroup
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.UnionNameSpaceSplitter
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.calculateDecreaseToReplicas
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.getFunnelTreeWithMatchPrefixUniqueKey
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.reconcileFunnelTree
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.visitSubTree
import org.springframework.beans.factory.annotation.Autowired
import kotlin.math.abs

abstract class AbstractNonOrientedScaleInScheduleRefactor : ScheduleFacade {
    val log by logger()

    @Autowired
    lateinit var scheduleStandardService: ScheduleStandardService

    @Autowired
    lateinit var skylineApi: SkylineApi

    @Autowired
    lateinit var scheduleStrategyService: ScheduleStrategyService

    @Autowired
    lateinit var resourcePoolService: ResourcePoolService

    override fun doSchedule(content: ScheduleRequestContent): ScheduleResult {
        preCheck(content)
        checkNotNull(content.scheduleRequestParam)
        // running state workload distribution
        val actualWorkloadDistribution = filterWorkload(
            skylineApi.listWorkloadMetadataConstraintThroughServerList(
                appName = content.resourceScope.appName,
                envStackId = null,
                resourceGroup = content.resourceScope.resourceGroup,
                resourceSNList = emptyList(),
                resourceFilteredTags = listOf(SERVER_SERVERLESS_SURGE)
            ),
            content
        )
        val toDecreaseReplicas = -abs(checkNotNull(content.scheduleRequestParam.replicas) { "missing replicas in scheduleRequestParam" })
        // expect running state workload distribution
        val expectDistribution = getScheduleStrategy(content)
        val workloadFinalResult = approachToExpectDistribution(
            actualWorkloadDistribution = actualWorkloadDistribution,
            expectDistributionStrategy = expectDistribution,
            toDecreaseReplicas = toDecreaseReplicas
        )
        launchCheck(workloadFinalResult, toDecreaseReplicas)
        return ScheduleResult(workloadFinalResult)
    }

    private fun launchCheck(workloadExpectedStates: List<WorkloadExpectedState>, toDecreaseReplicas: Int) {
        check(workloadExpectedStates.sumOf {
            it.params[ScheduleResultParamConstants.SCALE_NUM]?.toInt() ?: 0
        } == toDecreaseReplicas) {
            "SCALE_NUM replicas is not equal to toDecreaseReplicas"
        }
    }

    /**
     * 采用分层次权重方法进行运算 首先计算大范围的权重 层层递归计算
     * 1.构建权策略树
     * 2.利用权重树进行漏斗树构，挂载副本节点在树上
     * 3.从根节点进行递归分组，计算每个分组应该分桶多少操作数
     * 4.收集叶子节点结果，获得最后的Workload变化的结果
     *
     * @param actualWorkloadDistribution
     * @param expectDistributionStrategy
     * @param toDecreaseReplicas
     * @return
     */
    private fun approachToExpectDistribution(
        actualWorkloadDistribution: List<WorkloadMetadataConstraintAssemble>,
        expectDistributionStrategy: ScheduleStrategyResult,
        toDecreaseReplicas: Int //negative int
    ): List<WorkloadExpectedState> {
        // pre-check ops is illegal
        require(toDecreaseReplicas <= 0) { "toDecreaseReplicas must be not positive" }
        val runningReplicasSum = actualWorkloadDistribution.sumOf { it.num }
        checkReplicasEnough(toOperateReplicas = toDecreaseReplicas, actualRunningReplicas = runningReplicasSum)
        expectDistributionStrategy.validate()

        val finalExpectedDistributionMap = reconcileByFunnelTree(
            actualWorkloadDistribution,
            expectDistributionStrategy,
            toDecreaseReplicas
        ).associateBy { it.uniqueKey }

        return actualWorkloadDistribution.filter { workloadAssemble ->
            val rg =
                finalExpectedDistributionMap[workloadAssemble.workloadMetadataConstraint.toMetadataConstraintString()]
            workloadAssemble.num > checkNotNull(rg) { "missing workload assemble" }.replicas
        }.map { workloadAssemble ->
            val expectedReplicas =
                finalExpectedDistributionMap[workloadAssemble.workloadMetadataConstraint.toMetadataConstraintString()]!!.replicas
            WorkloadExpectedState(
                workloadMetadataConstraint = workloadAssemble.workloadMetadataConstraint,
                params = mapOf(ScheduleResultParamConstants.SCALE_NUM to (-(workloadAssemble.num - expectedReplicas)).toString()),
                clusterProfile = resourcePoolService.getClusterProfileNew(clusterId = workloadAssemble.workloadMetadataConstraint.clusterId)
            )
        }
    }

    /**
     * according to ScheduleStrategyResult to check weight to
     *
     * @param actualWorkloadDistribution
     * @param expectDistributionStrategy
     * @param toDecreaseReplicas
     * @return
     */
    fun reconcileByFunnelTree(
        actualWorkloadDistribution: List<WorkloadMetadataConstraintAssemble>,
        expectDistributionStrategy: ScheduleStrategyResult,
        toDecreaseReplicas: Int //negative int
    ): List<ResultGroup> {
        require(toDecreaseReplicas <= 0) { "toDecreaseReplicas must be not positive in scale in state" }
        val strategyTree = expectDistributionStrategy.getStrategyTree()
        val originalMap =
            actualWorkloadDistribution.associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val transformMap = transformUkMapping(originalMap, expectDistributionStrategy)
        val groups = transformMap.map { (transKey, originalKey) ->
            ResultGroup(uniqueKey = transKey, replicas = checkNotNull(originalMap[originalKey]).num)
        }
        val funnelTree = getFunnelTreeWithMatchPrefixUniqueKey(groups = groups, strategyTree = strategyTree)
        reconcileFunnelTree(
            root = funnelTree,
            operateReplicas = toDecreaseReplicas
        ) { toCalculateGroups, operateReplicas ->
            calculateDecreaseToReplicas(toCalculateGroups = toCalculateGroups, operateReplicas = operateReplicas)
        }
        val leafNodesWithElem = mutableListOf<ResultGroup>()
        funnelTree.visitSubTree { node ->
            if (node.isLeafNodeWithElem()) {
                leafNodesWithElem.add(
                    ResultGroup(
                        uniqueKey = transformMap[node.uniqueKey]!!,
                        replicas = node.afterTotal
                    )
                )
            }
        }
        return leafNodesWithElem
    }

    private fun transformUkMapping(
        workloadMap: Map<String, WorkloadMetadataConstraintAssemble>,
        expectDistributionStrategy: ScheduleStrategyResult
    ): Map<String, String> {
        val mapping = mutableMapOf<String, String>()
        workloadMap.forEach { (originalUniqueKey, workload) ->
            mapping[buildUniqueKey(workload, expectDistributionStrategy)] = originalUniqueKey
        }
        return mapping
    }

    fun buildUniqueKey(
        workload: WorkloadMetadataConstraintAssemble,
        expectDistributionStrategy: ScheduleStrategyResult
    ): String {
        val threeTuplesKey = workload.workloadMetadataConstraint.toThreeTuplesMetadataConstraintString()
        val apREStrategies = expectDistributionStrategy.apres
        val matchApREStrategy = apREStrategies.firstOrNull { it.uniqueKey() == threeTuplesKey } ?: let {
            return workload.workloadMetadataConstraint.toWorkloadCoordinateMetaDataConstraintString()
        }

        val resourceStrategies = matchApREStrategy.resources
        val matchResourceStrategy =
            resourceStrategies.firstOrNull { resourceStrategy ->
                val coverClusters = resourceStrategy.clusters.map { it.clusterId }
                coverClusters.contains(workload.workloadMetadataConstraint.clusterId)
            } ?: return workload.workloadMetadataConstraint.toWorkloadCoordinateMetaDataConstraintString()

        return buildPrefix(
            threeTuplesKey,
            matchResourceStrategy
        ) + NameSpaceSplitter + buildSuffix(workload.workloadMetadataConstraint)
    }

    private fun buildPrefix(threeTuplesKey: String, matchResourceStrategy: ResourceStrategy): String {
        return threeTuplesKey + NameSpaceSplitter + matchResourceStrategy.getNodeUniqueKey()
    }

    private fun buildSuffix(workloadMetadataConstraint: WorkloadMetadataConstraint): String {
        return if (workloadMetadataConstraint.runtimeId == null) {
            UnionNameSpaceSplitter + workloadMetadataConstraint.subgroup + UnionNameSpaceSplitter + workloadMetadataConstraint.clusterId + UnionNameSpaceSplitter
        } else {
            UnionNameSpaceSplitter + workloadMetadataConstraint.subgroup + UnionNameSpaceSplitter + workloadMetadataConstraint.runtimeId + UnionNameSpaceSplitter +
                    workloadMetadataConstraint.clusterId + UnionNameSpaceSplitter +
                    workloadMetadataConstraint.workloadName + UnionNameSpaceSplitter
        }
    }

    private fun checkReplicasEnough(toOperateReplicas: Int, actualRunningReplicas: Int) {
        if ((actualRunningReplicas + toOperateReplicas) < 0) {
            throw ScheduleException(
                "缩容->待缩容副本数[$toOperateReplicas]大于当前运行资源总数[$actualRunningReplicas]",
                ScheduleException.SCHEDULE_SCALE_IN_REPLICAS_MORE_THAN_RUNNING_NUM
            )
        }
    }

    private fun filterWorkload(
        workloadMetadataConstraintAssembleList: List<WorkloadMetadataConstraintAssemble>,
        content: ScheduleRequestContent
    ): List<WorkloadMetadataConstraintAssemble> {
        val scheduleRequestParam = checkNotNull(content.scheduleRequestParam)
        workloadMetadataConstraintAssembleList.filter {
            if (scheduleRequestParam.serverless) {
                !it.workloadMetadataConstraint.runtimeId.isNullOrBlank()
            } else {
                it.workloadMetadataConstraint.runtimeId.isNullOrBlank()
            }
        }.run {
            if (SchedulePatternEnum.NON_DECLARATIVE == content.scheduleType.schedulePattern) {
                val declaration = content.declarationData!!.declaration!!
                return this.filter {
                    it.workloadMetadataConstraint.site == declaration.site && it.workloadMetadataConstraint.unit == declaration.unit && it.workloadMetadataConstraint.stage == declaration.stage
                }
            }
            return this
        }
    }

    private fun preCheck(content: ScheduleRequestContent) {
        val scheduleRequestParam = checkNotNull(content.scheduleRequestParam) { "missing scheduleRequestParam" }
        if (scheduleRequestParam.replicas!! >= 0) {
            throw ScheduleException(
                "replicas[${content.scheduleRequestParam.replicas!!}] is invalid, replicas must be less than zero.",
                ScheduleException.INVALID_PARAM
            )
        }
    }

    private fun getScheduleStrategy(content: ScheduleRequestContent): ScheduleStrategyResult {
        return scheduleStrategyService.computeScheduleStrategy(buildScheduleStrategyReqDto(content))
    }

    private fun buildScheduleStrategyReqDto(content: ScheduleRequestContent): ScheduleStrategyReqDto {
        val resourceScope = content.resourceScope
        val scheduleRequestParam =
            checkNotNull(content.scheduleRequestParam) { "missing scheduleRequestParam in ScheduleRequestContent" }
        return ScheduleStrategyReqDto(
            appName = resourceScope.appName,
            resourceGroup = checkNotNull(resourceScope.resourceGroup) { "missing resource group" },
            scheduleStrategyEnvType = convertFromScheduleEnvType(scheduleRequestParam.scheduleEnvType),
            serverlessBaseAppName = scheduleRequestParam.serverlessBaseAppName,
            declarative = isDeclarative(),
            declaration = if (!isDeclarative()) checkNotNull(content.declarationData?.declaration) {
                "missing declarationData"
            } else null,
        )
    }

    private fun convertFromScheduleEnvType(scheduleEnvType: ScheduleEnvType): ScheduleStrategyEnvType {
        return when (scheduleEnvType) {
            ScheduleEnvType.ASI -> ASI
            ScheduleEnvType.SERVERLESS_APP -> SERVERLESS_APP
            else -> throw IllegalArgumentException("unsupported other scheduleEnvType but [ASI / SERVERLESS_APP] ")
        }
    }

    abstract fun isDeclarative(): Boolean

    abstract fun getApREDeed(content: ScheduleRequestContent): ApREDeedDO

    companion object {
        const val METADATA_DELIMITER: String = "$$"
    }
}

