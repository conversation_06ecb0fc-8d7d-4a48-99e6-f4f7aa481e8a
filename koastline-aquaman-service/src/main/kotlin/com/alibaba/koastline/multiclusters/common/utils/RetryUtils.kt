package com.alibaba.koastline.multiclusters.common.utils

import dev.failsafe.Failsafe
import dev.failsafe.RetryPolicy
import dev.failsafe.function.CheckedSupplier
import java.time.Duration

/**
 * @Date: 2025/4/11 10:25
 * @Author: longyin
 */
object RetryUtils {
    const val DEFAULT_RETRY_TIME = 3

    val DEFAULT_RETRY_DELAY: Duration = Duration.ofMillis(100)

    fun <T> retryIfFail(supplier: CheckedSupplier<T>, predicate: (T?, Throwable?) -> Boolean): T {
        val retryPolicy = RetryPolicy.builder<T?>()
            .handleIf { result, throwable -> predicate(result, throwable) }
            .withDelay(DEFAULT_RETRY_DELAY)
            .withMaxRetries(DEFAULT_RETRY_TIME)
            .build()
        return Failsafe.with(retryPolicy).get(supplier)
    }

}
