package com.alibaba.koastline.multiclusters.resourcescope

import com.alibaba.koastline.multiclusters.data.dao.resourcescope.EnvHostWorkloadMetaRepo
import com.alibaba.koastline.multiclusters.data.vo.resourcescope.EnvHostWorkloadMeta
import com.alibaba.koastline.multiclusters.resourcescope.model.AppGroupScopeRestriction
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeDO
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostWorkloadMetaCreateDto
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostWorkloadMetaDO
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author:    <EMAIL>
 * @date:    2025/3/10 11:18 AM
 */
@Service
class EnvHostWorkloadMetaService {
    @Autowired
    lateinit var envHostWorkloadMetaRepo: EnvHostWorkloadMetaRepo
    fun create(envHostWorkloadMetaCreateDto: EnvHostWorkloadMetaCreateDto) {
        envHostWorkloadMetaRepo.insert(
            EnvHostWorkloadMeta(
                envStackId = envHostWorkloadMetaCreateDto.envStackId,
                appName = envHostWorkloadMetaCreateDto.appName,
                resourceGroup = envHostWorkloadMetaCreateDto.resourceGroup,
                unit = envHostWorkloadMetaCreateDto.unit,
                site = envHostWorkloadMetaCreateDto.site,
                stage = envHostWorkloadMetaCreateDto.stage,
                clusterId = envHostWorkloadMetaCreateDto.clusterId,
                creator = envHostWorkloadMetaCreateDto.creator,
                modifier = envHostWorkloadMetaCreateDto.creator
            )
        )
    }

    fun listByEnvStackId(envStackId: String): List<EnvHostWorkloadMetaDO> {
        return envHostWorkloadMetaRepo.listByEnvStackId(envStackId).map {
            EnvHostWorkloadMetaDO(
                id = it.id!!,
                envStackId = it.envStackId,
                appName = it.appName,
                resourceGroup = it.resourceGroup,
                unit = it.unit,
                site = it.site,
                stage = it.stage,
                clusterId = it.clusterId,
                creator = it.creator,
                modifier = it.modifier,
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                isDeleted = it.isDeleted,
            )
        }
    }

    fun deleteByEnvStackId(envStackId: String){
        envHostWorkloadMetaRepo.deleteByEnvStackId(envStackId)
    }

    fun match(workloadExpectedState: WorkloadExpectedState, envHostWorkloadMetaList: List<EnvHostWorkloadMetaDO>): Boolean {
        envHostWorkloadMetaList.firstOrNull {
            it.toSixMetaWorkloadString() == workloadExpectedState.workloadMetadataConstraint.toSixMetaWorkloadString()
        }?.let { return true }
        return false
    }

    fun match(workloadExpectedState: WorkloadExpectedState, envHostResourceScope: EnvHostResourceScopeDO): Boolean {
        envHostResourceScope.resourceScope.appGroupScopes.firstOrNull {
            it.appGroupName == workloadExpectedState.workloadMetadataConstraint.resourceGroup
                    && match(workloadExpectedState.workloadMetadataConstraint, it.restrictions)
        }?.let { return true }
        return false
    }
    private fun match(workloadMetadataConstraint: WorkloadMetadataConstraint, restrictions: List<AppGroupScopeRestriction>): Boolean {
        if (restrictions.isEmpty()) {
            return true
        }
        return restrictions.firstOrNull {
            it.unit == workloadMetadataConstraint.unit
                    && (it.site == null || it.site == workloadMetadataConstraint.site)
        } != null
    }
}
