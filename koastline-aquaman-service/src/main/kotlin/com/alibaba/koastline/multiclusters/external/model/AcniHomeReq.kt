package com.alibaba.koastline.multiclusters.external.model

/**
 * @author: fudai.yf
 * @since: 2023/3/7
 */
data class AcniAssetRelatedK8sObjectsQuery(

    /**
     * 资产的生效范围
     */
    val scopes: List<AcniAssetEffectiveScope>,

    /**
     * 集群 ID
     */
    val clusterId: String,

    /**
     * 命名空间
     */
    val namespace: String,

    /**
     * K8s 资源的编码
     */
    val encoding: AcniK8sObjectEncoding

)

abstract class AcniAssetEffectiveScope(

    /**
     * 资产生效范围的类别
     */
    val type: AcniAssetEffectiveScopeType

)

data class AcniAoneAppEnvironment(

    /**
     * 应用名
     */
    val appName: String,

    /**
     * 应用环境的 Stack Id
     */
    val stackId: String

): AcniAssetEffectiveScope(AcniAssetEffectiveScopeType.AONE_APP_ENVIRONMENT) {

    companion object {
        fun from(metadata: AcniWorkloadMetadata) = AcniAoneAppEnvironment(
            appName = metadata.appName,
            stackId = metadata.envStackId
        )
    }
}

data class AcniNormandyAppGroup(

    /**
     * 应用名
     */
    val appName: String,

    /**
     * 应用分组名
     */
    val groupName: String

): AcniAssetEffectiveScope(AcniAssetEffectiveScopeType.NORMANDY_APP_GROUP) {

    companion object {
        fun from(metadata: AcniWorkloadMetadata) = AcniNormandyAppGroup(
            appName = metadata.appName,
            groupName = metadata.resourceGroup
        )
    }
}

data class AcniAppWorkloadScope(

    val appName: String,

    val resourceGroup: String,

    val site: String,

    val unit: String,

    val stage: String,

    val clusterId: String,

    val namespace: String,

    val envStackId: String,

    val subgroup: String?,

    val runtimeId: String?,

): AcniAssetEffectiveScope(AcniAssetEffectiveScopeType.APP_WORKLOAD) {

    companion object {
        fun from(metadata: AcniWorkloadMetadata) = AcniAppWorkloadScope(
            appName = metadata.appName,
            resourceGroup = metadata.resourceGroup,
            site = metadata.site,
            unit = metadata.unit,
            stage = metadata.stage,
            clusterId = metadata.clusterId,
            namespace = metadata.namespace,
            envStackId = metadata.envStackId,
            subgroup = metadata.subgroup,
            runtimeId = metadata.runtimeId
        )
    }
}


enum class AcniAssetEffectiveScopeType {
   /**
     * 应用工作负载
    */
    APP_WORKLOAD,

    /**
     * Aone 应用环境
     */
    AONE_APP_ENVIRONMENT,

    /**
     * Normandy 应用分组
     */
    NORMANDY_APP_GROUP;

}

enum class AcniK8sObjectEncoding {

    /**
     * Json 编码
     */
    JSON,

    /**
     * Yaml 编码
     */
    YAML
}

data class AcniWorkloadMetadata(

    /**
     * 应用名
     */
    val appName: String,

    /**
     * 分组名
     */
    val resourceGroup: String,

    /**
     * 站点
     */
    val site: String,

    /**
     * 单元
     */
    val unit: String,

    /**
     * 用途
     */
    val stage: String,

    /**
     * 集群ID
     */
    val clusterId: String,

    /**
     * 命名空间
     */
    val namespace: String,

    /**
     * 环境StackId
     */
    val envStackId: String,

    /**
     * 逻辑子组
     */
    val subgroup: String? = "default",

    /**
     * RuntimeID
     */
    val runtimeId: String? = null

)

data class SyncEffectiveScopesRequest(
    val appName: String,
    val operator: String = "aquaman",
    val dryRun: Boolean = false,
    val async: Boolean = true
)