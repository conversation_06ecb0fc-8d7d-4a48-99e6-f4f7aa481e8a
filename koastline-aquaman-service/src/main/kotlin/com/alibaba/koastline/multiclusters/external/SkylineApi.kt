package com.alibaba.koastline.multiclusters.external

import com.alibaba.ais.skyline.client.http.SkylineHttpClient
import com.alibaba.ais.skyline.common.service.Result
import com.alibaba.ais.skyline.domain.search.QueryRequest
import com.alibaba.ais.skyline.domain.search.response.ItemQueryResponse
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.exceptions.SkylineBizException
import com.alibaba.koastline.multiclusters.common.exceptions.SkylineException
import com.alibaba.koastline.multiclusters.common.logger
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.RetryUtils.retryIfFail
import com.alibaba.koastline.multiclusters.common.utils.toNullIfBlank
import com.alibaba.koastline.multiclusters.external.annotation.ExternalCall
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.external.model.Server
import com.alibaba.koastline.multiclusters.schedule.model.AppResource
import com.alibaba.koastline.multiclusters.schedule.model.AppResourceStatus
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraintAssemble
import com.fasterxml.jackson.core.type.TypeReference
import kotlinx.coroutines.*
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class SkylineApi : InitializingBean {
    @Value("\${skyline.host}")
    lateinit var skylineHost: String
    @Value("\${skyline.account}")
    lateinit var skylineAccount: String
    @Value("\${skyline.access.key}")
    lateinit var skylineAccessKey: String

    /**
     * AOP无法拦截方法内部调用，注入自身bean调用内部方法，以实现AOP拦截
     */
    @Autowired
    lateinit var skylineApi: SkylineApi
    val log by logger()
    var skylineHttpClient: SkylineHttpClient? = null

    override fun afterPropertiesSet() {
        skylineHttpClient = SkylineHttpClient().apply {
            this.host = skylineHost
            this.account = skylineAccount
            this.accessKey = skylineAccessKey
            this.appName = APP_NAME
            this.afterPropertiesSet()
        }
    }

    @ExternalCall(SYS_CALLED)
    fun getEnvStackEnvLevelByStackId(envStackId: String): String {
        val queryRequest = QueryRequest().apply {
            this.select = "environment.environment_type as $FIELD_ENV_LEVEL"
            this.from = "environment"
            this.condition = "environment.sn='$envStackId'"
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        if (rs.value.itemList.isNullOrEmpty()) {
            throw SkylineException("stack[$envStackId] is not found.")
        }
        return parseFieldValueFromItem(rs.value.itemList[0], FIELD_ENV_LEVEL)!!
    }

    @ExternalCall(SYS_CALLED)
    fun getEnvStackResourceGroupScopeByStackId(envStackId: String): List<String> {
        val rs = skylineHttpClient!!.itemSearch().query(QueryRequest().apply {
            this.select = "environment.groups_scope as $FIELD_GROUP_SCOPE"
            this.from = "environment"
            this.condition = "environment.sn='$envStackId'"
        })
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        if (rs.value.itemList.isNullOrEmpty()) {
            throw SkylineException("stack[$envStackId] is not found.")
        }
        if (rs.value.itemList.isNullOrEmpty()) {
            return emptyList()
        }
        return rs.value.itemList[0][FIELD_GROUP_SCOPE] ?.run {
            (this as JSONArray).toList().map { item ->
                item as String
            }
        } ?: emptyList()
    }

    @ExternalCall(SYS_CALLED)
    fun getAppGroup(appGroupName: String): AppGroup {
       val queryRequest = QueryRequest().apply {
            this.select = "app_group.name as $FIELD_NAME_RESOURCE_GROUP, app_group.usage_type as $FIELD_NAME_RESOURCE_GROUP_USAGE_TYPE, app_group.real_app_name as $FIELD_NAME_REAL_APP_NAME, app.name as $FIELD_NAME_APP_NAME, app_group.safety_out as $FIELD_NAME_SAFETY_OUT, $FIELD_NAME_TAGS"
            this.from = "app_group"
            this.condition = "app_group.name='$appGroupName'"
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        if (rs.value.itemList.isNullOrEmpty()) {
            throw SkylineBizException("appGroup[$appGroupName] is not found.")
        }
        rs.value.itemList[0].let {
            return AppGroup(
                name = parseFieldValueFromItem(it, FIELD_NAME_RESOURCE_GROUP)!!,
                usageType = parseFieldValueFromItem(it, FIELD_NAME_RESOURCE_GROUP_USAGE_TYPE)!!,
                appName = parseFieldValueFromItem(it, FIELD_NAME_REAL_APP_NAME)!!,
                originalName = parseFieldValueFromItem(it, FIELD_NAME_APP_NAME)!!,
                safetyOut = it[FIELD_NAME_SAFETY_OUT] as Int,
                tags = it[FIELD_NAME_TAGS] ?.run { this as List<String> } ?: emptyList()
            )
        }
    }

    /**
     * 查询应用以及旗下的所有的应用分组,可以加上过滤条件进行过滤
     *
     * @param appName
     * @param includeType
     * @return
     */
    @ExternalCall(SYS_CALLED)
    fun getAppGroupsByAppName(appName: String, includeType: List<String>? = null): List<AppGroup> {
        val queryRequest = QueryRequest().apply {
            this.select =
                "app_group.name as name, app_group.$FIELD_NAME_RESOURCE_GROUP_USAGE_TYPE as $FIELD_NAME_RESOURCE_GROUP_USAGE_TYPE, app_group.$FIELD_NAME_REAL_APP_NAME as $FIELD_NAME_APP_NAME"
            this.from = "app_group"
            this.condition = buildAppGroupConditionStatement(appName = appName, includeTypeList = includeType)
        }
        return queryItemList(queryRequest, object : TypeReference<List<AppGroup>>() {})
    }

    /**
     * 根据圈定范围生成过滤condition
     *
     * @param appName
     * @param includeTypeList
     * @return
     */
    private fun buildAppGroupConditionStatement(appName: String, includeTypeList: List<String>? = null): String {
        val baseCondition = "app_group.$FIELD_NAME_REAL_APP_NAME='$appName'"
        return if (includeTypeList.isNullOrEmpty()) {
            baseCondition
        } else {
            val includeUsageType = includeTypeList.distinct().joinToString(",") { "'$it'" }
            val extraCondition = " AND app_group.usage_type IN $includeUsageType"
            baseCondition + extraCondition
        }
    }

    @ExternalCall(SYS_CALLED)
    fun listClusterIdByResourceScopeAndDeclaration(envStackId: String?, resourceGroup: String?, declaration: Declaration): List<String> {
        val queryRequest = QueryRequest().apply {
            this.select = "server.pod_cluster_id as $FIELD_NAME_CLUSTER_ID"
            this.from = "server"
            this.condition = buildConditionWithDeclaration(envStackId, resourceGroup, null, declaration)
            this.groupBy = "server.pod_cluster_id"
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        if (rs.value.totalCount == 0) {
            return emptyList()
        }
        return rs.value.itemList.map { item ->
            parseFieldValueFromItem(item, FIELD_NAME_CLUSTER_ID)!!
        }
    }

    @ExternalCall(SYS_CALLED)
    fun listResourceGroupConfigByEnvStackId(envStackId: String) :List<String> {
        val queryRequest = QueryRequest().apply {
            this.select = "groups_scope"
            this.from = "environment"
            this.condition = "sn = '$envStackId'"
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        rs.value.itemList ?.let {
            if (it.isNullOrEmpty()) {
                return emptyList()
            }
            it[0]["groups_scope"] ?.let { resourceGroupList ->
                return resourceGroupList as List<String>
            }
        }
        return emptyList()
    }

    /**
     * 根据应用和单元查询资源
     */
    @ExternalCall(SYS_CALLED)
    fun listServerByAppAndRuntimeWorkloadId(appName: String, runtimeWorkloadIdList: List<String>) : List<Server>{
        return queryServerByCondition2(
            runtimeWorkloadIdList = runtimeWorkloadIdList,
            needTotal = false,
            page = 1,
            num = QUERY_SERVER_LIMIT_NUM
        ).run {
            convertItemQueryResponseToServerList(appName, this)
        }
    }

    private fun buildCondition(envStackId: String?, resourceGroup: String?, resourceSNList: List<String>?): String {
        var condition = ""
        envStackId ?.toNullIfBlank() ?.let {
            condition = "$condition and environment.sn='$envStackId'"
        }
        resourceGroup ?.toNullIfBlank() ?.let {
            condition = "$condition and app_group.name = '$resourceGroup'"
        }
        resourceSNList ?.let {
            if (resourceSNList.isNotEmpty()) {
                condition += resourceSNList.joinToString("','", " and server.sn IN '", "'")
            }
        }
        if (condition.isNullOrBlank()) {
            throw SkylineException("conditions[envStackId,resourceGroup,resourceSNList] must not be all null.")
        }
        //增加POD资源类型过滤
        condition = "device_model.uniform_type = 'POD'$condition"
        return condition
    }

    private fun buildConditionWithDeclaration(envStackId: String?, resourceGroup: String?, resourceSNList: List<String>?, declaration: Declaration): String {
        var condition = buildCondition(envStackId, resourceGroup, resourceSNList)
        declaration.az ?.let {
            condition += " and cabinet.logic_region = '$it'"
        }
        declaration.stage ?.let {
            condition += " and server.app_use_type = '$it'"
        }
        declaration.unit ?.let {
            condition += " and server.tags = '$it'"
        }
        return condition
    }

    @ExternalCall(SYS_CALLED)
    fun listWorkloadMetadataConstraintThroughServerList(appName: String, envStackId: String? = null, resourceGroup: String? = null, resourceSNList: List<String>? = emptyList(),
                                                        resourceFilteredTags: List<String>? = null
    ): List<WorkloadMetadataConstraintAssemble> {
        val itemQueryResponse = skylineApi.queryServerByCondition(appName, envStackId, resourceGroup, resourceSNList, true, 1, QUERY_SERVER_LIMIT_NUM)
        val workloadMetadataConstraintAssembleMapList = mutableListOf<Map<String, WorkloadMetadataConstraintAssemble>>()
        var validDiffNum = 0
        val serverList = convertItemQueryResponseToServerList(appName, itemQueryResponse)
        val workloadMetaIncludeNodeSn = !resourceSNList.isNullOrEmpty()
        computeWorkloadMetadataConstraintAssemble(
            serverList = serverList,
            isIncludeSn = workloadMetaIncludeNodeSn,
            resourceFilteredTags = resourceFilteredTags
        ).let { workloadMetadataConstraintAssembleMap ->
            workloadMetadataConstraintAssembleMapList.add(workloadMetadataConstraintAssembleMap)
            validDiffNum = serverList.size - workloadMetadataConstraintAssembleMap.values.sumOf { it.num }
        }
        if (!itemQueryResponse.isHasMore) {
            return ArrayList(workloadMetadataConstraintAssembleMapList[0].values).apply {
                checkWorkloadMetadataConstraintAssembleComputeResult(itemQueryResponse.totalCount - validDiffNum, this)
            }
        }
        // 并发处理
        runBlocking {
            val jobs = mutableListOf<Job>()
            var remainder = itemQueryResponse.totalCount - QUERY_SERVER_LIMIT_NUM
            var page = 2
            while (remainder > 0) {
                val currentPage = page
                val job = launch(Dispatchers.Default) {
                    val serverList = convertItemQueryResponseToServerList(appName,
                        skylineApi.queryServerByCondition(appName, envStackId, resourceGroup, resourceSNList, false, currentPage, QUERY_SERVER_LIMIT_NUM)
                    )
                    computeWorkloadMetadataConstraintAssemble(
                        serverList = serverList,
                        isIncludeSn = workloadMetaIncludeNodeSn,
                        resourceFilteredTags = resourceFilteredTags
                    ).let { workloadMetadataConstraintAssembleMap ->
                        synchronized(workloadMetadataConstraintAssembleMapList) {
                            workloadMetadataConstraintAssembleMapList.add(workloadMetadataConstraintAssembleMap)
                            validDiffNum += serverList.size - workloadMetadataConstraintAssembleMap.values.sumOf { it.num }
                        }
                    }
                }
                jobs.add(job)
                page ++
                remainder -= QUERY_SERVER_LIMIT_NUM
            }
            jobs.joinAll()
        }
        //计算汇总结果
        val aggregatedResult = mutableMapOf<String, WorkloadMetadataConstraintAssemble> ()
        workloadMetadataConstraintAssembleMapList.forEach{ workloadMetadataConstraintAssembleMap ->
            workloadMetadataConstraintAssembleMap.keys.forEach{ key ->
                val workloadMetadataConstraintAssemble = checkNotNull(workloadMetadataConstraintAssembleMap[key])
                aggregatedResult[key] ?.let {
                    aggregatedResult[key] = it.copy(
                        num = it.num + workloadMetadataConstraintAssemble.num,
                        resourceSnList = it.resourceSnList.apply { this.addAll(workloadMetadataConstraintAssemble.resourceSnList) }
                    )
                } ?:let {
                    aggregatedResult[key] = workloadMetadataConstraintAssemble
                }
            }
        }
        return ArrayList(aggregatedResult.values).apply {
            checkWorkloadMetadataConstraintAssembleComputeResult(itemQueryResponse.totalCount - validDiffNum, this)
        }
    }

    /**
     * 验证Workload元数据约束计算结果，扩缩容临界场景下会出现计算差异情况
     * 早期开启做错误拦截，后期可降级
     */
    private fun checkWorkloadMetadataConstraintAssembleComputeResult(total: Int, workloadMetadataConstraintAssembleList: List<WorkloadMetadataConstraintAssemble>) {
        val computeTotal = workloadMetadataConstraintAssembleList.sumOf {
            it.num
        }
        if (computeTotal < total) {
            throw BizException("Workload元数据约束计算结果错误，预期：${total},实际:${computeTotal},明细:${workloadMetadataConstraintAssembleList}")
        }
    }

    fun convertItemQueryResponseToServerList(appName: String, itemQueryResponse: ItemQueryResponse): List<Server> {
        val servers = mutableListOf<Server>()
        itemQueryResponse.itemList ?.forEach { item ->
            parseServerFromResult(appName, item) ?.let { server ->
                servers.add(server)
            }
        }
        return servers
    }

    /**
     * 打印日志太多，暂时去除监控
     * 待优化：扩展ExternalCall可以指定不打印结果
     */
//    @ExternalCall(SYS_CALLED)
    fun queryServerByCondition(appName: String, envStackId: String?, resourceGroup: String?, resourceSNList: List<String>?, needTotal: Boolean, page: Int, num: Int): ItemQueryResponse {
        val queryRequest = QueryRequest().apply {
            this.select = "ip,sn,app.name as $FIELD_NAME_APP_NAME,app_group.name as $FIELD_NAME_RESOURCE_GROUP,cabinet.logic_region as $FIELD_NAME_SITE," +
                    "server.app_use_type as $FIELD_NAME_STAGE,server.tags as $FIELD_NAME_TAGS,server.pod_cluster_id as $FIELD_NAME_CLUSTER_ID," +
                    "server.logic_sub_group as $FIELD_NAME_RESOURCE_SUB_GROUP,server.pod_namespace as $FIELD_NAME_NAMESPACE,server.pod_cluster_pool_id as $FIELD_NAME_CLUSTER_POOL_ID," +
                    "app_workload_name as $FIELD_NAME_WORKLOAD_NAME"
            this.from = "server"
            this.condition = buildCondition(envStackId, resourceGroup, resourceSNList)
            this.page = page
            this.num = num
            this.isNeedTotal = needTotal
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        log.info("queryServerByCondition,request:{appName:${appName},page:${page},num:${num}},rs num:${rs.value.itemList?.run { this.size }?:0}")
        return rs.value
    }

    fun queryServerByCondition2(runtimeWorkloadIdList: List<String>, needTotal: Boolean, page: Int, num: Int): ItemQueryResponse {
        val queryRequest = QueryRequest().apply {
            this.select = "ip,sn,app.name as $FIELD_NAME_APP_NAME,app_group.name as $FIELD_NAME_RESOURCE_GROUP,cabinet.logic_region as $FIELD_NAME_SITE," +
                    "server.app_use_type as $FIELD_NAME_STAGE,server.tags as $FIELD_NAME_TAGS,server.pod_cluster_id as $FIELD_NAME_CLUSTER_ID," +
                    "server.logic_sub_group as $FIELD_NAME_RESOURCE_SUB_GROUP,server.pod_namespace as $FIELD_NAME_NAMESPACE,server.pod_cluster_pool_id as $FIELD_NAME_CLUSTER_POOL_ID"
            this.from = "server"
            this.condition = "pod_cluster_pool_id in ${runtimeWorkloadIdList.joinToString("','","'","'")}"
            this.page = page
            this.num = num
            this.isNeedTotal = needTotal
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        log.info("queryServerByCondition2,request:{pod_cluster_pool_id:${runtimeWorkloadIdList},page:${page},num:${num}},rs num:${rs.value.itemList?.run { this.size }?:0}")
        return rs.value
    }

    private fun skylineItemQueryWithRetry(queryRequest: QueryRequest): Result<ItemQueryResponse> {
        return retryIfFail({ skylineHttpClient!!.itemSearch().query(queryRequest) }) { result, _ ->
            result?.isSuccess == false && result?.errorMessage?.contains(TIMEOUT_MSG) == true
        }
    }

    /**
     * 获取基座App资源分布
     */
    @ExternalCall(SYS_CALLED)
    fun listAppOfRuntimeBase(runtimeWorkloadIds: List<String>): List<AppResource> {
        if (runtimeWorkloadIds.isEmpty()) {
            return emptyList()
        }
        val queryRequest = QueryRequest().apply {
            this.select = "app.name as ${FIELD_NAME_APP_NAME},app_group.real_app_name as ${FIELD_NAME_REAL_APP_NAME},server.app_server_state as ${FIELD_NAME_APP_SERVER_STATE}, count(server.app_server_state) as ${FIELD_NAME_CNT}"
            this.from = "server"
            this.condition = "pod_cluster_pool_id in ${runtimeWorkloadIds.joinToString("','","'","'")}"
            this.page = ITEM_SEARCH_FIRST_PAGE
            this.num = ITEM_SEARCH_PAGE_SIZE
            this.isNeedTotal = true
            this.groupBy = "app.name,app_group.real_app_name,server.app_server_state"
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        val appResourceMap = mutableMapOf<String, AppResource>()
        rs.value.itemList?.forEach { item ->
            val realAppName = item[FIELD_NAME_REAL_APP_NAME] ?.run { this as String } ?:null
            val appName = (item[FIELD_NAME_APP_NAME] as String).run {
                if (this == TESTING_ENV_COMMON_APP_NAME)
                    if (realAppName.isNullOrEmpty()) this else realAppName
                else
                    this
            }
            val resourceStatus = item[FIELD_NAME_APP_SERVER_STATE] as String
            val num = item[FIELD_NAME_CNT] as Int
            appResourceMap[appName] ?.let { appResource ->
                appResourceMap[appName] = appResource.copy(
                    totalResourceNum = appResource.totalResourceNum + num,
                    appResourceStatusList = appResource.appResourceStatusList.toMutableList().apply {
                        this.add(AppResourceStatus(status = resourceStatus, num = num))
                    }
                )
            } ?:let {
                appResourceMap[appName] = AppResource(
                    appName = appName,
                    totalResourceNum = num,
                    appResourceStatusList = listOf(
                        AppResourceStatus(status = resourceStatus, num = num)
                    )
                )
            }
        }
        return appResourceMap.values.toList()
    }

    @ExternalCall(SYS_CALLED)
    fun listBindingEnvStackIdByResourceGroup(resourceGroup: String): List<String> {
        val appGroup = getAppGroup(resourceGroup)
        val queryRequest = QueryRequest().apply {
            this.select = "environment.sn as $FIELD_NAME_SN,environment.groups_scope as $FIELD_GROUP_SCOPE"
            this.from = "environment"
            this.condition = "app.name='${appGroup.appName}' and environment.groups_scope = '${resourceGroup}'"
            this.page = ITEM_SEARCH_FIRST_PAGE
            this.num = ITEM_SEARCH_PAGE_SIZE
            this.isNeedTotal = true
        }
        val rs = skylineItemQueryWithRetry(queryRequest)
        if (!rs.isSuccess) {
            throw SkylineException(rs.errorCodeMsg)
        }
        val envStackIds = mutableListOf<String>()
        rs.value.itemList.filter { item ->
            item[FIELD_GROUP_SCOPE] ?.run {
                (this as List<String>).contains(resourceGroup)
            } ?:false
        }.forEach {item ->
            envStackIds.add(item[FIELD_NAME_SN] as String)
        }
        return envStackIds
    }

    @ExternalCall(SYS_CALLED)
    fun filterIpsByServerProperties(ips: List<String>, stage: MetadataStageEnum, unitList: List<String>): List<String> {
        if (ips.isEmpty()) {
            return emptyList()
        }
        val speIps = mutableListOf<String>()
        var tempIps = ips;
        while(tempIps.isNotEmpty()) {
            val queryRequest = QueryRequest().apply {
                this.select = "ip,app_use_type as $FIELD_NAME_STAGE,tags as $FIELD_NAME_TAGS"
                this.from = "server"
                this.condition = "app_use_type = '${stage.name}' AND ip in ${tempIps.take(ITEM_SEARCH_PAGE_SIZE).joinToString("','","'","'")}"
                this.num = ITEM_SEARCH_PAGE_SIZE
            }
            val rs = skylineItemQueryWithRetry(queryRequest)
            if (!rs.isSuccess) {
                throw SkylineException(rs.errorCodeMsg)
            }
            speIps.addAll(
                rs.value.itemList?.filter {item ->
                    parseUnitFromItem(item)?.run {
                        unitList.isEmpty() || this in unitList
                    } ?:false && item[FIELD_NAME_STAGE] == stage.name
                }?.map { item ->
                    item[FIELD_NAME_IP] as String
                } ?:emptyList()
            )
            tempIps = tempIps.drop(ITEM_SEARCH_PAGE_SIZE)
        }
        return speIps
    }

    /**
     * 计算每一个workload列表旗下的所有元素统计
     * 当workload存在
     * @param serverList
     * @param isIncludeSn
     * @return
     */
    private fun computeWorkloadMetadataConstraintAssemble(serverList: List<Server>, isIncludeSn: Boolean, resourceFilteredTags: List<String>? = null): Map<String, WorkloadMetadataConstraintAssemble> {
        val workloadMetadataConstraintAssembleMap = mutableMapOf<String, WorkloadMetadataConstraintAssemble> ()
        serverList.filter { server ->
            !server.tags.contains(SERVER_SERVERLESS_BACKUP) && resourceFilteredTags ?.run {
                this.intersect(server.tags.toSet()).isEmpty()
            } ?:true
        }.forEach { server ->
            workloadMetadataConstraintAssembleMap[server.toMetadataConstraintString()]
                ?.let {
                    it.num = it.num + 1
                    if (isIncludeSn) {
                        it.resourceSnList.add(server.sn)
                    }
                }
                ?:let {
                    workloadMetadataConstraintAssembleMap.put(server.toMetadataConstraintString(), WorkloadMetadataConstraintAssemble(
                        WorkloadMetadataConstraint(
                            appName = server.appName,
                            resourceGroup = server.resourceGroup,
                            site = server.site,
                            unit = server.unit,
                            stage = server.stage,
                            subgroup = server.resourceSubGroup,
                            clusterId = server.clusterId,
                            namespace = server.namespace,
                            runtimeId = server.clusterPoolId,
                            workloadName = server.workloadName
                        ),
                        1,
                        if (isIncludeSn) mutableListOf(server.sn) else mutableListOf()
                    ))
                }
        }
        return workloadMetadataConstraintAssembleMap
    }

    /**
     * 从skyline逻辑分组里解析资源子组，skyline逻辑分组示例：koastline-aquaman_testing_2405473_testhost#default
     */
    private fun getResourceSubGroup(skylineLogicSubGroup: String?): String {
        if (skylineLogicSubGroup == DEFAULT_SUB_GROUP) {
            return DEFAULT_SUB_GROUP
        }
        return skylineLogicSubGroup ?.toNullIfBlank() ?.run {
            this.split("#")[1]
        } ?: DEFAULT_SUB_GROUP
    }

    private fun parseServerFromResult(appName: String, item: Map<String, Any?>): Server? {
        return Server(
            ip = parseFieldValueFromItem(item, FIELD_NAME_IP),
            sn = parseFieldValueFromItem(item, FIELD_NAME_SN) ?:let { return null },
            appName = appName,
            resourceGroup = parseFieldValueFromItem(item, FIELD_NAME_RESOURCE_GROUP) ?:let { return null },
            site = parseFieldValueFromItem(item, FIELD_NAME_SITE) ?.run { this.lowercase() } ?:let { return null },
            stage = parseFieldValueFromItem(item, FIELD_NAME_STAGE) ?:let { return null },
            unit = parseUnitFromItem(item) ?:let { return null },
            clusterId = parseFieldValueFromItem(item, FIELD_NAME_CLUSTER_ID) ?:let { return null },
            resourceSubGroup = getResourceSubGroup(parseFieldValueFromItem(item, FIELD_NAME_RESOURCE_SUB_GROUP)),
            namespace = parseFieldValueFromItem(item, FIELD_NAME_NAMESPACE) ?:let { return null },
            clusterPoolId = parseFieldValueFromItem(item, FIELD_NAME_CLUSTER_POOL_ID),
            tags = parseTagsFromItem(item),
            workloadName = parseFieldValueFromItem(item, FIELD_NAME_WORKLOAD_NAME)
        )
    }

    /**
     *  从结果项中解析单元
     *  @params item 样例:{"tags":["CENTER_UNIT.center"]}
     *  @return 单元
     */
    private fun parseUnitFromItem(item: Map<String, Any?>): String? {
        item[FIELD_NAME_TAGS] ?.let { tags ->
            (tags as JSONArray).firstOrNull { value ->
                (value as String).startsWith(FIELD_NAME_UNIT_PREFIX)
            } ?.let {
                return (it as String)
            }
        }
        log.error("server's details is defective,unit is null.Info:${JSON.toJSONString(item)}")
        return null
    }

    /**
     *  从结果项中解析标签列表
     *  @params item 样例:{"tags":["CENTER_UNIT.center"]}
     *  @return
     */
    private fun parseTagsFromItem(item: Map<String, Any?>): List<String> {
        item[FIELD_NAME_TAGS] ?.let { tags ->
          return (tags as JSONArray).map { it as String }
        }
        return emptyList()
    }

    private fun parseFieldValueFromItem(item: Map<String, Any?>, fieldName: String): String? {
        item[fieldName] ?.let {
            (it as String).toNullIfBlank() ?.let { value ->
                return value
            }
        }
        if (fieldName != FIELD_NAME_CLUSTER_POOL_ID && fieldName != FIELD_NAME_WORKLOAD_NAME) {
            log.error("server's details is defective,${fieldName} is null.Info:${JSON.toJSONString(item)}")
        }
        return null
    }

    /**
     * 分页进行最大20000条数据查询 查询所有的对象
     *
     * @param T
     * @param queryRequest
     * @param typeReference
     * @return
     */
    private fun <T> queryItemList(queryRequest: QueryRequest, typeReference: TypeReference<List<T>>): List<T> {
        queryRequest.num = ITEM_SEARCH_PAGE_SIZE
        queryRequest.page = ITEM_SEARCH_FIRST_PAGE
        // 分组最大容器数为200000，再大不再允许查询
        val domainItemList: MutableList<T> = ArrayList()
        for (i in 1..ITEM_SEARCH_MAX_PAGE_COUNT) {
            queryRequest.page = i
            val searchResult = skylineItemQueryWithRetry(queryRequest)
            check(searchResult.isSuccess) { "itemSearch search failed, ${searchResult.errorMessage}" }
            val itemList = searchResult.value?.itemList
            if (itemList.isNullOrEmpty()) {
                break
            }
            domainItemList.addAll(JsonUtils.readListValue(JsonUtils.writeValueAsString(itemList), typeReference))
            if (itemList.size < ITEM_SEARCH_PAGE_SIZE) {
                break
            }
        }
        return domainItemList
    }

    companion object{
        const val SYS_CALLED = "skyline"
        const val APP_NAME = "koastline-aquaman"

        const val FIELD_NAME_IP = "ip"
        const val FIELD_NAME_SN = "sn"
        const val FIELD_NAME_APP_NAME = "app_name"
        const val FIELD_NAME_REAL_APP_NAME = "real_app_name"
        const val FIELD_NAME_RESOURCE_GROUP = "resource_group"
        const val FIELD_NAME_SAFETY_OUT = "safety_out"
        const val FIELD_NAME_RESOURCE_SUB_GROUP = "resource_sub_group"
        const val FIELD_NAME_SITE = "site"
        const val FIELD_NAME_STAGE = "stage"
        const val FIELD_NAME_TAGS = "tags"
        const val FIELD_NAME_CLUSTER_ID = "cluster_id"
        const val FIELD_NAME_NAMESPACE = "namespace"
        const val FIELD_NAME_CLUSTER_POOL_ID = "cluster_pool_id"
        const val FIELD_NAME_WORKLOAD_NAME = "workload_name"
        const val FIELD_ENV_LEVEL = "env_level"
        const val FIELD_GROUP_SCOPE = "group_scope"
        const val FIELD_NAME_APP_SERVER_STATE = "app_server_state"
        const val FIELD_NAME_CNT = "cnt"

        const val FIELD_NAME_UNIT_PREFIX = "CENTER_UNIT."
        const val FIELD_NAME_RESOURCE_GROUP_USAGE_TYPE = "usage_type"

        const val TESTING_ENV_COMMON_APP_NAME = "common-app"
        const val DEFAULT_SUB_GROUP = "default"
        const val DEFAULT_UNIT = "CENTER_UNIT.center"

        const val PRODUCT_LINE_STAGE = "production"

        const val SERVER_SERVERLESS_SURGE= "serverless.surge"
        const val SERVER_SERVERLESS_BACKUP = "serverless.backup"

        const val QUERY_SERVER_LIMIT_NUM = 1000

        private const val ITEM_SEARCH_PAGE_SIZE = 1000
        private const val ITEM_SEARCH_MAX_PAGE_COUNT = 20
        private const val ITEM_SEARCH_FIRST_PAGE = 1

        private const val TIMEOUT_MSG = "TimeoutException"

    }
}


fun main(args: Array<String>) {
    val skylineHttpClient = SkylineHttpClient().apply {
        this.host = "pre-sky.alibaba-inc.com"
        this.account = "koastline-aquaman"
        this.accessKey = "QxL6RHXP26o0T3pw"
        this.appName = SkylineApi.APP_NAME
        this.afterPropertiesSet()
    }
    val queryRequest = QueryRequest().apply {
        this.select = "app.name as ${SkylineApi.FIELD_NAME_APP_NAME},server.app_server_state,count(server.app_server_state) as cnt"
        this.from = "server"
        this.condition = "pod_cluster_pool_id in 'cco-serverless-runtime.137328','cco-serverless-runtime.137330'"
        this.page = 1
        this.num = 10
        this.isNeedTotal = true
        this.groupBy = "app.name,server.app_server_state"
    }
    val rs = skylineHttpClient!!.itemSearch().query(queryRequest)
    println(rs)
}