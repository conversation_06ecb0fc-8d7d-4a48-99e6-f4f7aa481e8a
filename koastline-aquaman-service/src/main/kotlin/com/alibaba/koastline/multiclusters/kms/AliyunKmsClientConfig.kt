package com.alibaba.koastline.multiclusters.kms

import com.alibaba.koastline.multiclusters.common.config.CredentialsProperties
import com.alibaba.koastline.multiclusters.common.utils.CryptUtils
import com.aliyun.kms20160120.Client
import com.aliyun.teaopenapi.models.Config
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 */
@Configuration
class AliyunKmsClientConfig {
    @Autowired
    lateinit var credentialsProperties: CredentialsProperties

    @Bean(name = ["kmsClient"])
    fun aliyunKmsClient(): Client {
        val config: Config = Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(getAccessKeySecret())
        config.endpoint = endpoint
        logger.info("create a kms client")
        return Client(config)
    }

    private fun getAccessKeySecret(): String {
        val aesKey = credentialsProperties.credentials["aes-key"]!!
        return CryptUtils.decrypt(aesKey, accessKeySecretEncrypt)
    }

    companion object {
        const val accessKeyId = "LTAI5tQv73FAw8FfhNDC68Ww"
        private const val accessKeySecretEncrypt = "xvqzEpGBc02nAas9ON35tcurVrof8IDqOA1C9hitcnk="
        const val endpoint = "kms.cn-hangzhou.aliyuncs.com"
        private val logger = LoggerFactory.getLogger("DEBUG_LOGGER")!!
    }
}