package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.boot.diamond.listener.DataIdListener
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.taobao.diamond.client.Diamond
import org.slf4j.LoggerFactory
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 */
@Component
class CredentialDiamondConfig(val credentialsProperties: CredentialsProperties): DataIdListener {
    override fun getDiamondUrl(): String {
        return "diamond://koastline-aquaman/DEFAULT_GROUP/koasltine-aquaman-credentials"
    }

    override fun valueChanged(newResource: Resource?) {
        credentialsProperties.credentials = buildRegionProperties()
        logger.info("value changed, new credentials: ${credentialsProperties.credentials}")
    }

    companion object {
        private const val CREDENTIALS_DATA_ID = "koasltine-aquaman-credentials"
        private const val CREDENTIALS_MAPPING_GROUP_ID = "DEFAULT_GROUP"
        private val logger = LoggerFactory.getLogger("DEBUG_LOGGER")!!

        fun buildRegionProperties(): MutableMap<String, String> {
            val config = Diamond.getConfig(CREDENTIALS_DATA_ID, CREDENTIALS_MAPPING_GROUP_ID, 5000)
            val objectMapper = ObjectMapper()
            logger.info("get credential config: $config")
            return objectMapper.readValue(config)
        }
    }
}