package com.alibaba.koastline.multiclusters.schedule.service.schedule.core

/**
 * DistributionStrategy is used to describe what resources are expected, which use unique key to define every node in strategy
 *
 * Those elements belong to this tree.
 * eg:
 *              root
 *            /     \
 *         sub1     sub2
 *        /    \        \
 *  sub1-sub1 sub1-sub2 sub2-sub1
 *
 * all using DistributionStrategy to model should have unique key, node weight and sub nodes
 *
 */
interface DistributionStrategy {
    fun getNodeWeight(): Int
    fun getNodeUniqueKey(): String
    fun getSubDistributionStrategy(): List<DistributionStrategy>

    fun validateUniqueKeys() {
        val historyUniqueKeys = mutableListOf<String>()
        historyUniqueKeys.add(this.getNodeUniqueKey())
        for (subNode in this.getSubDistributionStrategy()) {
            subNode.validateUniqueKeys(historyUniqueKeys, this.getNodeUniqueKey())
        }
    }

    fun validateUniqueKeys(historyUniqueKeys: MutableList<String>, prefix: String) {
        if (historyUniqueKeys.contains(this.getNodeUniqueKey())) {
            throw IllegalStateException("unique key has existed!")
        }

        historyUniqueKeys.add(this.getNodeUniqueKey())
        for (subNode in this.getSubDistributionStrategy()) {
            subNode.validateUniqueKeys(historyUniqueKeys, this.getNodeUniqueKey())
        }
    }
}

/**
 * traverse all node by left depth first
 *
 * @param action
 */
fun DistributionStrategy.visitSubTree(action: (DistributionStrategy) -> Unit) {
    action(this) //solve this node
    for (subNode in this.getSubDistributionStrategy()) {
        subNode.visitSubTree(action)
    }
}

/**
 * prefix unique strategy tree, a implementation of distribution strategy tree
 */

const val RootStrategy = ""

/**
 * levels links by this like site1&unit1&stage1
 */
const val NameSpaceSplitter = "&"
/**
 * union group splitter like |cluster1|cluster2|
 */
const val UnionNameSpaceSplitter = "|"

/**
 * prefix unique key tree
 * sub elements belongs to upper node nameSpace
 * DistributionStrategy can be transformed with unique PrefixStrategyTreeNode
 *
 * @property uniqueKey
 * @property weight
 * @property subStrategies
 */
data class PrefixUniqueKeyDistributionStrategy(
    val uniqueKey: String,
    val weight: Int,
    val subStrategies: List<PrefixUniqueKeyDistributionStrategy> = emptyList()
) : DistributionStrategy {
    override fun getNodeWeight(): Int {
        return this.weight
    }

    override fun getNodeUniqueKey(): String {
        return this.uniqueKey
    }

    override fun getSubDistributionStrategy(): List<DistributionStrategy> {
        return this.subStrategies
    }

    override fun validateUniqueKeys() {
        val historyUniqueKeys = mutableListOf<String>()
        historyUniqueKeys.add(this.uniqueKey)
        for (subNode in subStrategies) {
            subNode.validateUniqueKeys(historyUniqueKeys, this.uniqueKey)
        }
    }

    override fun validateUniqueKeys(historyUniqueKeys: MutableList<String>, prefix: String) {
        if (historyUniqueKeys.contains(this.uniqueKey)) {
            throw IllegalStateException("unique key has existed!")
        }
        if (!this.uniqueKey.startsWith(prefix)) {
            throw IllegalStateException("invalid namespace setting!")
        }
        historyUniqueKeys.add(this.uniqueKey)
        val thisLevelKeys = this.getSubDistributionStrategy().map { it.getNodeUniqueKey() }.toSet()
        check(!hasPrefixRelation(thisLevelKeys)){ "has two unique key with prefix relation!" }
        for (subNode in subStrategies) {
            subNode.validateUniqueKeys(historyUniqueKeys, this.uniqueKey)
        }
    }

    /**
     * check all nodes in same level has prefix relation or not. If has, which is invalid.
     * time: O(n^2)  -> can ben optimizer to O(m)
     */
    private fun hasPrefixRelation(strings: Set<String>): Boolean {
        for (a in strings) {
            for (b in strings) {
                if (a != b && b.startsWith(a)) {
                    return true
                }
            }
        }
        return false
    }
}

/**
 * build buildStrategySubTree tree
 *
 * @param distributionStrategy
 * @param namespace
 * @return
 */
fun buildStrategyTree(distributionStrategy: DistributionStrategy, namespace: String = ""): PrefixUniqueKeyDistributionStrategy {
    val uniqueKey = buildNodeUniqueKey(distributionStrategy, namespace)
    return PrefixUniqueKeyDistributionStrategy(
        uniqueKey = uniqueKey,
        weight = distributionStrategy.getNodeWeight(),
        subStrategies = distributionStrategy.getSubDistributionStrategy().map { subNode ->
            buildStrategyTree(distributionStrategy = subNode, namespace = uniqueKey)
        }
    )
}

/**
 * build node unique key with prefix mode
 *
 * @param distributionStrategy
 * @param namespace
 * @return
 */
fun buildNodeUniqueKey(distributionStrategy: DistributionStrategy, namespace: String): String {
    // mark that unique key using prefix resolve
    if (namespace.isBlank()) {
        // root node
        return distributionStrategy.getNodeUniqueKey()
    }
    if (distributionStrategy.getNodeUniqueKey().startsWith(namespace + NameSpaceSplitter)) {
        return distributionStrategy.getNodeUniqueKey()
    }
    return namespace + NameSpaceSplitter + distributionStrategy.getNodeUniqueKey()
}

/**
 * build and validate tree
 *
 * @param distributionStrategy
 * @return
 */
fun getStrategyTree(distributionStrategy: DistributionStrategy): PrefixUniqueKeyDistributionStrategy {
    return buildStrategyTree(distributionStrategy).also { it.validateUniqueKeys() }
}

