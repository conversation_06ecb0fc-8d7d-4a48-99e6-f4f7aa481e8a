package com.alibaba.koastline.multiclusters.apre.model

import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import java.util.*


/**
 * 授权关系
 *
 * @property apREBindingData
 * @property matchScopeData
 */
data class Attorney(
    val apREBindingData: ApREBindingData,
    val matchScopeData: MatchScopeDataDO
)

/**
 * 运行时环境授权范围定义
 * @param id clusterBindingDataId
 */
data class ApREBindingData(
    val runtimeEnvKey: String,
    val apREBindingTerm: ApREBindingTerm?,
    val id: Long? = null,
)

data class ApREBindingTerm(
    // allSupported 为true开放所有特性
    val allSupported: Boolean? = true,
    // required 为空 表示不进行任何过滤 即全量开放
    val required: Required? = null
)

data class Required(
    /**
     * 只面向满足Label&Spec限定的资源申请
     */

    val allLabelSupported: Boolean? = true,
    val apRELabelSelectorTerms: List<ApRELabelSelectorTerm>? = null,
    /**
     * 只开放指定集群
     */
    val allClustersSupported: Boolean? = true,
    val clusters: List<String>? = emptyList()
)

data class ApRELabelSelectorTerm(
    val name: String?,
    /**
     * 1.置空表示引用所有
     * 2.暂不支持模式匹配
     */
    val value: String?,
    val apREFeatureSpecSelectorTerms: List<ApREFeatureSpecSelectorTerm>?
)

data class ApREFeatureSpecSelectorTerm(
    val matchExpressions: List<MatchExpression>
)

data class MatchExpression(
    val key: String,
    val operator: String,
    val values: List<String>
)

data class ServerlessAttorney(
    val serverlessBaseApp: String,
    val externalAndProperties: ExternalAndProperties
)

data class RuntimeProperties(
    val allRuntimeSupport: Boolean = false,
    val admissionEnvList: List<ServerlessEnvType> = listOf(ServerlessEnvType.PUBLISH)
)

data class ServerlessAttorneyGroup(
    val serverlessBaseApp: String,
    val externalAndPropertiesList: List<ExternalAndProperties>
) {
    companion object {
        fun transformServerlessAttorneyList(serverlessAttorneyList: List<ServerlessAttorney>): ServerlessAttorneyGroup {
            require(serverlessAttorneyList.isNotEmpty()) {
                "serverlessAttorneyList cannot be empty"
            }
            require(serverlessAttorneyList.map { it.serverlessBaseApp }.distinct().size == 1) {
                "merge serverlessAttorneyList should keep all serverlessBaseApp same!"
            }
            return ServerlessAttorneyGroup(
                serverlessBaseApp = serverlessAttorneyList.first().serverlessBaseApp,
                externalAndPropertiesList = serverlessAttorneyList.map { it.externalAndProperties }
            )
        }
    }
}

enum class ServerlessEnvType {
    PUBLISH,
    TESTING;
}

data class ExternalAndProperties(
    val runtimeProperties: RuntimeProperties,
    val creator: String,
    val gmtCreate: Date,
    val modifier: String,
    val gmtModified: Date,
    val externalType: MatchScopeExternalTypeEnum,
    val externalId: String,
    val description: String = ""
)