package com.alibaba.koastline.multiclusters.schedule.service.schedule.core

import kotlin.math.floor

/**
 * group conception: using a unique key to identify, also a replicas to represent quantity
 *
 */
interface Group {
    fun getGroupReplicas(): Int
    fun getGroupUniqueKey(): String
}

/**
 *
 * to calculate group: input group, add a weight to represent expected ratio in groups
 *
 * @property uniqueKey
 * @property weight
 * @property replicas
 */
data class ToCalculateGroup(
    val uniqueKey: String,
    val weight: Double,
    val replicas: Int
) : Group {

    override fun getGroupReplicas(): Int {
        return this.replicas
    }

    override fun getGroupUniqueKey(): String {
        return this.uniqueKey
    }

    fun isNotExpected() = weight == 0.0

    companion object {
        @JvmStatic
        fun ofDefaultUnit(uniqueKey: String, replicas: Int) =
            ToCalculateGroup(uniqueKey = uniqueKey, weight = 1.0, replicas = replicas)

        fun ofZeroUnit(uniqueKey: String, replicas: Int) =
            ToCalculateGroup(uniqueKey = uniqueKey, weight = 0.0, replicas = replicas)
    }
}

fun List<ToCalculateGroup>.validate() {
    require(this.map { it.uniqueKey }.distinct().size == this.size) {
        "ToCalculateGroup uniqueKey cannot be blank"
    }
    require(this.all { it.uniqueKey.isNotBlank() }) {
        "ToCalculateGroup uniqueKey cannot be blank"
    }
}

/**
 * output result
 *
 * @property uniqueKey
 * @property replicas
 */
data class ResultGroup(
    val uniqueKey: String,
    val replicas: Int
) : Group {
    override fun getGroupReplicas(): Int {
        return this.replicas
    }

    override fun getGroupUniqueKey(): String {
        return this.uniqueKey
    }

}

/**
 * according to weights in groups to decide to decease replicas
 * rule1: expend unexpected replicas, rule2: if unexpected replicas is not enough, expend these expected replicas
 *
 * @param toCalculateGroups
 * @param operateReplicas support positive int
 * @return
 */
fun calculateDecreaseToReplicas(toCalculateGroups: List<ToCalculateGroup>, operateReplicas: Int): List<ResultGroup> {
    require(operateReplicas <= 0) { "to deceaseReplicas must be not positive int" }
    if (operateReplicas == 0) {
        return toCalculateGroups.map { ResultGroup(it.uniqueKey, it.replicas) }
    }
    val totalSum = toCalculateGroups.sumOf { it.replicas }
    val resultGroups = mutableListOf<ResultGroup>()
    require((totalSum + operateReplicas) >= 0) { "total sum must be equal or more than to decrease operateReplicas, toCalculateGroups:${toCalculateGroups}, toDeceaseReplicas:$operateReplicas" }

    val notExpectedUnits = toCalculateGroups.filter { it.isNotExpected() }
    val expectedUnits = (toCalculateGroups.toSet() - notExpectedUnits.toSet()).toList()
    // rule1: if not expected replicas more than to decrease replicas, try to make unit keep weight ratio
    if ((notExpectedUnits.sumOf { it.replicas } + operateReplicas) > 0) {
        val toDecreaseResults = determineDecreaseToReplicas(notExpectedUnits.map { unit ->
            ToCalculateGroup.ofDefaultUnit(
                uniqueKey = unit.uniqueKey,
                replicas = unit.replicas
            )
        }, operateReplicas = operateReplicas)
        resultGroups.addAll(expectedUnits.map { ResultGroup(uniqueKey = it.uniqueKey, replicas = it.replicas) })
        resultGroups.addAll(toDecreaseResults)
        return resultGroups
    }
    // rule2: if not expected replicas less than to decrease replicas but less total, decrease not expected replicas firstly, then make left replicas keep weight ratio
    // using not expect first
    resultGroups.addAll(notExpectedUnits.map { unit -> ResultGroup(uniqueKey = unit.uniqueKey, replicas = 0) })
    val leftOperateReplicas = operateReplicas + notExpectedUnits.sumOf { it.replicas }
    // get left expected distribution
    // only have weight > 0 elements
    val finalExpectedDistribution =
        determineDecreaseToReplicas(toCalculateGroups = expectedUnits, operateReplicas = leftOperateReplicas)
    resultGroups.addAll(finalExpectedDistribution)
    return resultGroups
}

/**
 * determineDecreaseToReplicas, using lambda func[getMoreThan] to filter those nodes which are involve to decrease
 *
 * @param toCalculateGroups
 * @param operateReplicas
 * @return
 */
fun determineDecreaseToReplicas(
    toCalculateGroups: List<ToCalculateGroup>,
    operateReplicas: Int
): List<ResultGroup> {
    require(operateReplicas <= 0) { "to decrease operateReplicas must be not negative" }
    return determineToReplicas(
        toCalculateGroups = toCalculateGroups,
        operateReplicas = operateReplicas
    ) { originalDistribution, tryShare ->
        getMoreThan(
            origin = originalDistribution.map { ResultGroup(uniqueKey = it.uniqueKey, replicas = it.replicas) },
            after = tryShare
        )
    }
}

/**
 * determineIncreaseToReplicas, using lambda func[getLessThan] to filter those nodes which are involve to decrease
 *
 * @param toCalculateGroups
 * @param operateReplicas
 * @return
 */
fun determineIncreaseToReplicas(
    toCalculateGroups: List<ToCalculateGroup>,
    operateReplicas: Int
): List<ResultGroup> {
    require(operateReplicas >= 0) { "to increase operateReplicas must be not negative" }
    return determineToReplicas(
        toCalculateGroups = toCalculateGroups,
        operateReplicas = operateReplicas
    ) { originalDistribution, tryShare ->
        getLessThan(
            origin = originalDistribution.map { ResultGroup(uniqueKey = it.uniqueKey, replicas = it.replicas) },
            after = tryShare
        )
    }
}

/**
 * deploy Replicas to expect state, using lambda fun to filter those nodes which are involve to operate
 *
 * @param toCalculateGroups
 * @param operateReplicas
 * @param replicasCompareFilter
 * @return
 */
fun determineToReplicas(
    toCalculateGroups: List<ToCalculateGroup>,
    operateReplicas: Int,
    replicasCompareFilter: (List<ToCalculateGroup>, List<ResultGroup>) -> List<ResultGroup>
): List<ResultGroup> {
    toCalculateGroups.validate()
    val finalDistribution = mutableListOf<ResultGroup>()
    var originalDistribution = toCalculateGroups
    var tryShare = calculateDistribution(
        toCalculateGroups = originalDistribution, operateReplicas
    )
    var reserve = replicasCompareFilter(originalDistribution, tryShare)
    while (reserve.isNotEmpty()) {
        finalDistribution.addAll(originalDistribution.extractReserveGroups(reserve.map { it.uniqueKey }))
        originalDistribution = originalDistribution.filter { original ->
            !finalDistribution.map { final -> final.uniqueKey }.contains(original.uniqueKey)
        }
        tryShare = calculateDistribution(
            toCalculateGroups = originalDistribution, operateReplicas
        )
        reserve = replicasCompareFilter(originalDistribution, tryShare)
    }
    finalDistribution.addAll(tryShare)
    return finalDistribution.sortedBy { -it.replicas }
}

/**
 * split elements in reserve groups
 *
 * @param reserveKeys
 * @return
 */
fun List<ToCalculateGroup>.extractReserveGroups(reserveKeys: List<String>): List<ResultGroup> {
    return this.filter { o -> reserveKeys.contains(o.uniqueKey) }.map {
        ResultGroup(uniqueKey = it.uniqueKey, replicas = it.replicas)
    }
}

/**
 * calculate final expect distribution while adding operateReplicas, using resolveGap resolve int calculation gap
 *
 * @param toCalculateGroups
 * @param operateReplicas support positive or negative operateReplicas
 * @return
 */
fun calculateDistribution(
    toCalculateGroups: List<ToCalculateGroup>,
    operateReplicas: Int
): List<ResultGroup> {
    val total = toCalculateGroups.sumOf { it.replicas }
    check((total + operateReplicas) >= 0) { "final replicas cannot be negative" }
    val totalWeight = toCalculateGroups.sumOf { it.weight }
    val finalTotal = total + operateReplicas
    val expectDistribution = toCalculateGroups.map {
        ResultGroup(
            uniqueKey = it.uniqueKey,
            replicas = floor(finalTotal * getWeightRatio(it.weight, totalWeight)).toInt()
        )
    }.sortedBy { -it.replicas }
    //resolve gap
    val finalResult = resolveGap(originalResults = expectDistribution, finalReplica = finalTotal)
    check(finalResult.sumOf { it.replicas } == (total + operateReplicas)) { "calculate distribution with error" }
    return finalResult
}

/**
 * resolve gap of double trans to int gap calculation
 *
 * @param originalResults
 * @param finalReplica
 * @return
 */
fun resolveGap(originalResults: List<ResultGroup>, finalReplica: Int): List<ResultGroup> {
    require(originalResults.all { it.replicas >= 0 }) { "replicas must be zero or positive int" }
    require(finalReplica >= 0) { "finalReplica must be zero or positive int" }
    val originalTotal = originalResults.sumOf { it.replicas }
    // if gap is positive, which means original calculate result map is less than expected final replicas
    // so need increase replica, on the other hand, the gap is negative that originalResults needs decrease replica
    var gap = finalReplica - originalTotal
    if (gap == 0) {
        return originalResults
    }
    val sortResults = originalResults.sortedBy { -it.replicas }
    val finalResults = mutableListOf<ResultGroup>()
    var popUp = false
    sortResults.forEachIndexed { index, elem ->
        if (popUp) {
            finalResults.addAll(sortResults.subList(index, sortResults.size))
            return finalResults
        }
        if (gap != 0) {
            val tryShare = gap + elem.replicas
            if (tryShare > 0) {
                popUp = true
                finalResults.add(elem.copy(replicas = tryShare))
                gap = 0
            } else {
                // gap is negative, index elem can't hold the hole
                gap += elem.replicas
                finalResults.add(elem.copy(replicas = 0))
            }
        }
    }
    return finalResults
}

private fun getWeightRatio(partWeight: Double, totalWeight: Double): Double {
    return (partWeight / totalWeight)
}

fun getMoreThan(origin: List<ResultGroup>, after: List<ResultGroup>): List<ResultGroup> {
    return getDiff(origin, after).filter { it.replicas > 0 }
}

fun getLessThan(origin: List<ResultGroup>, after: List<ResultGroup>): List<ResultGroup> {
    return getDiff(origin, after).filter { it.replicas < 0 }
}

/**
 * make diff results
 *
 * @param origin
 * @param after
 * @return
 */
fun getDiff(origin: List<ResultGroup>, after: List<ResultGroup>): List<ResultGroup> {
    require(origin.map { it.uniqueKey }.toSet() == after.map { it.uniqueKey }.toSet()) {
        "original set must be equal to after set"
    }
    val originalMap = origin.associateBy { it.uniqueKey }
    val afterMap = after.associateBy { it.uniqueKey }
    val diffResult = mutableListOf<ResultGroup>()
    originalMap.forEach { (key, rs) ->
        diffResult.add(
            ResultGroup(
                uniqueKey = key,
                replicas = checkNotNull(afterMap[key]) { "missing after with key:$key" }.replicas - rs.replicas
            )
        )
    }
    return diffResult
}