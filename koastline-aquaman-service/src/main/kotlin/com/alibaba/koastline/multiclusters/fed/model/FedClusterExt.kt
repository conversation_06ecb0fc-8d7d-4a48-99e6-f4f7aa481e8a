package com.alibaba.koastline.multiclusters.fed.model

enum class FedClusterStatus {
    Online,
    Offline
}

object FedSource {
    const val NORMANDY_FED_SOURCE = "normandy"
}

/**
 * 必要参数 自定义模板中必要参数
 */
object FedTargetPropertiesKey {
    const val IS_DISABLED = "isDisabled"
    const val REGION = "region"
    const val UNIQUE_NAME_KEY = "uniqueNameKey"
    const val TENANT_CLUSTER_NAME = "tenantClusterName"
    const val MEMBER_CLUSTER_NAME_LIST = "memberClusterNameList"
}

/**
 * 必要参数 自定义模板中必要参数
 */
object FedPolicyPropertiesKey {
    const val POLICY_PRIORITY = "priority"
    const val RESOURCE_KIND = "resourceKind"
    const val FED_POLICY_UNIQUE_NAME = "name"
    const val FED_SPEC = "spec"
    const val FED_SOURCE = "source"
    const val FED_POLICY_NAME = "policyName"
}

/**
 * 必选&可选参数
 */
object FedPolicySpecPropertiesKey {
    const val CLUSTER_OVERRIDE = "clusterOverride" //optional
    const val GLOBAL_OVERRIDE = "globalOverride" //optional
    const val POLICY = "policy" //required
    const val POLICY_SELECTOR = "policySelector" // required
}

object FedPolicyDetailsPropertiesKey{
    const val SCHEDULE_PLUGIN = "schedulePlugin"
    const val RATIOS = "ratios"
}

object FedPolicyRatioPropertiesKey{
    const val CLUSTER_NAME = "clusterName"
    const val VALUE = "value"
}

/**
 * 必选参数  自定义模板中必要参数
 */
object FedPolicySelectorPropertiesKey {
    const val TENANT_CLUSTER = "tenantCluster"
    const val TENANT_NAME = "tenantName"
    const val REGION = "region"
    const val MATCH_EXPRESSIONS = "matchExpressions"
}

object PolicyResourceKind {
    const val POD = "Pod"
    const val RESERVE_RESOURCE_SET = "ReserveResourceSet"
}

object MatchExpressionsProperties{
    const val KEY = "key"
    const val OPERATOR = "operator"
    const val VALUES = "values"
}

const val LABEL_KV_SPLITTER = ":"