package com.alibaba.koastline.multiclusters.resourceobj.base.facade

import com.alibaba.koastline.multiclusters.resourceobj.base.WorkloadSpecContext
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint

interface WorkloadSpecFacade {
    /**
     * 前置校验，校验传入的spec转换workload对象是否正常
     * @param resourceObjectSpecStr workload spec string
     * @param resourceObjectFormatEnum 格式
     * @throws ResourceObjectException 转换失败
     */
    fun preCheck(resourceObjectSpecStr: String?, resourceObjectFormatEnum: ResourceObjectFormatEnum)

    /**
     * 获取初始化的Workload spec，包括基线版本以及预处理的workload spec
     * @param context 上下文
     * @return
     * @throws ResourceObjectException 当未找到基线版本时抛异常
     */
    fun getBaseSpec(context: WorkloadSpecContext): Map<String, Any>

    /**
     * 特性注入后修改Workload spec
     * 需要注意到这里要设置的是固定不变的特性，不能是动态的特性注入
     * @param resourceObjectSpec workload资源对象
     * @param context 上下文
     * @return
     */
    fun postModifyBaseSpec(resourceObjectSpec: Map<String, Any>, context: WorkloadSpecContext): Map<String, Any>

    /**
     * 获取发布的Workload spec，合并版本&运行态Workload Spec
     * @return
     * @throws ResourceObjectException 当未找到基线版本时抛异常
     */
    fun getDeploySpec(stackPkId: String, currentResourceObjectSpecStr: String?, currentResourceObjectFormatEnum: ResourceObjectFormatEnum, workloadMetadataConstraint: WorkloadMetadataConstraint): Map<String, Any>

    /**
     * 后置校验，校验生成的spec转换workload对象是否正常
     * @param resourceObjectSpecStr workload spec string
     * @param resourceObjectFormatEnum 格式
     * @throws ResourceObjectException 转换失败
     */
    fun postCheck(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum)

    /*
    后处理，整体二次加工
     */
    fun postApply(resourceObjectSpecStr: String, resourceObjectFormatEnum: ResourceObjectFormatEnum): String

    fun postCheckForScaleOut(
        currentResourceObjectSpecStr: String?,
        patchedResourceObjectSpecStr: String,
        resourceObjectFormatEnum: ResourceObjectFormatEnum,
    )
}