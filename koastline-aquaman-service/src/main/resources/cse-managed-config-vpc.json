[{"clusterName": "dingding-daily", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "alidoc-daily-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "alidoc-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "alidoc-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "dingtalk-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "dingtalk-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "ding-arch-daily-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "ding-arch-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "ding-arch-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "dianxiaomi-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "asi_zjk_oxs_test01", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-test.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default-pre", "templateVersion": "v7", "scheme": "https"}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-mpp-sh-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-iot-sh-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}}, {"clusterName": "", "clusterId": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "cse-asi-mpp-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}}, {"clusterName": "", "clusterId": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "**************", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "http"}}, {"clusterName": "", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-iot-hzpre.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default-pre", "templateVersion": "v7", "scheme": "https"}}, {"clusterName": "dianxiaomi_pre_1", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "************", "port": 8080, "templateVersion": "v7"}}, {"clusterName": "asi_hangzhou_vpc_pre_1", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-public-hz-pre.aliyun-inc.com", "port": 443, "templateVersion": "v7", "scheme": "https"}}]