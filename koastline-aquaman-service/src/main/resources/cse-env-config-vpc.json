[{"clusterName": "dingding-daily", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingding-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterName": "alidoc-daily-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterName": "alidoc-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterName": "alidoc-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "dingtalk-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingtalk-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterName": "dingtalk-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingtalk-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "ding-arch-daily-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterName": "ding-arch-pre-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterName": "ding-arch-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "dianxiaomi-prod-1", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dxm-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "asi_zjk_oxs_test01", "clusterId": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "oxs-na61-test", "unit": "CENTER_UNIT.center", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-test.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default-pre", "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "", "unit": "", "stage": "", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-mpp-sh-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "mpp-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-iot-sh-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "iot-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "cse-asi-mpp-prd.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-beijing", "az": "mpp-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "**************", "port": 443, "k8s-namespace": "cse-default", "templateVersion": "v7", "scheme": "http"}, "envMeta": {"envLevel": "", "region": "cn-beijing", "az": "cn-beijing-h", "unit": "ask-test", "stage": "", "externalId": "", "externalType": ""}}, {"clusterName": "", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-iot-hzpre.aliyun-inc.com", "port": 443, "k8s-namespace": "cse-default-pre", "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "asi-public-vpc-hz-pre", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterName": "dianxiaomi_pre_1", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "************", "port": 8080, "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "cn-hangzhou-h", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterName": "asi_hangzhou_vpc_pre_1", "clusterId": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-public-hz-pre.aliyun-inc.com", "port": 443, "templateVersion": "v7", "scheme": "https"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "asi-public-vpc-hz-pre", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}]