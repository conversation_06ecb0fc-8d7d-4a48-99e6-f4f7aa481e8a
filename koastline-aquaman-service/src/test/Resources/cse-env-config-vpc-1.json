[{"clusterId": "external-wn9rtufkpzf0qrugr4u1d0bcbtkloj", "clusterName": "dingding-daily", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingding-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterId": "external-y26x4cttpolowaswcc6uh3uroa9mdq", "clusterName": "alidoc-daily-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterId": "external-lkwchhufugdteuvmmg1eufrhk2a4by", "clusterName": "alidoc-pre-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-7knymkqmjewllz2yc11hoqzpi5kmeu", "clusterName": "alidoc-prod-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "alidoc-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-8ymmdkupetlsqarhg1rqunmzn7flkw", "clusterName": "dingtalk-pre-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingtalk-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-pe2z7padngiwxrjqb1tzmztkvcqnep", "clusterName": "dingtalk-prod-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dingtalk-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-hijwz4wxusplgwv6wqtsxmcnty4eoq", "clusterName": "ding-arch-daily-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-daily", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterId": "external-th0enbvnfh9h8jomc6orj4ogfcgqcu", "clusterName": "ding-arch-pre-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-pre", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-g2lbimgt8uiwesunkmcukm8ckpi6zl", "clusterName": "ding-arch-prod-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "*************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "ding-arch-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-8jqthpfyvftzogdl2yqrg5t3mppsac", "clusterName": "dianxiaomi-prod-1", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>-b", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "all-az", "unit": "dxm-prod", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-bkgsq3ssxkuidabfktxasisfjl2oc2", "clusterName": "asi_zjk_oxs_test01", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "cse-system", "gateway": {"host": "**************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "az": "oxs-na61-test", "unit": "CENTER_UNIT.center", "stage": "daily", "externalId": "", "externalType": ""}}, {"clusterId": "external-jpfkbgflwqmhfdsdisqmsywfbkchsw", "clusterName": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-test.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "iot-vpc-ET93", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-my2fadjjrrei667we7i3vpzew2qoek", "clusterName": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-mpp-sh-prd.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "mpp-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-muiwpljfykjcizirv07sx8lpokqgdq", "clusterName": "", "region": "cn-shanghai", "namespace": "", "gateway": {"host": "cse-asi-iot-sh-prd.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-shanghai", "az": "iot-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-ny3gtifvh61xoubg7xdbbhoxbuirkf", "clusterName": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "cse-asi-mpp-prd.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-beijing", "az": "mpp-vpc-sh-prd", "unit": "CENTER_UNIT.center", "stage": "publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-9rpkoh7rmxweloygcduizhxkig0mrc", "clusterName": "", "region": "cn-beijing", "namespace": "", "gateway": {"host": "**************", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-beijing", "az": "cn-beijing-h", "unit": "ask-test", "stage": "ask-test", "externalId": "", "externalType": ""}}, {"clusterId": "external-73bg1bmjxtaklwsr66hvyxpif0eqsy", "clusterName": "", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-iot-hzpre.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "asi-public-vpc-hz-pre", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-whly06lqfwnw3hwbnsmgveswqgyhxx", "clusterName": "dianxiaomi_pre_1", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "************", "port": 8080, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "cn-hangzhou-h", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}, {"clusterId": "external-csxp450ml0aru9p3vfxpnoytdgk07l", "clusterName": "asi_hangzhou_vpc_pre_1", "region": "cn-hangzhou", "namespace": "", "gateway": {"host": "cse-asi-public-hz-pre.aliyun-inc.com", "port": 443, "schema": "http", "templateVersion": "v7"}, "envMeta": {"envLevel": "", "region": "cn-hangzhou", "az": "asi-public-vpc-hz-pre", "unit": "CENTER_UNIT.center", "stage": "pre_publish", "externalId": "", "externalType": ""}}]