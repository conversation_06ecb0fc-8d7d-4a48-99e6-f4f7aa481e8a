package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import io.mockk.*
import org.junit.Test
import org.junit.jupiter.api.Assertions.*

public class JmenvApiTest {
  @Test
  fun `MW_ENV_PUBLISH`() {

    val objectMapper = ObjectMapperFactory.newTolerant()
    val api = spyk(JmenvApi(objectMapper)).also {
      it.host = "host"
      it.dailyHost = "dailyHost"
      }
    mockkObject(HttpClientUtils)
    every { HttpClientUtils.httpGet(any(), any(), any()) } returns "rg_id   "

    assertEquals(api.getMiddlewareEnv("PUBLISH", "unit"), "rg_id")
    verify {
      HttpClientUtils.httpGet(
        "host/env",
        mapOf("labels" to "app:vipserver,site:na61,unit:unit,stage:PUBLISH"),
        null
      )
    }
    unmockkAll()
  }

  @Test
  fun `MW_ENV_DAILY`() {

    val objectMapper = ObjectMapperFactory.newTolerant()
    val api = spyk(JmenvApi(objectMapper)).also {
      it.host = "host"
      it.dailyHost = "dailyHost"
    }
    mockkObject(HttpClientUtils)
    every { HttpClientUtils.httpGet(any(), any(), any()) } returns "rg_id   "

    assertEquals(api.getMiddlewareEnv("DAILY", "unit"), "rg_id")
    verify {
      HttpClientUtils.httpGet(
        "dailyHost/env", mapOf("labels" to "app:vipserver,site:na61,unit:unit,stage:DAILY"), null
      )
    }
    unmockkAll()
  }

}