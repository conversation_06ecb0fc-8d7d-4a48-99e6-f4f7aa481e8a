package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.service.schedule.test.DeployScheduleServiceTest.Companion.RESOURCE_GROUP
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.resourcescope.EnvHostWorkloadMetaService
import com.alibaba.koastline.multiclusters.resourcescope.model.AppGroupScope
import com.alibaba.koastline.multiclusters.resourcescope.model.AppGroupScopeRestriction
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostResourceScopeDO
import com.alibaba.koastline.multiclusters.resourcescope.model.EnvHostWorkloadMetaDO
import com.alibaba.koastline.multiclusters.resourcescope.model.HostResourceScope
import com.alibaba.koastline.multiclusters.resourcescope.model.HostResourceScopeTarget
import com.alibaba.koastline.multiclusters.schedule.model.DeployMode
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants.Companion.RESOURCE_NUM
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeployHostScheduleService
import io.mockk.every
import io.mockk.justRun
import io.mockk.spyk
import org.junit.Test
import java.util.UUID
import kotlin.test.assertEquals

/**
 * @author:    <EMAIL>
 * @date:    2025/3/11 1:57 PM
 */
class DeployHostScheduleServiceTest {
    @Test(expected = BizException::class)
    fun `testSchedule while not find env host resource scope`() {
        val content = DeployScheduleServiceTest.getBaseContent()
        val deployHostScheduleService = spyk<DeployHostScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
        }
        deployHostScheduleService.doSchedule(content)
    }

    @Test
    fun `testSchedule while resource scope by env`() {
        val content = DeployScheduleServiceTest.getBaseContent()
        val deployHostScheduleService = spyk<DeployHostScheduleService>() {
            this.envHostWorkloadMetaService = spyk<EnvHostWorkloadMetaService>() {
                every {
                    listByEnvStackId(content.resourceScope.envStackId!!)
                }returns emptyList()
                justRun {
                    deleteByEnvStackId(content.resourceScope.envStackId!!)
                }
                justRun {
                    create(any())
                }
            }
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns fakeEnvHostResourceScopeOfEnvTarget(content)
            every {
                runningStateScheduleService.doSchedule(any())
            }returns DeployScheduleServiceTest.getScheduleResult_only_asi()
        }
        val scheduleResult = deployHostScheduleService.doSchedule(content)
        assertEquals(1, scheduleResult.workloadExpectedStates.size)
        assertEquals(DeployMode.HOST, scheduleResult.deployMode)
    }

    @Test
    fun `testSchedule while resource scope by app group`() {
        val content = DeployScheduleServiceTest.getBaseContent()
        val deployHostScheduleService = spyk<DeployHostScheduleService>() {
            this.envHostWorkloadMetaService = spyk<EnvHostWorkloadMetaService>() {
                every {
                    listByEnvStackId(content.resourceScope.envStackId!!)
                }returns emptyList()
                justRun {
                    deleteByEnvStackId(content.resourceScope.envStackId!!)
                }
                justRun {
                    create(any())
                }
            }
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns fakeEnvHostResourceScopeOfAppGroupTarget(content)
            every {
                runningStateScheduleService.doSchedule(any())
            }returns DeployScheduleServiceTest.getScheduleResult_only_asi()
        }
        val scheduleResult = deployHostScheduleService.doSchedule(content)
        assertEquals(2, scheduleResult.workloadExpectedStates.size)
        assertEquals("na610", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.site)
        assertEquals("na620", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.site)
    }

    @Test
    fun `testSchedule while resource scope by app group single`() {
        val content = DeployScheduleServiceTest.getBaseContent()
        val deployHostScheduleService = spyk<DeployHostScheduleService>() {
            this.envHostWorkloadMetaService = spyk<EnvHostWorkloadMetaService>() {
                every {
                    listByEnvStackId(content.resourceScope.envStackId!!)
                }returns emptyList()
                justRun {
                    deleteByEnvStackId(content.resourceScope.envStackId!!)
                }
                justRun {
                    create(any())
                }
            }
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns fakeEnvHostResourceScopeOfAppGroupTargetSingle(content)
            every {
                runningStateScheduleService.doSchedule(any())
            }returns DeployScheduleServiceTest.getScheduleResult_only_asi()
        }
        val scheduleResult = deployHostScheduleService.doSchedule(content)
        assertEquals(1, scheduleResult.workloadExpectedStates.size)
        assertEquals("na610", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.site)
        assertEquals("4", scheduleResult.workloadExpectedStates[0].params[RESOURCE_NUM])
    }

    @Test
    fun `testSchedule while resource scope by app group and beta running workload`() {
        val content = DeployScheduleServiceTest.getBaseContent()
        val deployHostScheduleService = spyk<DeployHostScheduleService>() {
            this.envHostWorkloadMetaService = spyk<EnvHostWorkloadMetaService>() {
                every {
                    listByEnvStackId(content.resourceScope.envStackId!!)
                }returns listOf(
                    EnvHostWorkloadMetaDO(id = 1L,
                        envStackId = content.resourceScope.envStackId!!,
                        appName = content.resourceScope.appName,
                        resourceGroup = RESOURCE_GROUP,
                        site = "na610",
                        unit = "CENTER_UNIT.center",
                        stage = "PUBLISH",
                        clusterId = "cluster_core_a02",
                        creator = "test",
                        modifier = "test"
                    ),
                    EnvHostWorkloadMetaDO(id = 2L,
                        envStackId = content.resourceScope.envStackId!!,
                        appName = content.resourceScope.appName,
                        resourceGroup = RESOURCE_GROUP,
                        site = "na620",
                        unit = "CENTER_UNIT.center",
                        stage = "PUBLISH",
                        clusterId = "cluster_core_b01",
                        creator = "test",
                        modifier = "test"
                    )
                )
                justRun {
                    deleteByEnvStackId(content.resourceScope.envStackId!!)
                }
                justRun {
                    create(any())
                }
            }
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns fakeEnvHostResourceScopeOfAppGroupTarget(content)
            every {
                runningStateScheduleService.doSchedule(any())
            }returns DeployScheduleServiceTest.getScheduleResult_only_asi()
        }
        val scheduleResult = deployHostScheduleService.doSchedule(content)
        assertEquals(2, scheduleResult.workloadExpectedStates.size)
        assertEquals("na610", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.site)
        assertEquals("cluster_core_a02", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals("na620", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.site)
        assertEquals("cluster_core_b01", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
    }

    private fun fakeEnvHostResourceScopeOfEnvTarget(content: ScheduleRequestContent): EnvHostResourceScopeDO {
        return EnvHostResourceScopeDO(
            id = 1L,
            appName = content.resourceScope.appName,
            currentEnvStackId = content.resourceScope.envStackId!!,
            baseEnvStackId = UUID.randomUUID().toString(),
            resourceScope = HostResourceScope(
                target = HostResourceScopeTarget.ENV,
                appGroupScopes = emptyList()
            ),
            creator = "test",
            modifier = "test"
        )
    }

    private fun fakeEnvHostResourceScopeOfAppGroupTarget(content: ScheduleRequestContent): EnvHostResourceScopeDO {
        return EnvHostResourceScopeDO(
            id = 1L,
            appName = content.resourceScope.appName,
            currentEnvStackId = content.resourceScope.envStackId!!,
            baseEnvStackId = UUID.randomUUID().toString(),
            resourceScope = HostResourceScope(
                target = HostResourceScopeTarget.APP_GROUP,
                appGroupScopes = listOf(
                    AppGroupScope(
                        appGroupName = RESOURCE_GROUP,
                        restrictions = listOf(
                            AppGroupScopeRestriction(unit = "CENTER_UNIT.center", site = "na610"),
                            AppGroupScopeRestriction(unit = "CENTER_UNIT.center", site = "na620"),
                        )
                    )
                )
            ),
            creator = "test",
            modifier = "test"
        )
    }

    private fun fakeEnvHostResourceScopeOfAppGroupTargetSingle(content: ScheduleRequestContent): EnvHostResourceScopeDO {
        return EnvHostResourceScopeDO(
            id = 1L,
            appName = content.resourceScope.appName,
            currentEnvStackId = content.resourceScope.envStackId!!,
            baseEnvStackId = UUID.randomUUID().toString(),
            resourceScope = HostResourceScope(
                target = HostResourceScopeTarget.APP_GROUP,
                appGroupScopes = listOf(
                    AppGroupScope(
                        appGroupName = RESOURCE_GROUP,
                        restrictions = listOf()
                    )
                )
            ),
            creator = "test",
            modifier = "test"
        )
    }
}