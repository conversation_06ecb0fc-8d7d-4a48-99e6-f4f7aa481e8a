package com.alibaba.koastline.multiclusters.appenv.service.runtime.test

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.SERVERLESS_PREFIX
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeWorkloadMetaException
import com.alibaba.koastline.multiclusters.data.dao.env.RuntimeWorkloadMetaRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import kotlin.test.assertEquals
import org.junit.Rule
import org.junit.Test
import org.junit.rules.ExpectedException
import testutils.BaseTest

class RuntimeWorkloadMetaServiceTest : BaseTest() {
    @InjectMockKs
    lateinit var runtimeWorkloadMetaService: RuntimeWorkloadMetaService

    @MockK
    lateinit var apREFeatureSpecService: ApREFeatureSpecService

    @MockK
    lateinit var apRELabelService: ApRELabelService

    @MockK
    lateinit var apREService: ApREService

    @MockK
    lateinit var resourcePoolService: ResourcePoolService

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var runtimeWorkloadMetaRepo: RuntimeWorkloadMetaRepo

    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    @Test
    fun registryRuntimeWorkloadTest() {
        val workloadMetaData = getFirmMetaData()
        val annotation = mapOf<String, String>(
            "cpu" to "2", "memory" to "4Gi"
        )
        every {
            runtimeWorkloadMetaRepo.insert(any())
        } returns 1

        every {
            runtimeWorkloadMetaRepo.findByRuntimeWorkloadId(workloadMetaData.runtimeId!!)
        } returns null

        mockRequireApREAndResourcePool(
            unit = workloadMetaData.unit, site = workloadMetaData.site,
            stage = workloadMetaData.stage, clusterId = workloadMetaData.clusterId
        )

        every {
            apREService.generateServerlessRuntimeTemplateName(
                any<String>(), null, any<String>(), any<String>()
            )
        } answers {
            callOriginal()
        }

        val apRELabelCreateReqDto = slot<ApRELabelCreateReqDto>()
        every {
            apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(capture(apRELabelCreateReqDto))
        } returns manufacturePojo(ApRELabelDO::class.java)

        runtimeWorkloadMetaService.registryRuntimeWorkload(
            workloadMetaData, annotation, emptyMap(), true, "test",
        )
        val createDto = apRELabelCreateReqDto.captured
        softly.assertThat(createDto.apREFeatureSpecList.first().sourceId).isEqualTo(workloadMetaData.runtimeId)
    }

    @Test
    fun cancelRunningRuntimeWorkloadTest() {
        val workload = getFirmMetaData()
        val runtimeWorkload = workload.toMockRuntimeWorkloadMeta()
        val modifier = getString()
        every {
            runtimeWorkloadMetaRepo.findByRuntimeWorkloadId(
                workload.runtimeId!!
            )
        } returns runtimeWorkload
        every {
            runtimeWorkloadMetaRepo.deleteById(
                runtimeWorkload.id!!, modifier
            )
        } returns 1

        mockRequireApREAndResourcePool(
            site = workload.site, unit = workload.unit, stage = workload.stage,
            clusterId = workload.clusterId, needMockApRELabel = true,
            appName = workload.appName, sourceId = runtimeWorkload.runtimeWorkloadId
        )

        every {
            apREFeatureSpecService.deleteByLabelKeyAndSpecCode(
                any(), any()
            )
        } just runs

        runtimeWorkloadMetaService.cancelRunningRuntimeWorkload(
            workloadMetadataConstraint = workload, modifier = modifier
        )
    }

    @Test
    fun requireMatchApRELabelWithResourceTest() {

        val workload = getFirmMetaData()
        val runtimeWorkload = workload.toMockRuntimeWorkloadMeta()
        mockRequireApREAndResourcePool(
            site = workload.site, unit = workload.unit, stage = workload.stage,
            clusterId = workload.clusterId, needMockApRELabel = true,
            appName = workload.appName, sourceId = runtimeWorkload.runtimeWorkloadId
        )
        val (apRELabel, _) = runtimeWorkloadMetaService.requireMatchApRELabelWithResource(
            unit = workload.unit, site = workload.site, stage = workload.stage,
            clusterId = workload.clusterId, appName = workload.appName
        )

        softly.assertThat(apRELabel!!.name).isEqualTo(APRE_LABEL_FEATURE_NAME)
        softly.assertThat(apRELabel.value).isEqualTo(SERVERLESS_PREFIX + workload.appName)
    }

    @Test
    fun `requireMatchApRELabelWithResourceTest while repeated metadata resource`() {
        val workload = getFirmMetaData()
        every {
            apREService.listResourcePoolByMetadata(
                unit = workload.unit,
                site = workload.site,
                stage = workload.stage,
                clusterId = workload.clusterId
            )
        }returns listOf(
            manufacturePojo(ResourcePoolDataDO::class.java),
            manufacturePojo(ResourcePoolDataDO::class.java)
        )
        exceptionRule.expect(RuntimeWorkloadMetaException::class.java)
        exceptionRule.expectMessage("runtime workload meta data must have only one matched resource pool!")

        runtimeWorkloadMetaService.requireMatchApRELabelWithResource(
            unit = workload.unit, site = workload.site, stage = workload.stage,
            clusterId = workload.clusterId, appName = workload.appName
        )
    }

    @Test
    fun `requireMatchApRELabelWithResourceTest while empty resource`() {
        val workload = getFirmMetaData()
        every {
            apREService.listResourcePoolByMetadata(
                unit = workload.unit,
                site = workload.site,
                stage = workload.stage,
                clusterId = workload.clusterId
            )
        }returns listOf()
        exceptionRule.expect(RuntimeWorkloadMetaException::class.java)
        exceptionRule.expectMessage("runtime workload meta data have no matched resource pool!")

        runtimeWorkloadMetaService.requireMatchApRELabelWithResource(
            unit = workload.unit, site = workload.site, stage = workload.stage,
            clusterId = workload.clusterId, appName = workload.appName
        )
    }

    @Test
    fun `listEnvStackIdByRuntimeWorkloadIdTest while findRuntimeWorkloadMeta is null`() {
        val runtimeWorkloadIdList = listOf(
            getString(),
        )
        every {
            runtimeWorkloadMetaService.findRuntimeWorkloadMeta(runtimeWorkloadId = runtimeWorkloadIdList[0])
        } returns null
        val rs = runtimeWorkloadMetaService.listEnvStackIdByRuntimeWorkloadId(runtimeWorkloadIdList)
        assertEquals(0, rs.size)
    }

    @Test
    fun `listEnvStackIdByRuntimeWorkloadIdTest while findRuntimeWorkloadMeta exist`() {
        val workload = getFirmMetaData()
        val runtimeWorkload = workload.toMockRuntimeWorkloadMeta()
        val runtimeWorkloadIdList = listOf(
            getString(),
        )
        val envStackId = getString()
        every {
            runtimeWorkloadMetaService.findRuntimeWorkloadMeta(runtimeWorkloadId = runtimeWorkloadIdList[0])
        } returns runtimeWorkload.copy(runtimeWorkloadId = runtimeWorkloadIdList[0])
        every {
            skylineApi.listBindingEnvStackIdByResourceGroup(runtimeWorkload.resourceGroup)
        } returns listOf(envStackId)
        val rs = runtimeWorkloadMetaService.listEnvStackIdByRuntimeWorkloadId(runtimeWorkloadIdList)
        assertEquals(1, rs.size)
        assertEquals(envStackId, rs[0].envStackId)
        assertEquals(runtimeWorkload.resourceGroup, rs[0].resourceGroup)
        assertEquals(runtimeWorkloadIdList[0], rs[0].runtimeWorkloadId)
    }


    @Test
    fun `listEnvStackIdByRuntimeWorkloadIdTest while is same resource group`() {
        val workload = getFirmMetaData()
        val runtimeWorkload = workload.toMockRuntimeWorkloadMeta()
        val runtimeWorkloadIdList = listOf(
            getString(),
            getString(),
        )
        val envStackId = getString()
        every {
            runtimeWorkloadMetaService.findRuntimeWorkloadMeta(runtimeWorkloadId = runtimeWorkloadIdList[0])
        } returns runtimeWorkload.copy(runtimeWorkloadId = runtimeWorkloadIdList[0])
        every {
            runtimeWorkloadMetaService.findRuntimeWorkloadMeta(runtimeWorkloadId = runtimeWorkloadIdList[1])
        } returns runtimeWorkload.copy(runtimeWorkloadId = runtimeWorkloadIdList[1])
        every {
            skylineApi.listBindingEnvStackIdByResourceGroup(runtimeWorkload.resourceGroup)
        } returns listOf(envStackId)
        val rs = runtimeWorkloadMetaService.listEnvStackIdByRuntimeWorkloadId(runtimeWorkloadIdList)

        assertEquals(2, rs.size)
        assertEquals(runtimeWorkloadIdList[0], rs[0].runtimeWorkloadId)
        assertEquals(envStackId, rs[0].envStackId)
        assertEquals(runtimeWorkload.resourceGroup, rs[0].resourceGroup)
        assertEquals(runtimeWorkloadIdList[1], rs[1].runtimeWorkloadId)
        assertEquals(envStackId, rs[1].envStackId)
        assertEquals(runtimeWorkload.resourceGroup, rs[1].resourceGroup)

        verify(exactly = 1) { skylineApi.listBindingEnvStackIdByResourceGroup(runtimeWorkload.resourceGroup) }
    }

    private fun getFirmMetaData(): WorkloadMetadataConstraint {
        return manufacturePojo(WorkloadMetadataConstraint::class.java).let {
            it.copy(runtimeId = it.resolveBaseAppRuntimeId(), namespace = getString())
        }
    }

    private fun WorkloadMetadataConstraint.toMockRuntimeWorkloadMeta(): RuntimeWorkloadMeta {
        return manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
            runtimeWorkloadId = this.runtimeId!!,
            appName = this.appName,
            resourceGroup = this.resourceGroup,
            nameSpace = this.namespace!!,
            site = this.site,
            unit = this.unit,
            stage = this.stage,
            clusterId = this.clusterId,
            status = "INSTALLED"
        )
    }

    private fun mockRequireApREAndResourcePool(
        site: String, unit: String, stage: String, clusterId: String,
        needMockApRELabel: Boolean = false, appName: String? = null, sourceId: String? = null
    ) {
        val resourcePoolDataDO = manufacturePojo(ResourcePoolDataDO::class.java).copy(
            clusterId = clusterId
        )

        val labels = mutableListOf<ApRELabelDO>()

        if (needMockApRELabel) {
            val mockApRELabel = manufacturePojo(ApRELabelDO::class.java).copy(
                apRELabelKey = resourcePoolDataDO.resourcePoolKey,
                value = SERVERLESS_PREFIX + appName,
                name = APRE_LABEL_FEATURE_NAME,
                targetType = RESOURCE_POOL,
                type = SERVERLESS
            )
            val mockApREFeatureSpec = manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                apRELabelKey = mockApRELabel.apRELabelKey,
                isDeleted = "N",
                annotations = "{\"cpu\":\"2\",\"memory\":\"4Gi\"}",
                labels = "{}",
                sourceId = sourceId, specCode = sourceId,
                scope = ApREFeatureSpecScopeEnum.publish.name
            )
            labels.add(mockApRELabel.copy(apREFeatureSpecs = listOf(mockApREFeatureSpec)))
        }

        every {
            apREService.listResourcePoolByMetadata(
                unit = unit, site = site, stage = stage, clusterId = clusterId
            )
        } returns listOf(resourcePoolDataDO)
        every {
            resourcePoolService.findResourcePoolDetailWithAllLabelSpecByKey(resourcePoolDataDO.resourcePoolKey)
        } returns ResourcePoolDO(
            id = resourcePoolDataDO.id,
            resourcePoolKey = resourcePoolDataDO.resourcePoolKey,
            clusterId = resourcePoolDataDO.clusterId,
            managedClusterKey = resourcePoolDataDO.managedClusterKey,
            gmtCreate = resourcePoolDataDO.gmtCreate,
            gmtModified = resourcePoolDataDO.gmtModified,
            isDeleted = resourcePoolDataDO.isDeleted,
            apRELabels = labels,
        )
    }
}