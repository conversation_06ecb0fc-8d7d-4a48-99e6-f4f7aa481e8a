package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.ResultGroup
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.ToCalculateGroup
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.calculateDecreaseToReplicas
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.calculateDistribution
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.determineDecreaseToReplicas
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.determineIncreaseToReplicas
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.resolveGap
import org.junit.Test
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.assertThrows
import testutils.BaseTest

class SelectTest : BaseTest() {

    @Test
    fun calculateDistributionMapTest() {
        val toCalculateGroups = listOf(
            ToCalculateGroup("a", 1.0, 100),
            ToCalculateGroup("b", 2.0, 80),
            ToCalculateGroup("c", 3.0, 70),
        )
        val operateReplicas = 0
        val rs = calculateDistribution(toCalculateGroups, operateReplicas)
        println(rs)
    }

    @Test
    fun calculateDistributionMapTest_non_gap() {
        val toCalculateGroups = listOf(
            ToCalculateGroup("a", 1.0, 100),
            ToCalculateGroup("b", 2.0, 200),
            ToCalculateGroup("c", 3.0, 200),
        )
        val operateReplicas = -150
        val rs = calculateDistribution(toCalculateGroups, operateReplicas)
        println(rs)
    }

    @Test
    fun resolveGapTest_more_than() {
        val gap = 10
        val fistResult = listOf(
            ResultGroup("1", 100),
            ResultGroup("2", 200),
            ResultGroup("3", 300),
        )
        val finalResult = fistResult.sumOf { it.replicas } + gap
        val calculateResult = resolveGap(fistResult, finalReplica = finalResult)
        softly.assertThat(calculateResult.first().replicas == fistResult.first().replicas + gap)
    }

    @Test
    fun resolveGapTest_less_than() {
        val gap = -10
        val fistResult = listOf(
            ResultGroup("1", 100),
            ResultGroup("2", 100),
            ResultGroup("3", 200),
        )
        val finalResult = fistResult.sumOf { it.replicas } + gap
        val calculateResult = resolveGap(fistResult, finalReplica = finalResult)
        softly.assertThat(calculateResult.first().replicas == fistResult.first().replicas + gap)
    }

    @Test
    fun resolveGapTest_first_not_enough() {
        val gap = -4
        val fistResult = listOf(
            ResultGroup("1", 1),
            ResultGroup("2", 2),
            ResultGroup("3", 3),
        )
        val finalResult = fistResult.sumOf { it.replicas } + gap
        val calculateResult = resolveGap(fistResult, finalReplica = finalResult)
        softly.assertThat(calculateResult.first().replicas == 0)
        softly.assertThat(calculateResult[1].replicas == 1)
    }


    @Test
    fun `resolveGapTest with no gap`() {
        val originalResults = listOf(ResultGroup("a", 3), ResultGroup("b", 2), ResultGroup("c", 1))
        val finalResults = resolveGap(originalResults, 6)
        Assertions.assertEquals(originalResults, finalResults)
    }

    @Test
    fun `resolveGapTest with positive gap`() {
        val originalResults = listOf(ResultGroup("a", 3), ResultGroup("b", 2), ResultGroup("c", 1))
        // Add a gap of 1
        val finalResults = resolveGap(originalResults, 7)
        Assertions.assertEquals(listOf(ResultGroup("a", 4), ResultGroup("b", 2), ResultGroup("c", 1)), finalResults)
    }

    @Test
    fun `resolveGapTest with negative gap`() {
        val originalResults = listOf(ResultGroup("a", 3), ResultGroup("b", 2), ResultGroup("c", 1))
        // Remove a replica to create a gap of -1
        val finalResults = resolveGap(originalResults, 5)
        Assertions.assertEquals(listOf(ResultGroup("a", 2), ResultGroup("b", 2), ResultGroup("c", 1)), finalResults)
    }

    @Test
    fun `resolveGapTest with zero replicas in original results`() {
        val originalResults = listOf(ResultGroup("a", 0), ResultGroup("b", 0))
        val finalResults = resolveGap(originalResults, 0)
        Assertions.assertEquals(originalResults, finalResults)
    }

    @Test
    fun `resolveGapTest with negative replicas should throw exception`() {
        val originalResults = listOf(ResultGroup("a", -1), ResultGroup("b", 2))
        val exception = assertThrows<IllegalArgumentException> {
            resolveGap(originalResults, 3)
        }
        Assertions.assertEquals("replicas must be zero or positive int", exception.message)
    }

    @Test
    fun `resolveGapTest with negative finalReplica should throw exception`() {
        val originalResults = listOf(ResultGroup("a", 1), ResultGroup("b", 2))
        val exception = assertThrows<IllegalArgumentException> {
            resolveGap(originalResults, -1)
        }
        Assertions.assertEquals("finalReplica must be zero or positive int", exception.message)
    }

    @Test
    fun calculateDistribution_with_positive_operateReplicas_test() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 1),
            ToCalculateGroup.ofDefaultUnit("B", 2),
            ToCalculateGroup.ofDefaultUnit("C", 3)
        )
        val operateReplicas = 3
        val result = calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        val expectedTotal = groups.sumOf { it.replicas } + operateReplicas
        softly.assertThat(expectedTotal).isEqualTo(result.sumOf { it.replicas }) // 检查副本总数是否正确
        softly.assertThat(result.all { it.replicas >= 0 })
        for (group in result) {
            softly.assertThat(group.replicas).isEqualTo(expectedTotal / 3)
        }
    }

    @Test
    fun calculateDistribution_with_positive_weights_operateReplicas_test() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup("A", 1.0, 30),
            ToCalculateGroup("B", 2.0, 20),
            ToCalculateGroup("C", 3.0, 10)
        )
        val operateReplicas = 20
        val result = calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        val expectedTotal = groups.sumOf { it.replicas } + operateReplicas
        softly.assertThat(expectedTotal).isEqualTo(result.sumOf { it.replicas }) // 检查副本总数是否正确
        softly.assertThat(result.all { it.replicas >= 0 })
        softly.assertThat(result[0].replicas).isEqualTo(41)
        softly.assertThat(result[1].replicas).isEqualTo(26)
        softly.assertThat(result[2].replicas).isEqualTo(13)
    }

    @Test
    fun calculateDistribution_with_negative_operateReplicas_test() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 4),
            ToCalculateGroup.ofDefaultUnit("B", 6),
            ToCalculateGroup.ofDefaultUnit("C", 8)
        )
        val operateReplicas = -3
        val result = calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        val expectedTotal = groups.sumOf { it.replicas } + operateReplicas
        softly.assertThat(expectedTotal).isEqualTo(result.sumOf { it.replicas }) // 检查副本总数是否正确
        softly.assertThat(result.all { it.replicas >= 0 })
        for (group in result) {
            softly.assertThat(group.replicas).isEqualTo(expectedTotal / 3)
        }
    }

    @Test
    fun calculateDistribution_with_negative_weights_operateReplicas_test() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup("A", 2.0, 40),
            ToCalculateGroup("B", 2.0, 60),
            ToCalculateGroup("C", 3.0, 80)
        )
        val operateReplicas = -30
        val result = calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        val expectedTotal = groups.sumOf { it.replicas } + operateReplicas
        softly.assertThat(expectedTotal).isEqualTo(result.sumOf { it.replicas }) // 检查副本总数是否正确
        softly.assertThat(result.all { it.replicas >= 0 })
        softly.assertThat(result[0].replicas).isEqualTo(66)
        softly.assertThat(result[1].replicas).isEqualTo(42)
        softly.assertThat(result[2].replicas).isEqualTo(42)
    }

    @Test
    fun calculateDistribution_with_negative_weights_operateReplicas_test2() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup("A", 1.0, 10),
            ToCalculateGroup("B", 2.0, 20),
            ToCalculateGroup("C", 3.0, 30)
        )
        val operateReplicas = -60
        val result = calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        val expectedTotal = groups.sumOf { it.replicas } + operateReplicas
        softly.assertThat(expectedTotal).isEqualTo(result.sumOf { it.replicas }) // 检查副本总数是否正确
        softly.assertThat(result.all { it.replicas >= 0 })
        softly.assertThat(result[0].replicas).isEqualTo(0)
        softly.assertThat(result[0].replicas).isEqualTo(0)
        softly.assertThat(result[0].replicas).isEqualTo(0)
    }

    @Test
    fun calculateDistribution_with_negative_weights_operateReplicas_test3() {
        // 准备测试数据
        val groups = listOf(
            ToCalculateGroup("A", 5.0, 100),
            ToCalculateGroup("B", 2.0, 20),
            ToCalculateGroup("C", 3.0, 30)
        )
        val operateReplicas = -400
        exceptionTest({ e -> softly.assertThat(e.message).contains("final replicas cannot be negative") }) {
            calculateDistribution(toCalculateGroups = groups, operateReplicas = operateReplicas)
        }
    }

    @Test
    fun determineDecreaseToReplicas_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 10),
            ToCalculateGroup.ofDefaultUnit("B", 20),
            ToCalculateGroup.ofDefaultUnit("C", 30)
        )
        val toDecreaseReplicas = -15
        val result = determineDecreaseToReplicas(groups, toDecreaseReplicas)
        softly.assertThat(result.sumOf { it.replicas }).isEqualTo(groups.sumOf { it.replicas } + toDecreaseReplicas)
        softly.assertThat(result[0].replicas).isEqualTo(18)
        softly.assertThat(result[1].replicas).isEqualTo(17)
        softly.assertThat(result[2].replicas).isEqualTo(10)
    }

    @Test
    fun determineDecreaseToReplicas_weights_test() {
        val groups = listOf(
            ToCalculateGroup("A", 1.0, 10),
            ToCalculateGroup("B", 1.0, 20),
            ToCalculateGroup("C", 2.0, 20),
            ToCalculateGroup("d", 5.0, 20),
            ToCalculateGroup("e", 2.0, 30),
            ToCalculateGroup("f", 1.0, 50),
        )
        val toDecreaseReplicas = -30
        val result = determineDecreaseToReplicas(groups, toDecreaseReplicas)
        softly.assertThat(result.sumOf { it.replicas }).isEqualTo(groups.sumOf { it.replicas } + toDecreaseReplicas)
        softly.assertThat(result[0].replicas).isEqualTo(30)
        softly.assertThat(result[1].replicas).isEqualTo(20)
        softly.assertThat(result[2].replicas).isEqualTo(20)
        softly.assertThat(result[3].replicas).isEqualTo(20)
        softly.assertThat(result[4].replicas).isEqualTo(20)
        softly.assertThat(result[5].replicas).isEqualTo(10)
    }

    @Test
    fun determineIncreaseToReplicas_test() {
        val groups = listOf(
            ToCalculateGroup("A", 1.0, 10),
            ToCalculateGroup("B", 1.0, 20),
            ToCalculateGroup("C", 2.0, 20),
            ToCalculateGroup("d", 5.0, 20),
            ToCalculateGroup("e", 2.0, 30),
            ToCalculateGroup("f", 1.0, 50),
        )
        val toIncreaseReplicas = 30
        val result = determineIncreaseToReplicas(groups, toIncreaseReplicas)
        softly.assertThat(result.sumOf { it.replicas }).isEqualTo(groups.sumOf { it.replicas } + toIncreaseReplicas)
        softly.assertThat(result[0].replicas).isEqualTo(50)
        softly.assertThat(result[1].replicas).isEqualTo(50)
        softly.assertThat(result[2].replicas).isEqualTo(30)
        softly.assertThat(result[3].replicas).isEqualTo(20)
        softly.assertThat(result[4].replicas).isEqualTo(20)
        softly.assertThat(result[5].replicas).isEqualTo(10)
    }

    @Test
    fun decrease_replicas_under_total_sum_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 50),
            ToCalculateGroup.ofDefaultUnit("B", 30),
            ToCalculateGroup.ofZeroUnit("C", 20),
            ToCalculateGroup.ofZeroUnit("D", 10)
        )
        val toDeceaseReplicas = -15
        val result = calculateDecreaseToReplicas(groups, toDeceaseReplicas)
        // 预期减少后的副本分布结果
        val expectedResult = listOf(
            ResultGroup("A", 50), // 假设的结果
            ResultGroup("B", 30), // 假设的结果
            ResultGroup("C", 8),   // 权重为0的组先减少
            ResultGroup("D", 7)   // 权重为0的组先减少
        )
        softly.assertThat(expectedResult).isEqualTo(result)
    }

    @Test
    fun decrease_replicas_when_operateReplicas_is_zero_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 50),
            ToCalculateGroup.ofDefaultUnit("B", 30)
        )
        val toDeceaseReplicas = 0
        val result = calculateDecreaseToReplicas(groups, toDeceaseReplicas)
        softly.assertThat(groups.map { ResultGroup(it.uniqueKey, it.replicas) }).isEqualTo(result)
    }

    @Test
    fun decrease_replicas_when_operateReplicas_is_greater_than_total_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 10),
            ToCalculateGroup.ofDefaultUnit("B", 5)
        )
        val toDeceaseReplicas = -20

        // 预期抛出异常
        val exception = assertThrows<IllegalArgumentException> {
            calculateDecreaseToReplicas(groups, toDeceaseReplicas)
        }
        softly.assertThat(exception.message).contains("total sum must be equal or more than to decrease operateReplicas")
    }

    @Test
    fun decrease_all_replicas_to_zero_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 25),
            ToCalculateGroup.ofDefaultUnit("B", 25)
        )
        val toDeceaseReplicas = -50

        val result = calculateDecreaseToReplicas(groups, toDeceaseReplicas)

        val expectedResult = listOf(
            ResultGroup("A", 0),
            ResultGroup("B", 0)
        )
        softly.assertThat(expectedResult).isEqualTo(result)
    }

    @Test
    fun all_groups_have_zero_replicas_test() {
        val groups = listOf(
            ToCalculateGroup.ofDefaultUnit("A", 0),
            ToCalculateGroup.ofDefaultUnit("B", 0)
        )
        val toDeceaseReplicas = -10

        // 预期抛出异常，因为无法减少任何副本
        val exception = assertThrows<IllegalArgumentException> {
            calculateDecreaseToReplicas(groups, toDeceaseReplicas)
        }
        softly.assertThat(exception.message).contains("total sum must be equal or more than to decrease operateReplicas")
    }

    @Test
    fun all_groups_have_zero_weight_test() {
        val groups = listOf(
            ToCalculateGroup.ofZeroUnit("A", 10),
            ToCalculateGroup.ofZeroUnit("B", 10)
        )
        val toDeceaseReplicas = -5

        val result = calculateDecreaseToReplicas(groups, toDeceaseReplicas)

        val expectedResult = listOf(
            ResultGroup("A", 8),
            ResultGroup("B", 7)
        )
        softly.assertThat(expectedResult).isEqualTo(result)
    }

    @Test
    fun single_group_item_test() {
        val groups = listOf(ToCalculateGroup.ofDefaultUnit("A", 100))
        val toDeceaseReplicas = -50
        val result = calculateDecreaseToReplicas(groups, toDeceaseReplicas)
        val expectedResult = listOf(ResultGroup("A", 50))
        softly.assertThat(expectedResult).isEqualTo(result)
    }
}