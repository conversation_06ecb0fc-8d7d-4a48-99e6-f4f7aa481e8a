package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.atomcore.facade.param.StrategyParam
import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.exceptions.CallExternalSysException
import com.alibaba.koastline.multiclusters.common.exceptions.MetadataException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import io.mockk.every
import io.mockk.spyk
import io.mockk.verify
import org.junit.Rule
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import org.junit.rules.ExpectedException

class UserLabelBaseServiceTest {

    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()
    @Test
    fun testGetCpuRatio() {
        val strategyParamDaily = StrategyParam().apply {
            env = MetadataStageEnum.DAILY.name
        }
        val strategyParamNonDaily = StrategyParam().apply {
            env = MetadataStageEnum.PUBLISH.name
        }

        assertEquals("2", UserLabelBaseService.getCpuRatio(strategyParamDaily, "cainiao"))
        assertEquals("2", UserLabelBaseService.getCpuRatio(strategyParamDaily, "any_other_pool"))
        assertEquals("1", UserLabelBaseService.getCpuRatio(strategyParamNonDaily, "any_other_pool"))
    }

    @Test
    fun testGetResourcePool() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            every {
                commonProperties.contains(CommonProperties.VIDEO_CLOUD_APP_LIST, "VIDEO_APP")
            } returns true
            every {
                commonProperties.contains(CommonProperties.VIDEO_CLOUD_APP_LIST, "NORMAL_APP")
            } returns false
            every {
                commonProperties.contains(CommonProperties.VIDEO_CLOUD_APP_LIST, "SERVER_OWNER_APP")
            } returns false
            every {
                commonProperties.contains(CommonProperties.SERVER_OWNER_APP_LIST, "SERVER_OWNER_APP")
            } returns true
            every {
                commonProperties.contains(CommonProperties.SERVER_OWNER_APP_LIST, "NORMAL_APP")
            } returns false
            every {
                commonProperties.contains(CommonProperties.SERVER_OWNER_APP_LIST, "VIDEO_APP")
            } returns false
            every {
                appCenterApi.getAppInfoByName("VIDEO_APP")
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "VIDEO_APP",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every {
                appCenterApi.getAppInfoByName("NORMAL_APP")
            } returns AppInfo(
                id = 1L,
                buId = 47L,
                name = "NORMAL_APP",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every {
                appCenterApi.getAppInfoByName("SERVER_OWNER_APP")
            } returns AppInfo(
                id = 1L,
                buId = 3L,
                name = "SERVER_OWNER_APP",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        val strategyParamVideo = StrategyParam().apply {
            app = "VIDEO_APP"
        }
        assertEquals("video_cloud", userLabelBaseService.getResourcePool(strategyParamVideo))
        val strategyParamAnquan1 = StrategyParam().apply {
            app = "NORMAL_APP"
        }
        assertEquals("anquan", userLabelBaseService.getResourcePool(strategyParamAnquan1))
        val strategyParamAnquan2 = StrategyParam().apply {
            app = "NORMAL_APP"
            cell = "CENTER_UNIT.center"

        }
        assertEquals("sigma_public", userLabelBaseService.getResourcePool(strategyParamAnquan2))
        val strategyParamAnquan3 = StrategyParam().apply {
            app = "NORMAL_APP"
            cell = "CENTER_UNIT.center"
            site = "na62"

        }
        assertEquals("anquan", userLabelBaseService.getResourcePool(strategyParamAnquan3))
        val strategyParamAnquan4 = StrategyParam().apply {
            app = "NORMAL_APP"
            cell = "CENTER_UNIT.center"
            site = "sg52"

        }
        assertEquals("sigma_public", userLabelBaseService.getResourcePool(strategyParamAnquan4))
        val strategyParamServerOwner = StrategyParam().apply {
            app = "SERVER_OWNER_APP"
        }
        assertEquals("standalone", userLabelBaseService.getResourcePool(strategyParamServerOwner))

    }

    @Test
    fun testWhetherFallbackWithoutWildCard() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app1")
            } returns true
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app2")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*")
            } returns false
        }
        assertEquals(false, userLabelBaseService.whetherFallback("app1"))
        assertEquals(true, userLabelBaseService.whetherFallback("app2"))
    }

    @Test
    fun testWhetherFallbackWithWildCard() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app1")
            } returns true
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app2")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*")
            } returns true
        }
        assertEquals(false, userLabelBaseService.whetherFallback("app1"))
        assertEquals(false, userLabelBaseService.whetherFallback("app2"))
    }

    @Test
    fun testQueryStrategyWithoutCheck() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            every {
                whetherFallback("app1")
            } returns false
            every {
                getResourcePool(any<StrategyParam>())
            } returns UserLabelBaseService.RESOURCE_POOL.cainiao.name
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns true
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "app1",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        val ret = userLabelBaseService.queryStrategy(
            JsonUtils.writeValueAsString(StrategyParam().apply {
                app = "app1"
            })
        )
        assertEquals("cainiao", ret.resourcePool)
        assertEquals("2", ret.cpuRatio)
        verify(exactly = 0) { userLabelBaseService.atomApi.queryStrategy(any()) }

    }

    @Test
    fun testQueryStrategyWithCheckFail() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                getResourcePool(any<StrategyParam>())
            } returns UserLabelBaseService.RESOURCE_POOL.cainiao.name
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.queryStrategy(any())
            } returns StrategiesResultVO().apply {
                resourcePool = "sigma_public"
                cpuRatio = "2"
            }
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "app1",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        val ret = userLabelBaseService.queryStrategy(
            JsonUtils.writeValueAsString(StrategyParam().apply {
                app = "app1"
            })
        )
        assertEquals("sigma_public", ret.resourcePool)
        assertEquals("2", ret.cpuRatio)
        verify(exactly = 1) { userLabelBaseService.atomApi.queryStrategy(any()) }

    }

    @Test
    fun `testQueryStrategy SkipCheckWhenAtomException`() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                getResourcePool(any<StrategyParam>())
            } returns UserLabelBaseService.RESOURCE_POOL.cainiao.name
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.queryStrategy(any())
            } throws CallExternalSysException("atom queryStrategy failed!")
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "app1",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        val ret = userLabelBaseService.queryStrategy(
            JsonUtils.writeValueAsString(StrategyParam().apply {
                app = "app1"
            })
        )
        assertEquals("cainiao", ret.resourcePool)
        assertEquals("2", ret.cpuRatio)
        verify(exactly = 1) { userLabelBaseService.atomApi.queryStrategy(any()) }
        verify(exactly = 0) { userLabelBaseService.checkService.checkQueryStrategy(any(), any(), any()) }

    }

    @Test
    fun testQueryStrategyWithOfflineApp() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                getResourcePool(any<StrategyParam>())
            } returns UserLabelBaseService.RESOURCE_POOL.cainiao.name
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                atomApi.queryStrategy(any())
            } returns StrategiesResultVO().apply {
                resourcePool = "sigma_public"
                cpuRatio = "2"
            }
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "app1",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("OFFLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        exceptionRule.expect(MetadataException::class.java)
        exceptionRule.expectMessage("app1应用不存在!")
        userLabelBaseService.queryStrategy(
            JsonUtils.writeValueAsString(StrategyParam().apply {
                app = "app1"
            })
        )

    }

    @Test
    fun testQueryStrategyWithCheckSuccess() {
        val userLabelBaseService = spyk(UserLabelBaseService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                getResourcePool(any<StrategyParam>())
            } returns UserLabelBaseService.RESOURCE_POOL.cainiao.name
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.queryStrategy(any())
            } returns StrategiesResultVO().apply {
                resourcePool = "cainiao"
                cpuRatio = "2"
            }
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "app1",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
        }
        val ret = userLabelBaseService.queryStrategy(
            JsonUtils.writeValueAsString(StrategyParam().apply {
                app = "app1"
            })
        )
        assertEquals("cainiao", ret.resourcePool)
        assertEquals("2", ret.cpuRatio)
        verify(exactly = 1) { userLabelBaseService.atomApi.queryStrategy(any()) }

    }


}