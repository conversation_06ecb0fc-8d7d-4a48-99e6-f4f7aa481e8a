package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.FunnelTreeNode
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.Group
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.NameSpaceSplitter
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.PrefixUniqueKeyDistributionStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.RootStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.calculateDecreaseToReplicas
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.getFunnelTreeWithMatchPrefixUniqueKey
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.getStrategyTree
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.reconcileFunnelTree
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.visitSubTree
import org.junit.Test
import testutils.BaseTest
import kotlin.random.Random

class FunnelTreeTest : BaseTest() {

    @Test
    fun getFunnelTreeWithMatchPrefixUniqueKeyTest() {
        // test getFunnelTreeWithMatchPrefixUniqueKey
        val strategy = getStrategyTree(getPrefixStrategyTree())
        val funnelTree = getFunnelTreeWithMatchPrefixUniqueKey(
            groups = testThreeTuplesData(), strategyTree = strategy
        )
        funnelTree.validateTree()
        val visitPath = mutableListOf<String>()
        val leafNodes = mutableListOf<FunnelTreeNode<ThreeTuples>>()
        val leafNodesWithElem = mutableListOf<FunnelTreeNode<ThreeTuples>>()
        funnelTree.visitSubTree { cur ->
            visitPath.add(cur.uniqueKey)
            if (cur.subNodes.isEmpty()) {
                leafNodes.add(cur)
            }
            if (cur.isLeafNodeWithElem()) {
                leafNodesWithElem.add(cur)
            }
        }
        val correctPath = listOf(
            "",
            "x1",
            "x1&y1&z1",
            "x1&y2&z2",
            "x1&y3&z3",
            "x1&y4&z4",
            "x2",
            "x2&y2",
            "x2&y2&z6",
            "x2&y3",
            "x2&y3&z1",
            "x2&y3&z7",
            "x2&y1&z5",
            "x2&y4&z8"
        )
        softly.assertThat(visitPath).isEqualTo(correctPath)
        val a1 = leafNodes.map { it.uniqueKey }.sorted()
        val e1 = testThreeTuplesData().map { it.getGroupUniqueKey() }.sorted()
        softly.assertThat(a1.size - e1.size).isPositive
        softly.assertThat(e1).isEqualTo(leafNodesWithElem.map { it.uniqueKey }.sorted())
    }

    @Test
    fun treeSumTest() {
        val actualDistributions = testThreeTuplesData()
        val strategy = getStrategyTree(getPrefixStrategyTree())
        val funnelTree = getFunnelTreeWithMatchPrefixUniqueKey(
            groups = actualDistributions, strategyTree = strategy
        )
        softly.assertThat(actualDistributions.sumOf { it.getGroupReplicas() }).isEqualTo(funnelTree.beforeTotal)
    }

    @Test
    fun treeReconcileTest() {
        val actualDistributions = testThreeTuplesData()
        val strategy = getStrategyTree(getPrefixStrategyTree())
        val funnelTree = getFunnelTreeWithMatchPrefixUniqueKey(
            groups = actualDistributions, strategyTree = strategy
        )
        val toDecrease = -30
        reconcileFunnelTree(
            root = funnelTree, operateReplicas = toDecrease
        ) { toCalculateGroups, operateReplicas ->
            calculateDecreaseToReplicas(toCalculateGroups = toCalculateGroups, operateReplicas = operateReplicas)
        }
        softly.assertThat(funnelTree.afterTotal - toDecrease).isEqualTo(funnelTree.beforeTotal)
        val withElemLeafNodes = mutableListOf<FunnelTreeNode<ThreeTuples>>()
        funnelTree.visitSubTree {
            if (it.isLeafNodeWithElem()) {
                withElemLeafNodes.add(it)
            }
        }
        softly.assertThat(withElemLeafNodes.sumOf { it.beforeTotal - it.afterTotal }).isEqualTo(-toDecrease)
    }

    private fun getPrefixStrategyTree(): PrefixUniqueKeyDistributionStrategy {
        return PrefixUniqueKeyDistributionStrategy(
            uniqueKey = RootStrategy,
            weight = 100,
            subStrategies = listOf(
                PrefixUniqueKeyDistributionStrategy(
                    uniqueKey = "x1",
                    weight = 100,
                ),
                PrefixUniqueKeyDistributionStrategy(
                    uniqueKey = "x2",
                    weight = 100,
                    subStrategies = listOf(
                        PrefixUniqueKeyDistributionStrategy(
                            uniqueKey = "x2&y2",
                            weight = 100,
                        ),
                        PrefixUniqueKeyDistributionStrategy(
                            uniqueKey = "x2&y3",
                            weight = 100,
                            subStrategies = listOf(
                                PrefixUniqueKeyDistributionStrategy(
                                    uniqueKey = "x2&y3&z1",
                                    weight = 100
                                )
                            )
                        )
                    )
                )
            )
        )
    }

    /**
     * way of differentiating [x -> y -> z]
     * which represent abstract of coordinate
     *
     * @property x
     * @property y
     * @property z
     */
    data class ThreeTuples(
        val x: String,
        val y: String,
        val z: String,
        val replica: Int = 5 + Random.nextInt(100),
        val weight: Double = Random.nextInt(1, 5).toDouble()
    ) : Group {
        override fun getGroupReplicas(): Int {
            return replica
        }

        override fun getGroupUniqueKey(): String {
            return x + NameSpaceSplitter + y + NameSpaceSplitter + z
        }

        fun getGroupWeight(): Double {
            return weight
        }
    }

    private fun testThreeTuplesData(): List<ThreeTuples> {
        return listOf(
            ThreeTuples(x = "x1", y = "y1", z = "z1"),
            ThreeTuples(x = "x1", y = "y2", z = "z2"),
            ThreeTuples(x = "x1", y = "y3", z = "z3"),
            ThreeTuples(x = "x1", y = "y4", z = "z4"),
            ThreeTuples(x = "x2", y = "y1", z = "z5"),
            ThreeTuples(x = "x2", y = "y2", z = "z6"),
            ThreeTuples(x = "x2", y = "y3", z = "z7"),
            ThreeTuples(x = "x2", y = "y4", z = "z8"),
        )
    }

    private fun generateRandomThreeTuples(): List<ThreeTuples> {
        val xAvailableList = List(Random.nextInt(1, 5)) { "x" + Random.nextInt(1, 10) }
        val yAvailableList = List(Random.nextInt(1, 5)) { "y" + Random.nextInt(1, 10) }
        val zAvailableList = List(Random.nextInt(1, 5)) { "z" + Random.nextInt(1, 10) }
        val xCrossYCrossZ =
            calculateTheCrossProduct(calculateTheCrossProduct(xAvailableList, yAvailableList), zAvailableList)
        return xCrossYCrossZ.map { it.split(NameSpaceSplitter) }.map {
            ThreeTuples(x = it[0], y = it[1], z = it[2])
        }.distinctBy { it.getGroupUniqueKey() }
    }

    private fun calculateTheCrossProduct(part1: List<String>, part2: List<String>): List<String> {
        val finalCollections = mutableListOf<String>()
        part1.forEach { outer ->
            part2.forEach { inner ->
                finalCollections.add(outer + NameSpaceSplitter + inner)
            }
        }
        return finalCollections
    }
}