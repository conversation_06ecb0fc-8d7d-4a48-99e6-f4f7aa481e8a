package com.alibaba.koastline.multiclusters.fed.model.req

import org.junit.Assert
import org.junit.Test

/**
 * <AUTHOR>
 * date 2024/3/19 14:38
 */
class FedClusterPolicyApiCreateReqTest {

    @Test
    fun validateFedClusterPolicyApiCreateReqTest() {
        val reqWithoutSchedulePlugin = FedClusterPolicyApiCreateReq(
            "test-tenant-cluster",
            "test-policy",
            10,
            "Pod",
            policySpec = mapOf(
                "policy" to mapOf(
                    "ratios" to listOf(mapOf(
                        "clusterName" to "aaa",
                        "value" to 10
                    )),
                    "schedulePlugin" to listOf()
                ),
            ),
            creator = null
        )
        try {
            reqWithoutSchedulePlugin.validate()
            Assert.fail()
        } catch (e: IllegalArgumentException) {
            Assert.assertEquals("creator required", e.message)
        }
    }

}