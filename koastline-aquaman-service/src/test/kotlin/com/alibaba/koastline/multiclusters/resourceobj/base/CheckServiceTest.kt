package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.atomcore.facade.param.StrategyParam
import com.alibaba.atomcore.facade.result.strategy.StrategiesResultVO
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.exceptions.MetadataException
import com.alibaba.koastline.multiclusters.resourceobj.DispatchLabelService
import io.mockk.spyk
import org.junit.Test
import org.junit.jupiter.api.Assertions.*

class CheckServiceTest {
    @Test
    fun testCheckQueryStrategySuccess() {
        val strategyParam = StrategyParam().apply {
            app = "testApp"
            env = MetadataStageEnum.DAILY.name
        }

        val aquamanRet = StrategiesResultVO().apply {
            cpuRatio = "1"
            resourcePool = UserLabelBaseService.RESOURCE_POOL.sigma_public.name
        }

        val atomRet = StrategiesResultVO().apply {
            cpuRatio = "1"
            resourcePool = UserLabelBaseService.RESOURCE_POOL.sigma_public.name
        }
        val checkService = spyk(CheckService()) {

        }
        checkService.checkQueryStrategy(strategyParam, aquamanRet, atomRet)

    }

    @Test
    fun testCheckQueryStrategyFail() {
        val strategyParam = StrategyParam().apply {
            app = "testApp"
            env = MetadataStageEnum.DAILY.name
        }

        val aquamanRet = StrategiesResultVO().apply {
            cpuRatio = "1"
            resourcePool = UserLabelBaseService.RESOURCE_POOL.sigma_public.name
        }

        val atomRet = StrategiesResultVO().apply {
            cpuRatio = "1"
            resourcePool = UserLabelBaseService.RESOURCE_POOL.anquan.name
        }
        val checkService = spyk(CheckService()) {

        }
        org.junit.jupiter.api.assertThrows<MetadataException> {
            checkService.checkQueryStrategy(strategyParam, aquamanRet, atomRet)
        }

    }

    @Test
    fun testCheckGetSigmaConfigMapSuccess() {

        val checkService = spyk(CheckService()) {
        }
        val aquamanRet = """
            {
              "data": {
                "constraints": "{}",
                "spread": "{}",
                "allocSpec": "{\"CpuSetMode\":\"share\"}",
                "hostConfig": "{}",
                "extConfig": "{}",
                "prohibit": "{}",
                "monopolize": "{}"
              }
            }
        """.trimIndent()

        val atomRet = """
            {
              "data": {
                "constraints": "{}",
                "spread": "{}",
                "allocSpec": "{\"CpuSetMode\":\"share\"}",
                "hostConfig": "{}",
                "extConfig": "{}",
                "prohibit": "{}",
                "monopolize": "{}"
              }
            }
        """.trimIndent()

        checkService.checkGetSigmaConfigMap(
            appName = "test",
            atomData = DispatchLabelService.getSigmaConfigMapFromString(atomRet),
            aquamanData = DispatchLabelService.getSigmaConfigMapFromString(aquamanRet)
        )

    }

    @Test
    fun testCheckGetSigmaConfigMapFail() {

        val checkService = spyk(CheckService()) {
        }
        val aquamanRet = """
            {
              "data": {
                "constraints": "{}",
                "spread": "{}",
                "allocSpec": "{\"CpuSetMode\":\"share\"}",
                "hostConfig": "{}",
                "extConfig": "{}",
                "prohibit": "{}",
                "monopolize": "{}"
              }
            }
        """.trimIndent()

        val atomRet = """
            {
              "data": {
                "constraints": "{}",
                "spread": "{}",
                "allocSpec": "{\"CpuSetMode\":\"set\"}",
                "hostConfig": "{}",
                "extConfig": "{}",
                "prohibit": "{}",
                "monopolize": "{}"
              }
            }
        """.trimIndent()

        org.junit.jupiter.api.assertThrows<MetadataException> {
            checkService.checkGetSigmaConfigMap(
                appName = "test",
                atomData = DispatchLabelService.getSigmaConfigMapFromString(atomRet),
                aquamanData = DispatchLabelService.getSigmaConfigMapFromString(aquamanRet)
            )
        }

    }


}