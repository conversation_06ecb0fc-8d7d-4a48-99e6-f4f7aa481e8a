package com.alibaba.koastline.multiclusters.resourceobj.utils

import com.alibaba.koastline.multiclusters.common.exceptions.ThreeWayMergeDiffException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectPatchService
import com.alibaba.koastline.multiclusters.resourceobj.model.PatchStrategyDefinition
import net.javacrumbs.jsonunit.assertj.assertThatJson
import net.javacrumbs.jsonunit.core.Option
import org.junit.Assert
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.io.StringReader
import javax.json.Json
import javax.json.JsonValue


class ThreeWayMergeTest {

    @Test
    fun `test whether json value is boolean`() {
        val value1 = JsonValue.TRUE
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value1))
        val value2 = JsonValue.FALSE
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value2))
        val value3 = JsonValue.EMPTY_JSON_ARRAY
        Assert.assertFalse(ThreeWayMerge.whetherJsonBoolean(value3))
        val value4 = JsonValue.EMPTY_JSON_OBJECT
        Assert.assertFalse(ThreeWayMerge.whetherJsonBoolean(value4))
        val value5 = Json.createValue("true")
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value5))
        val value6 = Json.createValue("false")
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value6))
        val value7 = Json.createValue("NaN")
        Assert.assertFalse(ThreeWayMerge.whetherJsonBoolean(value7))
        val value8 = Json.createValue("""
            'true'
        """.trimIndent())
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value8))
        val value9 = Json.createValue("""
            "true"
        """.trimIndent())
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value9))
        val value10 = Json.createValue("""
            true
        """.trimIndent())
        Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value10))
      val value11 = Json.createValue("""
            yes
        """.trimIndent())
      Assert.assertTrue(ThreeWayMerge.whetherJsonBoolean(value11))

    }

    @Test
    fun `test whether json value is number`() {
        val value1 = JsonValue.TRUE
        Assert.assertFalse(ThreeWayMerge.whetherJsonNumber(value1))
        val value3 = JsonValue.EMPTY_JSON_ARRAY
        Assert.assertFalse(ThreeWayMerge.whetherJsonNumber(value3))
        val value4 = JsonValue.EMPTY_JSON_OBJECT
        Assert.assertFalse(ThreeWayMerge.whetherJsonNumber(value4))
        val value5 = Json.createValue("true")
        Assert.assertFalse(ThreeWayMerge.whetherJsonNumber(value5))
        val value6 = Json.createValue("123232")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value6))
        val value7 = Json.createValue("23.45")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value7))
        val value8 = Json.createValue("100%")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value8))
        val value9 = Json.createValue("32%")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value9))
        val value10 = Json.createValue("32.34%")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value10))
        val value11 = Json.createValue("0%")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value11))
        val value12 = Json.createValue("1823608139307835,1286411707916676")
        Assert.assertTrue(ThreeWayMerge.whetherJsonNumber(value12))

    }

    @Test
    fun `boolean diff`() {
        val originalJson = """
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "main",
            "env": null,
            "volumeMounts": [
              {
                "name": "acni-crevol-cmms-measure-info",
                "readOnly": true,
                "mountPath": "/etc/cmms"
              }
            ]
          }
        ]
      }
    }
  }
}
        """.trimIndent()
        val modifiedJson = """
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "main",
            "env": null,
            "volumeMounts": [
              {
                "name": "acni-crevol-cmms-measure-info",
                "readOnly": "true",
                "mountPath": "/etc/cmms"
              }
            ]
          }
        ]
      }
    }
  }
}
        """.trimIndent()
        val expectedPatch = """
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "main",
            "volumeMounts": [
              {
                "name": "acni-crevol-cmms-measure-info",
                "readOnly": "true"
              }
            ]
          }
        ]
      }
    }
  }
}
        """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, expectedPatch)
    }

    @Test
    fun `diffMerge source is empty`() {
        val originalJson = """[]""".trimIndent()
        val modifiedJson = """
                [
               
                	{
                		"name": "n4",
                		"hostPath": {
                			"path": "/tmp",
                			"name": "n2"
                		}
                	}
                ]
                """.trimIndent()
        val expectedPatch = """
            [
                {
                    "name": "n4",
                    "hostPath": {
                        "path": "/tmp",
                        "name": "n2"
                    }
                }
            ]
        """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, expectedPatch)

    }

    @Test
    fun `diffMerge target is empty`() {

        val originalJson = """
                [
               
                	{
                		"name": "n4",
                		"hostPath": {
                			"path": "/tmp",
                			"name": "n2"
                		}
                	}
                ]
                """.trimIndent()
        val modifiedJson = """[]""".trimIndent()
        val expectedPatch = """
            [
                {
                    "${"$"}patch": "delete",
                    "name": "n4"
                }
            ]
        """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, expectedPatch)

    }
    @Test
    fun `diffMerge array same type`() {
        val originalJson = """
               [
               	{
               		"name": "n1",
               		"emptyDir": {
               			"sizeLimit": "10"
               		}
               	},
               	{
               		"name": "n2",
               		"emptyDir": {}
               	},
               	{
               		"name": "n3",
               		"emptyDir": {}
               	}
               ]
                """.trimIndent()
        val modifiedJson = """
                [
                	{
                		"name": "n1",
                		"emptyDir": {}
                	},
                	{
                		"name": "n2",
                		"emptyDir": {
                			"sizeLimit": "10"
                		}
                	},
                	{
                		"name": "n4",
                		"hostPath": {
                			"path": "/tmp",
                			"name": "n2"
                		}
                	}
                ]
                """.trimIndent()
        val expectedPatch = """
            [
                {
                    "emptyDir": {
                        "sizeLimit": null
                    },
                    "name": "n1"
                },
                {
                    "emptyDir": {
                        "sizeLimit": "10"
                    },
                    "name": "n2"
                },
                {
                    "name": "n3",
                    "${"$"}patch": "delete"
                },
                {
                    "name": "n4",
                    "hostPath": {
                        "path": "/tmp",
                        "name": "n2"
                    }
                }
            ]
        """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, expectedPatch)

    }

    @Test
    fun `diffMerge array different type v1`() {
        val originalJson = """
              
               [
                	"name",
                	{
                		"name": "n2",
                		"emptyDir": {
                			"sizeLimit": "10"
                		}
                	}
                ]
                """.trimIndent()
        val modifiedJson = """
                [
               	{
               		"name": "n1",
               		"emptyDir": {
               			"sizeLimit": "10"
               		}
               	},
               	{
               		"name": "n2",
               		"emptyDir": {}
               	}
               ]
                """.trimIndent()
        val expectedPatch = """""".trimIndent()
        assertThrows<ThreeWayMergeDiffException> {
            diffMergeTest(originalJson, modifiedJson, expectedPatch)
        }



    }

    @Test
    fun `diffMerge array different type v2`() {
        val originalJson = """
              
               
                [
               	{
               		"name": "n1",
               		"emptyDir": {
               			"sizeLimit": "10"
               		}
               	},
               	{
               		"name": "n2",
               		"emptyDir": {}
               	}
               ]
                """.trimIndent()
        val modifiedJson = """
                [
                	"name",
                	{
                		"name": "n2",
                		"emptyDir": {
                			"sizeLimit": "10"
                		}
                	}
                ]
                """.trimIndent()
        val expectedPatch = """""".trimIndent()
        assertThrows<ThreeWayMergeDiffException> {
            diffMergeTest(originalJson, modifiedJson, expectedPatch)
        }


    }





    @Test
    fun diffMergeSimple() {
        val originalJson = """
                {
                	"spec": {
                		"template": {
                    	"metadata": {
                        "annotations": {
                          "alibabacloud.com/ip-stack": "ipv4"
                        }
                    	},
                			"spec": {
                				"containers": [{
                					"name": "staragent",
                					"resources": {
                						"limits": {
                							"cpu": "2",
                							"memory": "1Gi"
                						},
                						"requests": {
                							"cpu": "200m",
                							"memory": "1Gi"
                						}
                					}
                				}],
                				"volumes": [{
                						"name": "n1",
                						"emptyDir": {
                							"sizeLimit": "10"
                						}
                					},
                					{
                						"name": "n2",
                						"emptyDir": {}
                					},
                					{
                						"name": "n3",
                						"emptyDir": {}
                					}

                				]
                			}
                		}
                	}
                }
                """.trimIndent()
        val modifiedJson = """
                {
                	"spec": {
                		"template": {
                			"spec": {
                				"containers": [{
                					"name": "staragent",
                					"resources": {
                						"limits": {
                							"cpu": "1",
                							"memory": "1Gi"
                						},
                						"requests": {
                							"cpu": "200m",
                							"memory": "1Gi"
                						}
                					}
                				}],
                				"volumes": [{
                						"name": "n1",
                						"emptyDir": {}
                					},
                					{
                						"name": "n2",
                						"emptyDir": {
                							"sizeLimit": "10"
                						}
                					},
                					{
                						"name": "n4",
                						"hostPath": {
                							"path": "/tmp",
                							"name": "n2"
                						}
                					}
                				]
                			}
                		}
                	}
                }
                """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, """
            {
                "spec": {
                    "template": {
                        "metadata": {
                            "annotations": {
                                "alibabacloud.com/ip-stack": null
                            }
                        },
                        "spec": {
                            "containers": [
                                {
                                    "resources": {
                                        "limits": {
                                            "cpu": "1",
                                            "memory": "1Gi"
                                        },
                                        "requests": {
                                            "cpu": "200m",
                                            "memory": "1Gi"
                                        }
                                    },
                                    "name": "staragent"
                                }
                            ],
                            "volumes": [
                                {
                                    "emptyDir": {
                                        "sizeLimit": null
                                    },
                                    "name": "n1"
                                },
                                {
                                    "emptyDir": {
                                        "sizeLimit": "10"
                                    },
                                    "name": "n2"
                                },
                                {
                                    "name": "n3",
                                    "${"$"}patch": "delete"
                                },
                                {
                                    "name": "n4",
                                    "hostPath": {
                                        "path": "/tmp",
                                        "name": "n2"
                                    }
                                }
                            ]
                        }
                    }
                }
            }

        """.trimIndent())

    }


    @Test
    fun diffMergeProduction() {
        val originalJson = """
        {
            "metadata": {
                "name": "alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3",
                "namespace": "alsc-adv-l-model",
                "uid": "910b81ce-80b5-4ac9-9ea4-fd32ed360542",
                "resourceVersion": "38039603784",
                "generation": 9,
                "creationTimestamp": "2023-07-21T02:37:29Z",
                "labels": {
                    "apps.kruise.io/rollout-batch-id": "0",
                    "apps.kruise.io/rollout-id": "127318589",
                    "inplaceset.sigma.ali/proxy": "CloneSet",
                    "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
                    "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
                    "sigma.ali/app-name": "alsc-adv-l-model",
                    "sigma.ali/disable-default-pdb-strategy": "true",
                    "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                    "sigma.ali/site": "na610",
                    "sigma.ali/subgroup": "default",
                    "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                    "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
                    "statefulset.sigma.ali/mode": "sigma"
                },
                "annotations": {
                    "cloneset.beta1.sigma.ali/app-fail-count": "0",
                    "cloneset.beta1.sigma.ali/expected-updated-replicas": "1",
                    "cloneset.beta1.sigma.ali/image-fail-count": "0",
                    "cloneset.beta1.sigma.ali/publish-success-replicas": "1",
                    "cloneset.beta1.sigma.ali/scheduled-fail-count": "0",
                    "inplaceset.beta1.sigma.ali/upgrade-scatter": "pod.beta1.sigma.ali/naming-register-state=working_online",
                    "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{\"statefulset.beta1.sigma.ali/partition\":\"0%\",\"statefulset.beta1.sigma.ali/pod-upgrade-timeout\":\"1800\",\"statefulset.beta1.sigma.ali/publish-id\":\"127318589\",\"statefulset.beta1.sigma.ali/upgrade-max-unavailable\":\"1\"},\"labels\":{\"apps.kruise.io/rollout-batch-id\":\"0\",\"apps.kruise.io/rollout-id\":\"127318589\"},\"name\":\"alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3\",\"namespace\":\"alsc-adv-l-model\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1800\\\", \\\"ImagePullTimeoutSeconds\\\":\\\"600\\\", \\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"beta\"},{\"name\":\"trafficRouteLabel\",\"value\":\"beta\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish\",\"name\":\"main\",\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/alsc-adv-l-model/logs\",\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":1,\"volumes\":[{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}},\"updateStrategy\":{\"rollingUpdate\":{\"partition\":100000000}}}}\n",
                    "sigma.ali/disable-cascading-deletion": "true",
                    "statefulset.beta1.sigma.ali/apprules-update-required": "false",
                    "statefulset.beta1.sigma.ali/partition": "0%",
                    "statefulset.beta1.sigma.ali/pod-upgrade-timeout": "1800",
                    "statefulset.beta1.sigma.ali/pods-to-delete": "9d46d207-0e36-432d-ab5c-62fd5ba969f4",
                    "statefulset.beta1.sigma.ali/publish-id": "127318589",
                    "statefulset.beta1.sigma.ali/updated-ready-replicas": "1",
                    "statefulset.beta1.sigma.ali/upgrade-max-unavailable": "1",
                    "sigma.ali/upgrade-merge-annotations": "alibabacloud.com/lightweight-container,pod.beta1.sigma.ali/disable-lifecycle-hook-inject"
                },
                "finalizers": [
                    "alibabacloud.com/proxy-to-cloneset"
                ]
            },
            "spec": {
                "replicas": 1,
                "selector": {
                    "matchLabels": {
                        "sigma.ali/app-name": "alsc-adv-l-model",
                        "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                        "sigma.ali/site": "na610",
                        "sigma.ali/subgroup": "default",
                        "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                        "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
                    }
                },
                "template": {
                    "metadata": {
                        "creationTimestamp": null,
                        "labels": {
                            "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
                            "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
                            "pod.beta1.sigma.ali/naming-register-state": "working_online",
                            "sigma.ali/app-name": "alsc-adv-l-model",
                            "sigma.ali/container-model": "dockervm",
                            "sigma.ali/inject-staragent-sidecar": "true",
                            "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                            "sigma.ali/site": "na610",
                            "sigma.ali/subgroup": "default",
                            "sigma.ali/upstream-component": "normandy",
                            "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                            "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
                        },
                        "annotations": {
                            "alibabacloud.com/ip-stack": "ipv4",
                            "pod.beta1.alibabacloud.com/container-cpu-quota-unlimit": "true",
                            "pod.beta1.alibabacloud.com/zoom-in-cpu-quota": "1.2",
                            "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
                            "pod.beta1.sigma.ali/apprules": "{\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"constraints\":\"{}\",\"extConfig\":\"{}\",\"hostConfig\":\"{}\",\"monopolize\":\"{}\",\"prohibit\":\"{}\",\"resource_pool\":\"sigma_public\",\"spread\":\"{}\"}",
                            "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
                            "pod.beta1.sigma.ali/hostname-template": "alsc-adv-l-model{{.IpAddress}}.center.na610",
                            "pod.beta1.sigma.ali/naming-register-state": "working_online",
                            "sigma.ali/enable-apprules-injection": "true",
                            "sigma.ali/use-unified-pv": "true"
                        }
                    },
                    "spec": {
                        "volumes": [
                            {
                                "name": "44cde429260b721f6a8c26b99ab1601b",
                                "emptyDir": {}
                            },
                            {
                                "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                "emptyDir": {}
                            },
                            {
                                "name": "79e9ba5367e6c330f6e45be47de0bfad",
                                "emptyDir": {}
                            }
                        ],
                        "containers": [
                            {
                                "name": "main",
                                "image": "hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish",
                                "env": [
                                    {
                                        "name": "ali_aone_timestamp",
                                        "value": "*************"
                                    },
                                    {
                                        "name": "ali_start_app",
                                        "value": "no"
                                    },
                                    {
                                        "name": "ali_run_mode",
                                        "value": "common_vm"
                                    },
                                    {
                                        "name": "envSign",
                                        "value": "beta"
                                    },
                                    {
                                        "name": "trafficRouteLabel",
                                        "value": "beta"
                                    }
                                ],
                                "resources": {
                                    "limits": {
                                        "cpu": "64",
                                        "ephemeral-storage": "1099511627776",
                                        "memory": "206158430208",
                                        "nvidia.com/gpu": "8"
                                    },
                                    "requests": {
                                        "cpu": "64",
                                        "ephemeral-storage": "1099511627776",
                                        "memory": "206158430208",
                                        "nvidia.com/gpu": "8"
                                    }
                                },
                                "volumeMounts": [
                                    {
                                        "name": "44cde429260b721f6a8c26b99ab1601b",
                                        "mountPath": "/home/<USER>/logs"
                                    },
                                    {
                                        "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                        "mountPath": "/home/<USER>/cai/logs"
                                    },
                                    {
                                        "name": "79e9ba5367e6c330f6e45be47de0bfad",
                                        "mountPath": "/home/<USER>/alsc-adv-l-model/logs"
                                    }
                                ],
                                "terminationMessagePath": "/dev/termination-log",
                                "terminationMessagePolicy": "File",
                                "imagePullPolicy": "IfNotPresent"
                            }
                        ],
                        "restartPolicy": "Always",
                        "terminationGracePeriodSeconds": 1,
                        "dnsPolicy": "Default",
                        "automountServiceAccountToken": false,
                        "securityContext": {},
                        "affinity": {
                            "nodeAffinity": {
                                "requiredDuringSchedulingIgnoredDuringExecution": {
                                    "nodeSelectorTerms": [
                                        {
                                            "matchExpressions": [
                                                {
                                                    "key": "sigma.ali/resource-pool",
                                                    "operator": "In",
                                                    "values": [
                                                        "sigma_public"
                                                    ]
                                                },
                                                {
                                                    "key": "sigma.ali/is-ecs",
                                                    "operator": "In",
                                                    "values": [
                                                        "true"
                                                    ]
                                                },
                                                {
                                                    "key": "alibabacloud.com/gpu-card-model-detail",
                                                    "operator": "In",
                                                    "values": [
                                                        "Tesla-V100-SXM2-32GB"
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        },
                        "schedulerName": "default-scheduler",
                        "enableServiceLinks": false
                    }
                },
                "serviceName": "",
                "podManagementPolicy": "OrderedReady",
                "updateStrategy": {
                    "type": "RollingUpdate",
                    "rollingUpdate": {
                        "partition": 100000000
                    }
                },
                "revisionHistoryLimit": 10
            }
        }
    """.trimIndent()

        val modifiedJson = """
        {
    "metadata": {
        "name": "alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3",
        "namespace": "alsc-adv-l-model",
        "uid": "910b81ce-80b5-4ac9-9ea4-fd32ed360542",
        "resourceVersion": "38039603784",
        "generation": 10,
        "creationTimestamp": "2023-07-21T02:37:29Z",
        "labels": {
            "apps.kruise.io/rollout-batch-id": "0",
            "apps.kruise.io/rollout-id": "1273185",
            "inplaceset.sigma.ali/proxy": "CloneSet",
            "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
            "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
            "sigma.ali/app-name": "alsc-adv-l-model",
            "sigma.ali/disable-default-pdb-strategy": "true",
            "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
            "sigma.ali/site": "na620",
            "sigma.ali/subgroup": "default",
            "sigma.alibaba-inc.com/app-stage": "PUBLISH",
            "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
            "statefulset.sigma.ali/mode": "sigma",
            "aquaman": "test"
        },
        "annotations": {
            "cloneset.beta1.sigma.ali/app-fail-count": "1",
            "cloneset.beta1.sigma.ali/expected-updated-replicas": "1",
            "cloneset.beta1.sigma.ali/image-fail-count": "0",
            "cloneset.beta1.sigma.ali/publish-success-replicas": "2",
            "cloneset.beta1.sigma.ali/scheduled-fail-count": "0",
            "inplaceset.beta1.sigma.ali/upgrade-scatter": "pod.beta1.sigma.ali/naming-register-state=working_online",
            "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{\"statefulset.beta1.sigma.ali/partition\":\"0%\",\"statefulset.beta1.sigma.ali/pod-upgrade-timeout\":\"1800\",\"statefulset.beta1.sigma.ali/publish-id\":\"127318589\",\"statefulset.beta1.sigma.ali/upgrade-max-unavailable\":\"1\"},\"labels\":{\"apps.kruise.io/rollout-batch-id\":\"0\",\"apps.kruise.io/rollout-id\":\"127318589\"},\"name\":\"alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3\",\"namespace\":\"alsc-adv-l-model\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1800\\\", \\\"ImagePullTimeoutSeconds\\\":\\\"600\\\", \\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"beta\"},{\"name\":\"trafficRouteLabel\",\"value\":\"beta\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish\",\"name\":\"main\",\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/alsc-adv-l-model/logs\",\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":1,\"volumes\":[{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}},\"updateStrategy\":{\"rollingUpdate\":{\"partition\":100000000}}}}\n",
            "sigma.ali/disable-cascading-deletion": "true",
            "statefulset.beta1.sigma.ali/apprules-update-required": "false",
            "statefulset.beta1.sigma.ali/partition": "0%",
            "statefulset.beta1.sigma.ali/pod-upgrade-timeout": "1800",
            "statefulset.beta1.sigma.ali/pods-to-delete": "9d46d207-0e36-432d-ab5c-62fd5ba969f4",
            "statefulset.beta1.sigma.ali/publish-id": "127318589",
            "statefulset.beta1.sigma.ali/updated-ready-replicas": "1",
            "statefulset.beta1.sigma.ali/upgrade-max-unavailable": "1",
            "aquaman": "test",
            "sigma.ali/upgrade-merge-annotations": "alibabacloud.com/lightweight-container"
        },
        "finalizers": [
            "alibabacloud.com/proxy-to-cloneset",
            "aquaman"
        ]
    },
    "spec": {
        "replicas": 2,
        "selector": {
            "matchLabels": {
                "sigma.ali/app-name": "alsc-adv-l-model",
                "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                "sigma.ali/site": "na620",
                "sigma.ali/subgroup": "default",
                "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
                "aquaman": "test"
            }
        },
        "template": {
            "metadata": {
                "creationTimestamp": null,
                "labels": {
                    "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
                    "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
                    "pod.beta1.sigma.ali/naming-register-state": "working_online",
                    "sigma.ali/app-name": "alsc-adv-l-model",
                    "sigma.ali/container-model": "dockervm",
                    "sigma.ali/inject-staragent-sidecar": "true",
                    "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                    "sigma.ali/site": "na620",
                    "sigma.ali/subgroup": "default",
                    "sigma.ali/upstream-component": "normandy",
                    "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                    "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
                    "aquaman": "test"
                },
                "annotations": {
                    "alibabacloud.com/ip-stack": "ipv6",
                    "pod.beta1.alibabacloud.com/container-cpu-quota-unlimit": "true",
                    "pod.beta1.alibabacloud.com/zoom-in-cpu-quota": "1.3",
                    "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
                    "pod.beta1.sigma.ali/apprules": "{\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"constraints\":\"{}\",\"extConfig\":\"{}\",\"hostConfig\":\"{}\",\"monopolize\":\"{}\",\"prohibit\":\"{}\",\"resource_pool\":\"sigma_public\",\"spread\":\"{}\"}",
                    "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
                    "pod.beta1.sigma.ali/hostname-template": "alsc-adv-l-model{{.IpAddress}}.center.na610",
                    "pod.beta1.sigma.ali/naming-register-state": "working_online",
                    "sigma.ali/enable-apprules-injection": "true",
                    "sigma.ali/use-unified-pv": "false",
                    "aquaman": "test"
                }
            },
            "spec": {
                "volumes": [
                    {
                        "name": "44cde429260b721f6a8c26b99ab1601b",
                        "emptyDir": {}
                    },
                    {
                        "name": "85cfbbfa3aefacdb2f49532dbb",
                        "emptyDir": {}
                    },
                    {
                        "name": "79e9ba5367e6c330f6e45be47de0bfad",
                        "emptyDir": {}
                    },
                    {
                        "name": "79e9ba53e0bfad",
                        "emptyDir": {}
                    }
                ],
                "containers": [
                    {
                        "name": "main",
                        "image": "hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish",
                        "env": [
                            {
                                "name": "ali_aone_timestamp",
                                "value": "*************"
                            },
                            {
                                "name": "ali_start_app",
                                "value": "yes"
                            },
                            {
                                "name": "ali_run_mode",
                                "value": "common_vm"
                            },
                            {
                                "name": "envSign",
                                "value": "beta"
                            },
                            {
                                "name": "trafficRouteLabel",
                                "value": "beta"
                            },
                            {
                                "name": "aquaman",
                                "value": "test"
                            }
                        ],
                        "resources": {
                            "limits": {
                                "cpu": "36",
                                "ephemeral-storage": "1099511627776",
                                "memory": "206158430208",
                                "nvidia.com/gpu": "8"
                            },
                            "requests": {
                                "cpu": "64",
                                "ephemeral-storage": "1099511627776",
                                "memory": "206158430208",
                                "nvidia.com/gpu": "8"
                            }
                        },
                        "volumeMounts": [
                            {
                                "name": "44cde429260b721f6a8c26b",
                                "mountPath": "/home/<USER>/logs"
                            },
                            {
                                "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                "mountPath": "/home/<USER>/cai/logs"
                            },
                            {
                                "name": "79e9ba5367e6c330f6e45be47de0bfad",
                                "mountPath": "/home/<USER>/alsc-adv-l-model/logs"
                            },
                            {
                                "name": "aquaman",
                                "mountPath": "/home/<USER>/aquaman"
                            }
                        ],
                        "terminationMessagePath": "/dev/termination-log",
                        "terminationMessagePolicy": "File",
                        "imagePullPolicy": "IfNotPresent"
                    }
                ],
                "restartPolicy": "Always",
                "terminationGracePeriodSeconds": 1,
                "dnsPolicy": "Default",
                "automountServiceAccountToken": false,
                "securityContext": {},
                "affinity": {
                    "nodeAffinity": {
                        "requiredDuringSchedulingIgnoredDuringExecution": {
                            "nodeSelectorTerms": [
                                {
                                    "matchExpressions": [
                                        {
                                            "key": "sigma.ali/resource-pool",
                                            "operator": "In",
                                            "values": [
                                                "sigma_public"
                                            ]
                                        },
                                        {
                                            "key": "sigma.ali/is-ecs",
                                            "operator": "In",
                                            "values": [
                                                "true"
                                            ]
                                        },
                                        {
                                            "key": "alibabacloud.com/gpu-card-model-detail",
                                            "operator": "In",
                                            "values": [
                                                "Tesla-V100-SXM2-64GB"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                },
                "schedulerName": "default-scheduler",
                "enableServiceLinks": false
            }
        },
        "serviceName": "",
        "podManagementPolicy": "OrderedReady",
        "updateStrategy": {
            "type": "RollingUpdate",
            "rollingUpdate": {
                "partition": 100000000
            }
        },
        "revisionHistoryLimit": 10
    }
} 
    """.trimIndent()
        val expectedPatch = """
            
            {
                "metadata": {
                    "generation": 10,
                    "labels": {
                        "apps.kruise.io/rollout-id": "1273185",
                        "sigma.ali/site": "na620",
                        "aquaman": "test"
                    },
                    "annotations": {
                        "cloneset.beta1.sigma.ali/app-fail-count": "1",
                        "cloneset.beta1.sigma.ali/publish-success-replicas": "2",
                        "aquaman": "test",
                        "sigma.ali/upgrade-merge-annotations": "alibabacloud.com/lightweight-container"
                    },
                    "finalizers": [
                        "alibabacloud.com/proxy-to-cloneset",
                        "aquaman"
                    ]
                },
                "spec": {
                    "replicas": 2,
                    "selector": {
                        "matchLabels": {
                            "sigma.ali/site": "na620",
                            "aquaman": "test"
                        }
                    },
                    "template": {
                        "metadata": {
                            "labels": {
                                "sigma.ali/site": "na620",
                                "aquaman": "test"
                            },
                            "annotations": {
                                "alibabacloud.com/ip-stack": "ipv6",
                                "pod.beta1.alibabacloud.com/zoom-in-cpu-quota": "1.3",
                                "sigma.ali/use-unified-pv": "false",
                                "aquaman": "test"
                            }
                        },
                        "spec": {
                            "volumes": [
                                {
                                    "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                    "${"$"}patch": "delete"
                                },
                                {
                                    "name": "85cfbbfa3aefacdb2f49532dbb",
                                    "emptyDir": {
                                    }
                                },
                                {
                                    "name": "79e9ba53e0bfad",
                                    "emptyDir": {
                                    }
                                }
                            ],
                            "containers": [
                                {
                                    "env": [
                                        {
                                            "value": "yes",
                                            "name": "ali_start_app"
                                        },
                                        {
                                            "name": "aquaman",
                                            "value": "test"
                                        }
                                    ],
                                    "resources": {
                                        "limits": {
                                            "cpu": "36",
                                            "ephemeral-storage": "1099511627776",
                                            "memory": "206158430208",
                                            "nvidia.com/gpu": "8"
                                        },
                                        "requests": {
                                            "cpu": "64",
                                            "ephemeral-storage": "1099511627776",
                                            "memory": "206158430208",
                                            "nvidia.com/gpu": "8"
                                        }
                                    },
                                    "volumeMounts": [
                                        {
                                            "name": "44cde429260b721f6a8c26b99ab1601b",
                                            "${"$"}patch": "delete"
                                        },
                                        {
                                            "name": "44cde429260b721f6a8c26b",
                                            "mountPath": "/home/<USER>/logs"
                                        },
                                        {
                                            "name": "aquaman",
                                            "mountPath": "/home/<USER>/aquaman"
                                        }
                                    ],
                                    "name": "main"
                                }
                            ],
                            "affinity": {
                                "nodeAffinity": {
                                    "requiredDuringSchedulingIgnoredDuringExecution": {
                                        "nodeSelectorTerms": [
                                            {
                                                "matchExpressions": [
                                                    {
                                                        "key": "alibabacloud.com/gpu-card-model-detail",
                                                        "values": [
                                                            "Tesla-V100-SXM2-64GB"
                                                        ]
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    }
                }
            }
        """.trimIndent()
        diffMergeTest(originalJson, modifiedJson, expectedPatch)


    }

    private fun diffMergeTest(originalJson: String, modifiedJson: String, expectedPatch: String) {
        val source = Json.createReader(StringReader(originalJson)).readValue()
        val target = Json.createReader(StringReader(modifiedJson)).readValue()
        val diffOptions1 = ThreeWayMerge.DiffOptions(
            false,
            false,
        )
        val patch = ThreeWayMerge.diff(source, target, "", getDefaultStrategies()!!, null, diffOptions1)
//        println("patch\n")
//        println(JsonUtils.format(patch))
        assertThatJson(expectedPatch)
            .`when`(Option.IGNORING_ARRAY_ORDER)
            .isEqualTo(JsonUtils.format(patch))

        val diffOptions2 = ThreeWayMerge.DiffOptions(
            true,
            false,
        )
        val deletePatch = ThreeWayMerge.diff(source, target, "", getDefaultStrategies()!!, null, diffOptions2)

//        println("deletePatch\n")
//        println(JsonUtils.format(deletePatch))

        val diffOptions3 = ThreeWayMerge.DiffOptions(
            false,
            true,
        )
        val addModifyPatch = ThreeWayMerge.diff(source, target, "", getDefaultStrategies()!!, null, diffOptions3)
//        println("addModifyPatch\n")
//        println(JsonUtils.format(addModifyPatch))

        val combinedPatch = ThreeWayMerge.merge(addModifyPatch?: JsonValue.EMPTY_JSON_OBJECT, deletePatch?: JsonValue.EMPTY_JSON_OBJECT, "", getDefaultStrategies()!!, null,
          mergeOptions = ThreeWayMerge.MergeOptions(
            ignoreUnmatchedNulls = false,
          )
          )

//        println("combinedPatch\n")
//        println(JsonUtils.format(combinedPatch))

        assertThatJson(JsonUtils.format(combinedPatch)!!)
            .`when`(Option.IGNORING_ARRAY_ORDER)
            .isEqualTo(JsonUtils.format(patch))


        val result = ThreeWayMerge.merge(source, patch!!, "", getDefaultStrategies()!!, null,
          mergeOptions = ThreeWayMerge.MergeOptions(
            ignoreUnmatchedNulls = true,
          )
          )


//        println("result\n")
//        println(JsonUtils.format(result))
//        println("target\n")
//        println(JsonUtils.format(target))
        /*
        spec.template.metadata.annotations is empty map, ignore it when comparing
         */
        assertThatJson(JsonUtils.format(result)!!)
            .`when`(Option.IGNORING_ARRAY_ORDER)
            .`whenIgnoringPaths`("spec.template.metadata")
            .isEqualTo(JsonUtils.format(target))
    }


    fun getDefaultStrategies(): PatchStrategyDefinition? {

        return ResourceObjectPatchService.patchStrategyDefinitionList.firstOrNull()
    }
}