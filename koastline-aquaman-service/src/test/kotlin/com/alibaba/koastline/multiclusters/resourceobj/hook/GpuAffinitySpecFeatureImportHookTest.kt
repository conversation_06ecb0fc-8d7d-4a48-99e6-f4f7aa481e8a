package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.external.NormandyGropApi
import com.alibaba.koastline.multiclusters.external.model.GpuResourceMappingDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Rule
import org.junit.Test
import org.junit.rules.ExpectedException
import testutils.BaseTest
import kotlin.test.assertEquals

class GpuAffinitySpecFeatureImportHookTest : BaseTest() {

    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    @InjectMockKs
    private lateinit var hook: GpuAffinitySpecFeatureImportHook

    @MockK
    private lateinit var normandyGropApi: NormandyGropApi

    @Test
    fun `preProcess -- unexpected feature key`() {
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java)

        exceptionRule.expect(IllegalStateException::class.java)
        exceptionRule.expectMessage("unexpected")
        exceptionRule.expectMessage(featureImport.resourceObjectFeatureKey)

        hook.preProcess(featureImport)
    }

    @Test
    fun preProcess() {
        every {
            normandyGropApi.queryGpuResourceMapping()
        } returns listOf(
            GpuResourceMappingDTO("A", "A"),
            GpuResourceMappingDTO("B", "BB"),
            GpuResourceMappingDTO("C", "C,CC"),
            GpuResourceMappingDTO("MI308X", "MI308X,AMD-Instinct-MI308X-OAM"),
        )

        val param = mapOf(
            "gpu" to mapOf(
                "models" to listOf("A", "B", "C", "D", "MI308X"),
            )
        )
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = GpuAffinitySpecFeatureImportHook.FEATURE_KEY,
            paramMap = YamlUtils.dump(param)
        )
        val got = hook.preProcess(featureImport)
        println(got.paramMap)
        assertEquals(
            mapOf(
                "gpu" to mapOf(
                    "models" to listOf("A", "BB", "C", "CC", "D", "MI308X", "AMD-Instinct-MI308X-OAM"),
                )
            ),
            YamlUtils.load(got.paramMap!!)
        )
    }
}