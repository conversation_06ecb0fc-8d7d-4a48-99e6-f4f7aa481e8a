package com.alibaba.koastline.multiclusters.appenv.service.common.utils

import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import org.junit.Test
import kotlin.test.assertEquals

class FreemarkerFeatureTest {
    @Test
    fun testParseTemplate_for_pod_labels() {
        val tpl = """
<#if system.systemPodLabel?has_content>
    <#if system.systemPodLabel.upgradeMergeLabels?has_content>
metadata:
  annotations:
    sigma.ali/upgrade-merge-labels: ${'$'}{system.systemPodLabel.upgradeMergeLabels}
    </#if>
spec:
  template:
    metadata:
      labels:
<#list system.systemPodLabel.podLabels![] as podLabel>
        ${'$'}{podLabel.key}: ${'$'}{podLabel.value}
</#list>
</#if>
"""
        val paramMap = YamlUtils.load("""
            {
                "systemPodLabel": {
                    "upgradeMergeLabels": "aaa,bbb",
                    "podLabels": [
                        {
                            "key": "aaa",
                            "value": "aaa_value"
                        },
                        {
                            "key": "bbb",
                            "value": "bbb_value"
                        }
                    ]
                }
            }
        """.trimIndent())
        assertEquals("""
metadata:
  annotations:
    sigma.ali/upgrade-merge-labels: aaa,bbb
spec:
  template:
    metadata:
      labels:
        aaa: aaa_value
        bbb: bbb_value
""",FreemarkerUtils.parseTemplate(tpl, mapOf("system" to paramMap)))
    }
}