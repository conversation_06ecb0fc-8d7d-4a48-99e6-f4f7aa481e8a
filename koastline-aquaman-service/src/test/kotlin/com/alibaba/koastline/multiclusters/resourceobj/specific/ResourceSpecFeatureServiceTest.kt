package com.alibaba.koastline.multiclusters.resourceobj.specific

import org.junit.Test
import org.junit.jupiter.api.Assertions.*

class ResourceSpecFeatureServiceTest{
  @Test
  fun testConvertToM(){
    val mapWithValues = mapOf(
      "1024" to "0M",
      "12343534" to "11M",
      "12313878123" to "11743M"
    )
    mapWithValues.forEach(
      { input, expected -> assertEquals(expected, ResourceSpecFeatureService.convertStoreResourceSpecValueToMb(input))}
    )


  }
}