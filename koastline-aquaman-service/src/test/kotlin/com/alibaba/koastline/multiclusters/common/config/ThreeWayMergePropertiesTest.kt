package com.alibaba.koastline.multiclusters.common.config

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum
import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.spyk
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class ThreeWayMergePropertiesTest {

    private lateinit var threeWayMergeProperties: ThreeWayMergeProperties
    private lateinit var appCenterClient: AppCenterApi
    private lateinit var matchScopeService: MatchScopeService

    @BeforeEach
    fun setUp() {
        appCenterClient = mock(AppCenterApi::class.java)
        matchScopeService = spyk(MatchScopeService(ObjectMapperFactory.newTolerant()))
        threeWayMergeProperties = ThreeWayMergeProperties().apply {
            appCenterClient = <EMAIL>
            matchScopeService = <EMAIL>
            properties = JsonUtils.readValue("""
{
    "THREE_WAY_MERGE_APP_STAGE_BLACK_LIST": {
        "blackApp": [
            "STAGE"
        ]
    },
    "THREE_WAY_MERGE_APP_STAGE_WHITE_LIST": {
        "whiteApp": [
            "STAGE"
        ]
    },
    "THREE_WAY_MERGE_APPID_SUFFIX_WHITE_LIST": {
        "1": [
            "STAGE"
        ]
    },
    "THREE_WAY_MERGE_PROD_PRODUCTLINE_APPGRADE_WHITE_LIST": {
        "4#5": [
            "GRADE4"
        ]
    },
    "THREE_WAY_MERGE_PROD_PRODUCTLINE_APPID_SUFFIX_WHITE_LIST": {
        "4#5": [
            "3"
        ]
    },
    "THREE_WAY_MERGE_PROD_OPS_PROTOCOL_WHITE_LIST": {
        "DEPLOY": [
            "StatefulSet"
        ]
    }
}
    """, object : TypeReference<Map<String, Map<String, List<String>>>>() {})
        }
    }

    @Test
    fun testWhetherInThreeWayMerge() {
        val appInfoWhite = AppInfo(
            id = 1441L,
            buId = 3L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "4_5_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE4
        )
        `when`(appCenterClient.getAppInfoByName("suffixPassApp")).thenReturn(appInfoWhite)
        val appInfoBlock = AppInfo(
            id = 1442L,
            buId = 3L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "4_5_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE4
        )
        `when`(appCenterClient.getAppInfoByName("suffixBlockApp")).thenReturn(appInfoBlock)
        val appInfoEnableByProductlineAppgrade = AppInfo(
            id = 1442L,
            buId = 4L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "5_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE4
        )
        `when`(appCenterClient.getAppInfoByName("appInfoEnableByProductlineAppgrade")).thenReturn(
            appInfoEnableByProductlineAppgrade
        )

        val appInfoDisableByProductline = AppInfo(
            id = 1442L,
            buId = 4L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "6_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE4
        )
        `when`(appCenterClient.getAppInfoByName("appInfoDisableByProductline")).thenReturn(
            appInfoDisableByProductline
        )

        val appInfoDisableByAppgrade = AppInfo(
            id = 1442L,
            buId = 4L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "5_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE1
        )
        `when`(appCenterClient.getAppInfoByName("appInfoDisableByAppgrade")).thenReturn(
            appInfoDisableByAppgrade
        )

        val appInfoDisableByAppgradeButEnableByAppIdSuffix = AppInfo(
            id = 1443L,
            buId = 4L,
            name = "normandy-test-app4",
            productId = 5L,
            productFullLineIdPath = "5_6",
            status = AppStatusEnum.valueOf("ONLINE"),
            level = AppLevelEnum.GRADE1
        )
        `when`(appCenterClient.getAppInfoByName("appInfoDisableByAppgradeButEnableByAppIdSuffix")).thenReturn(
            appInfoDisableByAppgradeButEnableByAppIdSuffix
        )

        // disable by black list
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "blackApp",
                "STAGE",
                ResourceObjectProtocolEnum.ServerlessApp,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by suffix
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "suffixBlockApp",
                "STAGE",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // enable by productline and app grade
        assertTrue(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoEnableByProductlineAppgrade",
                "PUBLISH",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by stage
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoEnableByProductlineAppgrade",
                "DAILY",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by protocol
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoEnableByProductlineAppgrade",
                "PUBLISH",
                ResourceObjectProtocolEnum.CloneSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by scene
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoEnableByProductlineAppgrade",
                "PUBLISH",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.SCALE_OUT
            )
        )

        // disable by app grade
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoDisableByAppgrade",
                "PUBLISH",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by app grade but enable by app id suffix
        assertTrue(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoDisableByAppgradeButEnableByAppIdSuffix",
                "PUBLISH",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by productline
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "appInfoDisableByProductline",
                "PUBLISH",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        // enable by suffix
        assertTrue(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "suffixPassApp",
                "STAGE",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // enable by white list
        assertTrue(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "whiteApp",
                "STAGE",
                ResourceObjectProtocolEnum.StatefulSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )
        // enable by RollingSet
        assertTrue(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "anyApp",
                "STAGE",
                ResourceObjectProtocolEnum.RollingSet,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        // disable by ServerlessApp
        assertFalse(
            threeWayMergeProperties.whetherInThreeWayMerge(
                "anyApp",
                "STAGE",
                ResourceObjectProtocolEnum.ServerlessApp,
                ResourceObjectSceneEnum.DEPLOY
            )
        )
    }

    @Test
    fun testWhetherServeProductionTraffic() {
        assertTrue(threeWayMergeProperties.whetherServeProductionTraffic(MetadataStageEnum.PUBLISH.name))
        assertTrue(threeWayMergeProperties.whetherServeProductionTraffic(MetadataStageEnum.GRAY.name))
        assertTrue(threeWayMergeProperties.whetherServeProductionTraffic(MetadataStageEnum.SMALLFLOW.name))
        assertFalse(threeWayMergeProperties.whetherServeProductionTraffic(MetadataStageEnum.PRE_PUBLISH.name))
        assertFalse(threeWayMergeProperties.whetherServeProductionTraffic(MetadataStageEnum.DAILY.name))
    }
}