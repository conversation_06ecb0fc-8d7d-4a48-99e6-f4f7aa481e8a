package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.extensions.safeToInt
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils.objectTypeReference
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.SERVER_SERVERLESS_SURGE
import com.alibaba.koastline.multiclusters.schedule.model.ApREStrategy
import com.alibaba.koastline.multiclusters.schedule.model.ClusterWorkload
import com.alibaba.koastline.multiclusters.schedule.model.DeclarationData
import com.alibaba.koastline.multiclusters.schedule.model.OrientedDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.ResourceStrategy
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum.NON_ORIENTED_SCALE_IN
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum.DECLARATIVE
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum.NON_DECLARATIVE
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraintAssemble
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants.Companion.SCALE_NUM
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Test
import testutils.BaseTest
import java.util.*

class AbstractNonOrientedScaleInScheduleRefactorTest : BaseTest() {

    @InjectMockKs
    lateinit var scheduler: AbstractNonOrientedScaleInScheduleRefactorMockImpl

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var scheduleStrategyService: ScheduleStrategyService

    @MockK
    lateinit var resourcePoolService: ResourcePoolService

    @MockK
    lateinit var scheduleStandardService: ScheduleStandardService


    /**
     * case: serverless balance
     * all workload is serverless node
     */
    @Test
    fun scheduler_serverless_Test() {
        commonPrepare()
        // 返回预期内的workload
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_2_site_4_workload(true)

        // 返回期待的规则
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf(
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    clusterId = "zjk_core_a01",
                                    clusterName = "zjk_core_a01",
                                    runtimeId = "runtimeExpected1"
                                ),
                                ClusterWorkload(
                                    clusterId = "zjk_core_a02",
                                    clusterName = "zjk_core_a02",
                                    runtimeId = "runtimeExpected2"
                                ),
                                ClusterWorkload(
                                    clusterId = "zjk_core_a02",
                                    clusterName = "zjk_core_a02"
                                ),
                            ), weight = 1
                        ),
                    )
                )
            )
        )

        // case1: b01 & b02 各6个
        val firstDecrease = 12
        val content = getDeedByDecreaseReplicas(decrease = firstDecrease)
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-firstDecrease)


        // case 2  b01 & b02 各10个 a01 & a02 分别是 -2 -3
        val secondDecrease = 25
        val content2 = getDeedByDecreaseReplicas(decrease = secondDecrease)
        val scheduleResult2 = scheduler.doSchedule(content2)
        softly.assertThat(scheduleResult2.workloadExpectedStates.size).isEqualTo(4)
        softly.assertThat(scheduleResult2.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)

        // case 3 全部缩容
        val thirdDecrease = 40
        val content3 = getDeedByDecreaseReplicas(decrease = thirdDecrease)
        val scheduleResult3 = scheduler.doSchedule(content3)
        softly.assertThat(scheduleResult3.workloadExpectedStates.size).isEqualTo(4)
        softly.assertThat(scheduleResult3.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-thirdDecrease)

        // case 4 缩容非预期
        val fourthDecrease = 20
        val content4 = getDeedByDecreaseReplicas(decrease = fourthDecrease)
        val scheduleResult4 = scheduler.doSchedule(content4)
        println(scheduleResult4)
        softly.assertThat(scheduleResult4.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult4.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-fourthDecrease)
    }


    /**
     * case: serverless balance
     * all workload is serverless node
     */
    @Test
    fun scheduler_serverless_multi_sites_Test2() {
        commonPrepare()
        // 返回预期内的workload
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_actually_case()

        // 返回期待的规则
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns getStrategy()

        val firstDecrease = 11
        val content = getDeedByDecreaseReplicas(decrease = firstDecrease)
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(4)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-firstDecrease)

        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-7, -2, -1, -1)
            )

        val secondDecrease = 15
        val content2 = getDeedByDecreaseReplicas(decrease = secondDecrease)
        val scheduleResult2 = scheduler.doSchedule(content2)
        softly.assertThat(scheduleResult2.workloadExpectedStates.size).isEqualTo(4)
        softly.assertThat(scheduleResult2.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)

        softly.assertThat(scheduleResult2.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-8, -3, -2, -2)
            )
    }

    @Test
    fun scheduler_common_Test() {
        commonPrepare()
        mock2Sites4ClusterStrategy()
        // case: 没有其他逻辑子组 4个part 等价进行缩容

        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_2_site_4_workload(true)

        val firstDecrease = 12
        val content = getDeedByDecreaseReplicas(decrease = firstDecrease)
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(4)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM] ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-firstDecrease)

        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-3, -3, -3, -3)
            )
    }

    @Test
    fun scheduler_common_Test2() {
        commonPrepare()
        // 返回期待的规则
        mock2Sites4ClusterStrategy()
        // case: 存在其他逻辑子组 先过滤 剩下4个part 等价进行缩容
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_2_site_5_workload_with_subgroup(false)

        val secondDecrease = 12
        val content = getDeedByDecreaseReplicas(decrease = secondDecrease, isServerless = false)
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(3)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)

        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-10, -1, -1)
            )

    }

    @Test
    fun scheduler_common_Test3() {
        commonPrepare()
        // 返回期待的规则
        mock2SitesStrategy()
        // case: 优先缩容在非site中的资源单位
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_three_metadata_six_balanced_cluster_and_one_is_not_within_of_declaration()

        val secondDecrease = 12
        val content2 = getDeedByDecreaseReplicas(decrease = secondDecrease, isServerless = false)
        val scheduleResult = scheduler.doSchedule(content2)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)

        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-6, -6)
            )
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.workloadMetadataConstraint.site }.sorted())
            .isEqualTo(
                listOf("ea119", "ea119")
            )
    }

    @Test
    fun scheduler_common_Test4() {
        commonPrepare()
        // 返回期待的规则
        mock2SitesStrategy()
        // case: 优先缩容在非site中的资源单位 其次开始缩容site na620和site na610 的资源
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_three_metadata_six_balanced_cluster_and_one_is_not_within_of_declaration()

        val secondDecrease = 30
        val content2 = getDeedByDecreaseReplicas(decrease = secondDecrease, isServerless = false)
        val scheduleResult = scheduler.doSchedule(content2)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(6)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)

        val decreaseRsMap = mapOf(
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a01normandy-test-app4&null&null" to -2,
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a02normandy-test-app4&null&null" to -3,
            "normandy-test-app4&normandy-test-app4_prehost&default&na620&PUBLISH&CENTER_UNIT.center&zjk_core_b01normandy-test-app4&null&null" to -2,
            "normandy-test-app4&normandy-test-app4_prehost&default&na620&PUBLISH&CENTER_UNIT.center&zjk_core_b02normandy-test-app4&null&null" to -3,
            "normandy-test-app4&normandy-test-app4_prehost&default&ea119&PUBLISH&CENTER_UNIT.center&zjk_core_c01normandy-test-app4&null&null" to -10,
            "normandy-test-app4&normandy-test-app4_prehost&default&ea119&PUBLISH&CENTER_UNIT.center&zjk_core_c02normandy-test-app4&null&null" to -10,
        )
        for (rs in scheduleResult.workloadExpectedStates) {
            softly.assertThat(decreaseRsMap[rs.workloadMetadataConstraint.toMetadataConstraintString()] ?: -1)
                .isEqualTo(rs.params[SCALE_NUM]?.toInt() ?: 0)
        }
    }

    @Test
    fun scheduler_common_Test5() {
        commonPrepare()
        // 返回期待的规则
        mock1SitesStrategy()
        // case: 添加定向的范围 na610 在610 缩容同时保证均衡
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_2_site_4_workload(false)

        val toDecrease = 10
        val content = getDeedByDecreaseReplicas(decrease = toDecrease, isServerless = false).let {
            it.copy(
                declarationData = DeclarationData(
                    declaration = OrientedDeclaration(site = "na610", stage = "PUBLISH", unit = "CENTER_UNIT.center")
                ),
                scheduleType = it.scheduleType.copy(
                    schedulePattern = NON_DECLARATIVE
                )
            )
        }
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-toDecrease)

        val decreaseRsMap = mapOf(
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a01normandy-test-app4&null&null" to -5,
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a02normandy-test-app4&null&null" to -5,
        )
        for (rs in scheduleResult.workloadExpectedStates) {
            softly.assertThat(decreaseRsMap[rs.workloadMetadataConstraint.toMetadataConstraintString()] ?: -1)
                .isEqualTo(rs.params[SCALE_NUM]?.toInt() ?: 0)
        }
    }

    @Test
    fun scheduler_common_Test6() {
        commonPrepare()
        // 返回期待的规则
        mock1SitesStrategy()
        // case: 添加定向的范围 na610 在610 缩容同时保证均衡
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_same_metadata_three_balanced_cluster()

        val toDecrease = 3
        val content = getDeedByDecreaseReplicas(decrease = toDecrease, isServerless = false)
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-toDecrease)

        val decreaseRsMap = mapOf(
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a01normandy-test-app4&null&null" to -1,
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a02normandy-test-app4&null&null" to -2,
        )
        for (rs in scheduleResult.workloadExpectedStates) {
            softly.assertThat(decreaseRsMap[rs.workloadMetadataConstraint.toMetadataConstraintString()] ?: -1)
                .isEqualTo(rs.params[SCALE_NUM]?.toInt() ?: 0)
        }
    }

    @Test
    fun scheduler_common_Test7() {
        commonPrepare()
        // 返回期待的规则
        mock1Sites1CLusterStrategy()
        // case: 只有arm是期待中的
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_with_same_metadata_three_balanced_cluster()

        val toDecrease = 3
        val content = getDeedByDecreaseReplicas(decrease = toDecrease, isServerless = false)
        val scheduleResult = scheduler.doSchedule(content)

        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-toDecrease)

        val decreaseRsMap = mapOf(
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a01normandy-test-app4&null&null" to -1,
            "normandy-test-app4&normandy-test-app4_prehost&default&na610&PUBLISH&CENTER_UNIT.center&zjk_core_a02normandy-test-app4&null&null" to -2,
        )
        for (rs in scheduleResult.workloadExpectedStates) {
            softly.assertThat(decreaseRsMap[rs.workloadMetadataConstraint.toMetadataConstraintString()] ?: -1)
                .isEqualTo(rs.params[SCALE_NUM]?.toInt() ?: 0)
        }
    }

    @Test
    fun scheduler_mix_Test() {
        commonPrepare()
        // 返回期待的规则
        mock2Sites4ClusterStrategy()
        // case1:  存在普通应用 + serverless 应用 先过滤 普通应用 剩下2个part 等价进行缩容
        every {
            skylineApi.listWorkloadMetadataConstraintThroughServerList(APP_NAME, null, RESOURCE_GROUP, emptyList(), listOf(SERVER_SERVERLESS_SURGE))
        } returns getWorkloadMetadataConstraintAssembleList_mix_with_two_metadata_four_balanced_workload()

        val firstDecrease = 12
        val content = getDeedByDecreaseReplicas(
            decrease = firstDecrease,
            isServerless = true,
            serverlessRuntimeTemplate = "serverless/pre-runtime-test01\$\$common\$\$SPEC:4-8Gi"
        )
        val scheduleResult = scheduler.doSchedule(content)
        softly.assertThat(scheduleResult.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-firstDecrease)
        softly.assertThat(scheduleResult.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-6, -6)
            )
        for (workload in scheduleResult.workloadExpectedStates) {
            softly.assertThat(workload.workloadMetadataConstraint.runtimeId != null)
        }

        // case2:  存在普通应用 + serverless 应用 先过滤 severless应用 剩下2个part 等价进行缩容
        val secondDecrease = 12
        val content2 = getDeedByDecreaseReplicas(decrease = secondDecrease, isServerless = false)
        val scheduleResult2 = scheduler.doSchedule(content2)
        softly.assertThat(scheduleResult2.workloadExpectedStates.size).isEqualTo(2)
        softly.assertThat(scheduleResult2.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }
            .sumOf { it.safeToInt() })
            .isEqualTo(-secondDecrease)
        softly.assertThat(scheduleResult2.workloadExpectedStates.map { it.params[SCALE_NUM]?.toInt() ?: 0 }.sorted())
            .isEqualTo(
                listOf(-6, -6)
            )

        for (workload in scheduleResult.workloadExpectedStates) {
            softly.assertThat(workload.workloadMetadataConstraint.runtimeId == null)
        }
    }


    private fun mock2Sites4ClusterStrategy() {
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf(
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    clusterId = "zjk_core_a01",
                                    clusterName = "zjk_core_a01",
                                ),
                                ClusterWorkload(
                                    clusterId = "zjk_core_a02",
                                    clusterName = "zjk_core_a02",
                                )
                            ), weight = 1
                        )
                    )
                ),
                ApREStrategy(
                    site = "na620", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf(
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    clusterId = "zjk_core_b01",
                                    clusterName = "zjk_core_b01",
                                ),
                                ClusterWorkload(
                                    clusterId = "zjk_core_b02",
                                    clusterName = "zjk_core_b02",
                                )
                            ), weight = 1
                        )
                    )
                )
            )
        )
    }


    private fun mock1SitesStrategy() {
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf()
                )
            )
        )
    }

    private fun mock1Sites1CLusterStrategy() {
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf(
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    clusterName = "zjk_core_arm_01",
                                    clusterId = "zjk_core_arm_01"
                                )
                            ), weight = 1
                        )
                    )
                )
            )
        )
    }

    private fun mock2SitesStrategy() {
        every {
            scheduleStrategyService.computeScheduleStrategy(any())
        } returns ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf()
                ),
                ApREStrategy(
                    site = "na620", unit = "CENTER_UNIT.center", stage = "PUBLISH", weight = 1,
                    resources = listOf()
                )
            )
        )
    }

    private fun getDeedByDecreaseReplicas(
        decrease: Int,
        isServerless: Boolean = true,
        serverlessRuntimeTemplate: String? = null
    ): ScheduleRequestContent {
        require(decrease >= 0)
        return ScheduleRequestContent(
            resourceScope = ResourceScope(APP_NAME, null, RESOURCE_GROUP),
            scheduleType = ScheduleType(DECLARATIVE, NON_ORIENTED_SCALE_IN),
            scheduleRequestParam = ScheduleRequestParam(
                replicas = -decrease,
                scheduleEnvType = if (isServerless) SERVERLESS_APP else ASI,
                serverless = isServerless,
                serverlessRuntimeTemplate = serverlessRuntimeTemplate
            )
        )
    }

    @Test
    fun buildUniqueKeyTest_notExpected_notMatchThreeTuplesKey() {
        val goal = "na620&PUBLISH&CENTER_UNIT.center&default&clusterA&runtimeId1"
        val workload = manuPojoTestWorkload(
            site = "na620",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterA",
            runtimeId = "runtimeId1",
            appName = "siteops",
            resourceGroup = "siteops_host",
        )
        val strategy = getTestScheduleStrategyResult()
        val uniqueKey = scheduler.buildUniqueKey(workload = workload, expectDistributionStrategy = strategy)
        softly.assertThat(uniqueKey).isEqualTo(goal)
    }

    @Test
    fun buildUniqueKeyTest_notExpected_notMatchResource() {
        val goal = "na620&PUBLISH&CENTER_UNIT.center&default&clusterD&runtimeId4"
        val workload = manuPojoTestWorkload(
            site = "na620",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterD",
            runtimeId = "runtimeId4",
            appName = "siteops",
            resourceGroup = "siteops_host"
        )
        val strategy = getTestScheduleStrategyResult()
        val uniqueKey = scheduler.buildUniqueKey(workload = workload, expectDistributionStrategy = strategy)
        softly.assertThat(uniqueKey).isEqualTo(goal)
    }

    @Test
    fun buildUniqueKeyTest_Expected() {
        val goal = "na610&PUBLISH&CENTER_UNIT.center&|clusterA|clusterB|&|default|runtimeId1|clusterA|test-workload|"
        val workload = manuPojoTestWorkload(
            site = "na610",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterA",
            runtimeId = "runtimeId1",
            appName = "siteops",
            resourceGroup = "siteops_host"
        )
        val strategy = getTestScheduleStrategyResult()
        val uniqueKey = scheduler.buildUniqueKey(workload = workload, expectDistributionStrategy = strategy)
        softly.assertThat(uniqueKey).isEqualTo(goal)
    }

    /**
     * decrease site na620 not expect, using notExpected rules: Sharing Equally
     */
    @Test
    fun reconcileByFunnelTreeTest1() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -10
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 100,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 50,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 10,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 5,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 5
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    /**
     * decrease site na620 not expect to zero, using notExpected rules: Sharing Equally
     */
    @Test
    fun reconcileByFunnelTreeTest2() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -20
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 100,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 50,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 10,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 0
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    /**
     * 1.decrease site na620 not expect to zero, using notExpected rules: Sharing Equally
     * 2.decrease site na610 sub not expect center:clusterD to zero, using notExpected rules: Sharing Equally
     */
    @Test
    fun reconcileByFunnelTreeTest3() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -30
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 100,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 50,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 0
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    /**
     * 1.decrease site na620 not expect to zero, using notExpected rules: Sharing Equally
     * 2.decrease site na610 sub not expect center:clusterD to zero, using notExpected rules: Sharing Equally
     * 3.decrease site na610 sub not expect center:clusterA&clusterB, using Expected rules: approach to final expected
     */
    @Test
    fun reconcileByFunnelTreeTest4() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -50
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 80,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 50,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 0
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    /**
     * 1.decrease site na620 not expect to zero, using notExpected rules: Sharing Equally
     * 2.decrease site na610 sub not expect center:clusterD to zero, using notExpected rules: Sharing Equally
     * 3.decrease site na610 sub expect center:clusterA&clusterB to zero, using Expected rules: approach to final expected
     */
    @Test
    fun reconcileByFunnelTreeTest5() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -110 // 30 not expected 80 expected
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 35,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 35,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 0
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    @Test
    fun reconcileByFunnelTreeTest6() {
        val testWorkloadsMap =
            getTestWorkloadSets().associateBy { it.workloadMetadataConstraint.toMetadataConstraintString() }
        val toDecreaseReplicas = -180 // 30 not expected 150 expected
        val rss = scheduler.reconcileByFunnelTree(
            actualWorkloadDistribution = testWorkloadsMap.map { it.value }.toList(),
            expectDistributionStrategy = getTestScheduleStrategyResult(),
            toDecreaseReplicas = toDecreaseReplicas
        )
        val expectedResults = mapOf(
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterBnull&runtimeId2&test-workload" to 0,
            "siteops&siteops_host&default&na610&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId6&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterAnull&runtimeId1&test-workload" to 0,
            "siteops&siteops_host&default&na620&PUBLISH&CENTER_UNIT.center&clusterDnull&runtimeId4&test-workload" to 0
        )
        softly.assertThat(rss.size).isEqualTo(expectedResults.size)
        softly.assertThat(testWorkloadsMap.map { it.value } - toDecreaseReplicas)
        for ((rSKey, rsValue) in rss) {
            softly.assertThat(expectedResults[rSKey]).isEqualTo(rsValue)
        }
    }

    private fun getTestWorkloadSets() = listOf(
        manuPojoTestWorkload(
            site = "na620",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterA",
            runtimeId = "runtimeId1",
            appName = "siteops",
            resourceGroup = "siteops_host",
            replicas = 10
        ),
        manuPojoTestWorkload(
            site = "na620",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterD",
            runtimeId = "runtimeId4",
            appName = "siteops",
            resourceGroup = "siteops_host",
            replicas = 10
        ),
        manuPojoTestWorkload(
            site = "na610",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterA",
            runtimeId = "runtimeId1",
            appName = "siteops",
            resourceGroup = "siteops_host",
            replicas = 100
        ),
        manuPojoTestWorkload(
            site = "na610",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterB",
            runtimeId = "runtimeId2",
            appName = "siteops",
            resourceGroup = "siteops_host",
            replicas = 50
        ),
        manuPojoTestWorkload(
            site = "na610",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            clusterId = "clusterD",
            runtimeId = "runtimeId6",
            appName = "siteops",
            resourceGroup = "siteops_host",
            replicas = 10
        )
    )


    private fun manuPojoTestWorkload(
        site: String, unit: String, stage: String, clusterId: String, runtimeId: String, appName: String,
        resourceGroup: String, replicas: Int = 100
    ): WorkloadMetadataConstraintAssemble {
        return WorkloadMetadataConstraintAssemble(
            workloadMetadataConstraint = WorkloadMetadataConstraint(
                site = site,
                unit = unit,
                stage = stage,
                clusterId = clusterId,
                runtimeId = runtimeId,
                appName = appName,
                resourceGroup = resourceGroup,
                workloadName = "test-workload"
            ),
            num = replicas,
            resourceSnList = List(replicas) { getString() }.toMutableList()
        )
    }

    private fun getTestScheduleStrategyResult(): ScheduleStrategyResult {
        return ScheduleStrategyResult(
            apres = listOf(
                ApREStrategy(
                    site = "na610",
                    unit = "CENTER_UNIT.center",
                    stage = "PUBLISH",
                    weight = 1,
                    resources = listOf(
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    "clusterA", "clusterA", "runtimeId1", 1
                                ),
                                ClusterWorkload(
                                    "clusterB", "clusterB", "runtimeId2", 1
                                ),
                            ), weight = 1
                        ),
                        ResourceStrategy(
                            clusters = listOf(
                                ClusterWorkload(
                                    "clusterC", "clusterC", "runtimeId3", 2
                                ),
                            ), weight = 3
                        )
                    )
                )
            )
        )
    }

    /**
     * 返回[两]类四元组共[四]个[均衡]的集群资源 总共有40个副本资源
     */
    private fun getWorkloadMetadataConstraintAssembleList_with_2_site_4_workload(usingRuntime: Boolean = false): List<WorkloadMetadataConstraintAssemble> {
        return listOf(
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_a01",
                    APP_NAME, "runtimeExpected1"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_a02",
                    APP_NAME, "runtimeExpected2"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_b01",
                    APP_NAME, "runtimeNotExpected1"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_b02",
                    APP_NAME, "runtimeNotExpected2"
                ),
                num = 10
            )
        ).map {
            it.copy(
                workloadMetadataConstraint = it.workloadMetadataConstraint.copy(
                    runtimeId = if (usingRuntime) it.workloadMetadataConstraint.runtimeId else null
                )
            )
        }
    }


    /**
     * 返回[两]类四元组共[四]个[均衡]的集群资源 总共有40个副本资源
     */
    private fun getWorkloadMetadataConstraintAssembleList_with_2_site_5_workload_with_subgroup(usingRuntime: Boolean = false): List<WorkloadMetadataConstraintAssemble> {
        return listOf(
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_a01",
                    APP_NAME, "runtimeExpected1"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_a02",
                    APP_NAME, "runtimeExpected2"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_b01",
                    APP_NAME, "runtimeNotExpected1"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "zjk_core_b02",
                    APP_NAME, "runtimeNotExpected2"
                ),
                num = 10
            ),
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "otherSubGroup", "zjk_core_b03",
                    APP_NAME, "runtimeNotExpected2"
                ),
                num = 10
            )
        ).map {
            it.copy(
                workloadMetadataConstraint = it.workloadMetadataConstraint.copy(
                    runtimeId = if (usingRuntime) it.workloadMetadataConstraint.runtimeId else null
                )
            )
        }
    }

    private fun getWorkloadMetadataConstraintAssembleList_mix_with_two_metadata_four_balanced_workload(): List<WorkloadMetadataConstraintAssemble> {
        return listOf(
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a01",
                    APP_NAME,
                    "mock1RuntimeId"
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na620",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_b01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na620",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_b01",
                    APP_NAME,
                    "mock2RuntimeId"
                ),
                num = 10
            )
        )
    }


    private fun getWorkloadMetadataConstraintAssembleList_with_three_metadata_six_balanced_cluster_and_one_is_not_within_of_declaration(): List<WorkloadMetadataConstraintAssemble> {
        return listOf(
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a02",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na620",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_b01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na620",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_b02",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "ea119",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_c01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "ea119",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_c02",
                    APP_NAME,
                    null
                ),
                num = 10
            )
        )
    }

    private fun getWorkloadMetadataConstraintAssembleList_with_same_metadata_three_balanced_cluster(): List<WorkloadMetadataConstraintAssemble> {
        return listOf(
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a01",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_a02",
                    APP_NAME,
                    null
                ),
                num = 10
            ), WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = WorkloadMetadataConstraint(
                    APP_NAME,
                    RESOURCE_GROUP,
                    "na610",
                    "CENTER_UNIT.center",
                    "PUBLISH",
                    "default",
                    "zjk_core_arm_01",
                    APP_NAME,
                    null
                ),
                num = 7
            )
        )
    }

    private fun getWorkloadMetadataConstraintAssembleList_with_actually_case(): List<WorkloadMetadataConstraintAssemble> {
        return """
    [
      {
        "workloadMetadataConstraint": {
          "appName": "aone-qa-test-jizuoqianyi",
          "resourceGroup": "aone-qa-test-jizuoqianyi_test_prehost",
          "site": "na610",
          "unit": "CENTER_UNIT.center",
          "stage": "PRE_PUBLISH",
          "subgroup": "default",
          "clusterId": "c3eecd7cefc2646d0b4fbeecb731305a6",
          "namespace": "hippo-c2-serverless-pre",
          "runtimeId": "tao-serverless-runtime.103085"
        },
        "clusterProfile": {
          "clusterId": "c3eecd7cefc2646d0b4fbeecb731305a6",
          "clusterName": "asi_zjk_core_a02",
          "clusterProvider": "alibaba",
          "clusterType": "alibaba-asi",
          "siteList": [
            ""
          ],
          "componentDataList": [
            {
              "id": 81,
              "code": "POD_LISTER",
              "refObjectId": "c3eecd7cefc2646d0b4fbeecb731305a6",
              "refObjectType": "CLUSTER_UUID",
              "annotations": {
                "scheme": "http",
                "domain": "***********",
                "port": "8080"
              }
            }
          ],
          "useType": "publish",
          "status": "online"
        },
        "workloadDesc": {
          "scheduleEnvType": "SERVERLESS_APP",
          "resourceObjectProtocolEnum": "ServerlessApp"
        },
        "params": {
          "RESOURCE_NUM": "3",
          "RESOURCE_SN_LIST": ""
        }
      },
      {
        "workloadMetadataConstraint": {
          "appName": "aone-qa-test-jizuoqianyi",
          "resourceGroup": "aone-qa-test-jizuoqianyi_test_prehost",
          "site": "na620",
          "unit": "CENTER_UNIT.center",
          "stage": "PRE_PUBLISH",
          "subgroup": "default",
          "clusterId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
          "namespace": "hippo-c2-serverless-pre",
          "runtimeId": "tao-serverless-runtime.103153"
        },
        "clusterProfile": {
          "clusterId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
          "clusterName": "asi_zjk_core_b01",
          "clusterProvider": "alibaba",
          "clusterType": "alibaba-asi",
          "siteList": [
            ""
          ],
          "componentDataList": [
            {
              "id": 17,
              "code": "POD_LISTER",
              "refObjectId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
              "refObjectType": "CLUSTER_UUID",
              "annotations": {
                "scheme": "http",
                "domain": "************",
                "port": "8080"
              }
            }
          ],
          "useType": "publish",
          "status": "online"
        },
        "workloadDesc": {
          "scheduleEnvType": "SERVERLESS_APP",
          "resourceObjectProtocolEnum": "ServerlessApp"
        },
        "params": {
          "RESOURCE_NUM": "8",
          "RESOURCE_SN_LIST": ""
        }
      },
      {
        "workloadMetadataConstraint": {
          "appName": "aone-qa-test-jizuoqianyi",
          "resourceGroup": "aone-qa-test-jizuoqianyi_test_prehost",
          "site": "na610",
          "unit": "CENTER_UNIT.center",
          "stage": "PRE_PUBLISH",
          "subgroup": "default",
          "clusterId": "c42dd0b4a54824263934453d6462a9a1f",
          "namespace": "svl-new-runtime-app11",
          "runtimeId": "5706077369767f9a7217a1c35099d490"
        },
        "clusterProfile": {
          "clusterId": "c42dd0b4a54824263934453d6462a9a1f",
          "clusterName": "asi_zjk_core_a01",
          "clusterProvider": "alibaba",
          "clusterType": "alibaba-asi",
          "siteList": [
            ""
          ],
          "componentDataList": [
            {
              "id": 11,
              "code": "POD_LISTER",
              "refObjectId": "c42dd0b4a54824263934453d6462a9a1f",
              "refObjectType": "CLUSTER_UUID",
              "annotations": {
                "scheme": "http",
                "domain": "************",
                "port": "8080"
              }
            }
          ],
          "useType": "publish",
          "status": "online"
        },
        "workloadDesc": {
          "scheduleEnvType": "SERVERLESS_APP",
          "resourceObjectProtocolEnum": "ServerlessApp"
        },
        "params": {
          "RESOURCE_NUM": "2",
          "RESOURCE_SN_LIST": ""
        }
      },
      {
        "workloadMetadataConstraint": {
          "appName": "aone-qa-test-jizuoqianyi",
          "resourceGroup": "aone-qa-test-jizuoqianyi_test_prehost",
          "site": "na620",
          "unit": "CENTER_UNIT.center",
          "stage": "PRE_PUBLISH",
          "subgroup": "default",
          "clusterId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
          "namespace": "svl-new-runtime-app11",
          "runtimeId": "1ea80ac3203b5c8fa9e5b27f4e9c8af7"
        },
        "clusterProfile": {
          "clusterId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
          "clusterName": "asi_zjk_core_b01",
          "clusterProvider": "alibaba",
          "clusterType": "alibaba-asi",
          "siteList": [
            ""
          ],
          "componentDataList": [
            {
              "id": 17,
              "code": "POD_LISTER",
              "refObjectId": "c8714f9fdfdb142e7a52cbb89290f2a4a",
              "refObjectType": "CLUSTER_UUID",
              "annotations": {
                "scheme": "http",
                "domain": "************",
                "port": "8080"
              }
            }
          ],
          "useType": "publish",
          "status": "online"
        },
        "workloadDesc": {
          "scheduleEnvType": "SERVERLESS_APP",
          "resourceObjectProtocolEnum": "ServerlessApp"
        },
        "params": {
          "RESOURCE_NUM": "3",
          "RESOURCE_SN_LIST": ""
        }
      }
    ]
        """.trimIndent().let {
            JsonUtils.readListValue<WorkloadExpectedState>(
                value = it,
                valueType = objectTypeReference()
            )
        }.map { workloadExpectedState ->
            WorkloadMetadataConstraintAssemble(
                workloadMetadataConstraint = workloadExpectedState.workloadMetadataConstraint,
                num = workloadExpectedState.params["RESOURCE_NUM"]!!.toInt(),
                resourceSnList = mutableListOf()
            )
        }
    }

    private fun getStrategy(): ScheduleStrategyResult {
        return """
{
    "apres":[
        {
            "site":"na620",
            "stage":"PUBLISH",
            "unit":"CENTER_UNIT.center",
            "weight":1,
            "resources":[
                {
                    "clusters":[
                        {
                            "clusterId":"c8714f9fdfdb142e7a52cbb89290f2a4a",
                            "clusterName":"asi_zjk_core_b01",
                            "runtimeId":"b8af82217720eeee928887046d2c95a8",
                            "weight":null
                        },
                        {
                            "clusterId":"c8714f9fdfdb142e7a52cbb89290f2a4a",
                            "clusterName":"asi_zjk_core_b01",
                            "runtimeId":"507a0cce3d5027f0c12aa7be3fc36b2e",
                            "weight":null
                        },
                        {
                            "clusterId":"c3f506bb7b94f411397f4bb3cf1c23c81",
                            "clusterName":"asi_zjk_core_b",
                            "runtimeId":"d7b99f1f8f1d58dc42c00b5e6eb3c375",
                            "weight":null
                        },
                        {
                            "clusterId":"c3f506bb7b94f411397f4bb3cf1c23c81",
                            "clusterName":"asi_zjk_core_b",
                            "runtimeId":"c2934c8e4d42673637c81ac6f94a6e82",
                            "weight":null
                        }
                    ]
                }
            ]
        },
        {
            "site":"na610",
            "stage":"PUBLISH",
            "unit":"CENTER_UNIT.center",
            "weight":1,
            "resources":[
                {
                    "clusters":[
                        {
                            "clusterId":"c3eecd7cefc2646d0b4fbeecb731305a6",
                            "clusterName":"asi_zjk_core_a02",
                            "runtimeId":"f299a7fefd7791d037debd1e8ad42cf0",
                            "weight":null
                        },
                        {
                            "clusterId":"c42dd0b4a54824263934453d6462a9a1f",
                            "clusterName":"asi_zjk_core_a01",
                            "runtimeId":"da83ea8274ca83cb079e04c6eb1d3ac0",
                            "weight":null
                        }
                    ],
                    "weight":1
                }
            ]
        }
    ]
}
        """.trimIndent().let {
            JsonUtils.readValue(it, ScheduleStrategyResult::class.java)
        }
    }


    private fun commonPrepare() {
        every {
            scheduleStandardService.checkServerlessParams(any())
        } answers {
            callOriginal()
        }

        every {
            resourcePoolService.getClusterProfileNew(any())
        } answers { call ->
            ClusterProfileNew(
                call.invocation.args[0] as String,
                call.invocation.args[0] as String,
                "",
                "",
                emptyList(),
                emptyList(),
                ""
            )
        }
    }

    companion object {
        const val APP_NAME = "normandy-test-app4"
        const val RESOURCE_GROUP = "normandy-test-app4_prehost"
    }
}