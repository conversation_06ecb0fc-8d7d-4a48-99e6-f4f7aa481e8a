package com.alibaba.koastline.multiclusters.event.consumer.app

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.rocketmq.common.message.MessageExt
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SkylineMessageListenerTest {

    @InjectMockKs
    private lateinit var listener: SkylineMessageListener

    @MockK
    private lateinit var matchScopeService: MatchScopeService

    @BeforeEach
    fun setUp() = MockKAnnotations.init(this, relaxUnitFun = true)

    @Test
    fun consumeMessage_appGroup() {
        every { matchScopeService.deleteAppNameToGroupName(any(), any()) } returns Unit

        val message = """
            {
              "categoryName": "app_group",
              "key": "DELETE_ITEM_***************_@*************",
              "operateType": "DELETE_ITEM",
              "context": {
                "account": "normandy-arc",
                "operator": "normandy",
                "traceId": "210842af17129082436484127d088a",
                "serverIp": "************",
                "opTime": *************
              },
              "messageDetailDOList": [
                {
                  "itemId": ***************,
                  "params": {
                    "armory_id": "3581366"
                  },
                  "itemLogDO": {
                    "rawData": {
                      "safety_out": "1",
                      "env_usage_type": "DAILY",
                      "owner_string": "112633",
                      "is_unit": "false",
                      "armory_id": 3581366,
                      "usage_type": "test",
                      "name": "paimaitestfront_project_4728502_testhost",
                      "real_app_name": "paimaitestfront",
                      "app_id": "**********",
                      "tags": "resource_use_scope.aone,resource_use_type.weak_declaration,resource_uniform_type.pod"
                    },
                    "previousVersion": 5,
                    "currentVersion": 5
                  }
                }
              ]
            }
        """.trimIndent()
        val ext = MessageExt().also { it.body = message.toByteArray() }
        listener.consumeMessage(mutableListOf(ext), mockk(relaxed = true))
        verify {
            matchScopeService.deleteAppNameToGroupName(
                groupName = "paimaitestfront_project_4728502_testhost",
                appName = "paimaitestfront"
            )
        }
    }
}