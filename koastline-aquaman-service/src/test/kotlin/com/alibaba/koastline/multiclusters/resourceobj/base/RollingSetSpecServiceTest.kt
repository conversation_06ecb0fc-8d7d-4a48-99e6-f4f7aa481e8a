package com.alibaba.koastline.multiclusters.resourceobj.base

import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.spyk
import org.junit.Test

class RollingSetSpecServiceTest {

    @Test
    fun testGetDeploySpec() {
        val envStackPkId = "ceee9e80-5188-4246-9f01-7dd45749e9ff"
        val rollingSetSpecService = spyk(RollingSetSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                cloudCmdbApi.getEnvBaselineSpecByStackPKId(
                    envStackPkId,
                    RollingSetSpecService.ROLLING_SET_SPEC_YAML_ATTRIBUTE_NAME
                )
            } returns """---
apiVersion: carbon.taobao.com/v1
kind: RollingSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: \"true\"
    sigma.ali/upgrade-merge-labels: tags.sunfire.com/app-deploy-version
  labels:
    sigma.ali/app-name: new-runtime-app11
    app.c2.io/platform: serverless
    app.c2.io/runtime-source: aone-serverless
    serverless.io/subrs-enable: \"true\"
    app.hippo.io/pod-version: v3.0
spec:
  healthCheckerConfig:
    lv7Config:
      lostCountThreshold: 60
      lostTimeout: 300
      path: /checkpreload.htm
      port: 80
    type: Lv7Health
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: \"true\"
        pod.beta1.sigma.ali/naming-register-state: \"working_online\"
        sigma.ali/use-unified-pv: \"true\"
        sigma.ali/enable-apprules-injection: \"true\"
        pod.beta1.sigma.ali/container-extra-config: '{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}'
        pods.sigma.alibaba-inc.com/inject-pod-sn: \"true\"
        deployment.normandy.com/runtime-version: V146647941
      labels:
        sigma.ali/inject-staragent-sidecar: \"true\"
        app.hippo.io/maxInstancePerNode: \"10\"
        app.hippo.io/pod-version: v3.0
        sigma.ali/app-name: new-runtime-app11
        sigma.ali/env-sign: testing
        app.c2.io/platform: serverless
        app.c2.io/runtime-source: aone-serverless
        alibabacloud.com/cpuBindStrategy: Spread
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 1
      automountServiceAccountToken: false
      enableServiceLinks: false
      containerModel: VM
      containers:
      - env:
        - name: ali_aone_timestamp
          value: \"*************\"
        - name: ali_start_app
          value: \"no\"
        - name: ali_run_mode
          value: \"common_vm\"
        - name: ali_safty_out
          value: \"1\"
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.uid
        - name: ali_run_init
          value: \"thin\"
        - name: ali_jvm_cgroup
          value: \"true\"
        - name: FIBER_VERSION
          value: \"2_0\"
        - name: AJDK_WISP
          value: \"OFF\"
        - name: AJDK_TENANT
          value: \"OFF\"
        - name: exec_scm_hook
          value: \"yes\"
        - name: HIPPO_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['app.hippo.io/cluster-name']
        - name: C2_IDC
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['sigma.ali/site']
        - name: roleId
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['app.hippo.io/group-name']
        - name: envSign
          value: \"testing\"
        - name: ali_env_sign
          value: \"testing\"
        - name: trafficRouteLabel
          value: \"testing\"
        - name: crIds
          value: \"23097575\"
        image: hub.docker.alibaba-inc.com/aone/new-runtime-app11:20231106150301467810@sha256:6c1b8c2d748aba6f9694a0476aeb3a8110013c1ac066592a032168800788ecb5
        name: main
        args:
        - -c
        - /home/<USER>/start_supervisor.sh
        command:
        - /bin/sh
        volumeMounts:
        - name: 26f907fb3bf70c5f850d72be0d60db4a
          mountPath: /home/<USER>/liaoyuan-out # 从Dockerfile中获取的
      volumes: # 从Dockerfile中获取的Volume目录列表
      - name: 26f907fb3bf70c5f850d72be0d60db4a #将原目录名映射为volume卷名
        emptyDir: {}
"""
        }
        val specObj = rollingSetSpecService.getDeploySpec(
            envStackPkId, """
{
    "apiVersion": "carbon.taobao.com/v1",
    "kind": "RollingSet",
    "metadata": {
        "name": "new-runtime-app11-27a3de6b-5e19-4e8d-ad85--n3",
        "namespace": "svl-new-runtime-app11"
    }
}""".trimIndent(), ResourceObjectFormatEnum.JSON, WorkloadMetadataConstraint(
                appName = "new-runtime-app11",
                resourceGroup = "new-runtime-app11_testing_4409907_testhost",
                unit = "CENTER_UNIT.center",
                stage = "DAILY",
                site = "na131",
                clusterId = "cb87fee031fe34ef38e134529291cf115",
                subgroup = "default",
                namespace = "svl-new-runtime-app11"
            )
        )
        kotlin.test.assertEquals(
            """apiVersion: carbon.taobao.com/v1
kind: RollingSet
metadata:
  annotations:
    sigma.ali/upgrade-merge-labels: tags.sunfire.com/app-deploy-version
  labels: {}
  name: new-runtime-app11-27a3de6b-5e19-4e8d-ad85--n3
  namespace: svl-new-runtime-app11
spec:
  healthCheckerConfig:
    lv7Config:
      lostCountThreshold: 60
      lostTimeout: 300
      path: /checkpreload.htm
      port: 80
    type: Lv7Health
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: \"true\"
        pod.beta1.sigma.ali/naming-register-state: \"working_online\"
        sigma.ali/use-unified-pv: \"true\"
        sigma.ali/enable-apprules-injection: \"true\"
        pod.beta1.sigma.ali/container-extra-config: '{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\",
          \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}'
        pods.sigma.alibaba-inc.com/inject-pod-sn: \"true\"
        deployment.normandy.com/runtime-version: V146647941
      labels:
        sigma.ali/inject-staragent-sidecar: \"true\"
        app.hippo.io/maxInstancePerNode: \"10\"
        app.hippo.io/pod-version: v3.0
        sigma.ali/env-sign: testing
        app.c2.io/platform: serverless
        app.c2.io/runtime-source: aone-serverless
        alibabacloud.com/cpuBindStrategy: Spread
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - -c
        - /home/<USER>/start_supervisor.sh
        command:
        - /bin/sh
        env:
        - name: ali_aone_timestamp
          value: \"*************\"
        - name: ali_start_app
          value: \"no\"
        - name: ali_run_mode
          value: \"common_vm\"
        - name: ali_safty_out
          value: \"1\"
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.uid
        - name: ali_run_init
          value: \"thin\"
        - name: ali_jvm_cgroup
          value: \"true\"
        - name: FIBER_VERSION
          value: \"2_0\"
        - name: AJDK_WISP
          value: \"OFF\"
        - name: AJDK_TENANT
          value: \"OFF\"
        - name: exec_scm_hook
          value: \"yes\"
        - name: HIPPO_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['app.hippo.io/cluster-name']
        - name: C2_IDC
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['sigma.ali/site']
        - name: roleId
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['app.hippo.io/group-name']
        - name: envSign
          value: \"testing\"
        - name: ali_env_sign
          value: \"testing\"
        - name: trafficRouteLabel
          value: \"testing\"
        - name: crIds
          value: \"23097575\"
        image: hub.docker.alibaba-inc.com/aone/new-runtime-app11:20231106150301467810@sha256:6c1b8c2d748aba6f9694a0476aeb3a8110013c1ac066592a032168800788ecb5
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/liaoyuan-out
          name: 26f907fb3bf70c5f850d72be0d60db4a
      dnsPolicy: Default
      enableServiceLinks: false
      terminationGracePeriodSeconds: 1
      volumes:
      - emptyDir: {}
        name: 26f907fb3bf70c5f850d72be0d60db4a""".trimIndent(), YamlUtils.dump(specObj).trimIndent()
        )
    }
}