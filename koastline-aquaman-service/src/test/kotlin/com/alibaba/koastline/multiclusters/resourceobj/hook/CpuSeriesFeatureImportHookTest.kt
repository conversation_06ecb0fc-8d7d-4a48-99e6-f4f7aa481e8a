package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Rule
import org.junit.Test
import org.junit.rules.ExpectedException
import testutils.BaseTest
import kotlin.test.assertEquals

class CpuSeriesFeatureImportHookTest : BaseTest() {

    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    @InjectMockKs
    private lateinit var hook: CpuSeriesFeatureImportHook

    @MockK
    private lateinit var commonProperties: CommonProperties


    @Test
    fun preProcess_required() {
        every {
            commonProperties.get("C01")
        } returns listOf(
            "Skylake",
            "CascadeLake",
        )

        val param = mapOf(
            "requiredCpuModelList" to listOf("C01")
        )
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = CpuSeriesFeatureImportHook.CPU_MODEL_REQUIRED,
            paramMap = YamlUtils.dump(param)
        )
        val got = hook.preProcess(featureImport)
        println(got.paramMap)
        assertEquals(
            mapOf(
                "requiredCpuModelList" to listOf("Skylake", "CascadeLake")
            ),
            YamlUtils.load(got.paramMap!!)
        )
    }

    @Test
    fun preProcess_preferred() {
        every {
            commonProperties.get("C02")
        } returns listOf(
            "IceLake",
            "SapphireRapids",
        )

        val param = mapOf(
            "requiredCpuModelList" to listOf("C02")
        )
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = CpuSeriesFeatureImportHook.CPU_MODEL_PREFERRED,
            paramMap = YamlUtils.dump(param)
        )
        val got = hook.preProcess(featureImport)
        println(got.paramMap)
        assertEquals(
            mapOf(
                "requiredCpuModelList" to listOf("IceLake", "SapphireRapids")
            ),
            YamlUtils.load(got.paramMap!!)
        )
    }

    @Test
    fun preProcess_multiple() {
        every {
            commonProperties.get("C02")
        } returns listOf(
            "IceLake",
            "SapphireRapids",
        )

        every {
            commonProperties.get("C01")
        } returns listOf(
            "Skylake",
            "CascadeLake",
        )


        val param = mapOf(
            "requiredCpuModelList" to listOf("C02", "C01")
        )
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = CpuSeriesFeatureImportHook.CPU_MODEL_PREFERRED,
            paramMap = YamlUtils.dump(param)
        )
        val got = hook.preProcess(featureImport)
        println(got.paramMap)
        assertEquals(
            mapOf(
                "requiredCpuModelList" to listOf("IceLake", "SapphireRapids", "Skylake", "CascadeLake")
            ),
            YamlUtils.load(got.paramMap!!)
        )
    }
}