package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.data.vo.resourceobj.UserLabel
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.TESTING_ENV_COMMON_APP_NAME
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.schedule.model.DeclarationData
import com.alibaba.koastline.multiclusters.schedule.model.OrientedDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants.Companion.RESOURCE_NUM
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants.Companion.SCALE_NUM
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeployScheduleService
import io.mockk.every
import io.mockk.spyk
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import org.junit.Test

class DeployScheduleServiceTest {

    @Test
    fun testDoSchedule_while_is_not_serverless() {
        val content = getBaseContent()
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(4, scheduleResult.workloadExpectedStates.size)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_has_serverless_resources() {
        val content = getBaseContent()
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_contains_serverless()
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(5, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id", scheduleResult.workloadExpectedStates[4].workloadMetadataConstraint.runtimeId)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_has_not_serverless_resources_and_is_not_serverless_env() {
        val content = getBaseContent()
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = content.resourceScope.envStackId!!,
                    externalType = UserLabelExternalType.ENV_STACK.name,
                    labelName = "SERVERLESS_STATUS"
                )
            } returns null
            every {
                stackServerlessBaseAppBindingService.getStackServerlessBaseAppBindingData(
                    envStackId = content.resourceScope.envStackId!!
                )
            } returns null
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(4, scheduleResult.workloadExpectedStates.size)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_do_not_has_serverless_resources_with_declarative() {
        val now = Date(Instant.now().toEpochMilli())
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(scheduleEnvType = SERVERLESS_APP)
        )
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
            every {
                scheduleStandardService.getServerlessRuntimeTemplateByEnv(APP_NAME, RESOURCE_GROUP, ENV_STACK_ID)
            } returns RT
            every {
                scheduleStandardService.getServerlessRuntimeTemplate(any(), any(), any(), any())
            } answers {
                callOriginal()
            }
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(RESOURCE_GROUP)
            } returns ApREDeedResourceGroupBindingData(id = 1,apREDeedKey = "kgSa3ylA4GOv6WaB3VAQv1PBjgyphHMf", resourceGroup = RESOURCE_GROUP, appName = APP_NAME, gmtCreate = now, gmtModified = now)
            every {
                declarativeScaleOutScheduleService.doSchedule(any())
            } returns ScheduleResult(
                workloadExpectedStates = listOf(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", APP_NAME, "runtime_id_1"),
                        params = mapOf(SCALE_NUM to "1")
                    )
                )
            )
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = content.resourceScope.envStackId!!,
                    externalType = UserLabelExternalType.ENV_STACK.name,
                    labelName = "SERVERLESS_STATUS"
                )
            } returns UserLabel(
                externalType = UserLabelExternalType.ENV_STACK.name,
                externalId = content.resourceScope.envStackId!!,
                labelName = "SERVERLESS_STATUS",
                labelValue = "SUCCESS",
                creator = "",
                modifier = "",
                submitter = ""
            )
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(5, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id_1", scheduleResult.workloadExpectedStates[4].workloadMetadataConstraint.runtimeId)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_do_not_has_serverless_resources_with_serverlessAppName() {
        val now = Date(Instant.now().toEpochMilli())
        val serverlessBaseAppName = "serverless-base-app"
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(
                scheduleEnvType = SERVERLESS_APP,
                serverlessBaseAppName = serverlessBaseAppName
            )
        )
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
            every {
                scheduleStandardService.getServerlessRuntimeTemplateByBaseApp(appName = APP_NAME, resourceGroup = RESOURCE_GROUP, serverlessBaseAppName = serverlessBaseAppName)
            } returns RT
            every {
                scheduleStandardService.getServerlessRuntimeTemplate(any(), any(), any(), any())
            } answers {
                callOriginal()
            }
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(RESOURCE_GROUP)
            } returns ApREDeedResourceGroupBindingData(id = 1,apREDeedKey = "kgSa3ylA4GOv6WaB3VAQv1PBjgyphHMf", resourceGroup = RESOURCE_GROUP, appName = APP_NAME, gmtCreate = now, gmtModified = now)
            every {
                declarativeScaleOutScheduleService.doSchedule(any())
            } returns ScheduleResult(
                workloadExpectedStates = listOf(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", APP_NAME, "runtime_id_1"),
                        params = mapOf(SCALE_NUM to "1")
                    )
                )
            )
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = content.resourceScope.envStackId!!,
                    externalType = UserLabelExternalType.ENV_STACK.name,
                    labelName = "SERVERLESS_STATUS"
                )
            } returns UserLabel(
                externalType = UserLabelExternalType.ENV_STACK.name,
                externalId = content.resourceScope.envStackId!!,
                labelName = "SERVERLESS_STATUS",
                labelValue = "SUCCESS",
                creator = "",
                modifier = "",
                submitter = ""
            )
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(5, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id_1", scheduleResult.workloadExpectedStates[4].workloadMetadataConstraint.runtimeId)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_do_not_has_serverless_resources_and_daily_group_with_non_declarative() {
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(scheduleEnvType = SERVERLESS_APP)
        )
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
            every {
                scheduleStandardService.getServerlessRuntimeTemplate(any(), any(), any(), any())
            } answers {
                callOriginal()
            }
            every {
                scheduleStandardService.getServerlessRuntimeTemplateByEnv(APP_NAME, RESOURCE_GROUP, ENV_STACK_ID)
            } returns RT
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(RESOURCE_GROUP)
            } returns null
            every {
                skylineApi.getAppGroup(RESOURCE_GROUP)
            } returns AppGroup(name = RESOURCE_GROUP, usageType = "test", appName = APP_NAME, originalName = TESTING_ENV_COMMON_APP_NAME)
            every {
                declarativeScaleOutScheduleService.doSchedule(any())
            } returns ScheduleResult(
                workloadExpectedStates = listOf(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na131", "CENTER_UNIT.center", "DAILY", "default", "cluster_wulanchabu", APP_NAME, "runtime_id_1"),
                        params = mapOf(SCALE_NUM to "1")
                    )
                )
            )
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = content.resourceScope.envStackId!!,
                    externalType = UserLabelExternalType.ENV_STACK.name,
                    labelName = "SERVERLESS_STATUS"
                )
            } returns UserLabel(
                externalType = UserLabelExternalType.ENV_STACK.name,
                externalId = content.resourceScope.envStackId!!,
                labelName = "SERVERLESS_STATUS",
                labelValue = "SUCCESS",
                creator = "",
                modifier = "",
                submitter = ""
            )
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(5, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id_1", scheduleResult.workloadExpectedStates[4].workloadMetadataConstraint.runtimeId)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_do_not_has_serverless_resources_with_non_declarative() {
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(scheduleEnvType = SERVERLESS_APP)
        )
        val deployScheduleService = spyk<DeployScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            } returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
            every {
                scheduleStandardService.getServerlessRuntimeTemplate(any(), any(), any(), any())
            } answers {
                callOriginal()
            }
            every {
                scheduleStandardService.getServerlessRuntimeTemplateByEnv(APP_NAME, RESOURCE_GROUP, ENV_STACK_ID)
            } returns RT
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(RESOURCE_GROUP)
            } returns null
            every {
                skylineApi.getAppGroup(RESOURCE_GROUP)
            } returns AppGroup(name = RESOURCE_GROUP, usageType = "PUBLISH", appName = APP_NAME, originalName = APP_NAME)
            every {
                skylineApi.getEnvStackEnvLevelByStackId(ENV_STACK_ID)
            }returns "production"
            every {
                envLevelStageMappingService.getStandardEnvLevel("production")
            } answers {
                callOriginal()
            }
            every {
                envLevelStageMappingService.listStageByEnvLevel("production")
            }returns listOf("PUBLISH")
            every {
                scheduleStandardService.getServerlessMatchApRELabelList(RT,ENV_STACK_ID)
            }returns emptyList()
            every {
                apREService.requireApREDeedResultByApREDeedContent(any())
            } returns ApREDeedResult(
                deedDO = ApREDeedDO(), matchDeclarations = listOf(
                    MatchDeclaration(apres = listOf(
                        ApREDO(1L, "",null,"creator","MMMMMMM", "cn-zhangjiakou",
                            "na620","PUBLISH","CENTER_UNIT.center",null,null,null,null,null, mutableListOf(), emptyList())
                    ))
                )
            )
            every {
                nonDeclarativeScaleOutScheduleService.doSchedule(
                    content.copy(
                        resourceScope = ResourceScope(
                            appName = APP_NAME,
                            envStackId = ENV_STACK_ID,
                            resourceGroup = RESOURCE_GROUP
                        ),
                        declarationData = DeclarationData(
                            declaration = OrientedDeclaration(
                                site = "na620", unit = "CENTER_UNIT.center", stage = "PUBLISH"
                            )
                        ),
                        scheduleRequestParam = checkNotNull(content.scheduleRequestParam).copy(
                            replicas = 1,
                            serverless = true,
                            serverlessRuntimeTemplate = RT
                        )
                    )
                )
            } returns ScheduleResult(
                workloadExpectedStates = listOf(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", APP_NAME, "runtime_id_1"),
                        params = mapOf(SCALE_NUM to "1")
                    )
                )
            )
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalId = content.resourceScope.envStackId!!,
                    externalType = UserLabelExternalType.ENV_STACK.name,
                    labelName = "SERVERLESS_STATUS"
                )
            } returns UserLabel(
                externalType = UserLabelExternalType.ENV_STACK.name,
                externalId = content.resourceScope.envStackId!!,
                labelName = "SERVERLESS_STATUS",
                labelValue = "SUCCESS",
                creator = "",
                modifier = "",
                submitter = ""
            )
        }
        val scheduleResult = deployScheduleService.doSchedule(content)
        assertEquals(5, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id_1", scheduleResult.workloadExpectedStates[4].workloadMetadataConstraint.runtimeId)
    }


    companion object {
        const val APP_NAME = "normandy-test-app4"
        const val ENV_STACK_ID = "d13f890e-d4f4-48c6-81f4-6e0b8f595244"
        const val RESOURCE_GROUP = "normandy-test-app4_prehost"
        const val RT = "serverless/runtime-app-demo\$\$common\$\$SPEC:2-4"

        fun getBaseContent(): ScheduleRequestContent {
            return ScheduleRequestContent(
                resourceScope = ResourceScope(APP_NAME,ENV_STACK_ID, RESOURCE_GROUP),
                scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.DEPLOY),
                scheduleRequestParam = ScheduleRequestParam()
            )
        }

        fun getScheduleResult_only_asi(): ScheduleResult {
            return ScheduleResult(
                workloadExpectedStates = listOf(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_a01", APP_NAME, null),
                        params = mapOf(RESOURCE_NUM to "4")
                    ),
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_a02", APP_NAME, null),
                        params = mapOf(RESOURCE_NUM to "3")
                    ),
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b01", APP_NAME, null),
                        params = mapOf(RESOURCE_NUM to "2")
                    ),
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", APP_NAME, null),
                        params = mapOf(RESOURCE_NUM to "1")
                    )
                )
            )
        }

        fun getScheduleResult_contains_serverless(): ScheduleResult {
            return ScheduleResult(
                workloadExpectedStates = getScheduleResult_only_asi().workloadExpectedStates.toMutableList().apply {
                    this.add(
                        WorkloadExpectedState(
                            workloadMetadataConstraint = WorkloadMetadataConstraint(APP_NAME, RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", APP_NAME, "runtime_id"),
                            params = mapOf(RESOURCE_NUM to "1")
                        )
                    )
                }
            )
        }
    }
}