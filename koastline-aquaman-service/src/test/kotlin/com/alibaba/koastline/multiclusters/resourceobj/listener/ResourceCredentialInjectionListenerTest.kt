package com.alibaba.koastline.multiclusters.resourceobj.listener

import com.alibaba.cse.models.v1alpha1.cloneset.CloneSet
import com.alibaba.cse.models.v1alpha1.cloneset.CloneSetSpec
import com.alibaba.cse.models.v1alpha1.cloneset.CloneSetStatus
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import io.kubernetes.client.openapi.models.V1Container
import io.kubernetes.client.openapi.models.V1EnvVar
import io.kubernetes.client.openapi.models.V1ObjectMeta
import io.kubernetes.client.openapi.models.V1PodSpec
import io.kubernetes.client.openapi.models.V1PodTemplateSpec
import io.kubernetes.client.openapi.models.V1StatefulSet
import io.kubernetes.client.openapi.models.V1StatefulSetSpec
import org.junit.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows

/**
 * @author: fudai.yf
 * @since: 2023/10/10
 */
class ResourceCredentialInjectionListenerTest {

    @Test
    fun watch_cloneSetShouldThrowExceptionWhenEnvConflict() {

        assertThrows<FeatureConflictException> {
            ResourceCredentialInjectionListener().watch(
                baseResourceObjectSpec = YamlUtils.dump(
                    CloneSet(
                        spec = CloneSetSpec(null, null, null,
                            V1PodTemplateSpec().also {
                                it.spec = V1PodSpec().also { s ->
                                    s.containers = listOf(V1Container().also { c ->
                                        c.name = "main"
                                        c.env = listOf(V1EnvVar().also { e -> e.name = "ALIYUN_LOGTAIL_USER_ID" })
                                    })
                                }
                            }, null, null, null),
                        apiVersion = "", kind = "", metadata = V1ObjectMeta(), status = CloneSetStatus())
                ),
                resourceObjectProtocol = ResourceObjectProtocolEnum.CloneSet,
                featureSpec = YamlUtils.dump(
                    CloneSet(
                        spec = CloneSetSpec(null, null, null,
                            V1PodTemplateSpec().also {
                                it.spec = V1PodSpec().also { s ->
                                    s.containers = listOf(V1Container().also { c ->
                                        c.name = "main"
                                        c.env = listOf(V1EnvVar().also { e -> e.name = "ALIYUN_LOGTAIL_USER_ID" })
                                    })
                                }
                            }, null, null, null),
                        apiVersion = "", kind = "", metadata = V1ObjectMeta(), status = CloneSetStatus())
                )
            )
        }
    }

    @Test
    fun watch_cloneSetShouldNotThrowExceptionWhenEnvNotConflict() {

        assertDoesNotThrow {
            ResourceCredentialInjectionListener().watch(
                baseResourceObjectSpec = YamlUtils.dump(
                    CloneSet(
                        spec = CloneSetSpec(null, null, null,
                            V1PodTemplateSpec().also {
                                it.spec = V1PodSpec().also { s ->
                                    s.containers = listOf(V1Container().also { c ->
                                        c.name = "main"
                                        c.env = listOf(V1EnvVar().also { e -> e.name = "TEST_ENV" })
                                    })
                                }
                            }, null, null, null),
                        apiVersion = "", kind = "", metadata = V1ObjectMeta(), status = CloneSetStatus())
                ),
                resourceObjectProtocol = ResourceObjectProtocolEnum.CloneSet,
                featureSpec = YamlUtils.dump(
                    CloneSet(
                        spec = CloneSetSpec(null, null, null,
                            V1PodTemplateSpec().also {
                                it.spec = V1PodSpec().also { s ->
                                    s.containers = listOf(V1Container().also { c ->
                                        c.name = "main"
                                        c.env = listOf(V1EnvVar().also { e -> e.name = "TEST_ENV" })
                                    })
                                }
                            }, null, null, null),
                        apiVersion = "", kind = "", metadata = V1ObjectMeta(), status = CloneSetStatus())
                    )
                )
        }
    }

    @Test
    fun watch_statefulSetShouldNotThrowExceptionWhenEnvNotConflict() {

        assertDoesNotThrow {
            ResourceCredentialInjectionListener().watch(
                baseResourceObjectSpec = YamlUtils.dump(
                    V1StatefulSet().also {
                        it.spec = V1StatefulSetSpec().also {
                            V1PodTemplateSpec().also { pts ->
                                pts.spec = V1PodSpec().also { s ->
                                    s.containers = listOf(V1Container().also { c ->
                                        c.name = "main"
                                        c.env = listOf(V1EnvVar().also { e -> e.name = "ALIYUN_LOGTAIL_USER_ID" })
                                    })
                                }
                            }
                        }
                    }
                ),
                resourceObjectProtocol = ResourceObjectProtocolEnum.StatefulSet,
                featureSpec = YamlUtils.dump(
                    V1StatefulSetSpec().also {
                        V1PodTemplateSpec().also { pts ->
                            pts.spec = V1PodSpec().also { s ->
                                s.containers = listOf(V1Container().also { c ->
                                    c.name = "main"
                                    c.env = listOf(V1EnvVar().also { e -> e.name = "ALIYUN_LOGTAIL_USER_ID" })
                                })
                            }
                        }
                    }
                ))
        }
    }

    private fun YamlUtils.dump(obj: Any): String {
        val m = JsonUtils.gsonReadValue(JsonUtils.gsonWriteValueAsString(obj), Map::class.java)
        return dump(m as Map<String, Any>)
    }
}