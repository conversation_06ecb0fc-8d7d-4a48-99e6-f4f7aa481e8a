package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.resourceobj.UserLabel
import com.alibaba.koastline.multiclusters.event.Event
import com.alibaba.koastline.multiclusters.event.ResourceSpecChangeEventPayload
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.model.AppResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelType
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class UserLabelServiceTest {

    @Test
    fun `createOrUpdateAppResourceSpec -- send event`() {
        val req = AppResourceSpec(
            "a",
            ResourceSpec("4", "8", "60"),
            "xeid"
        )

        val eventCap = slot<Event<ResourceSpecChangeEventPayload>>()
        val service = spyk<UserLabelService>().apply {
            every { userLabelBaseService.findByExternalAndLabelWithCache("a", "APPLICATION", any()) } returns null
            every { userLabelBaseService.createWithCache(any()) } returns mockk()
            every { userExtraLabelService.updateExtraLabel(any()) } just runs
            every { eventProducer.sendIgnoreError(capture(eventCap)) } just runs
        }

        service.createOrUpdateAppResourceSpec(req)
        assertEquals(eventCap.captured.data.appName, "a")
        assertNull(eventCap.captured.data.resourceGroup)
        assertEquals(eventCap.captured.data.resourceSpec.cpu, "4")
    }

    @Test
    fun testGetAppResourceSpec_while_normal() {
        val appName = "normandy-test-app4"
        val userLabelService = spyk<UserLabelService>() {
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.MODEL.code)
            } returns UserLabel(
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                externalId = appName,
                labelName = UserLabelType.MODEL.code,
                labelValue = "4-8-60",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.GPU_COUNT.code)
            } returns UserLabel(
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                externalId = appName,
                labelName = UserLabelType.GPU_COUNT.code,
                labelValue = "2",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
        }
        val resourceSpec = userLabelService.getAppResourceSpec(appName)
        assertEquals(resourceSpec.cpu,"4")
        assertEquals(resourceSpec.memory,"8")
        assertEquals(resourceSpec.disk,"60")
        assertEquals(resourceSpec.gpu,"2")
    }

    @Test
    fun testGetAppResourceSpec_while_gpu_is_null() {
        val appName = "normandy-test-app4"
        val userLabelService = spyk<UserLabelService>() {
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.MODEL.code)
            } returns UserLabel(
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                externalId = appName,
                labelName = UserLabelType.MODEL.code,
                labelValue = "4-8-60",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.GPU_COUNT.code)
            } returns null
        }
        val resourceSpec = userLabelService.getAppResourceSpec(appName)
        assertEquals(resourceSpec.cpu,"4")
        assertEquals(resourceSpec.memory,"8")
        assertEquals(resourceSpec.disk,"60")
        assertEquals(resourceSpec.gpu,null)
    }

    @Test
    fun testGetAppResourceSpec_while_gpu_is_zero() {
        val appName = "normandy-test-app4"
        val userLabelService = spyk<UserLabelService>() {
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.MODEL.code)
            } returns UserLabel(
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                externalId = appName,
                labelName = UserLabelType.MODEL.code,
                labelValue = "4-8-60",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
            every { userLabelBaseService.findByExternalAndLabelWithCache(
                externalId = appName,
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                labelName = UserLabelType.GPU_COUNT.code)
            } returns UserLabel(
                externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                externalId = appName,
                labelName = UserLabelType.GPU_COUNT.code,
                labelValue = "0",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
        }
        val resourceSpec = userLabelService.getAppResourceSpec(appName)
        assertEquals(resourceSpec.cpu,"4")
        assertEquals(resourceSpec.memory,"8")
        assertEquals(resourceSpec.disk,"60")
        assertEquals(resourceSpec.gpu,null)
    }

    @Test
    fun testCreateAndOverrideWhileExist_while_normal() {
        val userLabelCreateReqDto = UserLabelCreateReqDto(
            externalId = "normandy-test-app4",
            externalType = UserLabelExternalType.APPLICATION,
            labelName = "model",
            labelValue = "4-8-60",
            creator = "creator"
        )
        val userLabelService = spyk<UserLabelService>() {
            justRun {
                userExtraLabelService.updateExtraLabel(any())
            }
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalType = userLabelCreateReqDto.externalType.name,
                    externalId = userLabelCreateReqDto.externalId,
                    labelName = userLabelCreateReqDto.labelName
                )
            } returns UserLabel(
                externalType = userLabelCreateReqDto.externalType.name,
                externalId = userLabelCreateReqDto.externalId,
                labelName = userLabelCreateReqDto.labelName,
                labelValue = "2-4-60",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
            justRun {
                userLabelBaseService.deleteByExternalAndLabelWithCache(
                    externalId = userLabelCreateReqDto.externalId,
                    externalType = userLabelCreateReqDto.externalType.name,
                    labelName = userLabelCreateReqDto.labelName,
                    modifier = userLabelCreateReqDto.creator
                )
            }
            every {
                userLabelBaseService.createWithCache(userLabelCreateReqDto)
            }returns UserLabel(
                externalType = userLabelCreateReqDto.externalType.name,
                externalId = userLabelCreateReqDto.externalId,
                labelName = userLabelCreateReqDto.labelName,
                labelValue = userLabelCreateReqDto.labelValue!!,
                submitter = "sys",
                creator = "creator",
                modifier = "creator"
            )

            every { eventProducer.sendIgnoreError(any()) } just runs
        }
        userLabelService.createAndOverrideWhileExist(userLabelCreateReqDto = userLabelCreateReqDto)
        verify {
            userLabelService.userLabelBaseService.deleteByExternalAndLabelWithCache(
                externalId = userLabelCreateReqDto.externalId,
                externalType = userLabelCreateReqDto.externalType.name,
                labelName = userLabelCreateReqDto.labelName,
                modifier = userLabelCreateReqDto.creator
            )
            userLabelService.userLabelBaseService.createWithCache(userLabelCreateReqDto)
        }
    }

    @Test
    fun testCreateAndOverrideWhileExist_while_gpuCount_label_value_is_null() {
        val userLabelCreateReqDto = UserLabelCreateReqDto(
            externalId = "normandy-test-app4",
            externalType = UserLabelExternalType.APPLICATION,
            labelName = UserLabelType.GPU_COUNT.code,
            labelValue = null,
            creator = "creator"
        )
        val userLabelService = spyk<UserLabelService>() {
            justRun {
                userExtraLabelService.updateExtraLabel(any())
            }
            every {
                userLabelBaseService.findByExternalAndLabelWithCache(
                    externalType = userLabelCreateReqDto.externalType.name,
                    externalId = userLabelCreateReqDto.externalId,
                    labelName = userLabelCreateReqDto.labelName
                )
            } returns UserLabel(
                externalType = userLabelCreateReqDto.externalType.name,
                externalId = userLabelCreateReqDto.externalId,
                labelName = userLabelCreateReqDto.labelName,
                labelValue = "1",
                submitter = "sys",
                creator = "creator",
                modifier = "modifier"
            )
            justRun {
                userLabelBaseService.deleteByExternalAndLabelWithCache(
                    externalId = userLabelCreateReqDto.externalId,
                    externalType = userLabelCreateReqDto.externalType.name,
                    labelName = userLabelCreateReqDto.labelName,
                    modifier = userLabelCreateReqDto.creator
                )
            }
        }
        userLabelService.createAndOverrideWhileExist(userLabelCreateReqDto = userLabelCreateReqDto)
        verify {
            userLabelService.userLabelBaseService.deleteByExternalAndLabelWithCache(
                externalId = userLabelCreateReqDto.externalId,
                externalType = userLabelCreateReqDto.externalType.name,
                labelName = userLabelCreateReqDto.labelName,
                modifier = userLabelCreateReqDto.creator
            )
        }
    }
}