import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.Test
import org.yaml.snakeyaml.DumperOptions
import org.yaml.snakeyaml.LoaderOptions
import org.yaml.snakeyaml.Yaml
import org.yaml.snakeyaml.nodes.Tag
import org.yaml.snakeyaml.serializer.SerializerException
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors

class YamlUtilsTest {


    @Test
    fun `test load function is thread-safe`() {
        val threadCount = 2000
        val latch = CountDownLatch(threadCount)
        val executor = Executors.newFixedThreadPool(threadCount)
        val yaml = """apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations: {sigma.ali/disable-cascading-deletion: 'true'}
  labels: {statefulset.sigma.ali/mode: sigma, sigma.ali/app-name: normandy-test-app4}
  "creationTimestamp": "2022-01-05T03:57:35Z"
spec:
  template:
    metadata:
      annotations: {pod.beta1.sigma.ali/alarming-off-upgrade: 'true'}
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
    spec:
      containers:
      - env:
        - {name: envSign, value: production}
        image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20221130190655566578_publish
        name: main
        volumeMounts:
        - {name: e6e7d04f2c902940a90aad534532bac9, mountPath: /home/<USER>/normandy-test-app4/logs}
        livenessProbe:
          initialDelaySeconds: 300
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
          exec:
            command: [/bin/sh, -c, '[ ! -x /home/<USER>/liveness.sh ] && exit 1; sudo
                -u admin /home/<USER>/liveness.sh 2>&1;']
      volumes:
      - name: e6e7d04f2c902940a90aad534532bac9
        emptyDir: {}""".trimIndent()

        repeat(threadCount) {
            executor.submit {
                try {
                    YamlUtils.load(yaml)
                } finally {
                    latch.countDown()
                }
            }
        }

        latch.await()
        executor.shutdown()
    }

    @Test
    fun `test load anchor and dump it back`() {

        val result = Yaml(DumperOptions()).dumpAs(getMapFromAnchor().first, Tag.MAP, DumperOptions.FlowStyle.BLOCK)
        assertEquals(getMapFromAnchor().second, result)

    }

    @Test(expected = SerializerException::class)
    fun `test prevent dump yaml anchor and throw exception`() {
        YamlUtils.dump(getMapFromAnchor().first)

    }

    fun getMapFromAnchor(): Pair<Map<String, Any>, String> {
        val yaml = """
&id001
*id001: 'true'

""".trimIndent()
        val map = Yaml(LoaderOptions().apply {
            this.allowRecursiveKeys = true
        }).load(yaml) as Map<String, Any>
        return Pair(map, yaml)
    }
}