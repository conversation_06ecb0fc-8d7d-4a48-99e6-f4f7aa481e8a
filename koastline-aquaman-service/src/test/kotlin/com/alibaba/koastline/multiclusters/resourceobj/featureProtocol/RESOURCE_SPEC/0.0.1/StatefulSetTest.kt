package com.alibaba.koastline.multiclusters.resourceobj.featureProtocol.RESOURCE_SPEC.`0`.`0`.`1`

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import com.alibaba.koastline.multiclusters.resourceobj.FeatureProtocolLoadService
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class StatefulSetTest {
    @Test
    fun testGetFeatureProtocol() {
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    any()
                )
            } returns true
        }

        val resourceSpecStatefulSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.1",
            """fake""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        assertEquals(featureProtocolLoadService.cache.size, 0)
        var patch = featureProtocolLoadService.getFeatureProtocol(resourceSpecStatefulSetProtocol)
        assertEquals(
            expectedStsProtocol(), patch
        )
    }

    companion object {
        fun expectedStsProtocol() = """
<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "by-cluster"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
        fun expectedStsBetaProtocol() = """<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "by-cluster"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
    }

}