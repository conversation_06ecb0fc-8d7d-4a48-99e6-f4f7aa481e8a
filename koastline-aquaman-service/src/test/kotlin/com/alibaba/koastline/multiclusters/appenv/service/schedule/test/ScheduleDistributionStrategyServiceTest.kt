package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelector
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType.CLUSTER_LABEL
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.StackServerlessBaseAppBindingDataDO
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpecOrigin
import com.alibaba.koastline.multiclusters.schedule.model.OrientedDeclaration
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyEnvType
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleStrategyReqDto
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.InternalPlatformDsl
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import java.util.*
import kotlin.test.assertEquals
import org.junit.Test

class ScheduleDistributionStrategyServiceTest {

    @Test
    fun `testComputeScheduleStrategy while don't has apREDeclarationPatch`(){
        val scheduleStrategyService = spyk<ScheduleStrategyService>() {
            this.resourcePoolService = ResourcePoolService()
            every {
                apREService.queryClustersByApREDeedContent(any())
            }returns fakeApREDeedResultWithSingleMatchDeclaration()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(any(),any(),any(),any(),any())
            }returns null
        }
        val scheduleStrategyResult = scheduleStrategyService.computeScheduleStrategy(ScheduleStrategyReqDto(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4host",
            scheduleStrategyEnvType = ScheduleStrategyEnvType.ASI,
            declarative = false,
            declaration = OrientedDeclaration (
                site = "na610",
                stage = "PUBLISH",
                unit = "CENTER_UNIT.center")
            )
        )
        assertEquals(1, scheduleStrategyResult.apres.size)
        assertEquals(1, scheduleStrategyResult.apres[0].resources.size)
        assertEquals(3, scheduleStrategyResult.apres[0].resources[0].clusters.size)
        assertEquals(1, scheduleStrategyResult.apres[0].resources[0].weight)
    }

    @Test
    fun `testComputeScheduleStrategy while is serverless`(){
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            apREService = ApREService(ObjectMapper())
            every {
                resourceObjectService.getResourceBaselineSpec(any())
            } returns Pair(ResourceSpec(
                cpu = "4", memory = "8", disk = "60", gpu = null
            ), ResourceSpecOrigin.APP)
        }
        val scheduleStrategyService = spyk<ScheduleStrategyService>() {
            this.resourcePoolService = ResourcePoolService()
            this.scheduleStandardService = scheduleStandardService
            every {
                apREService.queryClustersByApREDeedContent(any())
            }returns fakeApREDeedResultWithSingleMatchDeclaration()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(any(),any(),any(),any(),any())
            }returns null
            every {
                scheduleStandardService.stackServerlessBaseAppBindingService.getUniqueStackServerlessBaseAppBindingData(listOf("envStackId"))
            } returns StackServerlessBaseAppBindingDataDO(
                id = 1L, envStackId = "envStackId", serverlessBaseAppName = "serverlessBaseAppName", creator = "00001", modifier = "00001", extraParams = null
            )
            every {
                scheduleStandardService.skylineApi.listBindingEnvStackIdByResourceGroup("normandy-test-app4host")
            }returns listOf("envStackId")
        }
        val scheduleStrategyResult = scheduleStrategyService.computeScheduleStrategy(ScheduleStrategyReqDto(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4host",
                scheduleStrategyEnvType = ScheduleStrategyEnvType.SERVERLESS_APP,
                serverlessBaseAppName = "base_runtime",
                declarative = false,
                declaration = OrientedDeclaration (
                    site = "na610",
                    stage = "PUBLISH",
                    unit = "CENTER_UNIT.center")
            )
        )
        assertEquals(1, scheduleStrategyResult.apres.size)
        assertEquals(1, scheduleStrategyResult.apres[0].resources.size)
        assertEquals(4, scheduleStrategyResult.apres[0].resources[0].clusters.size)
        assertEquals(1, scheduleStrategyResult.apres[0].resources[0].weight)
    }

    @Test
    fun `testComputeScheduleStrategy while has apREDeclarationPatch`(){
        val scheduleStrategyService = spyk<ScheduleStrategyService>() {
            this.resourcePoolService = ResourcePoolService()
            every {
                apREService.queryClustersByApREDeedContent(any())
            }returns fakeApREDeedResultWithSingleMatchDeclaration()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(any(),any(),any(),any(),any())
            }returns ApREDeclarationPatchDataDO(
                unit = "CENTER_UNIT.center", stage = "PUBLISH", creator = "", modifier = "",
                declarationPatch = DeclarationPatch(
                    patchItems = listOf(
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "x86")
                                )
                            ),
                            weight = 10
                        ),
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "m")
                                )
                            ),
                            weight = 20
                        )
                    )
                )
            )
        }
        val scheduleStrategyResult = scheduleStrategyService.computeScheduleStrategy(ScheduleStrategyReqDto(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4host",
                scheduleStrategyEnvType = ScheduleStrategyEnvType.ASI,
                declarative = false,
                declaration = OrientedDeclaration (
                    site = "na610",
                    stage = "PUBLISH",
                    unit = "CENTER_UNIT.center")
            )
        )
        assertEquals(1, scheduleStrategyResult.apres.size)
        assertEquals(2, scheduleStrategyResult.apres[0].resources.size)
        assertEquals(2, scheduleStrategyResult.apres[0].resources[0].clusters.size)
        assertEquals(10, scheduleStrategyResult.apres[0].resources[0].weight)
        assertEquals(1, scheduleStrategyResult.apres[0].resources[1].clusters.size)
        assertEquals(20, scheduleStrategyResult.apres[0].resources[1].weight)
    }

    @Test
    fun `testComputeScheduleStrategy while has multi declaration`(){
        val scheduleStrategyService = spyk<ScheduleStrategyService>() {
            this.resourcePoolService = ResourcePoolService()
            every {
                apREService.queryClustersByApREDeedContent(any())
            }returns fakeApREDeedResultWithMultiMatchDeclaration()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                    appName = "normandy-test-app4",
                    resourceGroup = "normandy-test-app4host",
                    stage ="PUBLISH",
                    unit = "CENTER_UNIT.center",
                    site = "na610"
                )
            }returns ApREDeclarationPatchDataDO(
                unit = "CENTER_UNIT.center", stage = "PUBLISH", site = "na610", creator = "", modifier = "",
                declarationPatch = DeclarationPatch(
                    patchItems = listOf(
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "x86")
                                )
                            ),
                            weight = 10
                        ),
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "m")
                                )
                            ),
                            weight = 20
                        )
                    )
                )
            )
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                    appName = "normandy-test-app4",
                    resourceGroup = "normandy-test-app4host",
                    stage ="PUBLISH",
                    unit = "CENTER_UNIT.center",
                    site = "na620"
                )
            }returns null
        }
        val scheduleStrategyResult = scheduleStrategyService.computeScheduleStrategy(ScheduleStrategyReqDto(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4host",
                scheduleStrategyEnvType = ScheduleStrategyEnvType.ASI,
                declarative = false,
                declaration = OrientedDeclaration (
                    site = "na610",
                    stage = "PUBLISH",
                    unit = "CENTER_UNIT.center")
            )
        )
        assertEquals(2, scheduleStrategyResult.apres.size)

        assertEquals(2, scheduleStrategyResult.apres[0].resources.size)
        assertEquals(2, scheduleStrategyResult.apres[0].resources[0].clusters.size)
        assertEquals(10, scheduleStrategyResult.apres[0].resources[0].weight)
        assertEquals(1, scheduleStrategyResult.apres[0].resources[1].clusters.size)
        assertEquals(20, scheduleStrategyResult.apres[0].resources[1].weight)

        assertEquals(1, scheduleStrategyResult.apres[1].resources.size)
        assertEquals(3, scheduleStrategyResult.apres[1].resources[0].clusters.size)
        assertEquals(1, scheduleStrategyResult.apres[1].resources[0].weight)
    }

    @Test
    fun `testComputeScheduleStrategy while has multi declaration and is serverless`(){
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            apREService = ApREService(ObjectMapper())
            every {
                resourceObjectService.getResourceBaselineSpec(any())
            } returns Pair(ResourceSpec(
                cpu = "4", memory = "8", disk = "60", gpu = null
            ), ResourceSpecOrigin.APP)
        }
        val scheduleStrategyService = spyk<ScheduleStrategyService>() {
            this.resourcePoolService = ResourcePoolService()
            this.scheduleStandardService = scheduleStandardService
            every {
                apREService.queryClustersByApREDeedContent(any())
            }returns fakeApREDeedResultWithMultiMatchDeclaration()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                    appName = "normandy-test-app4",
                    resourceGroup = "normandy-test-app4host",
                    stage ="PUBLISH",
                    unit = "CENTER_UNIT.center",
                    site = "na610"
                )
            }returns ApREDeclarationPatchDataDO(
                unit = "CENTER_UNIT.center", stage = "PUBLISH", site = "na610", creator = "", modifier = "",
                declarationPatch = DeclarationPatch(
                    patchItems = listOf(
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "x86")
                                )
                            ),
                            weight = 10
                        ),
                        PatchItem(
                            clusterSelector = ClusterSelector(
                                selectType = CLUSTER_LABEL,
                                labels = listOf(
                                    ClusterLabel(name = "customer/machine/type", value = "m")
                                )
                            ),
                            weight = 20
                        )
                    )
                )
            )
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(
                    appName = "normandy-test-app4",
                    resourceGroup = "normandy-test-app4host",
                    stage ="PUBLISH",
                    unit = "CENTER_UNIT.center",
                    site = "na620"
                )
            }returns null
            every {
                scheduleStandardService.stackServerlessBaseAppBindingService.getUniqueStackServerlessBaseAppBindingData(listOf("envStackId"))
            } returns StackServerlessBaseAppBindingDataDO(
                id = 1L, envStackId = "envStackId", serverlessBaseAppName = "serverlessBaseAppName", creator = "00001", modifier = "00001", extraParams = null
            )
            every {
                scheduleStandardService.skylineApi.listBindingEnvStackIdByResourceGroup("normandy-test-app4host")
            }returns listOf("envStackId")
        }
        val scheduleStrategyResult = scheduleStrategyService.computeScheduleStrategy(ScheduleStrategyReqDto(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4host",
                scheduleStrategyEnvType = ScheduleStrategyEnvType.SERVERLESS_APP,
                serverlessBaseAppName = "base_runtime",
                declarative = false,
                declaration = OrientedDeclaration (
                    site = "na610",
                    stage = "PUBLISH",
                    unit = "CENTER_UNIT.center")
            )
        )
        assertEquals(2, scheduleStrategyResult.apres.size)

        assertEquals(2, scheduleStrategyResult.apres[0].resources.size)
        assertEquals(2, scheduleStrategyResult.apres[0].resources[0].clusters.size)
        assertEquals(10, scheduleStrategyResult.apres[0].resources[0].weight)
        assertEquals(2, scheduleStrategyResult.apres[0].resources[1].clusters.size)
        assertEquals(20, scheduleStrategyResult.apres[0].resources[1].weight)

        assertEquals(1, scheduleStrategyResult.apres[1].resources.size)
        assertEquals(4, scheduleStrategyResult.apres[1].resources[0].clusters.size)
        assertEquals(1, scheduleStrategyResult.apres[1].resources[0].weight)
    }

    @Test
    fun `testBuildApREDeed while is serverless`() {
        val scheduleStrategyReqDto = ScheduleStrategyReqDto(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4host",
            scheduleStrategyEnvType = ScheduleStrategyEnvType.SERVERLESS_APP,
            serverlessBaseAppName = "base_runtime",
            declarative = false,
            declaration = OrientedDeclaration (
                site = "na610",
                stage = "PUBLISH",
                unit = "CENTER_UNIT.center")
        )
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            apREService = ApREService(ObjectMapper())
            every {
                resourceObjectService.getResourceBaselineSpec(any())
            } returns Pair(ResourceSpec(
                cpu = "4", memory = "8", disk = "60", gpu = null
            ), ResourceSpecOrigin.APP)
            every {
                stackServerlessBaseAppBindingService.getUniqueStackServerlessBaseAppBindingData(listOf("envStackId"))
            } returns StackServerlessBaseAppBindingDataDO(
                id = 1L, envStackId = "envStackId", serverlessBaseAppName = "serverlessBaseAppName", creator = "00001", modifier = "00001", extraParams = null
            )
            every {
                skylineApi.listBindingEnvStackIdByResourceGroup("normandy-test-app4host")
            }returns listOf("envStackId")
        }
        val scheduleStrategyService = spyk<ScheduleStrategyService>(recordPrivateCalls = true) {
            this.scheduleStandardService = scheduleStandardService
        }
        val apREDeedDO = InternalPlatformDsl.dynamicCall(
            scheduleStrategyService,
            "buildApREDeed",
            arrayOf(scheduleStrategyReqDto)
        ) { mockk() } as ApREDeedDO
        assertEquals(1,apREDeedDO.declarations!!.size)
        assertEquals(1,apREDeedDO.declarations!![0]!!.matchApRELabels!!.size)
        assertEquals("alibaba/apre/feature",apREDeedDO.declarations!![0]!!.matchApRELabels!![0].name)
        assertEquals("serverless/base_runtime",apREDeedDO.declarations!![0]!!.matchApRELabels!![0].value)
        assertEquals(1,apREDeedDO.declarations!![0]!!.matchApRELabels!![0].matchApREFeatureSpecs!!.size)
        assertEquals("serverless/base_runtime\$\$common\$\$SPEC:4-8",apREDeedDO.declarations!![0]!!.matchApRELabels!![0].matchApREFeatureSpecs!![0].specType)
    }

    private fun fakeApREDeedResultWithSingleMatchDeclaration(): ApREDeedResult {
        return ApREDeedResult(
            matchDeclarations = listOf(
                MatchDeclaration(
                    apres = listOf(
                        ApREDO(1L, "runtime_env_key_1",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                fakeResourceOfX86(),
                                fakeResourceOfX86(),
                                fakeResourceOfArm()
                            )
                        )
                    ),
                    declaration = Declaration(
                        az = "na610",
                        unit = "CENTER_UNIT.center",
                        stage = "PUBLISH",
                    )
                )
            ),
            deedDO = ApREDeedDO()
        )
    }

    private fun fakeApREDeedResultWithMultiMatchDeclaration(): ApREDeedResult {
        return ApREDeedResult(
            matchDeclarations = listOf(
                MatchDeclaration(
                    apres = listOf(
                        ApREDO(1L, "runtime_env_key_1",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                fakeResourceOfX86(),
                                fakeResourceOfX86(),
                                fakeResourceOfArm()
                            )
                        )
                    ),
                    declaration = Declaration(
                        az = "na610",
                        unit = "CENTER_UNIT.center",
                        stage = "PUBLISH",
                    )
                ),
                MatchDeclaration(
                    apres = listOf(
                        ApREDO(2L, "runtime_env_key_2",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na620","PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                fakeResourceOfX86(),
                                fakeResourceOfX86(),
                                fakeResourceOfArm()
                            )
                        )
                    ),
                    declaration = Declaration(
                        az = "na620",
                        unit = "CENTER_UNIT.center",
                        stage = "PUBLISH",
                    )
                )
            ),
            deedDO = ApREDeedDO()
        )
    }

    private fun fakeResourceOfX86(): ResourceDO {
        return ResourceDO(
            resourcePoolKey = UUID.randomUUID().toString(),
            clusterProfileNew = ClusterProfileNew(UUID.randomUUID().toString(),"","","", siteList = emptyList(), componentDataList = emptyList(), ClusterProfileUseTypeEnum.publish.name),
            apRELabels = listOf(
                ApRELabelDO(
                    apRELabelKey = UUID.randomUUID().toString(),
                    name = "customer/machine/type",
                    value = "x86"
                ),
                ApRELabelDO(
                    apRELabelKey = UUID.randomUUID().toString(),
                    name = "alibaba/apre/feature",
                    value = "serverless/base_runtime",
                    apREFeatureSpecs = listOf(
                        fakeServerlessApRELabelSpec()
                    )
                )
            )
        )
    }

    private fun fakeResourceOfArm(): ResourceDO {
        return ResourceDO(
            resourcePoolKey = UUID.randomUUID().toString(),
            clusterProfileNew = ClusterProfileNew(UUID.randomUUID().toString(),"","","", siteList = emptyList(), componentDataList = emptyList(), ClusterProfileUseTypeEnum.publish.name),
            apRELabels = listOf(
                ApRELabelDO(
                    apRELabelKey = UUID.randomUUID().toString(),
                    name = "customer/machine/type",
                    value = "m"
                ),
                ApRELabelDO(
                    apRELabelKey = UUID.randomUUID().toString(),
                    name = "alibaba/apre/feature",
                    value = "serverless/base_runtime",
                    apREFeatureSpecs = listOf(
                        fakeServerlessApRELabelSpec(),
                        fakeServerlessApRELabelSpec()
                    )
                )
            )
        )
    }

    private fun fakeServerlessApRELabelSpec(): ApREFeatureSpecDO {
        return ApREFeatureSpecDO(
            id = 1L,
            apRELabelKey = UUID.randomUUID().toString(),
            title = "",
            specType = "serverless/base_runtime\$\$common\$\$SPEC:4-8",
            specCode = UUID.randomUUID().toString(),
            scope = "publish",
            status = "online",
            annotations = "",
            labels = null,
            gmtCreate = null,
            gmtModified = null,
            isDeleted = null,
            source = "DEFAULT"
        )
    }
}