package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterEnvironmentService
import com.alibaba.koastline.multiclusters.appenv.params.ClusterEnvironment
import com.alibaba.koastline.multiclusters.common.exceptions.ClusterEnvironmentException
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.fasterxml.jackson.module.kotlin.readValue
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.fail

class DefaultClusterEnvironmentServiceTest {
    private var objectMapper = ObjectMapperFactory.newTolerant()
    @Test
    fun testBuildProductlineTransformedIdsWithNullExternalId() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val transformedIds= defaultClusterEnvironmentService.buildProductlineTransformedIds("aone-productline", null)
        assertEquals(1, transformedIds.size)
        assertEquals(DefaultClusterEnvironmentService.ALIBABA_GROUP, transformedIds.get(0))
    }

    @Test
    fun testBuildProductlineTransformedIdsWithExternalId() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val transformedIds= defaultClusterEnvironmentService.buildProductlineTransformedIds("aone-productline", "3#24327_24330_24337")
        assertEquals(5, transformedIds.size)
        assertEquals(true, transformedIds.contains(DefaultClusterEnvironmentService.ALIBABA_GROUP))
        assertEquals(true, transformedIds.contains("3"))
        assertEquals(true, transformedIds.contains("3#24327"))
        assertEquals(true, transformedIds.contains("3#24327_24330"))
        assertEquals(true, transformedIds.contains("3#24327_24330_24337"))
    }

    @Test
    fun testBuildProductlineTransformedIdsWithSpecialExternalId() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        var transformedIds= defaultClusterEnvironmentService.buildProductlineTransformedIds("aone-productline", DefaultClusterEnvironmentService.GLOBAL_EXTERNAL_ID)
        assertEquals(1, transformedIds.size)
        assertEquals(true, transformedIds.contains(DefaultClusterEnvironmentService.GLOBAL_EXTERNAL_ID))

        transformedIds= defaultClusterEnvironmentService.buildProductlineTransformedIds("aone-productline", DefaultClusterEnvironmentService.ALIBABA_GROUP)
        assertEquals(1, transformedIds.size)
        assertEquals(true, transformedIds.contains(DefaultClusterEnvironmentService.ALIBABA_GROUP))
    }

    @Test
    fun testQueryByMatchRuleV3ForServerlessList() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val clusterEnvironments = objectMapper.readValue<ArrayList<ClusterEnvironment>>(getClusterEnvironmentArrayStr())
        every { defaultClusterEnvironmentService.getEnvironmentByExternalOwner(any(), any(),any()) } returns clusterEnvironments
        val result = defaultClusterEnvironmentService.queryByMatchRuleV3(
            "staging",
            "alibaba",
            "aone-productline",
            "serverless",
            null,
            null,
            null,
            null,
            null
        )
        assertEquals(1, result.size)
    }

    @Test
    fun testQueryByMatchRuleV3ForCommonList() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val clusterEnvironments = objectMapper.readValue<ArrayList<ClusterEnvironment>>(getClusterEnvironmentArrayStr())
        every {
            defaultClusterEnvironmentService.getEnvironmentByExternalOwner(
                any(),
                any(),
                any()
            )
        } returns clusterEnvironments
        var result = defaultClusterEnvironmentService.queryByMatchRuleV3(
            "staging",
            "alibaba",
            "aone-productline",
            "common",
            null,
            null,
            null,
            null,
            null
        )
        assertEquals(2, result.size)
    }
    @Test
    fun testQueryByMatchRuleV3ForCommonCheck() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val clusterEnvironments = objectMapper.readValue<ArrayList<ClusterEnvironment>>(getClusterEnvironmentArrayStr())
        every { defaultClusterEnvironmentService.findClusterEnvironmentGlobal(any(), any(),any()) } returns clusterEnvironments[0]
        val result = defaultClusterEnvironmentService.queryByMatchRuleV3(
            "staging",
            "alibaba",
            "aone-productline",
            "common",
            "us-east-1",
            "us44",
            "PRE_PUBLISH",
            "CENTER_UNIT.rg_us_east",
            "tao-guide_PUBLISH",
        )
        assertEquals(1, result.size)
    }
    @Test
    fun testCheckClusterEnvironmentUniquenessWithRepeatedCommonEnv() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        //普通集群环境->重复环境属性
        val currentEnvMeta = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH"
        )
        val envLabelsRef = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH"
        )
        try {
            defaultClusterEnvironmentService.checkClusterEnvironmentUniqueness(currentEnvMeta, envLabelsRef)
        } catch (e: ClusterEnvironmentException) {
            assertEquals("cluster environment already exist", e.message)
        }
    }
    @Test
    fun testCheckClusterEnvironmentUniquenessWithCommonAndServerlessEnv() {
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        //普通集群环境vsServerless集群环境
        val currentEnvMeta = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH"
        )
        val envLabelsRef = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH",
            "serverlessRuntime" to "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}"
        )
        try {
            defaultClusterEnvironmentService.checkClusterEnvironmentUniqueness(currentEnvMeta, envLabelsRef)
        } catch (e: ClusterEnvironmentException) {
            fail(e.message)
        }
    }
    @Test
    fun testCheckClusterEnvironmentUniquenessWithRepeatedServerlessEnv() {
        //Serverless集群环境 -> 重复环境
        val defaultClusterEnvironmentService = spyk(DefaultClusterEnvironmentService(objectMapper))
        val currentEnvMeta = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH",
            "serverlessRuntime" to "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}"
        )
        val envLabelsRef = mapOf(
            "envLevel" to "staging",
            "unit" to "CENTER_UNIT.rg_us_east",
            "stage" to "PRE_PUBLISH",
            "serverlessRuntime" to "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}"
        )
        try {
            defaultClusterEnvironmentService.checkClusterEnvironmentUniqueness(currentEnvMeta, envLabelsRef)
        } catch (e: ClusterEnvironmentException) {
            assertEquals("cluster environment already exist", e.message)
        }
    }
    private fun getClusterEnvironmentArrayStr(): String{
        return """
                   [
                        {
                            "clusterEnvironmentKey":"WbFbqIfJsC2Oq0WrtxGHj0n5CZFkBwlt",
                            "clusterProvider":"alibaba",
                            "clusterType":"alibaba-asi",
                            "region":"us-east-1",
                            "az":"us44",
                            "envTags":{
                                "envLevel":"staging",
                                "region":"us-east-1",
                                "regionName":"美东",
                                "az":"us44",
                                "azName":"可用区us44",
                                "unit":"CENTER_UNIT.rg_us_east",
                                "unitName":"",
                                "stage":"PRE_PUBLISH",
                                "externalId":"alibaba",
                                "externalType":"c",
                                "annotations":"{\"prometheus\":\"https://ap-southeast-1-share.log.aliyuncs.com/prometheus/asi-prometheus-ap-southeast/asi_xjp_core_a\"}"
                            },
                            "clusterId":"c0ccad3d50ca74abb997511e55549120f",
                            "clusterName":"asi_uc-west-1_core_a"
                        },
                        {
                            "clusterEnvironmentKey":"DPZ4OXzWIhMncVLUaJrHozlVKN0mr6s1",
                            "clusterProvider":"alibaba",
                            "clusterType":"alibaba-asi",
                            "region":"us-east-1",
                            "az":"us68",
                            "envTags":{
                                "envLevel":"staging",
                                "region":"us-east-1",
                                "regionName":"美东",
                                "az":"us68",
                                "azName":"可用区us68",
                                "unit":"CENTER_UNIT.rg_us_east",
                                "unitName":"",
                                "stage":"PRE_PUBLISH",
                                "externalId":"alibaba",
                                "externalType":"aone-productline",
                                "annotations":"{\"prometheus\":\"https://ap-southeast-1-share.log.aliyuncs.com/prometheus/asi-prometheus-ap-southeast/asi_xjp_core_a\"}"
                            },
                            "clusterId":"c0ccad3d50ca74abb997511e55549120f",
                            "clusterName":"asi_uc-west-1_core_a"
                        },
                        {
                            "clusterEnvironmentKey":"DPZ4OXzWIhMncVLUaJrHozlVKN0mr6s1",
                            "clusterProvider":"alibaba",
                            "clusterType":"alibaba-asi",
                            "region":"us-east-1",
                            "az":"us68",
                            "envTags":{
                                "envLevel":"staging",
                                "region":"us-east-1",
                                "regionName":"美东",
                                "az":"us68",
                                "azName":"可用区us68",
                                "unit":"CENTER_UNIT.rg_us_east",
                                "unitName":"",
                                "stage":"PRE_PUBLISH",
                                "externalId":"alibaba",
                                "externalType":"aone-productline",
                                "annotations":"{\"prometheus\":\"https://ap-southeast-1-share.log.aliyuncs.com/prometheus/asi-prometheus-ap-southeast/asi_xjp_core_a\"}",
                                "serverlessRuntime": "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}"
                            },
                            "clusterId":"c0ccad3d50ca74abb997511e55549120f",
                            "clusterName":"asi_uc-west-1_core_a"
                        } 
                    ]
                """
    }
}