package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.AoneGreyApi
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.BendiSpeApi
import com.alibaba.koastline.multiclusters.external.EnvCenterApi
import com.alibaba.koastline.multiclusters.external.KaolaSpeApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.model.Server
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.SpeScheduleService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockkObject
import kotlin.test.assertEquals
import org.junit.Test
import testutils.BaseTest

/**
 * @author:    <EMAIL>
 * @description:  TODO
 * @date:    2024/2/26 10:47 AM
 */
class SpeScheduleServiceTest: BaseTest() {
    @InjectMockKs
    lateinit var speScheduleService: SpeScheduleService

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var appCenterApi: AppCenterApi

    @MockK
    lateinit var runtimeWorkloadMetaService: RuntimeWorkloadMetaService

    @MockK
    lateinit var kaolaSpeApi: KaolaSpeApi

    @MockK
    lateinit var bendiSpeApi: BendiSpeApi

    @MockK
    lateinit var commonProperties: CommonProperties

    @MockK
    lateinit var aoneGreyApi: AoneGreyApi

    @MockK
    lateinit var envCenterApi: EnvCenterApi

    @Test
    fun `testQuerySpeAppIps - serverless runtime`() {
        val appName = "normandy-test-app4"
        val paramUrl = "https://xxxxxxxx/xxxxxx"
        every {
            appCenterApi.isRuntimeApp(appName)
        } returns true
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpGet(url = paramUrl, params = emptyMap()) } returns """
            {
                      "successful":  true,
                      "object":  {
                                "publishParams":  [
                                          {
                                                    "paramName":  "speUnit",
                                                    "cnName":  "speUnit",
                                                    "newValue":  "center,sh",
                                                    "oldValue":  "",
                                                    "hidden":  false
                                          }
                                ]
                      },
                      "errorMsg":  null
            }
        """.trimIndent()
        every {
            runtimeWorkloadMetaService.listRunningWorkloadListByApp(appName = appName)
        } returns fakeRuntimeWorkloadList()
        every {
            skylineApi.listServerByAppAndRuntimeWorkloadId(
                appName = appName,
                runtimeWorkloadIdList = listOf(
                    "runtime_workload_id_2","runtime_workload_id_4"
                )
            )
        } returns listOf(
            manufacturePojo(Server::class.java).copy(ip = "***********"),
            manufacturePojo(Server::class.java).copy(ip = "***********")
        )
        val ipList = speScheduleService.querySpeAppIps(appName, paramUrl)
        assertEquals(2, ipList.size)
        assertEquals("***********", ipList[0])
        assertEquals("***********", ipList[1])
    }

    @Test
    fun `testQuerySpeAppIps - common app`() {
        val appName = "normandy-test-app4"
        val paramUrl = "https://xxxxxxxx/xxxxxx"
        every {
            appCenterApi.isRuntimeApp(appName)
        } returns false
        every {
            kaolaSpeApi.isKaolaApp(appName)
        } returns false
        every {
            bendiSpeApi.isBendiApp(appName)
        } returns true
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpGet(any(), any()) } returns """
            {
                      "result":  {
                                "ips":  [
                                          "***********",
                                          "***********"
                                ]
                      },
                      "msg":  "success",
                      "extralMsg":  "",
                      "requestId":  "a61290d1-b5a4-4e66-ab02-239668bdbb2e",
                      "returnCode":  0,
                      "totalCount":  0,
                      "success":  true
            }
        """.trimIndent()
        speScheduleService.speHost = "http://XXXXXXX"
        speScheduleService.speAccount = "aone"
        speScheduleService.speAccessKey = "XXXXXX"
        val ipList = speScheduleService.querySpeAppIps(appName, paramUrl)
        assertEquals(2, ipList.size)
        assertEquals("***********", ipList[0])
        assertEquals("***********", ipList[1])
    }

    @Test
    fun `testQuerySpeAppIps - query common app by diamond`() {
        val diamondConfigParamUrl = "https://diamond-inner.alibaba-inc.com/diamond-ops/pop/aone/parameters?id=1&sign=XXXXXX"
        val appName = "normandy-test-app4"
        every {
            appCenterApi.isRuntimeApp(appName)
        }returns false
        every {
            kaolaSpeApi.isKaolaApp(appName)
        } returns false
        every {
            bendiSpeApi.isBendiApp(appName)
        }returns false
        every {
            envCenterApi.queryDiamondSpeIps(any(), any())
        } returns listOf("***********", "***********")
        every {
            commonProperties.contains(any(),any())
        }returns false
        every {
            commonProperties.firstOrNull(CommonProperties.SPE_DIAMOND_MULTI_APP_PUBLISH)
        } returns null
        val ips = speScheduleService.querySpeAppIps(appName, diamondConfigParamUrl)
        assertEquals(2, ips.size)
        assertEquals("***********", ips[0])
        assertEquals("***********", ips[1])
    }

    private fun fakeRuntimeWorkloadList(): List<RuntimeWorkloadMeta> {
        return listOf(
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                stage = "PUBLISH", unit = "CENTER_UNIT.center", runtimeWorkloadId = "runtime_workload_id_1"
            ),
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                stage = "SMALLFLOW", unit = "CENTER_UNIT.center", runtimeWorkloadId = "runtime_workload_id_2"
            ),
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                stage = "PUBLISH", unit = "CENTER_UNIT.sh", runtimeWorkloadId = "runtime_workload_id_3"
            ),
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                stage = "SMALLFLOW", unit = "CENTER_UNIT.sh", runtimeWorkloadId = "runtime_workload_id_4"
            ),
        )
    }
}