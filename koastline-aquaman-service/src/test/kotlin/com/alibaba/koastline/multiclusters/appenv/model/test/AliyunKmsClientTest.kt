package com.alibaba.koastline.multiclusters.appenv.model.test

import com.alibaba.koastline.multiclusters.common.utils.CryptUtils
import org.junit.Test
import java.util.*
import kotlin.test.assertEquals

/**
 * <AUTHOR>
 */
class AliyunKmsClientTest {
    @Test
    fun base64Decode_test() {
        val refString = "Aone9527GO!"
        val encodedString = "QW9uZTk1MjdHTyE="
        val rawString = Base64.getDecoder().decode(encodedString).decodeToString()
        assertEquals(refString, rawString)
    }
    @Test
    fun securityCrypt_test() {
        val pass = "rXtiBeFRX9NQB7M1oG5kHQsfAG4g7v"
        val encryptText = CryptUtils.encrypt("fjiejgioxjiojeoi", pass)
        val decryptTest = CryptUtils.decrypt("fjiejgioxjiojeoi", encryptText)
        println(encryptText)
        assertEquals(pass, decryptTest)
    }
}