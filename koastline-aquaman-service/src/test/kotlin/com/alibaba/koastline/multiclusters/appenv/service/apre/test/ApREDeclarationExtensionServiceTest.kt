package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.ApREDeclarationExtensionService
import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREAttorneyService
import com.alibaba.koastline.multiclusters.apre.base.MetadataService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelector
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.MetadataConstraintDO
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREDeclarationPatchCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_CLUSTER
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_SITE
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.APRE
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.runs
import org.junit.Test
import testutils.BaseTest
import java.lang.IllegalArgumentException
import java.time.Instant
import java.util.*

class ApREDeclarationExtensionServiceTest : BaseTest() {
    @InjectMockKs
    lateinit var apREDeclarationExtensionService: ApREDeclarationExtensionService

    @MockK
    lateinit var objectMapper: ObjectMapper

    @MockK
    lateinit var matchScopeService: MatchScopeService

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService

    @MockK
    lateinit var apREService: ApREService

    @MockK
    lateinit var apREAttorneyService: ApREAttorneyService

    @MockK
    lateinit var metadataService: MetadataService

    @Test
    fun findSiteBalanceApREDeclarationWithPropertiesVerifyTest() {
        val apREDeclarationId = getLong()
        var apREDeclaration = manufacturePojo(ApREDeclarationPatchDataDO::class.java).copy(
            balanceType = BALANCE_SITE.name,
            id = apREDeclarationId,
        )
        val metaDataConstraintList = mutableListOf<MetadataConstraintDO>()
        for (index in 1..100) {
            metaDataConstraintList.add(
                manufacturePojo(MetadataConstraintDO::class.java).copy(
                    unit = apREDeclaration.unit,
                    stage = apREDeclaration.stage,
                    site = getString() + index
                )
            )
        }

        val declarationPatch = DeclarationPatch(
            patchItems = listOf(
                PatchItem(
                    site = metaDataConstraintList[0].site
                )
            )
        )
        val ms = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "siteops",
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        apREDeclaration = apREDeclaration.copy(
            declarationPatch = declarationPatch,
            matchScopeDataDOs = listOf(ms)
        )
        every {
            apREDeclarationPatchService.findApREDeclarationPatchById(apREDeclarationId)
        }returns apREDeclaration

        every {
            metadataService.listSiteByUnitAndStage(unit = apREDeclaration.unit, stage = apREDeclaration.stage)
        }returns metaDataConstraintList

        val result = apREDeclarationExtensionService.findSiteBalanceApREDeclarationWithPropertiesVerify(apREDeclarationId)
        softly.assertThat(result).isNotNull
        softly.assertThat(result!!.apREDeclaration).isEqualTo(apREDeclaration)
        softly.assertThat(result.extraAppendSiteBalanceList.size).isEqualTo(100)
        softly.assertThat(result.existedSiteBalanceList.size).isEqualTo(1)
    }

    @Test
    fun createApREDeclarationPatchWithMatchScopeAndAttorneyTest_for_balance_cluster() {
        val site = getString()
        val matchScopeDataCreateDto = manufacturePojo(MatchScopeDataReqDto::class.java).copy(
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        val apREDeclarationPatchCreateReqDto = manufacturePojo(ApREDeclarationPatchCreateReqDto::class.java).copy(
            balanceType = BALANCE_CLUSTER,
            site = site,
            matchScopeDataReqDtoList = listOf(matchScopeDataCreateDto)
        )

        every {
            apREDeclarationPatchService.createApREDeclarationPatchWithMatchScope(
                apREDeclarationPatchCreateReqDto = apREDeclarationPatchCreateReqDto
            )
        } just runs

        apREDeclarationExtensionService.createApREDeclarationPatchWithMatchScopeAndAttorney(
            apREDeclarationPatchCreateReqDto = apREDeclarationPatchCreateReqDto
        )
    }

    @Test
    fun createApREDeclarationPatchWithMatchScopeAndAttorneyTest_for_balance_site() {
        val sites = List(10) { getString() }.distinct()

        val matchScopeDataCreateDto = manufacturePojo(MatchScopeDataReqDto::class.java).copy(
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
        )

        val apREDeclarationPatchCreateReqDto = manufacturePojo(ApREDeclarationPatchCreateReqDto::class.java).copy(
            balanceType = BALANCE_SITE,
            declarationPatch = DeclarationPatch(sites.map { PatchItem(site = it) }),
            matchScopeDataReqDtoList = listOf(matchScopeDataCreateDto)
        )

        every {
            apREDeclarationPatchService.createApREDeclarationPatchWithMatchScope(
                apREDeclarationPatchCreateReqDto = apREDeclarationPatchCreateReqDto
            )
        } just runs

        apREDeclarationExtensionService.createApREDeclarationPatchWithMatchScopeAndAttorney(
            apREDeclarationPatchCreateReqDto = apREDeclarationPatchCreateReqDto
        )
    }

    @Test
    fun findAvailableFeatLabelsTest() {
        val apRE = getComplexApREDTO()
        val apRELabel = apRE.resources[0].apRELabels[1]
        val site = apRE.az
        val stage = apRE.stage
        val unit = apRE.unit
        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(
                unit = unit, stage = stage, site = site
            )
        } returns listOf(apRE)
        val result = apREDeclarationExtensionService.findAvailableFeatLabels(
            site = site, stage = stage, unit = unit
        )
        // 验证 clusterLabel
        softly.assertThat(result.size).isEqualTo(1)
        softly.assertThat(result[0].name).isEqualTo(apRELabel.name)
        softly.assertThat(result[0].value).isEqualTo(apRELabel.value)
    }

    @Test
    fun findAvailableCLusterTest() {
        val apRE = getComplexApREDTO()
        val clusterIds = apRE.resources.map { it.clusterId }.toSet()
        val site = apRE.az
        val stage = apRE.stage
        val unit = apRE.unit
        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(
                unit = unit, stage = stage, site = site
            )
        } returns listOf(apRE)
        val result = apREDeclarationExtensionService.findAvailableClusters(
            site = site, stage = stage, unit = unit
        )
        // 验证 cluster
        softly.assertThat(result.size).isEqualTo(1)
        softly.assertThat(result.toSet()).isEqualTo(clusterIds)
    }

    @Test
    fun findFeatBalanceApREDeclarationWithPropertiesVerifyTest() {
        val apREDeclarationId = getLong()
        var apREDeclaration = manufacturePojo(ApREDeclarationPatchDataDO::class.java).copy(
            balanceType = BALANCE_CLUSTER.name,
            site = getString(),
            id = apREDeclarationId,
        )
        val apRE = getComplexApREDTO().copy(
            az = apREDeclaration.site!!,
            stage = apREDeclaration.stage,
            unit = apREDeclaration.unit
        )
        val apRELabel = apRE.resources[0].apRELabels[1]
        val declarationPatch = DeclarationPatch(
            patchItems = listOf(
                PatchItem(
                    clusterSelector = ClusterSelector(
                        labels = listOf(ClusterLabel(name = apRELabel.name, value = apRELabel.value))
                    )
                )
            )
        )
        val ms = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "siteops",
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        apREDeclaration = apREDeclaration.copy(
            declarationPatch = declarationPatch,
            matchScopeDataDOs = listOf(ms)
        )
        every {
            apREDeclarationPatchService.findApREDeclarationPatchById(apREDeclarationId)
        }returns apREDeclaration

        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(unit = apRE.unit, site = apRE.az, stage = apRE.stage)
        }returns listOf(apRE)

        val result = apREDeclarationExtensionService.findFeatBalanceApREDeclarationWithPropertiesVerify(apREDeclarationId)
        softly.assertThat(result).isNotNull
        softly.assertThat(result!!.apREDeclaration).isEqualTo(apREDeclaration)
        softly.assertThat(result.extraAppendFeatBalanceList.size).isEqualTo(1)
    }

    @Test
    fun findClusterBalanceApREDeclarationWithPropertiesVerifyTest() {
        val apREDeclarationId = getLong()
        var apREDeclaration = manufacturePojo(ApREDeclarationPatchDataDO::class.java).copy(
            balanceType = BALANCE_CLUSTER.name,
            site = getString(),
            id = apREDeclarationId,
        )
        val apRE = getComplexApREDTO().copy(
            az = apREDeclaration.site!!,
            stage = apREDeclaration.stage,
            unit = apREDeclaration.unit
        )
        val cluster = apRE.resources.mapNotNull { it.clusterId }
        val declarationPatch = DeclarationPatch(
            patchItems = listOf(
                PatchItem(
                    clusterSelector = ClusterSelector(
                        clusterIds = cluster
                    )
                )
            )
        )
        val ms = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "siteops",
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        apREDeclaration = apREDeclaration.copy(
            declarationPatch = declarationPatch,
            matchScopeDataDOs = listOf(ms)
        )
        every {
            apREDeclarationPatchService.findApREDeclarationPatchById(apREDeclarationId)
        }returns apREDeclaration

        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(unit = apRE.unit, site = apRE.az, stage = apRE.stage)
        }returns listOf(apRE)

        val result = apREDeclarationExtensionService.findClusterBalanceApREDeclarationWithPropertiesVerify(apREDeclarationId)
        softly.assertThat(result).isNotNull
        softly.assertThat(result!!.apREDeclaration).isEqualTo(apREDeclaration)
        softly.assertThat(result.existedClusterBalanceList.size).isEqualTo(1)
    }



    private fun getComplexApREDTO(): ApREDO {
        val resourceDO = manufacturePojo(ResourceDO::class.java).copy(
            apRELabels = listOf(getServerlessApRELabel(), getConsoleApRELabel(RESOURCE_POOL)),
            clusterId = getString()
        )
        return ApREDO(
            id = 2911,
            runtimeEnvKey = getString(),
            name = getString(),
            creator = "kostaline",
            managedClusterKey = "GfOvUwFDaydexVqs",
            region = "cn-zhangjiakou",
            az = "na610",
            stage = "PUBLISH",
            unit = "CENTER_UNIT.center",
            status = "ONLINE",
            metaData = null,
            gmtCreate = Date(Instant.now().toEpochMilli()),
            gmtModified = Date(Instant.now().toEpochMilli()),
            isDeleted = "N",
            apRELabels = listOf(
                getConsoleApRELabel(APRE)
            ),
            resources = listOf(
                resourceDO
            )
        )
    }

    private fun getServerlessApRELabel(): ApRELabelDO {
        return ApRELabelDO(
            null,
            null,
            ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            "serverless/tpp",
            null,
            null,
            null,
            null,
            listOf(
                ApREFeatureSpecDO(
                    null,
                    null,
                    "serverless_runtime_1.0",
                    null,
                    null,
                    "publish",
                    "online",
                    "appstack_runtime",
                    "1111",
                    "appstack_version",
                    "1111",
                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                    null,
                    null,
                    null,
                    null
                ),
                ApREFeatureSpecDO(
                    null,
                    null,
                    "serverless_runtime_1.0",
                    null,
                    null,
                    "publish",
                    "online",
                    "appstack_runtime",
                    "2222",
                    "appstack_version",
                    "2222",
                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                    null,
                    null,
                    null,
                    null
                )
            ),
            APRE,
            SERVERLESS
        )
    }

    private fun getConsoleApRELabel(targetType: ApRELabelTargetTypeEnum): ApRELabelDO {
        return ApRELabelDO(
            id = getLong(),
            targetKey = null,
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            value = getString(),
            gmtCreate = Date(),
            gmtModified = Date(),
            isDeleted = "N",
            apRELabelKey = getString(),
            listOf(),
            targetType = targetType,
            type = CONSOLE
        )
    }
}