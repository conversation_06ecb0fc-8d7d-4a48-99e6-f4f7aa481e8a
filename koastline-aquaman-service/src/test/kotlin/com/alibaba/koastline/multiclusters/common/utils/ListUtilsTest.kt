package com.alibaba.koastline.multiclusters.common.utils

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class ListUtilsTest {
    @Test
    fun testUnionOfList() {
        val list1 = listOf(1, 2, 3)
        val list2 = listOf(2, 3, 4)
        val list3 = listOf(3, 4, 5)

        val result = ListUtils.unionOfList(listOf(list1, list2, list3))

        assert(result == listOf(1, 2, 3, 4, 5))
    }
}