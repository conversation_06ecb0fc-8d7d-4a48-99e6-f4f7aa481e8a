package com.alibaba.koastline.multiclusters.appenv.model.test

import com.alibaba.koastline.multiclusters.backends.params.*
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import junit.framework.TestCase
import org.junit.Test
/**
 * <AUTHOR>
 */
class ClusterInitTest: TestCase() {
    private val objectMapper = ObjectMapper()
            .registerKotlinModule()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false)

    @Test
    fun testConvert() {
        val rawJsonInput = """
            [
                {
                "envLevel": "staging",
                "region": "cn-shenzhen",
                "regionName": "深圳",
                "az": "su121",
                "azName": "可用区su121 ",
                "unit": "CENTER_UNIT.unszyun",
                "unitName": "",
                "stage": "PRE_PUBLISH",
                "clusterId": "ca7997acc1e594744a6c931b86d5e0180"
                }
            ]
        """
        val clusterExpected = ClusterInfo(
                "ca7997acc1e594744a6c931b86d5e0180",
                null,
                "cn-shenzhen",
                null
        )
        val clusterInfoList = objectMapper.readValue<MutableList<ClusterInfo>>(rawJsonInput)
        assertEquals(clusterExpected, clusterInfoList[0])
    }
}