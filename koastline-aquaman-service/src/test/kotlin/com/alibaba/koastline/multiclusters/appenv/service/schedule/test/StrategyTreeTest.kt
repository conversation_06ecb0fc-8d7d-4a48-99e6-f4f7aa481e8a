package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.DistributionStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.PrefixUniqueKeyDistributionStrategy
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.getStrategyTree
import com.alibaba.koastline.multiclusters.schedule.service.schedule.core.visitSubTree
import org.junit.Test
import testutils.BaseTest

class StrategyTreeTest : BaseTest() {

    @Test
    fun visitAllTreeTest() {
        var collectedNodes = 0
        val totalNodes = 7
        val strategyTree = createValidTestStrategyTree()
        strategyTree.visitSubTree { collectedNodes += 1 }
        softly.assertThat(collectedNodes).isEqualTo(totalNodes)
    }

    @Test
    fun buildInvalidStrategyTreeTest() {
        val root = getUniqueKeyTree2()
        exceptionTest({ e -> softly.assertThat(e.message).contains("invalid namespace setting!") }) {
            root.validateUniqueKeys()
        }
    }

    @Test
    fun buildValidDistributionStrategyTreeTest() {
        val root = getUniqueKeyTree1() as DistributionStrategy
        root.validateUniqueKeys()
    }

    @Test
    fun visitPathTest() {
        val newTree1 = getStrategyTree(getUniqueKeyTree2())
        val newTree2 = createValidTestStrategyTree()
        var visitPath1 = ""
        var visitPath2 = ""
        newTree1.visitSubTree { current -> visitPath1 += current.getNodeUniqueKey() }
        newTree2.visitSubTree { current -> visitPath2 += current.getNodeUniqueKey() }
        softly.assertThat(visitPath1).isEqualTo(visitPath2)
    }

    private fun createValidTestStrategyTree(): PrefixUniqueKeyDistributionStrategy {
        // 叶子节点
        return getStrategyTree(getUniqueKeyTree1())
    }

    /**
     * unique prefix tree
     *
     * @return
     */
    private fun getUniqueKeyTree1(): PrefixUniqueKeyDistributionStrategy {
        val leaf1 = PrefixUniqueKeyDistributionStrategy("Root&Child1&Leaf1", 1)
        val leaf2 = PrefixUniqueKeyDistributionStrategy("Root&Child1&Leaf2", 1)
        val leaf3 = PrefixUniqueKeyDistributionStrategy("Root&Child2&Leaf3", 1)
        val leaf4 = PrefixUniqueKeyDistributionStrategy("Root&Child2&Leaf4", 1)
        // 子节点
        val child1 = PrefixUniqueKeyDistributionStrategy("Root&Child1", 2, listOf(leaf1, leaf2))
        val child2 = PrefixUniqueKeyDistributionStrategy("Root&Child2", 2, listOf(leaf3, leaf4))
        // 根节点
        return PrefixUniqueKeyDistributionStrategy("Root", 5, listOf(child1, child2))
    }

    /**
     * unique tree
     *
     * @return
     */
    private fun getUniqueKeyTree2(): PrefixUniqueKeyDistributionStrategy {
        // 叶子节点
        val leaf1 = PrefixUniqueKeyDistributionStrategy("Leaf1", 1)
        val leaf2 = PrefixUniqueKeyDistributionStrategy("Leaf2", 1)
        val leaf3 = PrefixUniqueKeyDistributionStrategy("Leaf3", 1)
        val leaf4 = PrefixUniqueKeyDistributionStrategy("Leaf4", 1)
        // 子节点
        val child1 = PrefixUniqueKeyDistributionStrategy("Child1", 2, listOf(leaf1, leaf2))
        val child2 = PrefixUniqueKeyDistributionStrategy("Child2", 2, listOf(leaf3, leaf4))
        // 根节点
        return PrefixUniqueKeyDistributionStrategy("Root", 5, listOf(child1, child2))
    }
}