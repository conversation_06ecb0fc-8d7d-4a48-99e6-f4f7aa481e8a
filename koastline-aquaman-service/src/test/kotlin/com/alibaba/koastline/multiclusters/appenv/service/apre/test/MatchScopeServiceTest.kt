package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.Exclusion
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Restriction
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.INHERIT
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.SELF_DEF
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.APPLICATION
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum.RESOURCE_GROUP
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.MatchScopeDataException
import com.alibaba.koastline.multiclusters.common.exceptions.SkylineBizException
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.Rule
import org.junit.Test
import org.junit.jupiter.api.assertThrows
import org.junit.rules.ExpectedException
import testutils.BaseTest
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue


class MatchScopeServiceTest: BaseTest() {
    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()
    private var objectMapper = ObjectMapperFactory.newTolerant()

    @Test
    fun testQueryMatchScopesByTargetAndExternal() {
        val appName = "app_name"
        val resourceGroup = "app_name_prehost"
        val now = Date(Instant.now().toEpochMilli())
        val matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                appCenterClient.getAppInfoByName(appName)
            } returns appInfo()

            val p1 = "alibaba"
            val p2 = "3"
            val p3 = "3#4"
            val p4 = "3#4_5"

            every {
                matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, listOf(resourceGroup))
            } returns emptyList()

            every {
                matchScopeDataRepo.listByTargetTypeAndExternal(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, MatchScopeExternalTypeEnum.APPLICATION.name, appName)
            } returns mutableListOf(
                MatchScopeData(1L, 1L, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName, MatchScopeExternalTypeEnum.APPLICATION.name, null,null,creator = "admin", modifier = "admin")
            )

            every {
                matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, AONE_PRODUCTLINE.name, listOf(p1, p2, p3, p4))
            } returns mutableListOf(
                MatchScopeData(2L, 2L, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, "3", AONE_PRODUCTLINE.name, objectMapper.writeValueAsString(
                    mutableListOf(
                        Exclusion( appName, MatchScopeExternalTypeEnum.APPLICATION.name)
                    )
                ),null,creator = "admin", modifier = "admin"),
                MatchScopeData(3L, 3L, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, "3#4", AONE_PRODUCTLINE.name, null,null,creator = "admin", modifier = "admin"),
                MatchScopeData(4L, 4L, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, "3#4_5", AONE_PRODUCTLINE.name, null,null,creator = "admin", modifier = "admin"),
                MatchScopeData(5L, 5L, MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, "3#4_5", AONE_PRODUCTLINE.name, null,null,creator = "admin", modifier = "admin")
            )
        }

        val matchScopeDataList = matchScopeService.findMatchScopesByTargetAndExternalForApp(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName, listOf(resourceGroup))

        assertEquals(4, matchScopeDataList.size)
        assertEquals(1L, matchScopeDataList[0].id)
        assertEquals(3L, matchScopeDataList[1].id)
        assertEquals(4L, matchScopeDataList[2].id)
        assertEquals(5L, matchScopeDataList[3].id)
    }

    private fun appInfo() = AppInfo(
        id = 1L,
        buId = 3L,
        name = "app_name",
        productId = 5L,
        productFullLineIdPath = "4_5",
        status = AppStatusEnum.valueOf("ONLINE"),
        level = AppLevelEnum.GRADE4
    )

    @Test
    fun test_isMatchRestrictionExtendedLabels_matchAnd_false() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val restriction = Restriction(
            extendedLabels = mapOf("appInfo.product_fullline_id_path" to "4_5", "appInfo.name" to "app_name2")
        )
        val isMatch = matchScopeService.isMatchRestrictionExtendedLabels(restriction, mapOf("appInfo" to appInfo()))
        assertEquals(false, isMatch)
    }

    @Test
    fun test_isMatchRestrictionExtendedLabels_matchAnd_true() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val restriction = Restriction(
            extendedLabels = mapOf("appInfo.product_fullline_id_path" to "4_5", "appInfo.name" to "app_name")
        )
        val isMatch = matchScopeService.isMatchRestrictionExtendedLabels(restriction, mapOf("appInfo" to appInfo()))
        assertEquals(true, isMatch)
    }

    @Test
    fun findMatchScopeByExternalAndResourceObjectFeatureKey() {
        val featureKey = "feature_key"
        val matchScopeDataReq = MatchScopeDataReqDto(
            "app_name",
            "APPLICATION",
            listOf(),
            listOf(
                Restriction("na610x", "DAILYx", "centerx", listOf("a", "b"))
            )
        )

        val matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                matchScopeDataRepo.findByExternalAndResourceObjectFeatureKey(
                    matchScopeDataReq.externalType,
                    matchScopeDataReq.externalId,
                    featureKey
                )
            } returns listOf(
                MatchScopeData(
                    id = 1L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 11L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na620x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                ),
                MatchScopeData(
                    id = 2L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 22L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na610x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                )
            )
        }
        val got = matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeDataReq, featureKey)
        assertEquals(2L, got?.id)
    }

    @Test
    fun `findMatchScopeByExternalAndResourceObjectFeatureKey - no match scope found`() {
        val featureKey = "feature_key"
        val matchScopeDataReq = MatchScopeDataReqDto(
            "app_name",
            "APPLICATION",
            listOf(),
            listOf(
                Restriction("na610x", "DAILYx", "centerx", listOf("a", "b"))
            )
        )

        val matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                matchScopeDataRepo.findByExternalAndResourceObjectFeatureKey(
                    matchScopeDataReq.externalType,
                    matchScopeDataReq.externalId,
                    featureKey
                )
            } returns listOf(
                MatchScopeData(
                    id = 1L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 11L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na620x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                ),
                MatchScopeData(
                    id = 2L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 22L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na630x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                )
            )
        }
        val got = matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeDataReq, featureKey)
        assertNull(got)
    }

    @Test
    fun `findMatchScopeByExternalAndResourceObjectFeatureKey - more than 1 match scopes`() {
        val featureKey = "feature_key"
        val matchScopeDataReq = MatchScopeDataReqDto(
            "app_name",
            "APPLICATION",
            listOf(),
            listOf(
                Restriction("na610x", "DAILYx", "centerx", listOf("a", "b"))
            )
        )

        val matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                matchScopeDataRepo.findByExternalAndResourceObjectFeatureKey(
                    matchScopeDataReq.externalType,
                    matchScopeDataReq.externalId,
                    featureKey
                )
            } returns listOf(
                MatchScopeData(
                    id = 1L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 11L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na610x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                ),
                MatchScopeData(
                    id = 2L,
                    externalType = matchScopeDataReq.externalType,
                    externalId = matchScopeDataReq.externalId,
                    targetType = "xx",
                    targetId = 22L,
                    creator = "x",
                    modifier = "xx",
                    exclusions = null,
                    restrictions = objectMapper.writeValueAsString(listOf(
                        Restriction("na610x", "DAILYx", "centerx", listOf("a", "b")),
                    ))
                )
            )
        }

        exceptionRule.expect(MatchScopeDataException::class.java)
        exceptionRule.expectMessage("预期最多存在 1 条")
        exceptionRule.expectMessage("2")
        matchScopeService.findMatchScopeByExternalAndResourceObjectFeatureKey(matchScopeDataReq, featureKey)
    }

    @Test
    fun testBuildProductLineTransformedIds() {
        val matchScopeService = spyk(MatchScopeService(objectMapper)) {
        }
        val transformedIds = InternalPlatformDsl.dynamicCall(
            matchScopeService,
            "buildProductLineTransformedIds",
            arrayOf(3L, "4_5")
        ) { mockk() } as List<String>
        assertEquals(4, transformedIds.size)
        assertEquals(MatchScopeService.ALIBABA_GROUP, transformedIds[0])
        assertEquals("3", transformedIds[1])
        assertEquals("3#4", transformedIds[2])
        assertEquals("3#4_5", transformedIds[3])
    }

    @Test
    fun testComparePriority() {
        val matchScopeService = MatchScopeService(ObjectMapper())
        assertEquals(-1, matchScopeService.compareScopePriority(
            AONE_PRODUCTLINE.name, "alibaba",
            AONE_PRODUCTLINE.name, "1#2"))
        assertEquals(-1, matchScopeService.compareScopePriority(
            AONE_PRODUCTLINE.name, "1#2",
            AONE_PRODUCTLINE.name, "1#2_3"))
        assertEquals(-1, matchScopeService.compareScopePriority(
            AONE_PRODUCTLINE.name, "1#2_3",
            MatchScopeExternalTypeEnum.APPLICATION.name, "normandy-test-app4"))
        assertEquals(-1, matchScopeService.compareScopePriority(MatchScopeExternalTypeEnum.APPLICATION.name, "normandy-test-app4",
            MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, "normandy-test-app4_prehost"))
        assertEquals(0, matchScopeService.compareScopePriority(AONE_PRODUCTLINE.name,"alibaba",AONE_PRODUCTLINE.name,"alibaba"))
    }

    @Test
    fun testIsMatchScopeRestricted() {
        val matchScopeService = MatchScopeService(ObjectMapper())
        assertEquals(true, matchScopeService.isMatchScopeRestricted(null, getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(site = "na610", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(site = "na620", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(stage = "PUBLISH", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(stage = "PRE_PUBLISH", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(unit = "CENTER_UNIT.center", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(unit = "CENTER_UNIT.unn2", clusterIdList = emptyList())), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(clusterIdList = listOf("cluster_a"))), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(clusterIdList = listOf("cluster_b"))), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(site = "na610", clusterIdList = listOf("cluster_a"))), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(site = "na620", clusterIdList = listOf("cluster_a"))), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(envStackId = "envStackId")), getWorkloadMetadataConstraint(), "envStackId"))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(envStackId = "envStackId", extendedLabels = mapOf("appInfo.level" to "GRADE4"))), getWorkloadMetadataConstraint(), "envStackId", systemInputParams = mapOf(
            "appInfo" to appInfo()
        )))
        assertEquals(true, matchScopeService.isMatchScopeRestricted(listOf(Restriction(envStackId = "envStackId", extendedLabels = null)), getWorkloadMetadataConstraint(), "envStackId", systemInputParams = mapOf(
            "appInfo" to appInfo()
        )))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(envStackId = "envStackId", extendedLabels = mapOf("appInfo.level" to "GRADE2"))), getWorkloadMetadataConstraint(), "envStackId", systemInputParams = mapOf(
            "appInfo" to appInfo()
        )))
        assertEquals(false, matchScopeService.isMatchScopeRestricted(listOf(Restriction(envStackId = "envStackId", extendedLabels = mapOf("appInfo.level" to "GRADE4", "appInfo.status" to "OFFLINE"))), getWorkloadMetadataConstraint(), "envStackId", systemInputParams = mapOf(
            "appInfo" to appInfo()
        )))
    }

    @Test
    fun getNestedMapValueTest() {
        val map = mapOf(
            "a" to mapOf(
                "b" to mapOf(
                    "c" to "d"
                )
            )
        )
        val matchScopeService = MatchScopeService(ObjectMapper())
        assertEquals("d", matchScopeService.getNestedMapValueByPath(map, "a.b.c"))
    }

    @Test
    fun getNestedMapValueTest_null() {
        val map = mapOf(
            "a" to mapOf(
                "b" to mapOf(
                    "c" to "d"
                )
            )
        )
        val matchScopeService = MatchScopeService(ObjectMapper())
        assertEquals(null, matchScopeService.getNestedMapValueByPath(map, "a.b.c.d.e"))
    }

    @Test
    fun findMatchScopesByTargetAndExternalForProductLineTest() {
        val buId = 3L
        val productFullLineIdPath = "4_5_6"
        val buildProductLineList = listOf("alibaba", "3", "3#4", "3#4_5", "3#4_5_6")
        val targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
        val matchScopeService = spyk(MatchScopeService(ObjectMapper()))
        val matchScopeData = manufacturePojo(MatchScopeData::class.java).copy(
            restrictions = null, exclusions = null
        )
        every {
            matchScopeService.matchScopeDataRepo.listByTargetTypeAndExternalTypeAndExternalIds(
                targetType = targetType, externalType = AONE_PRODUCTLINE.name, externalIds = buildProductLineList
            )
        } returns listOf(matchScopeData)

        val rs = matchScopeService.findMatchScopesByTargetAndExternalForProductLine(
            buId = buId, productFullLineIdPath = productFullLineIdPath, targetType = targetType
        )

        softly.assertThat(rs).isEqualTo(listOf(matchScopeData).map { matchScopeService.convertMatchScopeData(it) })
    }

    @Test
    fun isProductLineIllegalTest() {
        val checkMap = mapOf(
            "4" to true,
            "4#" to false,
            "4#3" to true,
            "4#12_" to false,
            "4#1_2——3" to false,
            "4#1_2_3" to true
        )
        val matchScopeService = MatchScopeService(ObjectMapper())
        for ((case, checkRs) in checkMap) {
            if (!checkRs) {
                exceptionTest({ e -> softly.assertThat(e.message?.contains("illegal productLine full path")).isTrue }) {
                    matchScopeService.checkProductLineIsLegal(case)
                }
            }
        }
    }

    // Case 1: goalExternalType and externalType are the same, and goalExternalId equals externalId
    @Test
    fun testCompareExternalScopeBelongs2_equals() {
        val goalExternalId = "4#1_2_3"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "4#1_2_3"
        val externalType = AONE_PRODUCTLINE
        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper()))
        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(0, result)
    }

    // Case 2: goalExternalType and externalType are the same, but goalExternalId does not equal externalId
    @Test
    fun testCompareExternalScopeBelongs2_overstep_1() {
        val goalExternalId = "4#1_2_3"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "4#5_6_7"
        val externalType = AONE_PRODUCTLINE
        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper()))
        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(1, result)
    }

    // Case 3: goalExternalType and externalType are different, and goalExternalType has lower priority than externalType
    @Test
    fun testCompareExternalScopeBelongs2_overstep_2() {
        val externalId = "siteops"
        val externalType = MatchScopeExternalTypeEnum.APPLICATION
        val goalExternalId = "4#1_2_3"
        val goalExternalType = AONE_PRODUCTLINE
        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper()))
        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(1, result)
    }

    // Case 4: goalExternalType and externalType are different, and goalExternalType has higher priority than externalType, but does not match
    @Test
    fun testCompareExternalScopeBelongs2_overstep_3() {
        val goalExternalId = "siteops_prehost"
        val appName = "siteops"
        val goalExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP
        val externalId = "4#1_2_3"
        val externalType = MatchScopeExternalTypeEnum.APPLICATION

        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper())){
            every { skylineApi.getAppGroup(goalExternalId) } returns manufacturePojo(AppGroup::class.java).copy(
                name = goalExternalId, appName = appName
            )
            every { appCenterClient.getAppInfoByName(appName) } returns manufacturePojo(AppInfo::class.java).copy(
                buId = 4, productFullLineIdPath = "2_3_15"
            )
        }

        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)
        assertEquals(1, result)
    }

    // Case 5: goalExternalType is RESOURCE_GROUP and externalType is APPLICATION, and appName is sub
    @Test
    fun testCompareExternalScopeBelongs2_included_1() {
        val goalExternalId = "siteops_prehost"
        val appName = "siteops"
        val goalExternalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP
        val externalId = "4#1_2_3"
        val externalType = AONE_PRODUCTLINE

        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper())){
            every { skylineApi.getAppGroup(goalExternalId) } returns manufacturePojo(AppGroup::class.java).copy(
                name = goalExternalId, appName = appName
            )
            every { appCenterClient.getAppInfoByName(appName) } returns manufacturePojo(AppInfo::class.java).copy(
                buId = 4, productFullLineIdPath = "1_2_3_15"
            )
        }

        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)
        assertEquals(-1, result)
    }

    // Case 6: goalExternalType is APPLICATION and externalType is AONE_PRODUCTLINE, and externalId starts with productLinePath
    @Test
    fun testCompareExternalScopeBelongs2_included_2() {
        val goalExternalId = "siteops"
        val goalExternalType = MatchScopeExternalTypeEnum.APPLICATION
        val externalId = "4#1_2_3"
        val externalType = AONE_PRODUCTLINE

        val matchScopeService = spyk(MatchScopeService(objectMapper = ObjectMapper())){
            every { appCenterClient.getAppInfoByName(goalExternalId) } returns manufacturePojo(AppInfo::class.java).copy(
                buId = 4, productFullLineIdPath = "1_2_3_15"
            )
        }

        val result = matchScopeService.compareExternalScopeBelongs2(goalExternalId, goalExternalType, externalId, externalType)
        assertEquals(-1, result)
    }

    // Case 1: goalExternalType is AONE_PRODUCTLINE and externalType is AONE_PRODUCTLINE, and goalExternalId is sub product line of externalId
    @Test
    fun testGetIncludeType_case1() {
        val goalExternalId = "4#1_2_3_4"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "4#1_2_3"
        val externalType = AONE_PRODUCTLINE
        val matchScopeService = spyk(MatchScopeService(ObjectMapper())){
            every { checkProductLineIsLegal(any()) } just runs
        }
        val result = matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(INHERIT, result)
    }

    // Case 2: goalExternalType is AONE_PRODUCTLINE and externalType is AONE_PRODUCTLINE, and goalExternalId is not sub product line of externalId
    @Test
    fun testGetIncludeType_case2() {
        val goalExternalId = "4#1_2_3_4"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "4#1_2_3_6"
        val externalType = AONE_PRODUCTLINE

        val matchScopeService = spyk(MatchScopeService(ObjectMapper())){
            every { checkProductLineIsLegal(any()) } just runs
        }

        assertThrows<ApREException> {
            matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)
        }
    }

    // Case 3: goalExternalType is AONE_PRODUCTLINE and externalType is APPLICATION, and goalExternalId equals externalId
    @Test
    fun testGetIncludeType_case3() {
        val goalExternalId = "4#1_2_3"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "siteops"
        val externalType = APPLICATION

        val matchScopeService = spyk(MatchScopeService(ObjectMapper())){
            every { checkProductLineIsLegal(any()) } just runs
        }
        assertThrows<ApREException> {
            matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)
        }
    }

    // Case 4: goalExternalType is APPLICATION and externalType is APPLICATION, and goalExternalId equals externalId
    @Test
    fun testGetIncludeType_case4() {
        val goalExternalId = "siteops"
        val goalExternalType = APPLICATION
        val externalId = "siteops"
        val externalType = APPLICATION

        val matchScopeService = spyk(MatchScopeService(ObjectMapper()))
        val result = matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(SELF_DEF, result)
    }

    // Case 5: goalExternalType is RESOURCE_GROUP and externalType is RESOURCE_GROUP, and goalExternalId equals externalId
    @Test
    fun testGetIncludeType_case5() {
        val goalExternalId = "siteops_prehost"
        val goalExternalType = RESOURCE_GROUP
        val externalId = "siteops_prehost"
        val externalType = RESOURCE_GROUP

        val matchScopeService = spyk(MatchScopeService(ObjectMapper()))
        val result = matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)

        assertEquals(SELF_DEF, result)
    }

    // Case 6: goalExternalType is AONE_PRODUCTLINE and externalType is APPLICATION, and goalExternalId does not equal externalId
    @Test
    fun testGetIncludeType_case6() {
        val goalExternalId = "4#1_2_3"
        val goalExternalType = AONE_PRODUCTLINE
        val externalId = "siteops"
        val externalType = APPLICATION

        val matchScopeService = spyk(MatchScopeService(ObjectMapper())){
            every { checkProductLineIsLegal(any()) } just runs
        }

        assertThrows<ApREException> {
            matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)
        }
    }

    // Case 7: goalExternalType is APPLICATION and externalType is AONE_PRODUCTLINE
    @Test
    fun testGetIncludeType_case7() {
        val goalExternalId = "siteops"
        val goalExternalType = APPLICATION
        val buId = 100L
        val productFullLineIdPath = "100_321_4121"
        val externalId = "4#1_2_3"
        val externalType = AONE_PRODUCTLINE

        val matchScopeService = spyk(MatchScopeService(ObjectMapper())){
            every { checkProductLineIsLegal(any()) } just runs
            every { appCenterClient.getAppInfoByName(goalExternalId) } returns manufacturePojo(AppInfo::class.java).copy(
                name = goalExternalId, buId = buId, productFullLineIdPath = productFullLineIdPath
            )
        }

        assertThrows<ApREException> {
            matchScopeService.getIncludeType(goalExternalId, goalExternalType, externalId, externalType)
        }
    }

    @Test
    fun test_saveAppName2ResourceGroup() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            externalId = "test-group"
        )
        val appName = "test-app"

        // Mock successful case
        every { matchScopeService.getAppNameByGroupNameFromSkyline(any()) } returns appName
        every { matchScopeService.saveAppNameToGroupName(any(), any()) } just runs

        matchScopeService.saveAppName2ResourceGroup(matchScopeDataDO)

        verify(exactly = 1) { matchScopeService.getAppNameByGroupNameFromSkyline("test-group") }
        verify(exactly = 1) { matchScopeService.saveAppNameToGroupName("test-group", appName) }

        // Mock case where target type doesn't match
        val nonMatchingTargetType = matchScopeDataDO.copy(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
        )
        matchScopeService.saveAppName2ResourceGroup(nonMatchingTargetType)
        verify(exactly = 1) { matchScopeService.getAppNameByGroupNameFromSkyline(any()) }

        // Mock case where external type doesn't match
        val nonMatchingExternalType = matchScopeDataDO.copy(
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        matchScopeService.saveAppName2ResourceGroup(nonMatchingExternalType)
        verify(exactly = 1) { matchScopeService.getAppNameByGroupNameFromSkyline(any()) }
    }

    @Test
    fun test_saveAppName2ResourceGroup_exception() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            externalId = "test-group"
        )
        val appName = "test-app"

        every { matchScopeService.getAppNameByGroupNameFromSkyline(any()) } throws SkylineBizException("test error")
        matchScopeService.saveAppName2ResourceGroup(matchScopeDataDO)
        verify(exactly = 0) { matchScopeService.saveAppNameToGroupName(any(), any()) }
    }

    @Test
    fun test_deleteAppName2ResourceGroup() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            externalId = "test-group"
        )
        val appName = "test-app"
        every {
            matchScopeService.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                externalId = "test-group",
                externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
            )
        } returns emptyList()
        // Mock successful case
        every { matchScopeService.getAppNameByGroupNameFromSkyline(any()) } returns appName
        every { matchScopeService.deleteAppNameToGroupName(any(), any()) } just runs

        matchScopeService.deleteAppName2ResourceGroup(matchScopeDataDO)

        verify(exactly = 1) { matchScopeService.getAppNameByGroupNameFromSkyline("test-group") }
        verify(exactly = 1) { matchScopeService.deleteAppNameToGroupName("test-group", appName) }

        // Mock case where target type doesn't match
        val nonMatchingTargetType = matchScopeDataDO.copy(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
        )
        matchScopeService.saveAppName2ResourceGroup(nonMatchingTargetType)
        verify(exactly = 1) { matchScopeService.getAppNameByGroupNameFromSkyline(any()) }

    }

    @Test
    fun test_deleteAppName2ResourceGroup_existingResourceGroup() {
        val matchScopeService = spyk(MatchScopeService(objectMapper))
        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
            externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name,
            externalId = "test-group"
        )

        // Mock listByTargetTypeAndExternal to return a non-empty list
        every {
            matchScopeService.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ResourceObjectFeatureImport.name,
                externalId = "test-group",
                externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
            )
        } returns listOf(manufacturePojo(MatchScopeDataDO::class.java))

        matchScopeService.deleteAppName2ResourceGroup(matchScopeDataDO)

        // Should not call deleteAppNameToGroupName since early return
        verify(exactly = 0) { matchScopeService.deleteAppNameToGroupName(any(), any()) }
    }

    @Test
    fun test_isMsExcluded() {
        val matchScopeService = MatchScopeService(ObjectMapper())

        // Case 1: No exclusions
        val msNoExclusions = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = null
        )
        assertFalse(matchScopeService.isMsExcluded(msNoExclusions, "100#1_2_3"))

        // Case 2: Empty exclusions
        val msEmptyExclusions = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = mutableListOf()
        )
        assertFalse(matchScopeService.isMsExcluded(msEmptyExclusions, "100#1_2_3"))

        // Case 3: Cluster ID exclusion match
        val msClusterExclusion = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = mutableListOf(
                Exclusion("cluster-1", MatchScopeExternalTypeEnum.CLUSTER_ID.name)
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msClusterExclusion, "100#1_2_3", clusterId = "cluster-1"))
        assertFalse(matchScopeService.isMsExcluded(msClusterExclusion, "100#1_2_3", clusterId = "cluster-2"))

        // Case 4: Product line exclusion match
        val msProductLineExclusion = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = mutableListOf(
                Exclusion("100#1", AONE_PRODUCTLINE.name)
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msProductLineExclusion, "100#1_2_3"))
        assertFalse(matchScopeService.isMsExcluded(msProductLineExclusion, "200#1_2_3"))

        // Case 5: Application exclusion match
        val msAppExclusion = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = mutableListOf(
                Exclusion("test-app", APPLICATION.name)
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msAppExclusion, "100#1_2_3", appName = "test-app"))
        assertFalse(matchScopeService.isMsExcluded(msAppExclusion, "100#1_2_3", appName = "other-app"))

        // Case 6: Resource group exclusion match
        val msResourceGroupExclusion = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "group-1",
            exclusions = mutableListOf(
                Exclusion("group-1", RESOURCE_GROUP.name)
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msResourceGroupExclusion, "100#1_2_3"))

        val msResourceGroupNoMatch = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "group-2",
            exclusions = mutableListOf(
                Exclusion("group-1", RESOURCE_GROUP.name)
            )
        )
        assertFalse(matchScopeService.isMsExcluded(msResourceGroupNoMatch, "100#1_2_3"))

        // Case 7: Multiple exclusions, one match
        val msMultipleExclusions = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = "group-1",
            exclusions = mutableListOf(
                Exclusion("group-2", RESOURCE_GROUP.name),
                Exclusion("test-app", APPLICATION.name),
                Exclusion("100#1", AONE_PRODUCTLINE.name)
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msMultipleExclusions, "100#1_2_3", appName = "test-app"))
        assertTrue(matchScopeService.isMsExcluded(msMultipleExclusions, "100#1_2_3"))
        assertFalse(matchScopeService.isMsExcluded(msMultipleExclusions, "200#1_2_3", appName = "other-app"))

        // Case 8: multiple Cluster ID exclusion match
        val msClustersExclusion = manufacturePojo(MatchScopeDataDO::class.java).copy(
            exclusions = mutableListOf(
                Exclusion("cluster-1", MatchScopeExternalTypeEnum.CLUSTER_ID.name),
                Exclusion("cluster-2", MatchScopeExternalTypeEnum.CLUSTER_ID.name),
                Exclusion("cluster-3", MatchScopeExternalTypeEnum.CLUSTER_ID.name),
            )
        )
        assertTrue(matchScopeService.isMsExcluded(msClustersExclusion, "100#1_2_3", clusterId = "cluster-1"))
        assertTrue(matchScopeService.isMsExcluded(msClustersExclusion, "100#1_2_3", clusterId = "cluster-2"))
        assertTrue(matchScopeService.isMsExcluded(msClustersExclusion, "100#1_2_3", clusterId = "cluster-3"))
    }

    private fun getWorkloadMetadataConstraint(): WorkloadMetadataConstraint {
        return WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            site = "na610",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            subgroup = "default",
            clusterId = "cluster_a"
        )
    }
}