package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.AcniHomeException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.model.AcniAoneAppEnvironment
import com.alibaba.koastline.multiclusters.external.model.AcniWorkloadMetadata
import com.alibaba.koastline.multiclusters.external.model.AcniAssetRelatedK8sObject
import com.alibaba.koastline.multiclusters.external.model.AcniAssetRelatedK8sObjectsQuery
import com.alibaba.koastline.multiclusters.external.model.AcniK8sObjectEncoding
import com.alibaba.koastline.multiclusters.external.model.AcniNormandyAppGroup
import com.alibaba.koastline.multiclusters.external.model.AcniAppWorkloadScope
import com.alibaba.koastline.multiclusters.external.model.AcniRestErrorHint
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.apache.commons.lang3.StringUtils
import org.junit.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue


/**
 * @author: fudai.yf
 * @since: 2023/3/9
 */
class AcniHomeApiTest {

    @Test
    fun queryRelatedK8sObjectList_withYamlEncoding() {

        val objectMapper = ObjectMapper()
        val api = spyk(AcniHomeApi(objectMapper))
            .also {
                it.acniHomeHost = "http://fake"
                it.acniHomeAppKey = "fake-app-key"
                it.acniHomeAppSec = "fake-app-sec"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("acni-home", "queryRelatedK8sObjectList") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        val expected = AcniAssetRelatedK8sObject(
            apiVersion = "v1",
            kind = "Secret",
            objectContent = "fake-content"
        )
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns let {
            listOf(expected).let { objectMapper.writeValueAsString(it) }
        }

        val mockMetadata = AcniWorkloadMetadata(
            appName = "fake-app",
            resourceGroup = "fake-group",
            site = "CENTER",
            unit = "nt12",
            stage = "DAILY",
            clusterId = "fake-cluster",
            namespace = "fake-ns",
            envStackId = "fake-stack-id"
        )
        val objects = api.queryRelatedK8sObjectList(mockMetadata, "YAML")
        assertEquals(listOf(expected), objects)
        unmockkAll()
    }

    @Test
    fun queryRelatedK8sObjectList_withJsonEncoding() {

        val objectMapper = ObjectMapper()
        val api = spyk(AcniHomeApi(objectMapper))
            .also {
                it.acniHomeHost = "http://fake"
                it.acniHomeAppKey = "fake-app-key"
                it.acniHomeAppSec = "fake-app-sec"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("acni-home", "queryRelatedK8sObjectList") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        val expected = AcniAssetRelatedK8sObject(
            apiVersion = "v1",
            kind = "Secret",
            objectContent = "fake-content"
        )
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns let {
            listOf(expected).let { objectMapper.writeValueAsString(it) }
        }

        val mockMetadata = AcniWorkloadMetadata(
            appName = "fake-app",
            resourceGroup = "fake-group",
            site = "CENTER",
            unit = "nt12",
            stage = "DAILY",
            clusterId = "fake-cluster",
            namespace = "fake-ns",
            envStackId = "fake-stack-id"
        )
        val objects = api.queryRelatedK8sObjectList(mockMetadata, "JSON")
        assertEquals(listOf(expected), objects)
        unmockkAll()
    }

    @Test
    fun queryRelatedK8sObjectList_verifyRequestBody() {

        val objectMapper = ObjectMapper()
        val api = spyk(AcniHomeApi(objectMapper))
            .also {
                it.acniHomeHost = "http://fake"
                it.acniHomeAppKey = "fake-app-key"
                it.acniHomeAppSec = "fake-app-sec"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("acni-home", "queryRelatedK8sObjectList") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        val expected = AcniAssetRelatedK8sObject(
            apiVersion = "v1",
            kind = "Secret",
            objectContent = "fake-content"
        )
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns let {
            listOf(expected).let { objectMapper.writeValueAsString(it) }
        }

        val mockMetadata = AcniWorkloadMetadata(
            appName = "fake-app",
            resourceGroup = "fake-group",
            site = "CENTER",
            unit = "nt12",
            stage = "DAILY",
            clusterId = "fake-cluster",
            namespace = "fake-ns",
            envStackId = "fake-stack-id"
        )
        api.queryRelatedK8sObjectList(mockMetadata, "JSON")

        val requestBody = AcniAssetRelatedK8sObjectsQuery(
            scopes = listOf(
                AcniAoneAppEnvironment.from(mockMetadata),
                AcniNormandyAppGroup.from(mockMetadata),
                AcniAppWorkloadScope.from(mockMetadata),
            ),
            clusterId = mockMetadata.clusterId,
            namespace = mockMetadata.namespace,
            encoding = AcniK8sObjectEncoding.JSON
        ).let { JsonUtils.writeValueAsString(it) }
        verify { HttpClientUtils.httpPost(any(), eq(requestBody), any()) }
        unmockkAll()
    }

    @Test
    fun queryRelatedK8sObjectList_downgrade() {

        val objectMapper = ObjectMapper()
        val api = spyk(AcniHomeApi(objectMapper))
            .also {
                it.acniHomeHost = "http://fake"
                it.acniHomeAppKey = "fake-app-key"
                it.acniHomeAppSec = "fake-app-sec"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("acni-home", "queryRelatedK8sObjectList") } returns true
                }
            }
        mockkObject(HttpClientUtils)

        val mockMetadata = AcniWorkloadMetadata(
            appName = "fake-app",
            resourceGroup = "fake-group",
            site = "CENTER",
            unit = "nt12",
            stage = "DAILY",
            clusterId = "fake-cluster",
            namespace = "fake-ns",
            envStackId = "fake-stack-id"
        )
        val objects = api.queryRelatedK8sObjectList(mockMetadata, "JSON")
        assertEquals(emptyList(), objects)
        verify(exactly = 0) { HttpClientUtils.httpPost(any(), any<String>(), any()) }
        unmockkAll()
    }

    @Test
    fun queryRelatedK8sObjectList_requestFailed() {

        val objectMapper = ObjectMapper()
        val api = spyk(AcniHomeApi(objectMapper))
            .also {
                it.acniHomeHost = "http://fake"
                it.acniHomeAppKey = "fake-app-key"
                it.acniHomeAppSec = "fake-app-sec"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("acni-home", "queryRelatedK8sObjectList") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        val hint = AcniRestErrorHint(
            status = 400,
            message = "Illegal K8s object encoding",
            path = "/apis/v1/public/assets/related-resources/k8s-objects/query",
            traceId = "7f00000116784165630581026dead7"
        )
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns let {
            objectMapper.writeValueAsString(hint)
        }

        val exp = assertThrows<AcniHomeException> { api.queryRelatedK8sObjectList(mockk(relaxed = true), "JSON") }

        assertTrue { StringUtils.contains(exp.message!!, hint.message!!) }
        assertTrue { StringUtils.contains(exp.message!!, hint.traceId!!) }
        unmockkAll()
    }

}