package com.alibaba.koastline.multiclusters.appenv.service.fed

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.data.dao.fed.FedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.resourceobj.ResourceObjectFeatureImportRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.data.vo.env.KManagedClusterData
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import com.alibaba.koastline.multiclusters.external.FedConfigApi
import com.alibaba.koastline.multiclusters.fed.FedClusterAssistant
import com.alibaba.koastline.multiclusters.fed.FedClusterService
import com.alibaba.koastline.multiclusters.fed.model.FedPolicyPropertiesKey.FED_SPEC
import com.alibaba.koastline.multiclusters.fed.model.FedPolicySpecPropertiesKey.CLUSTER_OVERRIDE
import com.alibaba.koastline.multiclusters.fed.model.FedTargetPropertiesKey.MEMBER_CLUSTER_NAME_LIST
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedMemberClusterDeleteReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterConditionQueryReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterPolicyCreateReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterRegisterReq
import com.alibaba.koastline.multiclusters.fed.model.req.FedClusterUpdateReq
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum.YAML
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolExtEnum
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants.FED_POLICY_KEY
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.slot
import org.junit.Test
import testutils.BaseTest
import java.util.*

class FedClusterServiceTest : BaseTest() {

    @InjectMockKs
    lateinit var fedClusterService: FedClusterService

    @MockK
    lateinit var resourceObjectFeatureService: ResourceObjectFeatureService

    @MockK
    lateinit var resourceObjectFeatureImportRepo: ResourceObjectFeatureImportRepo

    @MockK
    lateinit var fedClusterRepo: FedClusterRepo

    @MockK
    lateinit var fedConfigApi: FedConfigApi

    @MockK
    lateinit var defaultClusterService: DefaultClusterService

    @MockK
    lateinit var resourcePoolService: ResourcePoolService

    @MockK
    lateinit var matchScopeService: MatchScopeService

    @MockK
    lateinit var fedClusterAssistant: FedClusterAssistant

    @Test
    fun listFedClusterByConditionsTest() {
        val conditionQuery = FedClusterConditionQueryReq(
            region = getString(),
            tenantClusterName = getString(),
            memberClusterName = getString(),
            envNameKeyWords = getString(),
            status = "Online",
            pageNumber = 1,
            pageSize = 5
        )

        every {
            resourcePoolService.listByClusterName(
                or(
                    conditionQuery.memberClusterName!!,
                    conditionQuery.tenantClusterName!!
                )
            )
        } answers {
            listOf(
                ResourcePoolDataDO(
                    id = getLong(),
                    resourcePoolKey = getString(),
                    clusterId = getString(),
                    managedClusterKey = getString(),
                    creator = getString(),
                    modifier = getString(),
                    gmtCreate = Date(),
                    gmtModified = Date(),
                    isDeleted = "N"
                )
            )
        }

        val fedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = getLong(),
            isDeleted = "N",
            fedEnvName = conditionQuery.envNameKeyWords!! + getString()
        )

        every {
            fedClusterRepo.listByMemberClusterKeys(any())
        } returns listOf(fedCluster)

        every {
            fedClusterRepo.listByTenantClusterKeys(any())
        } returns listOf(fedCluster)

        every {
            fedClusterAssistant.listFedClusterByProperties(
                1,
                5,
                conditionQuery.envNameKeyWords,
                conditionQuery.region,
                conditionQuery.status,
                listOf(fedCluster.id!!),
            )
        } returns PageData.of(
            pageNumber = 1, pageSize = 5, totalCount = 1, data = listOf(fedCluster)
        )

        every {
            resourcePoolService.listByManagedClusterKey(any())
        } returns listOf(manufacturePojo(ResourcePoolDataDO::class.java))

        every {
            defaultClusterService.listClusterByClusterIdList(any())
        } answers { call ->
            val clusterIds = call.invocation.args[0] as List<String>
            clusterIds.map { clusterId ->
                manufacturePojo(ClusterProfileData::class.java).copy(
                    clusterId = clusterId
                )
            }
        }

        val rs = fedClusterService.listFedClusterByConditions(
            conditionQuery
        )

        softly.assertThat(rs.totalCount).isEqualTo(1L)
        softly.assertThat(rs.pageNumber).isEqualTo(1)
        softly.assertThat(rs.pageSize).isEqualTo(5)
        softly.assertThat(rs.data!![0].fedEnvName).contains(conditionQuery.envNameKeyWords)
    }

    @Test
    fun queryFedClusterByIdTest() {
        val id = getLong()
        val fedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = id
        )
        every {
            fedClusterRepo.findById(id)
        } returns fedCluster

        every {
            resourcePoolService.listByManagedClusterKey(fedCluster.tenantManageClusterKey)
        } returns listOf(
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = fedCluster.tenantManageClusterKey
            )
        )

        every {
            resourcePoolService.listByManagedClusterKey(fedCluster.memberManageClusterKey)
        } returns listOf(
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = fedCluster.memberManageClusterKey
            )
        )
        every {
            defaultClusterService.listClusterByClusterIdList(any<List<String>>())
        } answers {
            val clusterIds = call.invocation.args[0] as List<String>
            clusterIds.map { clusterId ->
                manufacturePojo(ClusterProfileData::class.java).copy(
                    clusterId = clusterId
                )
            }
        }
        val rs = fedClusterService.queryFedClusterById(id)
        softly.assertThat(rs).isNotNull
        softly.assertThat(rs?.id).isEqualTo(id)
    }

    @Test
    fun updateFedClusterEnvTest() {
        val fedClusterUpdateReq = manufacturePojo(FedClusterUpdateReq::class.java).copy(
            status = "Online"
        )
        every {
            fedClusterRepo.findById(any())
        } returns manufacturePojo(FedCluster::class.java)
        every {
            fedClusterRepo.update(
                any(), any(), any(), any()
            )
        } returns 1
        fedClusterService.updateFedClusterEnv(fedClusterUpdateReq)
    }

    @Test
    fun deleteFedClusterEnvTest() {
        val fedClusterId = getLong()
        val modifier = getString()
        val fedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = fedClusterId
        )
        every {
            resourcePoolService.deleteByManagedClusterKeyWithKManageCluster(
                any(), modifier
            )
        } just runs

        every {
            matchScopeService.listByTargetTypeAndExternal(
                externalId = fedClusterId.toString(), externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
                targetType = MatchScopeTargetTypeEnum.FedTarget.name
            )
        } returns emptyList()

        every {
            resourceObjectFeatureImportRepo.deleteById(fedClusterId)
        } returns 1

        every {
            fedClusterRepo.deleteById(fedClusterId, modifier)
        } returns 1

        every {
            fedClusterRepo.findById(fedClusterId)
        } returns fedCluster

        fedClusterService.deleteFedClusterEnv(fedClusterId, modifier)
    }

    @Test
    fun addMemberClusterTest() {
        val fedMemberClusterCreateReq = manufacturePojo(FedMemberClusterCreateReq::class.java)
        every {
            defaultClusterService.listClusterByClusterNameList(fedMemberClusterCreateReq.toAddFedMemberList)
        } answers {
            val clusterNames = call.invocation.args[0] as List<String>
            clusterNames.map { clusterName ->
                manufacturePojo(ClusterProfileData::class.java).copy(
                    clusterName = clusterName
                )
            }
        }

        val originalFedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = fedMemberClusterCreateReq.id
        )
        val originalClusters = listOf(getString(), getString())

        //cluster name to id
        val clusterNamingMap = mutableMapOf<String, String>()
        originalClusters.forEach {
            clusterNamingMap[it] = getString()
        }
        fedMemberClusterCreateReq.toAddFedMemberList.forEach {
            clusterNamingMap[it] = getString()
        }

        every {
            fedClusterRepo.findById(originalFedCluster.id!!)
        } returns originalFedCluster

        every {
            resourcePoolService.createResourcePoolWithClusterName(
                clusterName = match { fedMemberClusterCreateReq.toAddFedMemberList.contains(it) },
                managedClusterKey = originalFedCluster.memberManageClusterKey,
                creator = fedMemberClusterCreateReq.modifier
            )
        } returns manufacturePojo(ResourcePoolDataDO::class.java)

        every {
            resourcePoolService.listByManagedClusterKey(any())
        } returns (originalClusters + fedMemberClusterCreateReq.toAddFedMemberList).map { clusterName ->
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                clusterId = clusterNamingMap[clusterName] ?: ""
            )
        }

        every {
            defaultClusterService.listClusterByClusterIdList(
                any()
            )
        } answers { call ->
            val clusterList = call.invocation.args[0] as List<String>
            clusterList.map { clusterId ->
                manufacturePojo(ClusterProfileData::class.java).copy(
                    clusterId = clusterId,
                    clusterName = clusterNamingMap.filter { it.value == clusterId }.keys.firstOrNull() ?: ""
                )
            }
        }

        val clusterNameListSlot = slot<Map<String, List<String>>>()
        every {
            fedClusterAssistant.updateFedTarget(
                any(), any(), capture(clusterNameListSlot), any()
            )
        } just runs

        fedClusterService.addMemberCluster(fedMemberClusterCreateReq)

        val finalClusterNames = clusterNameListSlot.captured[MEMBER_CLUSTER_NAME_LIST]

        softly.assertThat(finalClusterNames).isEqualTo(originalClusters + fedMemberClusterCreateReq.toAddFedMemberList)
    }

    @Test
    fun removerMemberClusterTest() {
        val originalClusterMap = mutableMapOf<String, String>()
        for (index in 1..5) {
            originalClusterMap[getString()] = getString()
        }
        val fedMemberClusterDeleteReq = manufacturePojo(FedMemberClusterDeleteReq::class.java).copy(
            toDropMember = originalClusterMap.firstNotNullOf { it.key }
        )
        val fedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = fedMemberClusterDeleteReq.id
        )

        every {
            defaultClusterService.listClusterByClusterNameList(
                clusterNameList = any()
            )
        } returns listOf(manufacturePojo(ClusterProfileData::class.java))

        every {
            fedClusterRepo.findById(fedMemberClusterDeleteReq.id)
        } returns fedCluster

        every {
            resourcePoolService.listByManagedClusterKey(fedCluster.memberManageClusterKey)
        } returns originalClusterMap.map {
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                clusterId = it.value,
            )
        }

        every {
            defaultClusterService.listClusterByClusterIdList(
                clusterIdList = any()
            )
        } answers {
            val clusterIds = (call.invocation.args[0] as List<String>)
            clusterIds.map { clusterId ->
                manufacturePojo(ClusterProfileData::class.java).copy(
                    clusterId = clusterId,
                    clusterName = originalClusterMap.filter { it.value == clusterId }.keys.firstOrNull() ?: ""
                )
            }
        }

        every {
            resourcePoolService.deleteByManagedClusterKeyAndClusterId(
                clusterId = any(),
                managedClusterKey = any(),
                modifier = any()
            )
        } just runs

        val clusterNameListSlot = slot<Map<String, List<String>>>()
        every {
            fedClusterAssistant.updateFedTarget(
                any(), any(), capture(clusterNameListSlot), any()
            )
        } just runs

        fedClusterService.deleteMemberCluster(fedMemberClusterDeleteReq)
        val finalClusterNames = clusterNameListSlot.captured[MEMBER_CLUSTER_NAME_LIST]
        softly.assertThat(finalClusterNames)
            .isEqualTo((originalClusterMap.keys - fedMemberClusterDeleteReq.toDropMember).toList())
    }

    @Test
    fun listPolicySelectorsTest() {
        val fedClusterId = getLong()
        val fedCluster = manufacturePojo(FedCluster::class.java).copy(
            id = fedClusterId
        )
        every {
            fedClusterRepo.findById(any())
        } returns fedCluster
        val matchScopeData = manufacturePojo(MatchScopeDataDO::class.java).copy(
            externalId = fedClusterId.toString(),
            targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
            externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
        )
        every {
            matchScopeService.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.FedPolicy.name,
                externalId = fedClusterId.toString(),
                externalType = MatchScopeExternalTypeEnum.FED_CLUSTER.name,
            )
        } returns listOf(matchScopeData)

        every {
            resourceObjectFeatureService.queryFeatureImportPatchDOByIds(
                listOf(matchScopeData.targetId!!)
            )
        } returns listOf(
            manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
                resourceObjectFeatureKey = FED_POLICY_KEY,
                paramMap = getPolicyParamsMap()
            )
        )
        val rs = fedClusterService.listPolicySelectors(fedClusterId)
        softly.assertThat(rs).isEqualTo(listOf("app.kubernetes.io/name:123"))
    }

    @Test
    fun queryFedPolicyDetailsTest() {
        val fedPolicyId = getLong()
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = FED_POLICY_KEY,
            paramMap = getPolicyParamsMap()
        )
        every {
            resourceObjectFeatureImportRepo.findById(
                fedPolicyId
            )
        } returns featureImport

        every {
            fedClusterAssistant.resourceObjectFeatureService.assembleSpecByFeatureImport(
                resourceObjectFeatureImportList = any(),
                protocol = ResourceObjectProtocolExtEnum.FedCluster.name,
                version = null,
                systemInputParams = emptyMap(),
                extraTemplateFunc = mapOf("FreemarkerUtils" to FreemarkerUtils)
            )
        } answers {
            callOriginal()
        }

        every {
            fedClusterAssistant.resourceObjectFeatureService.featureProtocolLoadService.getFeatureProtocol(any())
        } returns getPolicyCrdTemplate()

        every {
            fedClusterAssistant.resourceObjectFeatureService.featureImportHookManager.preProcess(any())
        } returnsArgument 0

        every {
            fedClusterAssistant.assembleCrd(any(), null, emptyMap(), any())
        } answers {
            callOriginal()
        }

        every {
            fedClusterAssistant.resourceObjectFeatureService.resourceObjectFeatureProtocolRepo.findByResourceObjectFeatureKeyAndVersion(
                any(),
                any()
            )
        } returns listOf(
            manufacturePojo(ResourceObjectFeatureProtocol::class.java).copy(
                resourceObjectFeatureKey = FED_POLICY_KEY, protocol = "FedCluster", isDeleted = "N",
                patch = getPolicyCrdTemplate(), strategy = null
            )
        )

        every {
            fedClusterAssistant.resourceObjectFeatureImportRepo.findByIdList(
                any()
            )
        } returns listOf(featureImport)

        every {
            fedClusterAssistant.resourceObjectFeatureService.parseTemplate(
                any<String>(), any<String>(), YAML, emptyMap(), mapOf("FreemarkerUtils" to FreemarkerUtils)
            )
        } answers {
            callOriginal()
        }

        val details = fedClusterService.queryFedPolicyDetails(fedPolicyId)
        val finalYamlMap = YamlUtils.load(details!!.yamlStr)
        softly.assertThat(((finalYamlMap[FED_SPEC] as Map<String, Any>)[CLUSTER_OVERRIDE])).isNull()
    }

    @Test
    fun deleteFedPolicyTest() {
        val policyId = getLong()
        val modifier = getString()
        every {
            resourceObjectFeatureImportRepo.findById(any())
        }returns manufacturePojo(ResourceObjectFeatureImport::class.java).copy(
            resourceObjectFeatureKey = FED_POLICY_KEY,
            paramMap = getPolicyParamsMap(),
            id = policyId
        )

        every {
            matchScopeService.deleteMatchScopeByTarget(
                any(), MatchScopeTargetTypeEnum.FedPolicy.name, modifier
            )
        }just runs

        every {
            resourceObjectFeatureImportRepo.deleteById(any())
        } returns 1

        every {
            fedConfigApi.deleteFedPolicy(any(), any(), any())
        }just runs

        fedClusterService.deleteFedPolicy(policyId = policyId, modifier = modifier)
    }

    /**
     * fed target injection template
     *
     * @return
     */
    private fun getTargetCrdTemplate(): String {
        return """apiVersion: app.hippo.io/v1 
kind: FedTarget 
metadata: 
  labels: 
    fed.alibaba.com/is-disabled: ${'$'}{user.isDisabled} 
    fed.alibaba.com/region: ${'$'}{user.region} 
    fed.alibaba.com/source: normandy 
  name: ${'$'}{user.uniqueNameKey} 
spec: 
  localCluster: ${'$'}{user.tenantClusterName} 
  remoteClusters: 
    <#list user.memberClusterNameList> 
      <#items as memberClusterName> 
      - ${'$'}{memberClusterName} 
      </#items> 
    </#list>""".trimIndent()
    }

    /**
     * policy crd injection template
     *
     * @return
     */
    private fun getPolicyCrdTemplate(): String {
        return """apiVersion: app.hippo.io/v1
kind: FedPolicy
metadata:
  labels:
    fed.alibaba.com/source: normandy
    fed.alibaba.com/priority: ${'$'}{user.priority}
    fed.alibaba.com/resource-kind: ${'$'}{user.resourceKind}
  name: ${'$'}{user.name}
spec:
  <#if user.spec.clusterOverride?exists>
  clusterOverride: 
  <#list user.spec.clusterOverride>
  <#items as clusterConfig>
    ${'$'}{clusterConfig.clusterName}:
      ${'$'}{user.FreemarkerUtils.innerYamlPad(3,clusterConfig.clusterConfig)}
  </#items>
  </#list>
  </#if>
  <#if user.spec.globalOverride?exists>
  override: 
    ${'$'}{user.FreemarkerUtils.innerYamlPad(2,user.spec.globalOverride)}
  </#if>
  <#if user.spec.policy?exists>
  policy:
  <#list user.spec.policy.ratios>
    ratioPolicy:
    <#items as ratio>
    - clusterName: ${'$'}{ratio.clusterName}
      expectRatio: ${'$'}{ratio.value}
    </#items>
  </#list>
  <#if user.spec.policy.schedulePlugin?exists>
  <#list user.spec.policy.schedulePlugin>
    schedulePlugin:
    <#items as schedulePlugin>
    - ${'$'}{schedulePlugin}
    </#items>
  </#list>
  </#if>
  </#if>
  policySelector:
    tenantCluster: ${'$'}{user.spec.policySelector.tenantCluster}
    tenantName: ${'$'}{user.spec.policySelector.tenantName}
    region: ${'$'}{user.spec.policySelector.region} 
    objectSelector:
    <#list user.spec.policySelector.matchExpressions>
      matchExpressions:
      <#items as matchExpression>
      - key: ${'$'}{matchExpression.key}
        operator: ${'$'}{matchExpression.operator}
        <#list matchExpression.values>
        values:
        <#items as value>
        - qp_wireless_online_prod_na61_1
        </#items>
        </#list>
      </#items>
    </#list>""".trimIndent()
    }

    private fun getPolicyParamsMap() = """priority: 55
resourceKind: PreviewResource
name: policy-e9d5ce0a-7c2f-4eb9-bac0-c0c1ac2ab5be
spec:
  globalOverride: |-
    inject:
     dnsConfig:
     nameservers:
     - 11.9.230.57
     options:
     - name: ndots
     value: "5"
     searches:
     - dsw.svc.cluster.local
     - svc.cluster.local
  clusterOverride: []
  policy:
    ratios:
    - clusterName: asi_zjk_unify_test01
      value: 5
    schedulePlugin: 
    - PreviewResource
  policySelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - '123'
    tenantCluster: asi_zjk_unify_test01
    tenantName: asi_zjk_unify_test01
    region: cn-zhangjiakou
    """.trimIndent()
}

