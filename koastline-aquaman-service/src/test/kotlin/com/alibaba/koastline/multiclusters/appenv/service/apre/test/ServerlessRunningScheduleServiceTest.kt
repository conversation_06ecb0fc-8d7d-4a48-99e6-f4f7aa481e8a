package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RuntimeScheduleService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.extra.ServerlessRunningScheduleService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Test
import org.springframework.stereotype.Service
import testutils.BaseTest

@Service
class ServerlessRunningScheduleServiceTest : BaseTest() {

    @InjectMockKs
    lateinit var serverlessRunningScheduleService: ServerlessRunningScheduleService

    @MockK
    lateinit var scheduleStandardService: ScheduleStandardService

    @MockK
    lateinit var runtimeWorkloadMetaService: RuntimeWorkloadMetaService

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var apREService: ApREService

    @Test
    fun seekAvailableRuntimesTest() {

    }

    @Test
    fun seekServerlessAppMigrationBaseApp() {
        val appName = "testAppName"
        val appGroup = "testAppNameGroup"
        val site = "site"
        val unit = "unit"
        val stage = "stage"
        val clusterId = getString()
        val orignalRuntimeId = getString()
        val migrationToBaseApp = getString()
        val migrationToBaseAppGroup = getString()
        val migrationToBaseAppGroupEnvStackId = getString()
        val migrationToRuntimeId = getString()
        val originalWorkloadMetadataConstraint = manufacturePojo(WorkloadMetadataConstraint::class.java).copy(
            appName = appName, resourceGroup = appGroup, site = site,
            unit = unit, stage = stage, clusterId = clusterId, runtimeId = orignalRuntimeId
        )
        val originalRuntimeWorkload = manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
            runtimeWorkloadId = orignalRuntimeId, appName = migrationToBaseApp, resourceGroup = migrationToBaseAppGroup,
            site = site, unit = unit, stage = stage, clusterId = clusterId
        )
        every {
            runtimeWorkloadMetaService.listRunningWorkloadList(
                appName = migrationToBaseApp, envStackId = migrationToBaseAppGroupEnvStackId
            )
        } returns listOf(
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                runtimeWorkloadId = migrationToRuntimeId,
                appName = migrationToBaseApp,
                resourceGroup = migrationToBaseAppGroup,
                site = site,
                unit = unit,
                stage = stage,
                clusterId = clusterId
            )
        )

        val serverlessFeatureSpecCode = "serverless/$migrationToBaseApp\$\$common\$\$SPEC:4-8"
        every {
            scheduleStandardService.getServerlessRuntimeTemplate(
                serverlessBaseAppName = migrationToBaseApp, appName = appName, resourceGroup = appGroup
            )
        } returns serverlessFeatureSpecCode

        val serverlessLabel = manufacturePojo(ApRELabelDO::class.java).copy(
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            type = SERVERLESS,
            value = "serverless/$migrationToBaseApp"
        ).copy(
            apREFeatureSpecs = listOf(
                manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                    specType = serverlessFeatureSpecCode,
                    specCode = migrationToRuntimeId,
                    status = RuntimeScheduleService.RUNTIME_STATUS_ONLINE
                )
            )
        )

        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(
                unit = unit, site = site, stage = stage
            )
        } returns listOf(
            manufacturePojo(ApREDO::class.java).copy(
                az = site, stage = stage, unit = unit
            ).copy(
                resources = listOf(
                    manufacturePojo(ResourceDO::class.java).copy(
                        clusterId = clusterId,
                        apRELabels = listOf(serverlessLabel)
                    )
                )
            )
        )

        val result = serverlessRunningScheduleService.seekAvailableRuntimes(
            originalRuntimeWorkload = originalRuntimeWorkload,
            originalWorkloadMetadataConstraint = originalWorkloadMetadataConstraint,
            migrationToBaseApp = migrationToBaseApp,
            migrationToEnvStackId = migrationToBaseAppGroupEnvStackId
        )
        softly.assertThat(result.size == 1)
        softly.assertThat(result.first() == migrationToRuntimeId).isTrue
    }


    @Test
    fun `seekServerlessAppMigrationBaseApp_with_no_match_runtime`() {
        val appName = "testAppName"
        val appGroup = "testAppNameGroup"
        val site = "site"
        val unit = "unit"
        val stage = "stage"
        val clusterId = getString()
        val orignalRuntimeId = getString()
        val orignalBaseAppGroup = getString()
        val migrationToBaseApp = getString()
        val migrationToBaseAppGroupEnvStackId = getString()
        val migrationToBaseAppGroup = listOf(getString(), getString())
        val migrationToRuntimeId = listOf(getString(), getString())
        val originalWorkloadMetadataConstraint = manufacturePojo(WorkloadMetadataConstraint::class.java).copy(
            appName = appName, resourceGroup = appGroup, site = site,
            unit = unit, stage = stage, clusterId = clusterId, runtimeId = orignalRuntimeId
        )
        val originalRuntimeWorkload = manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
            runtimeWorkloadId = orignalRuntimeId, appName = migrationToBaseApp, resourceGroup = orignalBaseAppGroup,
            site = site, unit = unit, stage = stage, clusterId = clusterId
        )
        every {
            runtimeWorkloadMetaService.listRunningWorkloadList(
                appName = migrationToBaseApp, envStackId = migrationToBaseAppGroupEnvStackId
            )
        } returns listOf(
            //app group1 4C8G
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                runtimeWorkloadId = migrationToRuntimeId[0],
                appName = migrationToBaseApp,
                resourceGroup = migrationToBaseAppGroup[0],
                site = site,
                unit = unit,
                stage = stage,
                clusterId = clusterId
            ),
            //app group2 8C16G
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                runtimeWorkloadId = migrationToRuntimeId[1],
                appName = migrationToBaseApp,
                resourceGroup = migrationToBaseAppGroup[1],
                site = site,
                unit = unit,
                stage = stage,
                clusterId = clusterId
            ),
        )

        val serverlessFeatureSpecCode = "serverless/$migrationToBaseApp\$\$common\$\$SPEC:4-8"
        every {
            scheduleStandardService.getServerlessRuntimeTemplate(
                serverlessBaseAppName = migrationToBaseApp, appName = appName, resourceGroup = appGroup
            )
        } returns serverlessFeatureSpecCode

        val serverlessLabel = manufacturePojo(ApRELabelDO::class.java).copy(
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            type = SERVERLESS,
            value = "serverless/$migrationToBaseApp"
        ).copy(
            apREFeatureSpecs = listOf(
                manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                    specType = "serverless/$migrationToBaseApp\$\$common\$\$SPEC:4-8",
                    specCode = migrationToRuntimeId[0],
                    status = RuntimeScheduleService.RUNTIME_STATUS_ONLINE
                ),
                manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                    specType = "serverless/$migrationToBaseApp\$\$common\$\$SPEC:8-16",
                    specCode = migrationToRuntimeId[1],
                    status = RuntimeScheduleService.RUNTIME_STATUS_ONLINE
                )
            )
        )

        every {
            apREService.listApREDetailsBySiteAndUnitAndStage(
                unit = unit, site = site, stage = stage
            )
        } returns listOf(
            manufacturePojo(ApREDO::class.java).copy(
                az = site, stage = stage, unit = unit
            ).copy(
                resources = listOf(
                    manufacturePojo(ResourceDO::class.java).copy(
                        clusterId = clusterId,
                        apRELabels = listOf(serverlessLabel)
                    )
                )
            )
        )

        val result = serverlessRunningScheduleService.seekAvailableRuntimes(
            originalRuntimeWorkload = originalRuntimeWorkload,
            originalWorkloadMetadataConstraint = originalWorkloadMetadataConstraint,
            migrationToBaseApp = migrationToBaseApp,
            migrationToEnvStackId = migrationToBaseAppGroupEnvStackId
        )
        softly.assertThat(result.size == 1)
        softly.assertThat(result.first() == migrationToRuntimeId[0]).isTrue
    }
}