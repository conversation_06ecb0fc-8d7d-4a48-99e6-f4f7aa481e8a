package com.alibaba.koastline.multiclusters.appenv.model.test

import kotlin.random.Random

/**
 * <AUTHOR>
 */

object KeyGenerator {
    private val charList: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')

    /**
     * Generates an alphanumeric key of given length.
     */
    fun generateAlphanumericKey(length: Int): String {
        return (1..length).map { Random.nextInt(0, charList.size) }.map { charList[it] }.joinToString("")
    }
}