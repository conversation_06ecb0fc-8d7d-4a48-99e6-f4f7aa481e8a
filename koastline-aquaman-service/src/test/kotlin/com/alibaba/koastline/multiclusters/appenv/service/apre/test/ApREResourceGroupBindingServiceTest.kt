package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.ApREResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREResourceGroupBindingDataDO
import com.alibaba.koastline.multiclusters.apre.model.Required
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApRENotFoundException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREResourceGroupBindingException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREResourceGroupBindingUniqueExistException
import com.alibaba.koastline.multiclusters.data.vo.env.ApREResourceGroupBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.justRun
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*

class ApREResourceGroupBindingServiceTest {

    @Test(expected = ApREResourceGroupBindingUniqueExistException::class)
    fun testCreateApREResourceGroupBindingData_with_check_ApREResourceGroupBinding_unique_exist() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val runtimeEnvKey = "1zhwCcBhBwBWKgDDfo4ycdZj3k5q4Phg"
        val apREResourceGroupBindingService = spyk(ApREResourceGroupBindingService(ObjectMapper())) {
            every {
                apREResourceGroupBindingRepo.findByCondition(appName, resourceGroup, runtimeEnvKey)
            } returns ApREResourceGroupBindingData(1L, appName,resourceGroup,runtimeEnvKey, "", now, now, Constants.IS_NOT_DELETED)
        }
        apREResourceGroupBindingService.createApREResourceGroupBindingData(
            ApREResourceGroupBindingDataDO(null, appName,resourceGroup,runtimeEnvKey,
                ApREBindingTerm(
                    required = Required(
                        clusters = listOf("c42dd0b4a54824263934453d6462a9a1f")
                    )
                )
            )
        )
    }

    @Test(expected = ApRENotFoundException::class)
    fun testCreateApREResourceGroupBindingData_with_check_apre_not_found() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val runtimeEnvKey = "1zhwCcBhBwBWKgDDfo4ycdZj3k5q4Phg"
        val apREResourceGroupBindingService = spyk(ApREResourceGroupBindingService(ObjectMapper())) {
            every {
                apREResourceGroupBindingRepo.findByCondition(appName, resourceGroup, runtimeEnvKey)
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns null
        }
        apREResourceGroupBindingService.createApREResourceGroupBindingData(
            ApREResourceGroupBindingDataDO(null, appName,resourceGroup,runtimeEnvKey,
                ApREBindingTerm(
                    required = Required(
                        clusters = listOf("c42dd0b4a54824263934453d6462a9a1f")
                    )
                )
            )
        )
    }

    @Test(expected = ApREResourceGroupBindingException::class)
    fun testCreateApREResourceGroupBindingData_with_check_empty_clusterList() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val runtimeEnvKey = "1zhwCcBhBwBWKgDDfo4ycdZj3k5q4Phg"
        val managedClusterKey = "izQsiF9xOUWwdjWN"
        val apREResourceGroupBindingService = spyk(ApREResourceGroupBindingService(ObjectMapper())) {
            every {
                apREResourceGroupBindingRepo.findByCondition(appName, resourceGroup, runtimeEnvKey)
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns AppRuntimeEnvironmentData(1L,runtimeEnvKey,null,"111111",managedClusterKey,"cn-zhangjiakou",
                "na610","PUBLISH","center_unit.center","ONLINE","",now,now,Constants.IS_NOT_DELETED)
            every {
                resourcePoolService.listByManagedClusterKey(managedClusterKey)
            } returns listOf(
                ResourcePoolDataDO(
                    id = 1L,
                    clusterId = "c42dd0b4a54824263934453d6462a9a1f",
                    managedClusterKey = managedClusterKey,
                    creator = "000000",
                    modifier = "000000",
                    resourcePoolKey = "resourcePoolKey",
                    gmtCreate = Date(Instant.now().toEpochMilli()),
                    gmtModified = Date(Instant.now().toEpochMilli()),
                    isDeleted = IS_NOT_DELETED
                )
            )
        }
        apREResourceGroupBindingService.createApREResourceGroupBindingData(
            ApREResourceGroupBindingDataDO(null, appName,resourceGroup,runtimeEnvKey,
                ApREBindingTerm(
                    required = Required(
                        clusters = emptyList()
                    )
                )
            )
        )
    }

    @Test(expected = ApREResourceGroupBindingException::class)
    fun testCreateApREResourceGroupBindingData_with_check_unknown_clusterList() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val runtimeEnvKey = "1zhwCcBhBwBWKgDDfo4ycdZj3k5q4Phg"
        val managedClusterKey = "izQsiF9xOUWwdjWN"
        val clusterAId = "clusterAId"
        val clusterBId = "clusterBId"
        val apREResourceGroupBindingService = spyk(ApREResourceGroupBindingService(ObjectMapper())) {
            every {
                apREResourceGroupBindingRepo.findByCondition(appName, resourceGroup, runtimeEnvKey)
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns AppRuntimeEnvironmentData(1L,runtimeEnvKey,null,"111111",managedClusterKey,"cn-zhangjiakou",
                "na610","PUBLISH","center_unit.center","ONLINE","",now,now,Constants.IS_NOT_DELETED)
            every {
                resourcePoolService.listByManagedClusterKey(managedClusterKey)
            } returns listOf(
                ResourcePoolDataDO(
                    id = 1L,
                    clusterId = clusterAId,
                    managedClusterKey = managedClusterKey,
                    creator = "000000",
                    modifier = "000000",
                    resourcePoolKey = "resourcePoolKey",
                    gmtCreate = Date(Instant.now().toEpochMilli()),
                    gmtModified = Date(Instant.now().toEpochMilli()),
                    isDeleted = IS_NOT_DELETED
                )
            )
            every {
                defaultClusterService.getSimpleClusterProfileDataByClusterId(clusterBId)
            } returns ClusterProfileData(
                1L,
                clusterBId,
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                ClusterProfileUseTypeEnum.publish.name,
                now,
                now,
                IS_NOT_DELETED,
                ClusterStatus.ONLINE.name,
                "SYSTEM_ADMIN",
                "SYSTEM_ADMIN",
            )
        }
        apREResourceGroupBindingService.createApREResourceGroupBindingData(
            ApREResourceGroupBindingDataDO(null, appName,resourceGroup,runtimeEnvKey,
                ApREBindingTerm(
                    required = Required(
                        clusters = listOf(clusterBId)
                    )
                )
            )
        )
    }
}