package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.appenv.service.apre.test.ApRELabelServiceTest.Companion
import com.alibaba.koastline.multiclusters.appenv.service.apre.test.ApREMockDataUtils.getApRELabelDO
import com.alibaba.koastline.multiclusters.appenv.service.apre.test.ApREMockDataUtils.getComplexApREDTO
import com.alibaba.koastline.multiclusters.appenv.service.apre.test.ApREMockDataUtils.getRandomResourcePool
import com.alibaba.koastline.multiclusters.apre.ApRELabelDefineService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt.APRE_LABEL_FEATURE_NAME
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApREDefaultFeatureService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecSelectorTerm
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelSelectorTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREResourceGroupBindingDataDO
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclaredLocation
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.MatchExpression
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.MetadataOfSiteDO
import com.alibaba.koastline.multiclusters.apre.model.MixApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Required
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.model.ShortIdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREAndRelativeUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREBaseDetailsCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREBaseDetailsUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRECreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ClusterWithFeatureSelectorDto
import com.alibaba.koastline.multiclusters.apre.model.req.ResourcePoolCreateOrUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREDefaultFeatureUsageEnum.DEFAULT_IMPORT
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSourceEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum.publish
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum.online
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.APRE
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.apre.params.ApREStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREException
import com.alibaba.koastline.multiclusters.common.exceptions.ApREUniqueExistMetadataConstraintException
import com.alibaba.koastline.multiclusters.common.exceptions.ManagedClusterNotExistException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.COMMON
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.DEFAULT
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.KManagedClusterData
import com.alibaba.koastline.multiclusters.data.vo.env.MetadataOfSite
import com.alibaba.koastline.multiclusters.external.model.UnitLocation
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import testutils.BaseTest
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class ApREServiceTest : BaseTest() {
    private var objectMapper = ObjectMapperFactory.newTolerant()

    @Test
    fun testRequireGroupAuthorizedDeclarations() {
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                requireApREDeedDOByResourceGroup(any())
            } returns ApREDeedDO(declarations = mutableListOf(
                Declaration(az = "na610", unit = "center"),
                Declaration(az = "na620", unit = "center")
            ))

            every {
                hcrmApi.listUnitizationUnitLocation(any(), any())
            } returns listOf(UnitLocation(site = "cccaaa", unit = "unsh", unitType = "", ecs = ""))

            every {
                requireApREDeedResultByApREDeed(any())
            } answers {
                val apREDeedDO = it.invocation.args[0] as ApREDeedDO
                val matchDeclarations = apREDeedDO.declarations!!.map { decl ->
                    val declAz = decl.az
                    if (declAz == "cccaaa") {
                        MatchDeclaration(
                            declaration = decl, apres = listOf(
                                ApREDO(
                                    creator = "171031", managedClusterKey = "",
                                    region = "", az = declAz, stage = "", unit = decl.unit!!
                                )
                            )
                        )
                    } else {
                        MatchDeclaration(declaration = decl)
                    }
                }
                ApREDeedResult(
                    deedDO = apREDeedDO, matchDeclarations = matchDeclarations
                )
            }
        }

        val groupDeclarations = apREService.requireGroupAuthorizedDeclarations(
            "test-app", "test-group", "staging"
        )
        val declaredLocations = groupDeclarations.declaredLocations
        assertEquals(3, declaredLocations.size)
        assertEquals(DeclaredLocation("center", "na610", false), declaredLocations[0])
        assertEquals(DeclaredLocation("center", "na620", false), declaredLocations[1])
        assertEquals(DeclaredLocation("unsh", "cccaaa", true), declaredLocations[2])
    }

    @Test
    fun testRequireApREDeedDOByResourceGroupWithRegion() {
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                apREDeedService.findApREDeedByKey(any())
            } returns ApREDeedDO()

            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(any())
            } returns ApREDeedResourceGroupBindingData(123, "sadad=", "aaa_host",
                "app-bbb", Date(), Date())

            every {
                apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(any())
            } returns ApREDeedDO(declarations = mutableListOf(
                Declaration(region = "aaa", az = "bbb"),
                Declaration(az = "cc")
            ))

            every {
                apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(any())
            } returns ApREDeedDO(declarations = mutableListOf(
                Declaration(region = "aaa", az = "bbb"),
                Declaration(az = "cc")
            ))

            every {
                metadataOfSiteRepo.findBySite(eq("cc"))
            } returns MetadataOfSite(123L, "cc", "mm_r", Date(), Date(), "N", "177031", "0")
        }

        val apREDeedDO = apREService.requireApREDeedDOByResourceGroupWithRegion("test-group")
        val decl0 = apREDeedDO!!.declarations!![0]
        assertEquals("aaa", decl0.region)
        assertEquals("bbb",  decl0.az)
        val decl1 = apREDeedDO.declarations!![1]
        assertEquals("mm_r", decl1.region)
        assertEquals("cc",  decl1.az)
    }

    @Test
    fun testFindApREByKey() {
        val apREKey = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(apREKey)
            } returns ApREMockDataUtils.getAppRuntimeEnvironmentData(runtimeEnvKey = apREKey)
        }
        val apREDTO = apREService.findApREByKey("iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61")
        assertEquals("iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61", apREDTO!!.runtimeEnvKey)
    }

    @Test(expected = ManagedClusterNotExistException::class)
    fun testCreateApRE_with_managed_cluster_not_exist() {
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                kManagedClusterRepo.findKManagedClusterByManagedClusterKey(any())
            } returns null
        }
        apREService.createApREBaseDetails(getSimpleApRECreateReqDto())
    }

    @Test(expected = ApREUniqueExistMetadataConstraintException::class)
    fun testCreateApRE_with_apre_unique_exist_metadata_constraint() {
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                kManagedClusterRepo.findKManagedClusterByManagedClusterKey(any())
            } returns getDefaultKManagedCluster()
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(any())
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(any(), any(), any(), any())
            } returns listOf(ApREMockDataUtils.getAppRuntimeEnvironmentData())
        }
        apREService.createApREBaseDetails(getSimpleApRECreateReqDto())
    }

    @Test(expected = ApREException::class)
    fun testCreateApRE_with_apre_insert_fail() {
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                kManagedClusterRepo.findKManagedClusterByManagedClusterKey(any())
            } returns getDefaultKManagedCluster()
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(any())
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(any(), any(), any(), any())
            } returns listOf()
            every {
                appRuntimeEnvironmentDataRepo.insert(any())
            } returns 0
            justRun {
                metadataService.checkMetadataConstraintWithError(any(), any(), any(), any())
            }
        }
        apREService.createApREBaseDetails(getSimpleApRECreateReqDto())
    }

    @Test
    fun testCreateApRE_with_apre_insert_success() {
        val apRECreateReqDto = getSimpleApRECreateReqDto()
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                kManagedClusterRepo.findKManagedClusterByManagedClusterKey(any())
            } returns getDefaultKManagedCluster()
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(any())
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(any(), any(), any(), any())
            } returns listOf()
            every {
                appRuntimeEnvironmentDataRepo.insert(any())
            } returns 1
            every {
                findApREByKey(any())
            } returns getSimpleApREDTOWithExistRuntimeEnvKey()
            justRun {
                metadataService.checkMetadataConstraintWithError(any(), any(), any(), any())
            }
        }
        apREService.createApREBaseDetails(apRECreateReqDto)
        verify {
            apREService.appRuntimeEnvironmentDataRepo.insert(match {
                it.az == apRECreateReqDto.az
                it.region == apRECreateReqDto.region
                it.stage == apRECreateReqDto.stage.name
                it.unit == apRECreateReqDto.unit
                it.status == "ONLINE"
                it.isDeleted == Constants.IS_NOT_DELETED
            })
        }
    }

    @Test
    fun testCreateApREIgnoreWhileExistWithLabel() {
        val apRECreateReqDto = getComplexApRECreateReqDto()
        val apREDO = getComplexApREDTO()
        val apREDOResult = getComplexApREDTO().copy(
            runtimeEnvKey = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        )
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                kManagedClusterRepo.findKManagedClusterByManagedClusterKey(any())
            } returns getDefaultKManagedCluster()
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(any())
            } returns null
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(any(), any(), any(), any())
            } returns listOf()
            every {
                findApREByKey(any())
            } returns getSimpleApREDTOWithExistRuntimeEnvKey()
            every {
                appRuntimeEnvironmentDataRepo.insert(any())
            } returns 1
            every {
                apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(any())
            } returns apREDO.apRELabels!![0]
            every {
                findApREDetailByKey(any())
            } returns getComplexApREDTO()
            justRun {
                metadataService.checkMetadataConstraintWithError(any(), any(), any(), any())
            }
        }
        apREService.createApREBaseDetails(apRECreateReqDto)
        verify {
            apREService.apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(match {
                it.targetKey!!.isNotBlank()
            })
        }
    }

    @Test
    fun testFindCombinedApREDetailByKey_with_apREBindingTerm_null() {
        val runtimeEnvKey = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns ApREMockDataUtils.getAppRuntimeEnvironmentData(runtimeEnvKey = runtimeEnvKey)
            every {
                regionPropertiesConfig.regionProperties.regions[any()]
            } returns "张北"
            every {
                regionPropertiesConfig.regionProperties.az[any()]
            } returns "na610"
            every {
                apRELabelService.findApRELabelByRuntimeEnvKeyWithResourcePoolAndDefault(runtimeEnvKey)
            } returns getComplexApREDTO().apRELabels!!
            every {
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(any())
            } returns null
        }
        //不测试集群绑定
        apREService.resourcePoolService = mockk()
        every {
            apREService.resourcePoolService.listByManagedClusterKey(any())
        } returns emptyList()

        val apREDO = apREService.findCombinedApREBaseDetailByKey(runtimeEnvKey, null)
        assertEquals(1, apREDO!!.apRELabels!!.size)
        assertEquals(2, apREDO.apRELabels!![0].apREFeatureSpecs!!.size)
    }

    @Test
    fun testFindCombinedApREDetailByKey_with_apREBindingTerm_single_match() {
        val runtimeEnvKey = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns ApREMockDataUtils.getAppRuntimeEnvironmentData(runtimeEnvKey = runtimeEnvKey)
            every {
                regionPropertiesConfig.regionProperties.regions[any()]
            } returns "张北"
            every {
                regionPropertiesConfig.regionProperties.az[any()]
            } returns "na610"
            every {
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(any())
            } returns null
        }
        apREService.apRELabelService = spyk(ApRELabelService()) {
            every {
                findApRELabelByRuntimeEnvKeyWithResourcePoolAndDefault(runtimeEnvKey)
            } returns
                    listOf(
                        ApRELabelDO(
                            1L,
                            null,
                            APRE_LABEL_FEATURE_NAME,
                            "compute",
                            null,
                            null,
                            null,
                            null,
                            listOf(
                                ApREFeatureSpecDO(
                                    1L,
                                    null,
                                    "4C8G",
                                    null,
                                    null,
                                    "publish",
                                    "online",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                                ),
                                ApREFeatureSpecDO(
                                    2L,
                                    null,
                                    "2C4G",
                                    null,
                                    null,
                                    "test",
                                    "online",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                                )
                            ), APRE,
                            COMMON
                        ),
                        ApRELabelDO(
                            2L,
                            null,
                            APRE_LABEL_FEATURE_NAME,
                            "serverless/tpp",
                            null,
                            null,
                            null,
                            null,
                            listOf(
                                ApREFeatureSpecDO(
                                    3L,
                                    null,
                                    "serverless_runtime_1.0",
                                    null,
                                    null,
                                    "publish",
                                    "online",
                                    "appstack_runtime",
                                    "1111",
                                    "appstack_version",
                                    "1111",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null
                                ),
                                ApREFeatureSpecDO(
                                    4L,
                                    null,
                                    "serverless_runtime_1.0",
                                    null,
                                    null,
                                    "test",
                                    "online",
                                    "appstack_runtime",
                                    "2222",
                                    "appstack_version",
                                    "2222",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null
                                )
                            ), APRE,
                            SERVERLESS
                        )
                    )
        }
        val apREBindingTerm = ApREBindingTerm(
            allSupported = false,
            required = Required(
                allLabelSupported = false,
                apRELabelSelectorTerms = listOf(
                    ApRELabelSelectorTerm(
                        APRE_LABEL_FEATURE_NAME,
                        "serverless/tpp",
                        listOf(
                            ApREFeatureSpecSelectorTerm(
                                listOf(
                                    MatchExpression("scope", "in", listOf("publish"))
                                )
                            )
                        )
                    )
                ),
                allClustersSupported = true
            )
        )
        val apREDO = apREService.findCombinedApREBaseDetailByKey(runtimeEnvKey, apREBindingTerm)
        assertEquals(1, apREDO!!.apRELabels!!.size)
        assertEquals(1, apREDO.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("publish", apREDO.apRELabels!![0].apREFeatureSpecs!![0].scope)
    }

    @Test
    fun testFindCombinedApREDetailByKey_with_apREBindingTerm_multi_match() {
        val runtimeEnvKey = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns ApREMockDataUtils.getAppRuntimeEnvironmentData(runtimeEnvKey= runtimeEnvKey)
            every {
                regionPropertiesConfig.regionProperties.regions[any()]
            } returns "张北"
            every {
                regionPropertiesConfig.regionProperties.az[any()]
            } returns "na610"
            every {
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(any())
            } returns null
        }
        apREService.apRELabelService = spyk(ApRELabelService()) {
            every {
                findApRELabelByRuntimeEnvKeyWithResourcePoolAndDefault(runtimeEnvKey)
            } returns
                    listOf(
                        ApRELabelDO(
                            1L,
                            null,
                            APRE_LABEL_FEATURE_NAME,
                            "compute",
                            null,
                            null,
                            null,
                            null,
                            listOf(
                                ApREFeatureSpecDO(
                                    1L,
                                    null,
                                    "4C8G",
                                    null,
                                    null,
                                    "publish",
                                    "online",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                                ),
                                ApREFeatureSpecDO(
                                    2L,
                                    null,
                                    "2C4G",
                                    null,
                                    null,
                                    "test",
                                    "online",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                                )
                            ),
                            APRE,
                            DEFAULT
                        ),
                        ApRELabelDO(
                            2L,
                            null,
                            APRE_LABEL_FEATURE_NAME,
                            "serverless/tpp",
                            null,
                            null,
                            null,
                            null,
                            listOf(
                                ApREFeatureSpecDO(
                                    3L,
                                    null,
                                    "serverless_runtime_1.0",
                                    null,
                                    null,
                                    "publish",
                                    "online",
                                    "appstack_runtime",
                                    "1111",
                                    "appstack_version",
                                    "1111",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null
                                ),
                                ApREFeatureSpecDO(
                                    4L,
                                    null,
                                    "serverless_runtime_1.0",
                                    null,
                                    null,
                                    "test",
                                    "online",
                                    "appstack_runtime",
                                    "2222",
                                    "appstack_version",
                                    "2222",
                                    "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                    null,
                                    null,
                                    null,
                                    null
                                )
                            ),
                            APRE,
                            SERVERLESS
                        )
                    )
        }
        val apREBindingTerm = ApREBindingTerm(
            allSupported = false,
            required = Required(
                allLabelSupported = false,
                allClustersSupported = true,
                apRELabelSelectorTerms = listOf(
                    ApRELabelSelectorTerm(
                        APRE_LABEL_FEATURE_NAME,
                        "compute",
                        listOf(
                            ApREFeatureSpecSelectorTerm(
                                listOf(
                                    MatchExpression("scope", "in", listOf("publish", "test"))
                                )
                            )
                        )
                    ),
                    ApRELabelSelectorTerm(
                        APRE_LABEL_FEATURE_NAME,
                        "serverless/tpp",
                        listOf(
                            ApREFeatureSpecSelectorTerm(
                                listOf(
                                    MatchExpression("scope", "in", listOf("publish"))
                                )
                            )
                        )
                    )
                )
            )
        )
        val apREDO = apREService.findCombinedApREBaseDetailByKey(runtimeEnvKey, apREBindingTerm)
        assertEquals(2, apREDO!!.apRELabels!!.size)
        assertEquals(2, apREDO!!.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals(1, apREDO!!.apRELabels!![1].apREFeatureSpecs!!.size)
        assertEquals("publish", apREDO!!.apRELabels!![1].apREFeatureSpecs!![0].scope)
    }

    @Test
    fun testQueryClustersByApREDeedContent_with_has_resource_group_binding() {
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val apREDO = getSimpleApREDTOWithExistRuntimeEnvKey()
        val apREDeedDO = ApREDeedDO(
            identityInfo = IdentityInfo(
                appName = appName,
                envLevel = "production",
                nodeGroup = resourceGroup
            )
        )
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                requireApREDeedResultByApREDeed(any())
            } returns ApREDeedResult(
                deedDO = apREDeedDO,
                matchDeclarations = listOf(
                    MatchDeclaration(
                        apres = listOf(apREDO)
                    )
                )
            )
            every {
                resourcePoolService.listOnlineResourceDOByManagedClusterKey(apREDO.managedClusterKey)
            } returns listOf(
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew(
                        "c8714f9fdfdb142e7a52cbb89290f2a4a", "zjk_core_a", "alibaba", "alibaba-asi",
                        listOf("na610"),
                        emptyList(), ClusterProfileUseTypeEnum.publish.name
                    )
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew(
                        "c8714f9fdfdb142e7a52cbb89290f2a4b", "zjk_core_b", "alibaba", "alibaba-asi",
                        listOf("na610"),
                        emptyList(), ClusterProfileUseTypeEnum.test.name
                    )
                )
            )
            every {
                apREResourceGroupBindingService.getApREResourceGroupBindingDataByCondition(
                    appName,
                    resourceGroup,
                    apREDO.runtimeEnvKey!!
                )
            } returns ApREResourceGroupBindingDataDO(
                appName = appName, resourceGroup = resourceGroup, runtimeEnvKey = apREDO.runtimeEnvKey!!,
                selector = ApREBindingTerm(
                    allSupported = false,
                    required = Required(
                        clusters = listOf("c8714f9fdfdb142e7a52cbb89290f2a4b"),
                        allClustersSupported = false
                    )
                )
            )
        }
        val result = apREService.queryClustersByApREDeedContent(apREDeedDO)
        val resources = result.matchDeclarations[0].apres[0].resources
        assertEquals(1, resources.size)
        assertEquals("zjk_core_b", resources[0].clusterProfileNew!!.clusterName)
        assertEquals("test", resources[0].clusterProfileNew!!.useType)
    }

    @Test
    fun testQueryClustersByApREDeedContent_with_has_not_resource_group_binding() {
        val appName = "normandy-test-app4"
        val resourceGroup = "normandy-test-app4_prehost"
        val apREDO = getSimpleApREDTOWithExistRuntimeEnvKey()
        val apREDeedDO = ApREDeedDO(
            identityInfo = IdentityInfo(
                appName = appName,
                envLevel = "production",
                nodeGroup = resourceGroup
            )
        )
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                requireApREDeedResultByApREDeed(any())
            } returns ApREDeedResult(
                deedDO = apREDeedDO,
                matchDeclarations = listOf(
                    MatchDeclaration(
                        apres = listOf(apREDO)
                    )
                )
            )
            every {
                resourcePoolService.listOnlineResourceDOByManagedClusterKey(apREDO.managedClusterKey)
            } returns listOf(
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew(
                        "c8714f9fdfdb142e7a52cbb89290f2a4a", "zjk_core_a", "alibaba", "alibaba-asi",
                        listOf("na610"),
                        emptyList(), ClusterProfileUseTypeEnum.publish.name
                    )
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew(
                        "c8714f9fdfdb142e7a52cbb89290f2a4b", "zjk_core_b", "alibaba", "alibaba-asi",
                        listOf("na610"),
                        emptyList(), ClusterProfileUseTypeEnum.test.name
                    )
                )
            )
            every {
                apREResourceGroupBindingService.getApREResourceGroupBindingDataByCondition(
                    appName,
                    resourceGroup,
                    apREDO.runtimeEnvKey!!
                )
            } returns null
        }
        val result = apREService.queryClustersByApREDeedContent(apREDeedDO)
        val resources = result.matchDeclarations[0].apres[0].resources
        assertEquals(1, resources.size)
        assertEquals("zjk_core_a", resources[0].clusterProfileNew!!.clusterName)
        assertEquals("publish", resources[0].clusterProfileNew!!.useType)
    }

    @Test
    fun testFindApRELabelByRuntimeEnvKeyWithDefault_aggregated() {
        val apRELabel = ApRELabel(
            1L,
            ApRELabelServiceTest.RUNTIME_ENV_KEY,
            APRE_LABEL_FEATURE_NAME,
            "serverless/tpp",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            Constants.IS_NOT_DELETED,
            "iZfo5rAlMCVD1hi",
            APRE.name,
            SERVERLESS
        )

        val apRELabelDefineService = spyk(ApRELabelDefineService()) {
            every {
                fillApRELabelTitles(any())
            } just runs
        }

        val apREService = spyk(ApREService(objectMapper)) {
            val apREFeatureSpecService = spyk(ApREFeatureSpecService()) {
                every {
                    convertDefaultFeatureSpec(any())
                } answers {
                    callOriginal()
                }
                every {
                    transformToDefaultApREFeatureSpecDO(any())
                } answers {
                    callOriginal()
                }
                every {
                    batchQueryOnlineApREFeatureSpecByLabel(any())
                } returns listOf(
                    ApREFeatureSpecDO(
                        null,
                        apRELabel.apRELabelKey,
                        "serverless_runtime_1.0",
                        null,
                        null,
                        "publish",
                        "online",
                        "appstack_runtime",
                        "1111",
                        "appstack_version",
                        "1111",
                        "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                        null,
                        null,
                        null,
                        null
                    )
                )
                every {
                    apREDefaultFeatureService.findApREDefaultFeatureDetail(any())
                }   returns null
            }

            val apREDefaultFeatureService = spyk(ApREDefaultFeatureService()) {
                every {
                    findApREDefaultFeatureDetailWithDefaultImportUsage()
                } returns listOf(
                    ApREDefaultFeatureDO(
                        2L,
                        "8cVomjKmBQa61",
                        "compute",
                        DEFAULT_IMPORT.name,
                        Date(Instant.now().toEpochMilli()),
                        Date(Instant.now().toEpochMilli()),
                        Constants.IS_NOT_DELETED,
                        listOf(
                            ApREDefaultFeatureSpecDO(
                                3L,
                                "8cVomjKmBQa61",
                                "2C",
                                "cpu",
                                "2C",
                                publish.name,
                                online.name,
                                null,
                                Date(Instant.now().toEpochMilli()),
                                Date(Instant.now().toEpochMilli()),
                                Constants.IS_NOT_DELETED
                            )
                        )
                    )
                )
            }

            val apRELabelService = spyk(ApRELabelService(), recordPrivateCalls = true) {
                this.apRELabelDefinitionService = apRELabelDefineService
                this.apREDefaultFeatureService = apREDefaultFeatureService
                this.apREFeatureSpecService = apREFeatureSpecService

                every {
                    apRELabelRepo.findByTarget(any(), any())
                } returns listOf(
                    apRELabel
                )
            }

            this.apRELabelService = apRELabelService

            val resourcePoolService = spyk(ResourcePoolService()) {
                this.apRELabelService = apRELabelService
                this.apRELabelDefinitionService = apRELabelDefineService
                every {
                    listByManagedClusterKey(ApRELabelServiceTest.MANAGED_CLUSTER_KEY)
                } returns listOf(
                    ResourcePoolDataDO(
                        1L,
                        ApRELabelServiceTest.RESOURCE_POOL_KEY,
                        "cluster_id",
                        ApRELabelServiceTest.MANAGED_CLUSTER_KEY,
                        "",
                        "",
                        Date(Instant.now().toEpochMilli()),
                        Date(Instant.now().toEpochMilli()),
                        "N"
                    )
                )

                val appRuntimeEnvironmentDataRepo = spyk<AppRuntimeEnvironmentDataRepo> {
                    every {
                        appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(ApRELabelServiceTest.RUNTIME_ENV_KEY)
                    } returns AppRuntimeEnvironmentData(
                        1L,
                        ApRELabelServiceTest.RUNTIME_ENV_KEY,
                        ",",
                        "",
                        ApRELabelServiceTest.MANAGED_CLUSTER_KEY,
                        "cn-zhangjiakou",
                        "na610",
                        "PUBLISH",
                        "center",
                        "ONLINE",
                        "",
                        Date(Instant.now().toEpochMilli()),
                        Date(Instant.now().toEpochMilli()),
                        "N"
                    )
                }
                this.appRuntimeEnvironmentDataRepo = appRuntimeEnvironmentDataRepo
            }
            this.apRELabelDefinitionService = apRELabelDefineService
            this.apREDefaultFeatureService = apREDefaultFeatureService
            this.apREFeatureSpecService = apREFeatureSpecService
            this.resourcePoolService = resourcePoolService
        }

        val apRELabelDOs =
            apREService.findApRELabelFromApREAndResourcePoolWithDefault(ApRELabelServiceTest.RUNTIME_ENV_KEY)
        assertEquals(3, apRELabelDOs.size)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[0].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[0].apREFeatureSpecs!![0].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[1].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[1].apREFeatureSpecs!![0].source)
        assertEquals(ApREFeatureSourceEnum.DEFAULT.name, apRELabelDOs[2].source)
        assertEquals(ApREFeatureSourceEnum.DEFAULT.name, apRELabelDOs[2].apREFeatureSpecs!![0].source)
    }

    @Test
    fun testFindApRELabelByRuntimeEnvKeyWithDefault_ignore_default() {
        val apRELabel1 = ApRELabel(
            1L,
            Companion.RUNTIME_ENV_KEY,
            APRE_LABEL_FEATURE_NAME,
            "serverless/tpp",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            Constants.IS_NOT_DELETED,
            "iZfo5rAlMCVD1hi",
            APRE.name,
            SERVERLESS
        )
        val apRELabel2 = ApRELabel(
            2L,
            Companion.RUNTIME_ENV_KEY,
            APRE_LABEL_FEATURE_NAME,
            "compute",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            Constants.IS_NOT_DELETED,
            "hhhhhhhhhhh",
            APRE.name,
            DEFAULT
        )

        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            val apRELabelDefinitionService = spyk(ApRELabelDefineService()) {
                every {
                    fillApRELabelTitles(any())
                } just runs
            }

            val apREDefaultFeatureService = spyk(ApREDefaultFeatureService()) {
                every {
                    apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage()
                } returns listOf(
                    ApREDefaultFeatureDO(
                        2L,
                        "8cVomjKmBQa61",
                        "compute",
                        DEFAULT_IMPORT.name,
                        Date(Instant.now().toEpochMilli()),
                        Date(Instant.now().toEpochMilli()),
                        Constants.IS_NOT_DELETED,
                        listOf(
                            ApREDefaultFeatureSpecDO(
                                3L,
                                "8cVomjKmBQa61",
                                "2C",
                                "cpu",
                                "2C",
                                publish.name,
                                online.name,
                                null,
                                Date(Instant.now().toEpochMilli()),
                                Date(Instant.now().toEpochMilli()),
                                Constants.IS_NOT_DELETED
                            )
                        )
                    )
                )
            }

            val apREFeatureSpecService = spyk(ApREFeatureSpecService()) {
                every {
                    batchQueryOnlineApREFeatureSpecByLabel(listOf(apRELabel1.apRELabelKey, apRELabel2.apRELabelKey))
                } returns listOf(
                    ApREFeatureSpecDO(
                        null,
                        apRELabel1.apRELabelKey,
                        "serverless_runtime_1.0",
                        null,
                        null,
                        "publish",
                        "online",
                        "appstack_runtime",
                        "1111",
                        "appstack_version",
                        "1111",
                        "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                        null,
                        null,
                        null,
                        null
                    ),
                    ApREFeatureSpecDO(
                        null,
                        apRELabel2.apRELabelKey,
                        "4C",
                        "cpu",
                        "4C",
                        "publish",
                        "online",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    )
                )
            }

            val apRELabelService = spyk(ApRELabelService(), recordPrivateCalls = true) {
                this.apRELabelDefinitionService = apRELabelDefinitionService
                this.apREFeatureSpecService = apREFeatureSpecService
                every {
                    apRELabelRepo.findByTarget(any(), any())
                } returns listOf(
                    apRELabel1, apRELabel2
                )
            }


            val resourcePoolService = spyk(ResourcePoolService()) {
                this.apRELabelService = apRELabelService
                this.apRELabelDefinitionService = apRELabelDefinitionService
                every {
                    listByManagedClusterKey(Companion.MANAGED_CLUSTER_KEY)
                } returns listOf(
                    ResourcePoolDataDO(
                        1L,
                        Companion.RESOURCE_POOL_KEY,
                        "cluster_id",
                        Companion.MANAGED_CLUSTER_KEY,
                        "",
                        "",
                        Date(Instant.now().toEpochMilli()),
                        Date(Instant.now().toEpochMilli()),
                        "N"
                    )
                )

                every {
                    appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(Companion.RUNTIME_ENV_KEY)
                } returns AppRuntimeEnvironmentData(
                    1L,
                    Companion.RUNTIME_ENV_KEY,
                    ",",
                    "",
                    Companion.MANAGED_CLUSTER_KEY,
                    "cn-zhangjiakou",
                    "na610",
                    "PUBLISH",
                    "center",
                    "ONLINE",
                    "",
                    Date(Instant.now().toEpochMilli()),
                    Date(Instant.now().toEpochMilli()),
                    "N"
                )
            }

            this.apREDefaultFeatureService = apREDefaultFeatureService
            this.apRELabelService = apRELabelService
            this.apRELabelDefinitionService = apRELabelDefinitionService
            this.resourcePoolService = resourcePoolService
        }

        val apRELabelDOs = apREService.findApRELabelFromApREAndResourcePoolWithDefault(Companion.RUNTIME_ENV_KEY)
        assertEquals(4, apRELabelDOs.size)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[0].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[0].apREFeatureSpecs!![0].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[1].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[1].apREFeatureSpecs!![0].source)
        assertEquals("4C", apRELabelDOs[1].apREFeatureSpecs!![0].specCode)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[2].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[2].apREFeatureSpecs!![0].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[3].source)
        assertEquals(ApREFeatureSourceEnum.CUSTOMIZED.name, apRELabelDOs[3].apREFeatureSpecs!![0].source)
        assertEquals("4C", apRELabelDOs[3].apREFeatureSpecs!![0].specCode)
    }

    private fun getDefaultKManagedCluster(): KManagedClusterData {
        return KManagedClusterData(
            110,
            "iZfo5rAlMCVD1",
            123,
            456,
            "cse-system",
            26,
            "cn-zhangjiakoiu",
            "{}",
            "created",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli())
        )
    }

    @Test
    fun listApREDetailsByPropertiesTest() {
        val appRuntimeEnvData = ApREMockDataUtils.getAppRuntimeEnvironmentData()
            .copy(
            id = getLong(),
            runtimeEnvKey = getString(),
            name = getString()
        )

        val resourcePool = getRandomResourcePool(
            manageClusterKey = appRuntimeEnvData.managedClusterKey,
            clusterId = getString()
        )

        val resourceLabelDO = getApRELabelDO(RESOURCE_POOL)
        val apRELabelDO = getApRELabelDO(APRE)

        val apREBindingData = ApREBindingData(
            runtimeEnvKey = appRuntimeEnvData.runtimeEnvKey,
            id = getLong(),
            apREBindingTerm = ApREBindingTerm(
                allSupported = true,
                required = null
            )
        )
        val attorneyProductLineScope = "4#1_2_3"

        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetId = apREBindingData.id,
            targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
            externalId = attorneyProductLineScope,
            exclusions = null,
            restrictions = null,
            isDeleted = "N"
        )

        val unit = appRuntimeEnvData.unit
        val site = appRuntimeEnvData.az
        val stage = appRuntimeEnvData.stage
        val status = appRuntimeEnvData.status
        val pageSize = 1
        val pageNumber = 1
        val totalCount = 1L

        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                listBaseApREByProperties(
                    unit = unit,
                    site = site,
                    stage = stage,
                    status = status,
                    pageSize = pageSize,
                    pageNumber = pageNumber,
                    keyWords = null
                )
            } returns PageData(
                pageSize = pageSize,
                pageNumber = pageNumber,
                totalCount = totalCount,
                data = mutableListOf(
                    appRuntimeEnvData
                )
            )

            every {
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfigByClusterEnvKeyList(
                    mutableListOf(appRuntimeEnvData.runtimeEnvKey)
                )
            } returns emptyList()

            every {
                resourcePoolService.listByManagedClusterKey(resourcePool.managedClusterKey)
            } returns mutableListOf(resourcePool)

            every {
                apRELabelService.findApRELabelByTarget(
                    resourcePool.resourcePoolKey, RESOURCE_POOL.name
                )
            } returns mutableListOf(resourceLabelDO)

            every {
                attorneyService.listAttorneyByApREKey(appRuntimeEnvData.runtimeEnvKey)
            } returns listOf(
                 Attorney(
                     apREBindingData, matchScopeDataDO
                 )
            )

            // mock不加自定标签
            every {
                apRELabelService.findApRELabelByTargetWithDefaultLabel(appRuntimeEnvData.runtimeEnvKey, APRE.name)
            } returns mutableListOf(apRELabelDO)

            every {
                apRELabelDefinitionService.fillApRELabelTitles(any())
            } answers {
                val labels = it.invocation.args[0] as List<ApRELabelDO>
                labels.forEach { label ->
                    label.title = getString()
                }
            }
        }

        val apREDetailPage = apREService.listApREDetailsByProperties(
            unit = unit, site = site, stage = stage, status = status,
            keyWords = null, pageSize = 1, pageNumber = 1
        )

        // 校验展示相关的展示数据
        val apREDetailsList = apREDetailPage.data
        val realPageSize = apREDetailPage.pageSize
        val realPageNumber = apREDetailPage.pageNumber
        softly.assertThat(apREDetailPage.pageNumber).isEqualTo(realPageNumber)
        softly.assertThat(apREDetailsList).isNotEmpty
        softly.assertThat(apREDetailsList!!.size).isEqualTo(realPageSize)
        // 基础数据 + 授权 + 特性 + 集群（以及集群特性）
        softly.assertThat(apREDetailsList[0].az).isEqualTo(site)
        softly.assertThat(apREDetailsList[0].stage).isEqualTo(stage)
        softly.assertThat(apREDetailsList[0].unit).isEqualTo(unit)
        softly.assertThat(apREDetailsList[0].attorneys.size).isEqualTo(1)
        softly.assertThat(apREDetailsList[0].attorneys[0].matchScopeData).isEqualTo(matchScopeDataDO)
        softly.assertThat(apREDetailsList[0].apRELabels).isEqualTo(mutableListOf(apRELabelDO))
        softly.assertThat(apREDetailsList[0].resources[0].apRELabels).isEqualTo(
            mutableListOf(resourceLabelDO)
        )
        softly.assertThat(apREDetailsList[0].resources[0].clusterId).isEqualTo(resourcePool.clusterId)
    }

    /**
     * 测试通过ApREBindingTerm来过滤对应开放集群标签和
     */
    @Test
    fun findApREDetailByKey_withFullResource() {
        val runtimeEnvKey = getString()
        val apRE = ApREMockDataUtils.getAppRuntimeEnvironmentData()
        val resourceLabel = getApRELabelDO(RESOURCE_POOL)
        val resourcePool = manufacturePojo(ResourcePoolDataDO::class.java).copy(
            managedClusterKey = apRE.managedClusterKey
        )
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                appRuntimeEnvironmentDataRepo.findByRuntimeEnvKey(runtimeEnvKey)
            } returns apRE
            every {
                regionPropertiesConfig.regionProperties.regions[any()]
            } returns getString()
            every {
                regionPropertiesConfig.regionProperties.az[any()]
            } returns getString()
            every {
                apRELabelService.findApRELabelByTargetWithDefaultLabel(any(), any())
            } answers {
                val targetKey = call.invocation.args[0] as String
                val targetType = call.invocation.args[1] as String
                mutableListOf(
                    manufacturePojo(
                        ApRELabelDO::class.java
                    ).copy(
                        targetKey = targetKey,
                        targetType = ApRELabelTargetTypeEnum.valueOf(targetType)
                    )
                )
            }
            every {
                clusterEnvironmentConfigRepo.queryClusterEnvironmentConfig(any())
            } returns null

            //测试 resourcePool 相关注入 绑定了一个集群
            resourcePoolService = mockk()
            apRELabelService = mockk()
            apRELabelDefinitionService = mockk()

            every {
                resourcePoolService.listByManagedClusterKey(apRE.managedClusterKey)
            } answers {
                mutableListOf(
                    resourcePool
                )
            }

            every {
                apRELabelDefinitionService.fillApRELabelTitles(any())
            } answers {
                val labels = it.invocation.args[0] as List<ApRELabelDO>
                labels.forEach { label ->
                    label.title = getString()
                }
            }

            every {
                apRELabelService.findApRELabelByTarget(any(), RESOURCE_POOL.name)
            } returns mutableListOf(resourceLabel)
        }
        val apREDO = apREService.findApREDetailByKey(runtimeEnvKey)
        softly.assertThat(apREDO!!.apRELabels!!.size).isEqualTo(1)
        softly.assertThat(apREDO.resources.size).isEqualTo(1)
        softly.assertThat(apREDO.resources[0].apRELabels).isEqualTo(mutableListOf(resourceLabel))
        softly.assertThat(apREDO.resources[0].clusterId).isEqualTo(resourcePool.clusterId)
    }
    //补充一个Resource

    @Test
    fun createApREAndRelativeObjectsTest() {
        val selfApRELabelCreateReq = ApRELabelServiceTest.getComplexApRELabelCreateReqDto()
        val selfApRELabel = ApRELabelServiceTest.getComplexApRELabelData().copy(
            id = getLong()
        )

        val resourcePoolLabelCreateReq = selfApRELabelCreateReq.copy(
            targetType = RESOURCE_POOL.name,
            targetKey = null,
        )

        val externalId = getString()
        val attorneyScope = MatchScopeDataDO(
            id = null, targetId = null, targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            exclusions = null, restrictions = null,
            creator = getString(), modifier = getString(),
            externalId = externalId, externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )

        val apREAndRelativeCreateReqDto = ApREAndRelativeCreateReqDto(
            apREBaseCreateReqDto = manufacturePojo(ApREBaseDetailsCreateReqDto::class.java).copy(
                status = ApREStatusEnum.ONLINE,
                apRELabelList = mutableListOf(
                    selfApRELabelCreateReq.copy(
                        targetKey = null
                    )
                )
            ),
            clusterSelector = mutableListOf(
                ClusterWithFeatureSelectorDto(
                    clusterId = getString(),
                    clusterFeatureLabel = mutableListOf(
                        resourcePoolLabelCreateReq
                    )
                )
            ),
        )

        val region = getString()

        val kManagedCluster = manufacturePojo(KManagedClusterData::class.java).copy(
            region = region, status = "created"
        )

        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            val runTimeKey = getString()

            every {
                metadataService.getMetadataOfSite(apREAndRelativeCreateReqDto.apREBaseCreateReqDto.az)
            } returns manufacturePojo(MetadataOfSiteDO::class.java).copy(
                site = apREAndRelativeCreateReqDto.apREBaseCreateReqDto.az,
                region = region
            )

            every {
                defaultClusterService.createSimpleKManageCluster(
                    region = any(), status = "created"
                )
            } returns kManagedCluster

            every {
                resourcePoolService.createResourcePoolIgnoreWhileExistWithLabel(any())
            } answers {
                val resourcePoolCreateReqDto = call.invocation.args[0] as ResourcePoolCreateOrUpdateReqDto
                if (resourcePoolCreateReqDto.managedClusterKey != kManagedCluster.managedClusterKey || resourcePoolCreateReqDto.apRELabelList != mutableListOf(
                        resourcePoolLabelCreateReq
                    )
                ) {
                    throw IllegalArgumentException("resourcePool参数没有匹配")
                } else {
                    manufacturePojo(ResourcePoolDO::class.java)
                }
            }

            //match 特定的ApRE创建参数
            every {
                createApREBaseDetails(any())
            } answers {
                val apRECreateReqDto = call.invocation.args[0] as ApRECreateReqDto
                if (apRECreateReqDto.managedClusterKey == kManagedCluster.managedClusterKey) {
                    ApREDO(
                        id = getLong(),
                        runtimeEnvKey = runTimeKey,
                        creator = apRECreateReqDto.creator,
                        name = apRECreateReqDto.name,
                        region = apRECreateReqDto.region,
                        az = apRECreateReqDto.az,
                        unit = apRECreateReqDto.unit,
                        stage = apRECreateReqDto.stage.name,
                        apRELabels = mutableListOf(
                            selfApRELabel.copy(
                                targetKey = runTimeKey,
                            )
                        ),
                        managedClusterKey = apRECreateReqDto.managedClusterKey,
                        gmtModified = null, gmtCreate = null,
                        isDeleted = "N",
                    )
                } else {
                    throw IllegalArgumentException("managedClusterKey 没有匹配")
                }
            }

            every {
                attorneyService.createAttorney(any())
            } answers {
                val attorney = call.invocation.args[0] as Attorney
                if (attorney.apREBindingData.runtimeEnvKey != runTimeKey) {
                    throw IllegalArgumentException("runtimeEnvKey 没有匹配")
                }
                val matchScopeDataDO = attorney.matchScopeData
                if (matchScopeDataDO.externalId != externalId
                    || matchScopeDataDO.targetType != MatchScopeTargetTypeEnum.ApREBindingData.name
                    || matchScopeDataDO.externalType != MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
                ) {
                    throw IllegalArgumentException("matchScopeData 没有匹配: $matchScopeDataDO")
                }
            }
        }
        apREService.createApREAndRelativeObjects(apREAndRelativeCreateReqDto)
    }

    /**
     * 测试创建归并后的 ApREsWithProductLineAttorney
     */
    @Test
    fun `requireApREDeedResultByMixApREDeed -- fill identityInfo nodeGroup`() {
        val now = Date(Instant.now().toEpochMilli())
        val mixApREDeedDO = MixApREDeedDO(
            identityInfo = ShortIdentityInfo(
                envLevel = "DAILY",
                appName = "appName",
                nodeGroup = "nodeGroup"
            )
        )
        val apREDeedDO = ApREDeedDO(
            identityInfo = IdentityInfo(
                envLevel = "DAILY",
                appName = "appName"
            )
        )
        val apREService = spyk(ApREService(objectMapper)) {
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(mixApREDeedDO.identityInfo.nodeGroup)
            } returns ApREDeedResourceGroupBindingData(
                apREDeedKey = "apREDeedKey",
                appName = mixApREDeedDO.identityInfo.appName,
                resourceGroup = mixApREDeedDO.identityInfo.nodeGroup,
                gmtModified = now,
                gmtCreate = now,
                id = 1L
            )
            every {
                apREDeedService.findApREDeedByKey("apREDeedKey")
            } returns apREDeedDO
            every {
                apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(
                    //验证nodeGroup是否添加
                    apREDeedDO.copy(identityInfo = apREDeedDO.identityInfo!!.copy(nodeGroup = "nodeGroup"))
                )
            } returns apREDeedDO
            every {
                requireApREDeedResultByApREDeed(any())
            } returns ApREDeedResult(deedDO = apREDeedDO, matchDeclarations = emptyList())
        }
        apREService.requireApREDeedResultByMixApREDeed(mixApREDeedDO)
    }


    @Test
    fun updateApREAndRelativeObjectsTest_deleteOutOfConsoleControlResource() {
        val apREAndRelativeUpdateReqDtoMiniature = manufacturePojo(ApREAndRelativeUpdateReqDto::class.java)

        val runtimeEnvKey = apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.runtimeEnvKey

        val resourcePoolLabelCreateReq = ApRELabelServiceTest.getComplexApRELabelCreateReqDto().copy(
            targetType = RESOURCE_POOL.name,
        )

        val selfApRELabelCreateReq = ApRELabelServiceTest.getComplexApRELabelCreateReqDto()

        val clusterSelector = mutableListOf(
            ClusterWithFeatureSelectorDto(
                clusterId = getString(),
                clusterFeatureLabel = mutableListOf(resourcePoolLabelCreateReq)
            )
        )

        var apREDO = getComplexApREDTO()

        val kManagedClusterKey = apREDO.managedClusterKey

        val externalId = getString()

        val attorneyScope = MatchScopeDataDO(
            id = null, targetId = null, targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            exclusions = null, restrictions = null,
            creator = getString(), modifier = getString(),
            externalId = externalId, externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )
        val apREBindingDataId = getLong()

        val apREAndRelativeUpdateReqDto = apREAndRelativeUpdateReqDtoMiniature.copy(
            clusterSelector = clusterSelector,
            apREBaseUpdateReqDto= apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.copy(
                apRELabelList = listOf(selfApRELabelCreateReq)
            )
        )

        apREDO = apREDO.copy(
            runtimeEnvKey = runtimeEnvKey,
            managedClusterKey = kManagedClusterKey
        )

        // 存量存在2个resourcePool
        val resourceDOs = mutableListOf(
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = kManagedClusterKey
            ),
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = kManagedClusterKey
            )
        )

        val resourcePoolService = spyk(ResourcePoolService()) {

            every {
                listByManagedClusterKey(kManagedClusterKey)
            } answers {
                resourceDOs
            }

            every {
                deleteWithLabelsByResourcePoolKeyWhileCheck(
                    any(), any()
                )
            } answers {
                callOriginal()
            }

            every {
                getResourcePoolByResourcePoolKey(any())
            }answers {
                resourceDOs.find { it.resourcePoolKey == (call.invocation.args[0] as String) }
            }

            every {
                updateResourcePoolIgnoreWhileExistWithLabel(any())
            } answers {
                val resourcePoolCreateDto = call.invocation.args[0] as ResourcePoolCreateOrUpdateReqDto
                if (
                    resourcePoolCreateDto.clusterId != clusterSelector[0].clusterId
                    || resourcePoolCreateDto.managedClusterKey != apREDO.managedClusterKey
                    || resourcePoolCreateDto.creator != apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.modifier
                ) {
                    throw IllegalArgumentException("resourcePoolCreateDto 参数错误, clusterId:${resourcePoolCreateDto.clusterId}|${clusterSelector[0].clusterId}, " +
                            "managedClusterKey:${resourcePoolCreateDto.managedClusterKey}" +
                            "|${apREDO.managedClusterKey}, creator:${resourcePoolCreateDto.creator}" +
                            "|${apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.modifier}")
                } else {
                    manufacturePojo(ResourcePoolDO::class.java)
                }
            }

            every {
                apRELabelService.isHasApiRelativeObjectResource(any())
            } answers {
                callOriginal()
            }

            every {
                apRELabelService.isApiCreatedApRELabel(any())
            } answers {
                callOriginal()
            }

            // 存在部分 resourcePool 已经绑定上了部分ApRELabel 这些resourcePool 删除的时候 不删除接管范围内
            every {
                apRELabelService.findApRELabelByTarget(any(), RESOURCE_POOL.name)
            } returns mutableListOf(
                manufacturePojo(ApRELabelDO::class.java).copy(
                    name = "${ApRELabelExt.SERVERLESS_PREFIX}testApRE",
                    type = SERVERLESS
                )
            )
        }

        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {

            this.resourcePoolService = resourcePoolService

            every {
                findApREByKey(runtimeEnvKey)
            } returns apREDO

            every {
                defaultClusterService.kManagedClusterRepo.findKManagedClusterByManagedClusterKey(
                    kManagedClusterKey
                )
            } returns manufacturePojo(KManagedClusterData::class.java)

            every {
                apREBindingService.clusterBindingRepo.findById(apREBindingDataId)
            } answers {
                manufacturePojo(ClusterEnvironmentBindingData::class.java).copy(
                    clusterEnvKey = runtimeEnvKey,
                )
            }

            every {
                apRELabelService.deleteApRELabelByTargetAndType(
                    targetType = APRE.name,
                    targetKey = runtimeEnvKey,
                    type = CONSOLE
                )
            } just runs

            every {
                apRELabelService.updateApRELabelWithFeatureSpec(any())
            } just runs

            every {
                matchScopeService.listByTarget(
                    targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                    targetId = apREBindingDataId
                )
            } returns mutableListOf(attorneyScope)

            every {
                updateApREBaseDetails(any())
            } answers {
                val apREBaseUpdateReqDto = call.invocation.args[0] as ApREBaseDetailsUpdateReqDto
                val (runtimeEnvKeyArg, nameArg, statusArg, modifierArg, _) = apREBaseUpdateReqDto
                if (runtimeEnvKey != runtimeEnvKeyArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.name != nameArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.status != statusArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.modifier != modifierArg) {
                    throw IllegalArgumentException("updateApRE 参数匹配错误")
                }
                manufacturePojo(ApREDO::class.java)
            }
        }
        exceptionTest({ e -> softly.assertThat(e.message).contains("更新中存在删除资源resourcePool") }) {
            apREService.updateApREAndRelativeObjects(apREAndRelativeUpdateReqDto)
        }
    }

    @Test
    fun updateApREAndRelativeObjectsTest_normalTest(){
        val apREAndRelativeUpdateReqDtoMiniature = manufacturePojo(ApREAndRelativeUpdateReqDto::class.java)
        val runtimeEnvKey = apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.runtimeEnvKey
        val resourcePoolLabelCreateReq = ApRELabelServiceTest.getComplexApRELabelCreateReqDto().copy(
            targetType = RESOURCE_POOL.name,
        )

        val selfApRELabelCreateReq = ApRELabelServiceTest.getComplexApRELabelCreateReqDto()

        val newClusterId = getString()

        val clusterSelector = mutableListOf(
            ClusterWithFeatureSelectorDto(
                clusterId = newClusterId,
                clusterFeatureLabel = mutableListOf(resourcePoolLabelCreateReq)
            )
        )

        var apREDO = getComplexApREDTO()

        val kManagedClusterKey = apREDO.managedClusterKey

        val externalId = getString()

        val attorneyScope = MatchScopeDataDO(
            id = null, targetId = null, targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            exclusions = null, restrictions = null,
            creator = getString(), modifier = getString(),
            externalId = externalId, externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )
        val apREBindingDataId = getLong()

        val apREAndRelativeUpdateReqDto = apREAndRelativeUpdateReqDtoMiniature.copy(
            clusterSelector = clusterSelector,
            apREBaseUpdateReqDto= apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.copy(
                apRELabelList = listOf(selfApRELabelCreateReq)
            )
        )

        apREDO = apREDO.copy(
            runtimeEnvKey = runtimeEnvKey,
            managedClusterKey = kManagedClusterKey
        )

        // 存量存在2个resourcePool 全部删除
        val resourceDOs = mutableListOf(
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = kManagedClusterKey
            ),
            manufacturePojo(ResourcePoolDataDO::class.java).copy(
                managedClusterKey = kManagedClusterKey
            )
        )

        val resourcePoolService = spyk(ResourcePoolService()) {
            every {
                listByManagedClusterKey(kManagedClusterKey)
            } answers {
                resourceDOs
            }

            every {
                deleteWithLabelsByResourcePoolKeyWhileCheck(
                    any(), any()
                )
            } answers {
                callOriginal()
            }

            every {
                apRELabelService.deleteApRELabelByTargetAndType(
                    targetType = RESOURCE_POOL.name,
                    targetKey = any<String>(),
                    type = CONSOLE
                )
            } just runs

            every {
                getResourcePoolByResourcePoolKey(any())
            }answers {
                resourceDOs.find { it.resourcePoolKey == (call.invocation.args[0] as String) }
            }

            every {
                deleteByResourcePoolKey(any(), any())
            }just runs

            every {
                updateResourcePoolIgnoreWhileExistWithLabel(any())
            } answers {
                val resourcePoolCreateDto = call.invocation.args[0] as ResourcePoolCreateOrUpdateReqDto
                if (
                    resourcePoolCreateDto.clusterId != clusterSelector[0].clusterId
                    || resourcePoolCreateDto.managedClusterKey != apREDO.managedClusterKey
                    || resourcePoolCreateDto.creator != apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.modifier
                ) {
                    throw IllegalArgumentException("resourcePoolCreateDto 参数错误, clusterId:${resourcePoolCreateDto.clusterId}|${clusterSelector[0].clusterId}, " +
                            "managedClusterKey:${resourcePoolCreateDto.managedClusterKey}" +
                            "|${apREDO.managedClusterKey}, creator:${resourcePoolCreateDto.creator}" +
                            "|${apREAndRelativeUpdateReqDtoMiniature.apREBaseUpdateReqDto.modifier}")
                } else {
                    manufacturePojo(ResourcePoolDO::class.java)
                }
            }

            every {
                apRELabelService.isHasApiRelativeObjectResource(any())
            } answers {
                callOriginal()
            }

            every {
                apRELabelService.isApiCreatedApRELabel(any())
            } answers {
                callOriginal()
            }

            // 存在部分 resourcePool 绑定上ApRELabel 均为console标签
            every {
                apRELabelService.findApRELabelByTarget(any(), RESOURCE_POOL.name)
            } returns mutableListOf(
                manufacturePojo(ApRELabelDO::class.java).copy(
                    name = APRE_LABEL_FEATURE_NAME,
                    type = CONSOLE
                )
            )
        }

        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {

            this.resourcePoolService = resourcePoolService

            every {
                findApREByKey(runtimeEnvKey)
            } returns apREDO

            every {
                defaultClusterService.kManagedClusterRepo.findKManagedClusterByManagedClusterKey(
                    kManagedClusterKey
                )
            } returns manufacturePojo(KManagedClusterData::class.java)

            every {
                apREBindingService.clusterBindingRepo.findById(apREBindingDataId)
            } answers {
                manufacturePojo(ClusterEnvironmentBindingData::class.java).copy(
                    clusterEnvKey = runtimeEnvKey,
                )
            }

            every {
                apRELabelService.deleteApRELabelByTargetAndType(
                    targetType = APRE.name,
                    targetKey = runtimeEnvKey,
                    type = CONSOLE
                )
            } just runs

            every {
                apRELabelService.updateApRELabelWithFeatureSpec(any())
            } just runs

            every {
                matchScopeService.listByTarget(
                    targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                    targetId = apREBindingDataId
                )
            } returns mutableListOf(attorneyScope)

            every {
                updateApREBaseDetails(any())
            } answers {
                val apREBaseUpdateReqDto = call.invocation.args[0] as ApREBaseDetailsUpdateReqDto
                val (runtimeEnvKeyArg, nameArg, statusArg, modifierArg, _) = apREBaseUpdateReqDto
                if (runtimeEnvKey != runtimeEnvKeyArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.name != nameArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.status != statusArg
                    || apREAndRelativeUpdateReqDto.apREBaseUpdateReqDto.modifier != modifierArg) {
                    throw IllegalArgumentException("updateApRE 参数匹配错误")
                }
                manufacturePojo(ApREDO::class.java)
            }
        }

        apREService.updateApREAndRelativeObjects(apREAndRelativeUpdateReqDto)

        verify(exactly = 2) {
            resourcePoolService.deleteWithLabelsByResourcePoolKeyWhileCheck(any(), any())
        }
    }

    private fun getSimpleApRECreateReqDto(): ApRECreateReqDto {
        return ApRECreateReqDto(
            null,
            "kostaline",
            "GfOvUwFDaydexVqs",
            "cn-zhangjiakou",
            "na610",
            MetadataStageEnum.PUBLISH,
            "CENTER_UNIT.center",
            ApREStatusEnum.ONLINE,
            emptyMap(),
            emptyList()
        )
    }

    private fun getSimpleApREDTOWithExistRuntimeEnvKey(): ApREDO {
        return ApREDO(
            null,
            "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61",
            null,
            "kostaline",
            "GfOvUwFDaydexVqs",
            "cn-zhangjiakou",
            "na610",
            "PUBLISH",
            "CENTER_UNIT.center",
            null,
            null,
            null,
            null,
            null,
            null,
            emptyList()
        )
    }

    private fun getComplexApRECreateReqDto(): ApRECreateReqDto {
        return ApRECreateReqDto(
            null,
            "kostaline",
            "GfOvUwFDaydexVqs",
            "cn-zhangjiakou",
            "na610",
            MetadataStageEnum.PUBLISH,
            "CENTER_UNIT.center",
            ApREStatusEnum.ONLINE,
            emptyMap(),
            listOf(
                ApRELabelCreateReqDto(
                    name = APRE_LABEL_FEATURE_NAME,
                    value = "serverless/tpp",
                    apREFeatureSpecList = listOf(
                        ApREFeatureSpecCreateReqDto(
                            title = "",
                            specCode = "serverless_runtime_1.0",
                            specType = "common",
                            scope = ApREFeatureSpecScopeEnum.publish,
                            status = ApREFeatureSpecStatusEnum.online,
                            sourceType = "appstack_runtime",
                            sourceId = "1111",
                            versionType = "appstack_version",
                            versionId = "1111",
                            annotations = JsonUtils.readValue(
                                "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                mutableMapOf<String, String>().javaClass
                            )
                        ),
                        ApREFeatureSpecCreateReqDto(
                            title = "",
                            specCode = "serverless_runtime_1.0",
                            specType = "common",
                            scope = ApREFeatureSpecScopeEnum.publish,
                            status = ApREFeatureSpecStatusEnum.online,
                            sourceType = "appstack_runtime",
                            sourceId = "2222",
                            versionType = "appstack_version",
                            versionId = "2222",
                            annotations = JsonUtils.readValue(
                                "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                                mutableMapOf<String, String>().javaClass
                            )
                        )
                    ),
                    targetType = APRE.name,
                    type = SERVERLESS,
                )
            )
        )
    }
}