package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleFilterException
import com.alibaba.koastline.multiclusters.schedule.model.*
import com.alibaba.koastline.multiclusters.schedule.service.fiter.RunningStateScheduleFilterProcessor
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals

class RunningStateScheduleFilterProcessorTest {

    @Test
    fun testDoFilter_while_running_cluster_is_empty() {
        val content = getScheduleRequestContent()
        val scheduleFilterFacade = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration(content.resourceScope.envStackId!!, content.resourceScope.resourceGroup!!,getDeclaration())
            } returns emptyList()
        }

        val matchDeclaration = scheduleFilterFacade.doFilter(getMatchDeclaration(), content)
        assertEquals(2, matchDeclaration.apres.size)
        assertEquals(2, matchDeclaration.apres[0].resources.size)
        assertEquals(1, matchDeclaration.apres[1].resources.size)
    }

    @Test
    fun testDoFilter_while_running_cluster_is_single_value() {
        val content = getScheduleRequestContent()
        val scheduleFilterFacade = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration(content.resourceScope.envStackId!!, content.resourceScope.resourceGroup!!,getDeclaration())
            } returns listOf("clusterId_b")
        }

        val matchDeclaration = scheduleFilterFacade.doFilter(getMatchDeclaration(), content)
        assertEquals(1, matchDeclaration.apres.size)
        assertEquals(1, matchDeclaration.apres[0].resources.size)
        assertEquals("clusterId_b", matchDeclaration.apres[0].resources[0].clusterProfileNew!!.clusterId)
    }

    @Test
    fun testDoFilter_while_running_cluster_is_multi_value() {
        val content = getScheduleRequestContent()
        val scheduleFilterFacade = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration(content.resourceScope.envStackId!!, content.resourceScope.resourceGroup!!,getDeclaration())
            } returns listOf("clusterId_b","clusterId_c","clusterId_d")
        }

        val matchDeclaration = scheduleFilterFacade.doFilter(getMatchDeclaration(), content)
        assertEquals(2, matchDeclaration.apres.size)
        assertEquals(1, matchDeclaration.apres[0].resources.size)
        assertEquals(1, matchDeclaration.apres[1].resources.size)
        assertEquals("clusterId_b", matchDeclaration.apres[0].resources[0].clusterProfileNew!!.clusterId)
        assertEquals("clusterId_c", matchDeclaration.apres[1].resources[0].clusterProfileNew!!.clusterId)
    }

    @Test(expected = ScheduleFilterException::class)
    fun testDoFilter_while_running_cluster_is_not_in_authority_scope() {
        val content = getScheduleRequestContent()
        val scheduleFilterFacade = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration(content.resourceScope.envStackId!!, content.resourceScope.resourceGroup!!,getDeclaration())
            } returns listOf("clusterId_e")
        }

        scheduleFilterFacade.doFilter(getMatchDeclaration(), content)
    }

    private fun getScheduleRequestContent(): ScheduleRequestContent {
        return ScheduleRequestContent(
            ResourceScope(
                appName = "normandy-test-app4",
                envStackId = "c3f506bb7b94f411397f4bb3cf1c23c81",
                resourceGroup = "normandy-test-app4_prehost"
            ),
            DeclarationData(
                declaration= OrientedDeclaration(
                    region = "cn-zhangjiakou",
                    site = "na610",
                    stage = "PUBLISH",
                    unit = "CENTER_UNIT.center"
                )
            ),
            ScheduleType(
                SchedulePatternEnum.NON_DECLARATIVE,
                SceneEnum.SCALE_OUT
            ),
            ScheduleRequestParam(
                replicas = 10
            )
        )
    }

    private fun getMatchDeclaration(): MatchDeclaration {
        return MatchDeclaration(
            apres = mutableListOf(
                ApREDO(1L, "",null,"creator","MMMMMMM",
                    "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                    , listOf(
                        ResourceDO(clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),
                            ClusterProfileUseTypeEnum.publish.name)),
                        ResourceDO(clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name))
                    )
                ),
                ApREDO(2L, "",null,"creator","MMMMMMM",
                    "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                    , listOf(
                        ResourceDO(clusterProfileNew = ClusterProfileNew("clusterId_c","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name))
                    )
                )
            ),
            declaration = getDeclaration()
        )
    }

    private fun getDeclaration(): Declaration {
        return Declaration(
            null,
            "cn-zhangjiakou",
            "na610",
            "PRE_PUBLISH",
            "center"
        )
    }
}