package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.env.orchestration.protocol.common.V2ResourceStatus
import com.alibaba.env.orchestration.protocol.common.V2StatusPhase
import com.fasterxml.jackson.databind.node.ObjectNode
import org.junit.Assert
import org.junit.Test

/**
 * <AUTHOR>
 * date 2024/11/8 00:00
 */
class JsonUtilsTest {

    @Test
    fun readTreeTest() {
        val jsonNode = JsonUtils.readTree(
            """
            {
              "a":1,
              "b":2
            }
        """.trimIndent()
        )
        (jsonNode as ObjectNode).set("c", JsonUtils.value2Tree(V2ResourceStatus(
            phase = V2StatusPhase.APPLY_RUNNING
        )))
        Assert.assertEquals("{\"a\":1,\"b\":2,\"c\":{\"phase\":\"APPLY_RUNNING\"}}", JsonUtils.writeValueAsString(jsonNode))
    }

}