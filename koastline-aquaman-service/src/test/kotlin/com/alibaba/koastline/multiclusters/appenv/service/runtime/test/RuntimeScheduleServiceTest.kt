package com.alibaba.koastline.multiclusters.appenv.service.runtime.test

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedResourceGroupBindingService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.common.ApREFeatureSpecService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDO
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.resourceobj.UserLabelService
import com.alibaba.koastline.multiclusters.resourceobj.base.UserLabelBaseService
import com.alibaba.koastline.multiclusters.resourceobj.model.req.UserLabelCreateReqDto
import com.alibaba.koastline.multiclusters.resourceobj.params.UserLabelExternalType
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadMetaService
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RuntimeScheduleService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RuntimeScheduleService.Companion.RUNTIME_STATUS_OFFLINE
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.runs
import io.mockk.slot
import org.junit.Test
import testutils.BaseTest

class RuntimeScheduleServiceTest : BaseTest() {


    @InjectMockKs
    lateinit var runtimeScheduleService: RuntimeScheduleService

    @MockK
    lateinit var apREService: ApREService

    @MockK
    lateinit var apREFeatureSpecService: ApREFeatureSpecService

    @MockK
    lateinit var defaultClusterService: DefaultClusterService

    @MockK
    lateinit var resourcePoolService: ResourcePoolService

    @MockK
    lateinit var scheduleStandardService: ScheduleStandardService

    @MockK
    lateinit var skylineApi: SkylineApi

    @MockK
    lateinit var userLabelService: UserLabelService

    @MockK
    lateinit var runtimeWorkloadMetaService: RuntimeWorkloadMetaService

    @MockK
    lateinit var apREDeedResourceGroupBindingService: ApREDeedResourceGroupBindingService

    @MockK
    lateinit var apREDeclarationPatchService: ApREDeclarationPatchService

    @MockK
    lateinit var apREDeedService: ApREDeedService

    @MockK
    lateinit var appCenterApi: AppCenterApi

    @MockK
    lateinit var userLabelBaseService: UserLabelBaseService



    @Test
    fun seekAvailableRuntimeCoordinatesTest() {
        val appName = "runtime-app-demo"
        val resourceGroup = "runtime-app-demo.153283"
        val scheduleEnvType = ScheduleEnvType.RUNTIME
        val apREDeedKey = "IjJSL0ZwT0h6a0V6bWlhSHljczFCQ0E9PSI="
        val clusterId = "cb87fee031fe34ef38e134529291cf115"

        every {
            apREDeedResourceGroupBindingService.getByResourceGroup(
                resourceGroup
            )
        } returns manufacturePojo(ApREDeedResourceGroupBindingData::class.java).copy(
            appName = appName, resourceGroup = resourceGroup, apREDeedKey = apREDeedKey
        )

        every {
            runtimeWorkloadMetaService.listRunningWorkloadList(
                appName = appName, resourceGroup = resourceGroup
            )
        } returns emptyList()

        mockQueryClustersByApREDeedKey(apREDeedKey)

        every {
            scheduleStandardService.generateNameSpace(any())
        } returns getString()

        val rs = runtimeScheduleService.seekAvailableRuntimeCoordinates(
            appName = appName, resourceGroup = resourceGroup, scheduleEnvType = scheduleEnvType
        )

        softly.assertThat(rs.workloadExpectedStates.size).isEqualTo(1)
        val meta = rs.workloadExpectedStates.first()
        softly.assertThat(meta.workloadMetadataConstraint.appName).isEqualTo(appName)
        softly.assertThat(meta.workloadMetadataConstraint.resourceGroup).isEqualTo(resourceGroup)
        softly.assertThat(meta.workloadMetadataConstraint.clusterId).isEqualTo(clusterId)
    }

    @Test
    fun seekAvailableRuntimeCoordinates_with_blank_resourceGroup() {
        val appName = "myApp"
        val resourceGroup = ""
        val scheduleEnvType = ScheduleEnvType.RUNTIME

        exceptionTest({ e ->
            softly.assertThat(e.message).contains("cannot be blank")
        }) {
            runtimeScheduleService.seekAvailableRuntimeCoordinates(
                appName = appName, resourceGroup = resourceGroup, scheduleEnvType = scheduleEnvType
            )
        }
    }

    @Test
    fun seekAvailableRuntimeCoordinates_with_error_scheduleEnvType() {
        val appName = "myApp"
        val resourceGroup = "myApp_prehost"
        val scheduleEnvType = ScheduleEnvType.ASI

        exceptionTest({ e ->
            softly.assertThat(e.message).contains("unsupported schedule env type")
        }) {
            runtimeScheduleService.seekAvailableRuntimeCoordinates(
                appName = appName, resourceGroup = resourceGroup, scheduleEnvType = scheduleEnvType
            )
        }
    }

    @Test
    fun requireRunningWorkloadTest() {
        val appName = "runtime-app-demo"
        val resourceGroup = "runtime-app-demo.153283"
        val site = "na131"
        val unit = "CENTER_UNIT.center"
        val stage = "daily"
        val clusterId = "cb87fee031fe34ef38e134529291cf115"
        val runtimeId = "fcfec460ee7d24bb289d3321ed87a7f3"
        every {
            runtimeWorkloadMetaService.listRunningWorkloadList(
                appName, null, resourceGroup
            )
        } returns listOf(
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                appName = appName, resourceGroup = resourceGroup,
                clusterId = clusterId, runtimeWorkloadId = runtimeId,
                site = site, unit = unit, stage = stage
            )
        )

        every {
            resourcePoolService.listClusterProfileNew(
                any()
            )
        } answers { call ->
            (call.invocation.args[0] as List<String>).map { clusterId ->
                manufacturePojo(ClusterProfileNew::class.java).copy(
                    clusterId = clusterId,
                    status = ClusterStatus.ONLINE.status
                )
            }
        }

        val rs = runtimeScheduleService.requireRunningWorkload(
            appName = appName, resourceGroup = resourceGroup,
            scheduleEnvType = ScheduleEnvType.RUNTIME
        )

        softly.assertThat(rs.workloadExpectedStates.size).isEqualTo(1)
    }

    @Test
    fun setGroupHidedTest() {
        val appName = "runtime-app-demo"
        val resourceGroup = "runtime-app-demo.153283"
        val site = "na131"
        val unit = "CENTER_UNIT.center"
        val stage = "daily"
        val clusterId = "cb87fee031fe34ef38e134529291cf115"
        val runtimeId = "fcfec460ee7d24bb289d3321ed87a7f3"
        every {
            userLabelService.createAndOverrideWhileExist(any())
        } just runs
        every {
            skylineApi.getAppGroup(resourceGroup)
        } returns manufacturePojo(AppGroup::class.java).copy(
            appName = appName
        )

        every {
            runtimeWorkloadMetaService.listRunningWorkloadList(
                appName, null, resourceGroup
            )
        } returns listOf(
            manufacturePojo(RuntimeWorkloadMeta::class.java).copy(
                appName = appName, resourceGroup = resourceGroup,
                clusterId = clusterId, runtimeWorkloadId = runtimeId,
                site = site, unit = unit, stage = stage
            )
        )

        every {
            runtimeWorkloadMetaService.requireMatchApRELabelWithResource(
                appName = appName, clusterId = clusterId, site = site, unit = unit, stage = stage
            )
        } returns mockRequireApREAndResourcePool(
            clusterId = clusterId,
            appName = appName,
            sourceId = runtimeId
        )

        every {
            runtimeWorkloadMetaService.matchSpec(
                any(), any()
            )
        } answers {
            callOriginal()
        }

        every {
            runtimeWorkloadMetaService.matchLabel(
                any(), any()
            )
        } answers {
            callOriginal()
        }

        every {
            runtimeWorkloadMetaService.matchSpec(any(), any())
        } answers {
            callOriginal()
        }

        every {
            apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(any())
        } returns manufacturePojo(ApREFeatureSpecDO::class.java)

        runtimeScheduleService.setGroupHided(
            resourceGroup = resourceGroup, status = RUNTIME_STATUS_OFFLINE, modifier = getString()
        )

    }

    @Test
    fun setGroupBufferTest() {
        val userLabelSlot = slot<UserLabelCreateReqDto>()
        every {
            userLabelService.createAndOverrideWhileExist(
                capture(userLabelSlot)
            )
        }just runs

        runtimeScheduleService.setGroupBuffer(
            resourceGroup = getString(), bufferConfig = "{}", modifier = getString()
        )
        val createDto = userLabelSlot.captured
        softly.assertThat(createDto.labelName).isEqualTo(RuntimeScheduleService.RUNTIME_WORKLOAD_BUFFER)
        softly.assertThat(createDto.externalType).isEqualTo(UserLabelExternalType.RESOURCE_GROUP)
    }

    private fun mockQueryClustersByApREDeedKey(apREDeedKey: String): ApREDeedResult {

        every {
            apREDeedService.findApREDeedByKey(apREDeedKey)
        }returns manufacturePojo(ApREDeedDO::class.java)

        every {
            apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(
                any()
            )
        } returns manufacturePojo(ApREDeedDO::class.java)

        every {
            apREService.queryClustersByApREDeedContent(
                any()
            )
        } returns getApREDeedTestData()
        return getApREDeedTestData()
    }

    private fun getApREDeedTestData(): ApREDeedResult {
        return JsonUtils.readValue(
            mockApREDeedTestData(), ApREDeedResult::class.java
        )
    }

    private fun mockRequireApREAndResourcePool(
        clusterId: String, appName: String? = null, sourceId: String? = null
    ): Pair<ApRELabelDO, ResourcePoolDO> {
        val resourcePool = manufacturePojo(ResourcePoolDO::class.java).copy(
            apRELabels = emptyList(), clusterId = clusterId
        )

        val mockApRELabel = manufacturePojo(ApRELabelDO::class.java).copy(
            apRELabelKey = resourcePool.resourcePoolKey,
            value = ApRELabelExt.SERVERLESS_PREFIX + appName,
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            targetType = RESOURCE_POOL,
            type = SERVERLESS
        )
        val mockApREFeatureSpec = manufacturePojo(ApREFeatureSpecDO::class.java).copy(
            apRELabelKey = mockApRELabel.apRELabelKey,
            isDeleted = "N",
            annotations = "{\"cpu\":\"2\",\"memory\":\"4Gi\"}",
            labels = "{}",
            sourceId = sourceId, specCode = sourceId,
            scope = ApREFeatureSpecScopeEnum.publish.name
        )

        return Pair(mockApRELabel, resourcePool)
    }

    private fun mockApREDeedTestData() = """{
  "deedDO":{
    "key":"IjJSL0ZwT0h6a0V6bWlhSHljczFCQ0E9PSI=",
    "identityInfo":{
      "envLevel":"daily",
      "appName":"runtime-app-demo"
    },
    "declarations":[
      {
        "id":"af844a31-6c39-4219-bb2d-da8c04f252fb",
        "region":"cn-wulanchabu",
        "az":"na131",
        "stage":"DAILY",
        "unit":"CENTER_UNIT.center",
        "weight":1
      }
    ]
  },
  "matchDeclarations":[
    {
      "declarationId":"af844a31-6c39-4219-bb2d-da8c04f252fb",
      "apres":[
        {
          "id":2983,
          "runtimeEnvKey":"v9Yc3wWnn89l0FKS7gxMKLztpIo9wfvG",
          "name":"daily-center-na131",
          "creator":"137285",
          "managedClusterKey":"q1AMXZo6dj8nWEQo",
          "region":"cn-wulanchabu",
          "regionName":"cn-wulanchabu",
          "az":"na131",
          "azName":"na131",
          "stage":"DAILY",
          "unit":"CENTER_UNIT.center",
          "status":"ONLINE",
          "metaData":{

          },
          "gmtCreate":"2023-07-25T07:27:30.000+0000",
          "gmtModified":"2023-08-02T02:44:36.000+0000",
          "apRELabels":[
            {
              "id":10122,
              "runtimeEnvKey":"z6LYduRRn7LXQM3L6AsFifwbU8k1VfvP",
              "name":"alibaba/apre/feature",
              "value":"compute",
              "apRELabelKey":"iEvqrbPmx2dIVFfK5DG0Utv6MBYdUxSk",
              "apREFeatureSpecs":[
                {
                  "id":11,
                  "apRELabelKey":"iKtUUZU2YKMAfgPeSUHxBM7wi41kRHSF",
                  "title":"4C8G",
                  "specType":"common",
                  "specCode":"4-8",
                  "scope":"publish",
                  "status":"online",
                  "annotations":"{\"cpu\":\"4\",\"memory\":\"8294967296\"}",
                  "source":"DEFAULT"
                }
              ],
              "source":"CUSTOMIZED",
              "targetType":"RESOURCE_POOL",
              "type":"CONSOLE",
              "title":"CPU资源"
            },
            {
              "id":10133,
              "runtimeEnvKey":"z6LYduRRn7LXQM3L6AsFifwbU8k1VfvP",
              "name":"alibaba/apre/feature",
              "value":"serverless/runtime-app-demo",
              "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
              "apREFeatureSpecs":[
                {
                  "id":49,
                  "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
                  "title":"",
                  "specType":"serverless/tao-message-runtime${'$'}${'$'}common${'$'}${'$'}SPEC:4-8",
                  "specCode":"tao-message-runtime.153283",
                  "scope":"daily",
                  "status":"online",
                  "sourceType":"",
                  "sourceId":"",
                  "versionType":"",
                  "versionId":"",
                  "annotations":"{}",
                  "labels":"{\"envStackId\":\"123\"}",
                  "source":"CUSTOMIZED"
                },
                {
                  "id":50,
                  "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
                  "title":"",
                  "specType":"serverless/runtime-app-demo${'$'}${'$'}common${'$'}${'$'}SPEC:4-8",
                  "specCode":"runtime-app-demo.153283",
                  "scope":"publish",
                  "status":"online",
                  "sourceType":"",
                  "sourceId":"",
                  "versionType":"",
                  "versionId":"",
                  "annotations":"{}",
                  "labels":"{\"envStackId\":\"123\"}",
                  "source":"CUSTOMIZED"
                }
              ],
              "source":"CUSTOMIZED",
              "targetType":"RESOURCE_POOL",
              "type":"SERVERLESS",
              "title":"alibaba/apre/feature=serverless/runtime-app-demo"
            }
          ],
          "resources":[
            {
              "clusterProfileNew":{
                "clusterId":"cb87fee031fe34ef38e134529291cf115",
                "clusterName":"asi_wlcb_aone_daily01",
                "clusterProvider":"alibaba",
                "clusterType":"alibaba-asi",
                "siteList":[
                  ""
                ],
                "componentDataList":[

                ],
                "useType":"publish",
                "status":"online"
              },
              "resourcePoolKey":"z6LYduRRn7LXQM3L6AsFifwbU8k1VfvP",
              "apRELabels":[
                {
                  "id":10122,
                  "runtimeEnvKey":"z6LYduRRn7LXQM3L6AsFifwbU8k1VfvP",
                  "name":"alibaba/apre/feature",
                  "value":"compute",
                  "apRELabelKey":"iEvqrbPmx2dIVFfK5DG0Utv6MBYdUxSk",
                  "apREFeatureSpecs":[
                    {
                      "id":11,
                      "apRELabelKey":"iKtUUZU2YKMAfgPeSUHxBM7wi41kRHSF",
                      "title":"4C8G",
                      "specType":"common",
                      "specCode":"4-8",
                      "scope":"publish",
                      "status":"online",
                      "annotations":"{\"cpu\":\"4\",\"memory\":\"8294967296\"}",
                      "source":"DEFAULT"
                    }
                  ],
                  "source":"CUSTOMIZED",
                  "targetType":"RESOURCE_POOL",
                  "type":"CONSOLE",
                  "title":"CPU资源"
                },
                {
                  "id":10133,
                  "runtimeEnvKey":"z6LYduRRn7LXQM3L6AsFifwbU8k1VfvP",
                  "name":"alibaba/apre/feature",
                  "value":"serverless/runtime-app-demo",
                  "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
                  "apREFeatureSpecs":[
                    {
                      "id":49,
                      "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
                      "title":"",
                      "specType":"serverless/tao-message-runtime${'$'}${'$'}common${'$'}${'$'}SPEC:4-8",
                      "specCode":"tao-message-runtime.153283",
                      "scope":"publish",
                      "status":"online",
                      "sourceType":"",
                      "sourceId":"",
                      "versionType":"",
                      "versionId":"",
                      "annotations":"{}",
                      "labels":"{\"envStackId\":\"123\"}",
                      "source":"CUSTOMIZED"
                    },
                    {
                      "id":50,
                      "apRELabelKey":"8uDtL8Zd9KhKOzOqwdHDJRwl6yRt6hbc",
                      "title":"",
                      "specType":"serverless/runtime-app-demo${'$'}${'$'}common${'$'}${'$'}SPEC:4-8",
                      "specCode":"runtime-app-demo.153283",
                      "scope":"publish",
                      "status":"online",
                      "sourceType":"",
                      "sourceId":"",
                      "versionType":"",
                      "versionId":"",
                      "annotations":"{}",
                      "labels":"{\"envStackId\":\"123\"}",
                      "source":"CUSTOMIZED"
                    }
                  ],
                  "source":"CUSTOMIZED",
                  "targetType":"RESOURCE_POOL",
                  "type":"SERVERLESS",
                  "title":"alibaba/apre/feature=serverless/runtime-app-demo"
                }
              ]
            }
          ],
          "attorneys":[

          ],
          "limitedClusterIdList":[

          ]
        }
      ],
      "weight":1
    }
  ],
  "apREDeclarationPatchDatas":[]
}
    """.trimIndent()
}