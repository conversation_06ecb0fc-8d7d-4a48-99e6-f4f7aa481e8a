package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractNonOrientedScaleInScheduleRefactor

class AbstractNonOrientedScaleInScheduleRefactorMockImpl : AbstractNonOrientedScaleInScheduleRefactor() {
    override fun getApREDeed(content: ScheduleRequestContent): ApREDeedDO {
        return ApREDeedDO()
    }

    override fun isDeclarative(): Bo<PERSON>an {
        return true
    }
}