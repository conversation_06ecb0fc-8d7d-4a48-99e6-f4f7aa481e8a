package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectProtocolNotFoundException
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.featureProtocol.RESOURCE_SPEC.`0`.`0`.`1`.StatefulSetTest
import com.alibaba.koastline.multiclusters.resourceobj.featureProtocol.RESOURCE_SPEC.`0`.`0`.`1`.StatefulSetTest.Companion.expectedStsBetaProtocol
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectConstants
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class FeatureProtocolLoadServiceTest {
    @Test
    fun testGetFeatureProtocol() {
        // white list all feature
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    any()
                )
            } returns true
        }
        // statefulSet resourceSpec
        val resourceSpecStatefulSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.1",
            """spec:
  template:
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              ephemeral-storage: "${'$'}{user.resources.requests.disk}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.requests.gpu}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              ephemeral-storage: "${'$'}{user.resources.limits.disk}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.limits.gpu}"
              </#if>""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        assertEquals(featureProtocolLoadService.cache.size, 0)
        var patch = featureProtocolLoadService.getFeatureProtocol(resourceSpecStatefulSetProtocol)
        assertEquals(
            StatefulSetTest.expectedStsProtocol(), patch
        )
        assertEquals(featureProtocolLoadService.cache.size, 1)

        // re-fetch from cache
        patch = featureProtocolLoadService.getFeatureProtocol(resourceSpecStatefulSetProtocol)
        assertEquals(
            StatefulSetTest.expectedStsProtocol(), patch
        )
        assertEquals(featureProtocolLoadService.cache.size, 1)

        // cloneSet resourceSpec

        val resourceSpecCloneSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "CloneSet",
            "0.0.1",
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "yundisk-pv"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.requests.gpu}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.limits.gpu}"
              </#if>""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        patch = featureProtocolLoadService.getFeatureProtocol(resourceSpecCloneSetProtocol)
        assertEquals(
            """
<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "yundisk-pv"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>""".trimIndent(), patch
        )
        assertEquals(featureProtocolLoadService.cache.size, 2)

        // rollingSet resourceSpec

        val resourceSpecRollingSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "RollingSet",
            "0.0.1",
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "yundisk-pv"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.requests.gpu}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${'$'}{user.resources.limits.gpu}"
              </#if>>""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        patch = featureProtocolLoadService.getFeatureProtocol(resourceSpecRollingSetProtocol)
        assertEquals(
            """
<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-mode: "yundisk-pv"
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>""".trimIndent(), patch
        )
        assertEquals(featureProtocolLoadService.cache.size, 3)


    }

    @Test(expected = ResourceObjectProtocolNotFoundException::class)
    fun testGetFeatureProtocol_Fail() {
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    "RESOURCE_SPEC"
                )
            } returns true
        }
        val protocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.2",
            """spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        val patch = featureProtocolLoadService.getFeatureProtocol(protocol)
    }

    @Test
    fun testGetFeatureProtocol_gray_matchByApp() {
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    any()
                )
            } returns true
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "normandy-test-app4",
                productId = 5L,
                productFullLineIdPath = "3#4_5_6",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every {
                protocolUpgradeProperties.whetherAppStageInGray(any(), any())
            } returns true
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray(any(), any())
            } returns false
            matchScopeService = spyk(MatchScopeService(ObjectMapperFactory.newTolerant()))

        }
        val context = WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        )
        val resourceSpecStatefulSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.1",
            """fake""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        var patch = featureProtocolLoadService.getFeatureProtocol(
            resourceSpecStatefulSetProtocol,
            workloadMetadataConstraint = context
        )
        assertEquals(
            expectedStsBetaProtocol(), patch
        )

    }

    @Test
    fun testGetFeatureProtocol_gray_excludeByStage() {
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    any()
                )
            } returns true
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "normandy-test-app4",
                productId = 5L,
                productFullLineIdPath = "3#4_5_6",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every {
                protocolUpgradeProperties.whetherAppStageInGray(any(), "PUBLISH")
            } returns false
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray(any(), any())
            } returns false
            matchScopeService = spyk(MatchScopeService(ObjectMapperFactory.newTolerant()))

        }
        val context = WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        )
        val resourceSpecStatefulSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.1",
            """fake""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        var patch = featureProtocolLoadService.getFeatureProtocol(
            resourceSpecStatefulSetProtocol,
            workloadMetadataConstraint = context
        )
        assertEquals(
            StatefulSetTest.expectedStsProtocol(), patch
        )

    }

    @Test
    fun testGetFeatureProtocol_gray_matchByProductLine() {
        val featureProtocolLoadService = spyk(FeatureProtocolLoadService()) {
            every {
                commonProperties.contains(
                    FeatureProtocolLoadService.IN_CODE_FEATURE_PROTOCOL_FEATURE_KET_LIST,
                    any()
                )
            } returns true
            every {
                appCenterApi.getAppInfoByName(any())
            } returns AppInfo(
                id = 1L,
                buId = 3L,
                name = "normandy-test-app4",
                productId = 5L,
                productFullLineIdPath = "4_5",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every {
                protocolUpgradeProperties.whetherAppStageInGray(any(), any())
            } returns false
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray("3#4", any())
            } returns true
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray("3#4_5", any())
            } returns false
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray("3", any())
            } returns false
            every {
                protocolUpgradeProperties.whetherProductLineStageInGray("alibaba", any())
            } returns false
            matchScopeService = spyk(MatchScopeService(ObjectMapperFactory.newTolerant()))

        }
        val context = WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        )
        val resourceSpecStatefulSetProtocol = ResourceObjectFeatureProtocol(
            1,
            ResourceObjectConstants.RESOURCE_SPEC_FEATURE_KEY,
            "StatefulSet",
            "0.0.1",
            """fake""".trimIndent(),
            "000001",
            "000001",
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N"
        )
        var patch = featureProtocolLoadService.getFeatureProtocol(
            resourceSpecStatefulSetProtocol,
            workloadMetadataConstraint = context
        )
        assertEquals(
            expectedStsBetaProtocol(), patch
        )

    }

}