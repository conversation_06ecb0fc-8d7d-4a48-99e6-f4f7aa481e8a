package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.VipCrException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.external.model.CpuShareStatus
import com.alibaba.koastline.multiclusters.external.model.VipServiceReq
import com.alibaba.koastline.multiclusters.resourceobj.DispatchLabelService
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.Test
import org.junit.jupiter.api.Assertions.*
import javax.validation.constraints.AssertFalse

class GropApiTest{
  @Test
  fun `test unmarshal CpuShareStatus`() {
    val cpuShareStatusStr =
      """
        {"CpuSetMode":"share"}
      """.trimIndent()
    val cpuShareStatus = JsonUtils.readValue(cpuShareStatusStr, CpuShareStatus::class.java)
    assertEquals("share", cpuShareStatus.cpuSetMode)
  }

  @Test
  fun `cpushare downgrade false`() {

    val objectMapper = ObjectMapper()
    val api = spyk(GropApi(objectMapper))
      .also {
        it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
          every { it.isDowngrade("grop", "queryCpuShare") } returns true
        }
      }
    mockkObject(HttpClientUtils)
    every { HttpClientUtils.httpGetWithHeaders(any(), any(), any(), any()) } returns """
            {
                "code": 0,
                "data": "{\"apiVersion\":\"v1\",\"data\":{\"prohibit\":\"{}\",\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"extConfig\":\"{}\",\"monopolize\":\"{}\",\"hostConfig\":\"{}\",\"constraints\":\"{}\",\"spread\":\"{}\"},\"kind\":\"ConfigMap\",\"metadata\":{\"labels\":{\"sigma.ali/instance-group\":\"ae-address-ns-s_intgstaging_4553923_prehost\",\"sigma.alibaba-inc.com/app-unit\":\"CENTER_UNIT.rg_us_east\",\"sigma.alibaba-inc.com/app-stage\":\"PRE_PUBLISH\"},\"name\":\"app-rules--rg-us-east--pre-publish--ae-address-ns-s-intgstaging-4553923-prehost\",\"nameSpace\":\"ae-address-ns-s\"}}",
                "success": true,
                "traceId": "2107968d17050474310151319e0e2a"
            }
        """.trimIndent()

    assertFalse(api.queryCpuShare("appName", "instanceGroup", "appUnit", "appStage", "site"))
    unmockkAll()
  }

  @Test
  fun `cpushare true`() {

    val objectMapper = ObjectMapperFactory.newTolerant()
    val api = spyk(GropApi(objectMapper))
      .also {
        it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
          every { it.isDowngrade("grop", "queryCpuShare") } returns false
        }
        it.dispatchLabelService = mockk<DispatchLabelService>().also {
          every {
            it.getSigmaConfigMap(
              any(),
              any(),
              any(),
              any(),
              any()
            )
          } returns "{\"apiVersion\":\"v1\",\"data\":{\"prohibit\":\"{}\",\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"extConfig\":\"{}\",\"monopolize\":\"{}\",\"hostConfig\":\"{}\",\"constraints\":\"{}\",\"spread\":\"{}\"},\"kind\":\"ConfigMap\",\"metadata\":{\"labels\":{\"sigma.ali/instance-group\":\"ae-address-ns-s_intgstaging_4553923_prehost\",\"sigma.alibaba-inc.com/app-unit\":\"CENTER_UNIT.rg_us_east\",\"sigma.alibaba-inc.com/app-stage\":\"PRE_PUBLISH\"},\"name\":\"app-rules--rg-us-east--pre-publish--ae-address-ns-s-intgstaging-4553923-prehost\",\"nameSpace\":\"ae-address-ns-s\"}}"
        }
        it.host = "mock"
        it.account = "mock"
        it.accessKey = "mock"
      }

    assertTrue(api.queryCpuShare("appName", "instanceGroup", "appUnit", "appStage", "site"))
    unmockkAll()
  }

  @Test
  fun `cpushare false`() {

    val objectMapper = ObjectMapperFactory.newTolerant()
    val api = spyk(GropApi(objectMapper))
      .also {
        it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
          every { it.isDowngrade("grop", "queryCpuShare") } returns false
        }
        it.dispatchLabelService = mockk<DispatchLabelService>().also {
          every {
            it.getSigmaConfigMap(
              any(),
              any(),
              any(),
              any(),
              any()
            )
          } returns "{\"apiVersion\":\"v1\",\"data\":{\"prohibit\":\"{}\",\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"set\\\"}\",\"extConfig\":\"{}\",\"monopolize\":\"{}\",\"hostConfig\":\"{}\",\"constraints\":\"{}\",\"spread\":\"{}\"},\"kind\":\"ConfigMap\",\"metadata\":{\"labels\":{\"sigma.ali/instance-group\":\"ae-address-ns-s_intgstaging_4553923_prehost\",\"sigma.alibaba-inc.com/app-unit\":\"CENTER_UNIT.rg_us_east\",\"sigma.alibaba-inc.com/app-stage\":\"PRE_PUBLISH\"},\"name\":\"app-rules--rg-us-east--pre-publish--ae-address-ns-s-intgstaging-4553923-prehost\",\"nameSpace\":\"ae-address-ns-s\"}}"
        }
        it.host = "mock"
        it.account = "mock"
        it.accessKey = "mock"
      }
    assertFalse(api.queryCpuShare("appName", "instanceGroup", "appUnit", "appStage", "site"))
    unmockkAll()
  }
}