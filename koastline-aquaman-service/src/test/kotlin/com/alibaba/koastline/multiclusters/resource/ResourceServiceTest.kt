package com.alibaba.koastline.multiclusters.resource

/**
 * @author:    <EMAIL>
 * @date:    2024/6/27 6:01 PM
 */
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceException
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceBoxRepo
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceOwnerReferenceRepo
import com.alibaba.koastline.multiclusters.data.dao.resource.ResourceRepo
import com.alibaba.koastline.multiclusters.data.vo.resource.Resource
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceBox
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceOwnerReference
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceCreateReq
import com.alibaba.koastline.multiclusters.resource.model.ResourceOwnerReferenceDelReq
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Test
import testutils.BaseTest
import kotlin.test.assertEquals

class ResourceServiceTest : BaseTest() {
    @InjectMockKs
    lateinit var resourceService: ResourceService

    @MockK
    lateinit var resourceRepo: ResourceRepo

    @MockK
    lateinit var resourceBoxRepo: ResourceBoxRepo

    @MockK
    lateinit var resourceOwnerReferenceRepo: ResourceOwnerReferenceRepo

    @Test
    fun `test applyResourceSpec when creating a new resource`() {
        val resourceKind = "MyKind"
        val resourceName = "my-resource-name"
        val resourceSpec = """
            {
                "metadata": {
                    "name": "${resourceName}",
                    "ownerReferences": [
                        {
                            "kind": "AppGroupFront",
                            "name": "global-voyager-provider-s_qingfeng24_grayhost",
                            "blockOwnerDeletion": true
                        }
                    ],
                    "audit":{"submitSys":"aone","submitterEmpId":"177031"},
                    "serviceProvider":"serviceProvider"
                },
                "kind": "${resourceKind}",
                "spec": {
                }
            }
        """.trimIndent()
        val resource = Resource(
            id = 1L,
            kind = resourceKind,
            name = resourceName,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 0L,
            creator = "creator",
            modifier = "modifier"
        )
        every { resourceRepo.findByKindAndName(resourceKind, resourceName) } returns null
        every { resourceRepo.insert(any()) } answers {
            val resource = firstArg<Resource>()
            val field = Resource::class.java.getDeclaredField("id")
            field.isAccessible = true
            field[resource] = 1L
            1
        }
        every { resourceBoxRepo.findByResourceIdAndVersion(any(), any()) } returns null
        every { resourceBoxRepo.insert(any()) } returns 1
        every { resourceOwnerReferenceRepo.insert(any()) } returns 1


        // Act
        resourceService.applyResourceSpec(resourceSpec)

        // Assert
        verify(exactly = 1) { resourceRepo.findByKindAndName(any(), any()) }
        verify(exactly = 1) { resourceRepo.insert(any()) }
        verify(exactly = 1) { resourceBoxRepo.insert(any()) }
        verify(exactly = 1) { resourceOwnerReferenceRepo.insert(any()) }
    }

    @Test
    fun `test setBaselineOfResourceBox with valid baseline`() {
        val resourceKind = "MyKind"
        val resourceName = "my-resource-name"
        val resourceSpec = "{\"kind\":\"${resourceKind}\",\"metadata\":{\"name\":\"${resourceName}\",\"audit\":{\"submitSys\":\"aone\",\"submitterEmpId\":\"177031\"}}}"
        val resource = Resource(
            id = 1L,
            kind = resourceKind,
            name = resourceName,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 0L,
            creator = "creator",
            modifier = "modifier"
        )
        val resourceBox = ResourceBox(
            id = 2L,
            resourceId = resource.id!!,
            spec = "spec",
            version = 1,
            creator = "creator",
            modifier = "modifier"
        )
        every { resourceRepo.findByKindAndName(resourceKind, resourceName) } returns resource
        every { resourceBoxRepo.findByResourceIdAndVersion(any(), any()) } returns resourceBox
        every { resourceRepo.updateBaselineBoxIdById(resource.id!!, resourceBox.id!!) } just Runs

        resourceService.setBaselineOfResourceBox(resourceSpec)

        // Assert
        verify(exactly = 1) { resourceRepo.updateBaselineBoxIdById(resource.id!!, resourceBox.id!!) }
    }

    @Test(expected = ResourceException::class)
    fun `test setBaselineOfResourceBox with invalid resource`() {
        val resourceKind = "MyKind"
        val resourceName = "my-resource-name"
        val resourceSpec = "{\"kind\":\"${resourceKind}\",\"metadata\":{\"name\":\"${resourceName}\",\"audit\":{\"submitSys\":\"aone\",\"submitterEmpId\":\"177031\"}}}"
        every { resourceRepo.findByKindAndName(resourceKind, resourceName) } returns null

        resourceService.setBaselineOfResourceBox(resourceSpec)
    }

    @Test(expected = ResourceException::class)
    fun `test setBaselineOfResourceBox with invalid resource box`() {
        val resourceKind = "MyKind"
        val resourceName = "my-resource-name"
        val resourceSpec = "{\"kind\":\"${resourceKind}\",\"metadata\":{\"name\":\"${resourceName}\",\"audit\":{\"submitSys\":\"aone\",\"submitterEmpId\":\"177031\"}}}"
        val resource = Resource(
            id = 1L,
            kind = resourceKind,
            name = resourceName,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 0L,
            creator = "creator",
            modifier = "modifier"
        )
        every { resourceRepo.findByKindAndName(resourceKind, resourceName) } returns resource
        every { resourceBoxRepo.findByResourceIdAndVersion(any(), any()) } returns null

        resourceService.setBaselineOfResourceBox(resourceSpec)
    }

    @Test(expected = ResourceException::class)
    fun `test setBaselineOfResourceBox with invalid resource box version`() {
        val resourceKind = "MyKind"
        val resourceName = "my-resource-name"
        val resourceSpec = "{\"kind\":\"${resourceKind}\",\"metadata\":{\"name\":\"${resourceName}\",\"audit\":{\"submitSys\":\"aone\",\"submitterEmpId\":\"177031\"}}}"
        val resource = Resource(
            id = 1L,
            kind = resourceKind,
            name = resourceName,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 2L,
            creator = "creator",
            modifier = "modifier"
        )
        val resourceBox = ResourceBox(
            id = 1L,
            resourceId = resource.id!!,
            spec = "spec",
            version = 1,
            creator = "creator",
            modifier = "modifier"
        )
        every { resourceRepo.findByKindAndName(resourceKind, resourceName) } returns resource
        every { resourceBoxRepo.findByResourceIdAndVersion(any(), any()) } returns resourceBox

        resourceService.setBaselineOfResourceBox(resourceSpec)
    }

    @Test
    fun `test_queryCascadedResourceDescList`() {
        val kind = "V2Env"
        val name = "01452a65-80c0-4c94-b384-0be0D4"
        every {
            resourceRepo.findByKindAndName(kind = kind, name = name)
        } returns Resource(
            id = 1L,
            kind = kind,
            name = name,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 1L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceBoxRepo.findById(1L)
        } returns ResourceBox(
            id = 1L,
            resourceId = 1L,
            spec = "spec",
            version = 1,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L)
        } returns emptyList()
        every {
            resourceOwnerReferenceRepo.querySubResourceByOwnerReference(ownerRefKind = kind, ownerRefName = name)
        } returns listOf(
            ResourceOwnerReference(
                id = 1L,
                ownerRefKind = kind,
                ownerRefName = name,
                subResourceId = 2L,
                creator = "creator",
                modifier = "modifier"
            )
        )
        every {
            resourceRepo.findById(2L)
        }returns Resource(
            id = 2L,
            kind = "V2AppGroup",
            name = "normandy-test-app4host",
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 2L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceBoxRepo.findById(2L)
        }returns ResourceBox(            id = 2L,
            resourceId = 2L,
            spec = "spec",
            version = 1,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceRepo.findByKindAndName(kind = "V2AppGroup", name = "normandy-test-app4host")
        } returns Resource(
            id = 2L,
            kind = "V2AppGroup",
            name = "normandy-test-app4host",
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 2L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L)
        } returns emptyList()
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(2L)
        } returns listOf(ResourceOwnerReference(
            id = 1L,
            ownerRefKind = kind,
            ownerRefName = name,
            subResourceId = 2L,
            creator = "creator",
            modifier = "modifier"
        ))
        every {
            resourceOwnerReferenceRepo.querySubResourceByOwnerReference(
                ownerRefKind = "V2AppGroup",
                ownerRefName = "normandy-test-app4host"
            )
        }returns emptyList()
        val resourceDescList = resourceService.queryCascadedResourceDescList(kind, name, -1)
        assertEquals(2, resourceDescList.size)
    }

    @Test(expected = ResourceException::class)
    fun `test_deleteResource while exist sub resource`() {
        val kind = "V2Env"
        val name = "01452a65-80c0-4c94-b384-0be0D4"
        every {
            resourceOwnerReferenceRepo.querySubResourceByOwnerReference(ownerRefKind = kind, ownerRefName = name)
        } returns listOf(
            ResourceOwnerReference(
                id = 1L,
                ownerRefKind = kind,
                ownerRefName = name,
                subResourceId = 2L,
                creator = "creator",
                modifier = "modifier"
            )
        )
        every {
            resourceRepo.findById(2L)
        }returns Resource(            id = 2L,
            kind = "V2AppGroup",
            name = "normandy-test-app4host",
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 2L,
            creator = "creator",
            modifier = "modifier"
        )
        resourceService.deleteResource(kind, name)
    }

    @Test
    fun `test_deleteResource while not sub resource`() {
        val kind = "V2Env"
        val name = "01452a65-80c0-4c94-b384-0be0D4"
        every {
            resourceOwnerReferenceRepo.querySubResourceByOwnerReference(ownerRefKind = kind, ownerRefName = name)
        } returns emptyList()
        every {
            resourceRepo.findByKindAndName(kind = kind, name = name)
        }returns Resource(
            id = 1L,
            kind = kind,
            name = name,
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 1L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L)
        }returns listOf(ResourceOwnerReference(
            id = 1L,
            ownerRefKind = kind,
            ownerRefName = name,
            subResourceId = 2L,
            creator = "creator",
            modifier = "modifier"
        ))
        every {
            resourceOwnerReferenceRepo.deleteById(1L)
        }returns 1
        every {
            resourceRepo.deleteById(1L)
        }returns 1
        resourceService.deleteResource(kind, name)
        verify(exactly = 1) { resourceOwnerReferenceRepo.querySubResourceByOwnerReference(ownerRefKind = kind, ownerRefName = name) }
        verify(exactly = 1) { resourceRepo.findByKindAndName(kind = kind, name = name) }
        verify(exactly = 1) { resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L) }
        verify(exactly = 1) { resourceOwnerReferenceRepo.deleteById(1L) }
        verify(exactly = 1) { resourceRepo.deleteById(1L) }
    }

    @Test(expected = ResourceException::class)
    fun `test_createResourceOwnerReference while not sub resource`() {
        every {
            resourceRepo.findByKindAndName(kind = "V2Env", name = "01452a65-80c0-4c94-b384-0be0D4")
        }returns null
        resourceService.createResourceOwnerReference(ResourceOwnerReferenceCreateReq(
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            ownerKind = "V2AppGroup",
            ownerName = "normandy-test-app4host",
            creator = "creator")
        )
    }

    @Test
    fun `test_createResourceOwnerReference`() {
        every {
            resourceRepo.findByKindAndName(kind = "V2Env", name = "01452a65-80c0-4c94-b384-0be0D4")
        }returns Resource(
            id = 1L,
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 1L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L)
        } returns emptyList()
        every {
            resourceOwnerReferenceRepo.insert(any())
        }returns 1
        resourceService.createResourceOwnerReference(ResourceOwnerReferenceCreateReq(
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            ownerKind = "V2AppGroup",
            ownerName = "normandy-test-app4host",
            creator = "creator")
        )
        verify(exactly = 1) { resourceOwnerReferenceRepo.insert(any()) }
    }

    @Test(expected = ResourceException::class)
    fun `test_delResourceOwnerReference while not sub resource`() {
        every {
            resourceRepo.findByKindAndName(kind = "V2Env", name = "01452a65-80c0-4c94-b384-0be0D4")
        }returns null
        resourceService.delResourceOwnerReference(
            ResourceOwnerReferenceDelReq(
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            ownerKind = "V2AppGroup",
            ownerName = "normandy-test-app4host",
            modifier = "modifier")
        )
    }

    @Test
    fun `test_delResourceOwnerReference`() {
        every {
            resourceRepo.findByKindAndName(kind = "V2Env", name = "01452a65-80c0-4c94-b384-0be0D4")
        }returns Resource(
            id = 1L,
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            serviceProvider = "service-provider",
            controller = true,
            needRecycling = true,
            baselineBoxId = 1L,
            creator = "creator",
            modifier = "modifier"
        )
        every {
            resourceOwnerReferenceRepo.queryOwnerReferenceBySubResourceId(1L)
        }returns listOf(ResourceOwnerReference(
            id = 1L,
            ownerRefKind = "V2AppGroup",
            ownerRefName = "normandy-test-app4host",
            subResourceId = 1L,
            creator = "creator",
            modifier = "modifier"
        ))
        every {
            resourceOwnerReferenceRepo.deleteById(any())
        }returns 1
        resourceService.delResourceOwnerReference(ResourceOwnerReferenceDelReq(
            kind = "V2Env",
            name = "01452a65-80c0-4c94-b384-0be0D4",
            ownerKind = "V2AppGroup",
            ownerName = "normandy-test-app4host",
            modifier = "creator")
        )
        verify(exactly = 1) { resourceOwnerReferenceRepo.deleteById(1L) }
    }
}