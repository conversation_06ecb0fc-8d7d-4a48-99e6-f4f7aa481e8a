package com.alibaba.koastline.multiclusters.appenv.service.runtime.test

import com.alibaba.koastline.multiclusters.appenv.service.schedule.test.DeployScheduleServiceTest
import com.alibaba.koastline.multiclusters.appenv.service.schedule.test.DeployScheduleServiceTest.Companion
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeData
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkload
import com.alibaba.koastline.multiclusters.runtime.RuntimeService
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeType.COMMON
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.spyk
import java.lang.Runtime.getRuntime
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import org.junit.Test

class RuntimeServiceTest {

    @Test
    fun testComputeRuntimeInstalledWorkload() {
        val now = Date(Instant.now().toEpochMilli())
        val runtimeData = fakeRuntimeData()
        val runtimeService = spyk<RuntimeService>() {
            every {
                runtimeBaseService.findRuntime(runtimeData.appName, runtimeData.runtimeKey)
            } returns runtimeData
            every {
                runtimeBaseService.listInstalledWorkloadByRuntimeKey(runtimeData.appName, runtimeData.runtimeKey)
            } returns listOf(
                RuntimeWorkload(
                    runtimeKey = runtimeData.runtimeKey,
                    site = "na610",
                    unit = "center",
                    stage = "PUBLISH",
                    clusterId = "asi_zjk_core_a01",
                    status = "ONLINE",
                    runningStatus = "INSTALLED",
                    gmtCreate = now,
                    gmtModified = now,
                    creator = "10000",
                    modifier = "10000",
                    isDeleted = "N"
                ),
                RuntimeWorkload(
                    runtimeKey = runtimeData.runtimeKey,
                    site = "na620",
                    unit = "center",
                    stage = "PUBLISH",
                    clusterId = "asi_zjk_core_b01",
                    status = "ONLINE",
                    runningStatus = "INSTALLED",
                    gmtCreate = now,
                    gmtModified = now,
                    creator = "10000",
                    modifier = "10000",
                    isDeleted = "N"
                )
            )
            every {
                resourcePoolService.getClusterProfileNew("asi_zjk_core_a01")
            } returns ClusterProfileNew(
                clusterId = "asi_zjk_core_a01",
                clusterName = "asi_zjk_core_a01",
                clusterProvider = "alibaba",
                clusterType = "alibaba-asi",
                siteList = listOf(),
                componentDataList = listOf(),
                useType = "publish"
            )
            every {
                resourcePoolService.getClusterProfileNew("asi_zjk_core_b01")
            } returns ClusterProfileNew(
                clusterId = "asi_zjk_core_b01",
                clusterName = "asi_zjk_core_b01",
                clusterProvider = "alibaba",
                clusterType = "alibaba-asi",
                siteList = listOf(),
                componentDataList = listOf(),
                useType = "publish"
            )
        }
        val workloadExpectedStates = runtimeService.computeRuntimeInstalledWorkload(runtimeData.appName, runtimeData.runtimeKey)
        assertEquals(2, workloadExpectedStates.size)
        assertEquals(DeployScheduleServiceTest.APP_NAME, workloadExpectedStates[0].workloadMetadataConstraint.appName)
        assertEquals(DeployScheduleServiceTest.RESOURCE_GROUP, workloadExpectedStates[0].workloadMetadataConstraint.resourceGroup)
        assertEquals("PUBLISH", workloadExpectedStates[0].workloadMetadataConstraint.stage)
        assertEquals("center", workloadExpectedStates[0].workloadMetadataConstraint.unit)
        assertEquals("na610", workloadExpectedStates[0].workloadMetadataConstraint.site)
        assertEquals("asi_zjk_core_a01", workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals(null, workloadExpectedStates[0].workloadMetadataConstraint.runtimeId)
        assertEquals(DeployScheduleServiceTest.APP_NAME, workloadExpectedStates[1].workloadMetadataConstraint.appName)
        assertEquals(DeployScheduleServiceTest.RESOURCE_GROUP, workloadExpectedStates[1].workloadMetadataConstraint.resourceGroup)
        assertEquals("PUBLISH", workloadExpectedStates[1].workloadMetadataConstraint.stage)
        assertEquals("center", workloadExpectedStates[1].workloadMetadataConstraint.unit)
        assertEquals("na620", workloadExpectedStates[1].workloadMetadataConstraint.site)
        assertEquals("asi_zjk_core_b01", workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
        assertEquals(null, workloadExpectedStates[1].workloadMetadataConstraint.runtimeId)
    }

    @Test
    fun `testComputeRuntimeScheduleResult ~ is standard runtime`() {
        val envStackId = UUID.randomUUID().toString()
        val stackBindResourceGroupList = listOf("resource_group_1")
        val runtimeData = fakeRuntimeData()
        val runtimeService = spyk<RuntimeService>() {
            every {
                isStandardRuntime(any())
            }returns true
            every {
                skylineApi.listResourceGroupConfigByEnvStackId(envStackId)
            } returns stackBindResourceGroupList
            every {
                runtimeRepo.findByResourceGroupName(resourceGroupName = stackBindResourceGroupList[0])
            }returns runtimeData
            every {
                computeRuntimeInstalledWorkload(runtimeData.appName,runtimeData.runtimeKey)
            }returns listOf(
                WorkloadExpectedState(
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = runtimeData.appName,
                        resourceGroup = runtimeData.resourceGroupName,
                        unit = "center",
                        stage = "PUBLISH",
                        site = "na610",
                        clusterId = "cluster",
                        namespace = "normandy-test-app4"
                    ),
                    params = emptyMap()
                )
            )
        }
        val scheduleResult = runtimeService.computeRuntimeScheduleResult(envStackId = envStackId)
        assertEquals(1, scheduleResult.workloadExpectedStates.size)
    }


    private fun fakeRuntimeData(): RuntimeData {
        val now = Date(Instant.now().toEpochMilli())
        return RuntimeData(
            id = 1L,
            runtimeKey = UUID.randomUUID().toString(),
            appName = Companion.APP_NAME,
            envStackId = Companion.ENV_STACK_ID,
            resourceGroupName = Companion.RESOURCE_GROUP,
            type = COMMON.toString(),
            gmtCreate = now,
            gmtModified = now,
            creator = "10000",
            modifier = "10000",
            isDeleted = "N"
        )
    }
}