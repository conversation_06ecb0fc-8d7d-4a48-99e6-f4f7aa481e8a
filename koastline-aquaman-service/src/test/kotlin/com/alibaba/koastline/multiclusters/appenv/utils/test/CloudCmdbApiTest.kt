package com.alibaba.koastline.multiclusters.appenv.utils.test

import com.alibaba.koastline.multiclusters.external.CloudCmdbApi
import com.alibaba.koastline.multiclusters.external.model.CmdbAttribute
import com.alibaba.koastline.multiclusters.external.model.CmdbStackConfig
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals

class CloudCmdbApiTest {

    @Test
    fun testGetIsolationEnvVars() {
        val stackId  = "stack_id"
        val objectMapper = ObjectMapper().registerKotlinModule()
        val cmdbApi = spyk(CloudCmdbApi(objectMapper)) {
            every {
                getStackConfig(stackId)
            } returns CmdbStackConfig(
                "1",stackId,"测试环境1",1,"normal","online","normandy-test-app4","testing","tao",
                "[{\"name\":\"enableScmHook\",\"type\":\"string\",\"value\":\"true\",\"valueType\":\"boolean\"},{\"name\":\"deployFlowId\",\"type\":\"userdefined\",\"value\":\"113021194\",\"valueType\":\"string\"}]",
                mapOf("-" to listOf(
                    CmdbAttribute("0ebd50ac","envSign","envvariable","testing","string"),
                    CmdbAttribute("0ebd50ac","envGroup","envvariable","normandy-test-app4_testhot","string"),
                    CmdbAttribute("0ebd50ac","appDeployType","envvariable","aol","string")
                ))
            )
        }

        val params = cmdbApi.getIsolationEnvVars(stackId)
        assertEquals(4, params.size)
        assertEquals("testing", params["ali_env_sign"])
        assertEquals("normandy-test-app4_testhot", params["ali_env_group"])
        assertEquals("aol", params["ali_deploy_type"])
    }
}