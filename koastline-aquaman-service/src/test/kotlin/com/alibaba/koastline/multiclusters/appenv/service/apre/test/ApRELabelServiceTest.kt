package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.req.ApREFeatureSpecCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.ApRELabelUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecScopeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREFeatureSpecStatusEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import io.mockk.every
import io.mockk.justRun
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class ApRELabelServiceTest {

    @Test
    fun `testFindApRELabelByTarget`() {
        val targetKey = UUID.randomUUID().toString()
        val targetType = ApRELabelTargetTypeEnum.RESOURCE_POOL.name
        val apRELabel1 = ApRELabel(
            id = 1,
            targetKey = targetKey,
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            value = "serverless/tpp",
            apRELabelKey = UUID.randomUUID().toString(),
            targetType = targetType,
            type = ApRELabelType.SERVERLESS
        )
        val apRELabel2 = ApRELabel(
            id = 1,
            targetKey = targetKey,
            name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
            value = "compute",
            apRELabelKey = UUID.randomUUID().toString(),
            targetType = targetType,
            type = ApRELabelType.CONSOLE
        )
        val apRELabelService = spyk(ApRELabelService()) {
            every {
                apRELabelRepo.findByTarget(targetKey, targetType)
            } returns listOf(apRELabel1,apRELabel2)
            every {
                apREFeatureSpecService.batchQueryOnlineApREFeatureSpecByLabel(listOf(apRELabel1.apRELabelKey,apRELabel2.apRELabelKey))
            }returns listOf(
                ApREFeatureSpecDO(
                    id = 1L,
                    apRELabelKey = apRELabel1.apRELabelKey,
                    "title",
                    null,
                    "specCode",
                    "publish",
                    "online",
                    "sourceType",
                    "1",
                    "versionType",
                    "1",
                    "{}",
                    null,
                    Date(Instant.now().toEpochMilli()),
                    Date(Instant.now().toEpochMilli()),
                    "N"
                ),
                ApREFeatureSpecDO(
                    id = 2L,
                    apRELabelKey = apRELabel2.apRELabelKey,
                    "title",
                    null,
                    "specCode",
                    "publish",
                    "online",
                    "sourceType",
                    "1",
                    "versionType",
                    "1",
                    "{}",
                    null,
                    Date(Instant.now().toEpochMilli()),
                    Date(Instant.now().toEpochMilli()),
                    "N"
                )
            )
            justRun {
                apRELabelDefinitionService.fillApRELabelTitles(any())
            }
        }
        val apRELabelDOList = apRELabelService.findApRELabelByTarget(targetKey = targetKey, targetType = targetType)
        assertEquals(2, apRELabelDOList.size)

        assertEquals(apRELabel1.apRELabelKey, apRELabelDOList[0].apRELabelKey)
        assertEquals(1, apRELabelDOList[0].apREFeatureSpecs!!.size)
        assertEquals(apRELabel1.apRELabelKey, apRELabelDOList[0].apREFeatureSpecs!![0]!!.apRELabelKey)

        assertEquals(apRELabel2.apRELabelKey, apRELabelDOList[1].apRELabelKey)
        assertEquals(1, apRELabelDOList[1].apREFeatureSpecs!!.size)
        assertEquals(apRELabel2.apRELabelKey, apRELabelDOList[1].apREFeatureSpecs!![0]!!.apRELabelKey)
    }

    @Test
    fun testCreateApRELabelIgnoreWhileExistWithFeatureSpec_with_not_exist_label() {
        val apRELabelCreateReqDto = getComplexApRELabelCreateReqDto()
        val apRELabelService = spyk(ApRELabelService()) {
            every {
                apRELabelRepo.findByTarget(apRELabelCreateReqDto.targetKey!!, apRELabelCreateReqDto.targetType)
            } returns emptyList()
            every {
                apRELabelRepo.insert(any())
            } returns 1
            every {
                apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(any())
            } returns getComplexApRELabelData().apREFeatureSpecs!![0]
        }
        apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(apRELabelCreateReqDto)
        verify {
            apRELabelService.apRELabelRepo.insert(match {
                it.targetKey == apRELabelCreateReqDto.targetKey
            })
        }
    }

    @Test
    fun testCreateApRELabelIgnoreWhileExistWithFeatureSpec_with_exist_label() {
        val now = Date(Instant.now().toEpochMilli())
        val apRELabelCreateReqDto = getComplexApRELabelCreateReqDto()
        val apRELabelService = spyk(ApRELabelService()) {
            every {
                apRELabelRepo.findByTarget(apRELabelCreateReqDto.targetKey!!, apRELabelCreateReqDto.targetType)
            } returns listOf(
                ApRELabel(
                    1,
                    "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61",
                    ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                    "serverless/tpp",
                    now,
                    now,
                    Constants.IS_NOT_DELETED,
                    "2Zfo5rAlMCVD1hi0FkM8cVomjKmBQa62",
                    ApRELabelTargetTypeEnum.APRE.name,
                    ApRELabelType.SERVERLESS
                ),
            )
            every {
                apREFeatureSpecService.createApREFeatureSpecUpdateWhileExist(any())
            } returns ApREFeatureSpecDO(
                1,
                "2Zfo5rAlMCVD1hi0FkM8cVomjKmBQa62",
                "title",
                null,
                "specCode",
                "publish",
                "online",
                "sourceType",
                "1",
                "versionType",
                "1",
                "{}",
                null,
                now,
                now,
                "N"
            )
        }
        val result = apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(apRELabelCreateReqDto)

        assertEquals("iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61", result.targetKey)
        assertEquals("2Zfo5rAlMCVD1hi0FkM8cVomjKmBQa62", result.apRELabelKey)
        assertEquals(1, result.apREFeatureSpecs!!.size)
        assertEquals("2Zfo5rAlMCVD1hi0FkM8cVomjKmBQa62", result.apREFeatureSpecs!![0].apRELabelKey)
    }


    companion object {
        val RUNTIME_ENV_KEY = "iZfo5rAlMCVD1hi0FkM8cVomjKmBQa61"
        val MANAGED_CLUSTER_KEY = "izQsiF9xOUWwdjWN"
        val RESOURCE_POOL_KEY = "f8E25NFno5zZVRd3htyN1b3W800tOErR"

        fun getComplexApRELabelCreateReqDto(): ApRELabelCreateReqDto {
            return ApRELabelCreateReqDto(
                targetKey = RUNTIME_ENV_KEY,
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = "serverless/tpp",
                apREFeatureSpecList = listOf(
                    ApREFeatureSpecCreateReqDto(
                        title = "serverless_runtime_1.0",
                        specType = "common",
                        specCode = "runtime-app-demo.71024",
                        scope = ApREFeatureSpecScopeEnum.publish,
                        status = ApREFeatureSpecStatusEnum.online,
                        sourceType = "appstack_runtime",
                        sourceId = "1111",
                        versionType = "appstack_version",
                        versionId = "1111",
                        annotations = JsonUtils.readValue("{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            mutableMapOf<String,String>().javaClass
                        )
                    )
                ),
                ApRELabelTargetTypeEnum.APRE.name,
                ApRELabelType.SERVERLESS
            )
        }
        fun getComplexApRELabelUpdateReqDto(): ApRELabelUpdateReqDto {
            return ApRELabelUpdateReqDto(
                apRELabelKey = "dahihabo3131nio",
                targetKey = RUNTIME_ENV_KEY,
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = "serverless/tpp",
                apREFeatureSpecList = listOf(
                    ApREFeatureSpecCreateReqDto(
                        title = "serverless_runtime_1.0",
                        specType = "common",
                        specCode = "runtime-app-demo.71024",
                        scope = ApREFeatureSpecScopeEnum.publish,
                        status = ApREFeatureSpecStatusEnum.online,
                        sourceType = "appstack_runtime",
                        sourceId = "1111",
                        versionType = "appstack_version",
                        versionId = "1111",
                        annotations = JsonUtils.readValue("{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            mutableMapOf<String,String>().javaClass
                        )
                    )
                ),
                targetType = ApRELabelTargetTypeEnum.APRE.name,
                ApRELabelType.SERVERLESS
            )
        }

        fun getComplexApRELabelData(): ApRELabelDO {
            return ApRELabelDO(
                null,
                RUNTIME_ENV_KEY,
                ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                "serverless/tpp",
                null,
                null,
                null,
                null,
                listOf(
                    ApREFeatureSpecDO(
                        null,
                        null,
                        "serverless_runtime_1.0",
                        null,
                        null,
                        "publish",
                        "online",
                        "appstack_runtime",
                        "1111",
                        "appstack_version",
                        "1111",
                        "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                        null,
                        null,
                        null,
                        null
                    )
                ),
                ApRELabelTargetTypeEnum.APRE,
                ApRELabelType.SERVERLESS
            )
        }
    }
}