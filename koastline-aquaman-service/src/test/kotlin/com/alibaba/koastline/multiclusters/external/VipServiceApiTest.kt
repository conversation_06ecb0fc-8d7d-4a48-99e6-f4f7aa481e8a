package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.config.ExternalCallDowngradeProperties
import com.alibaba.koastline.multiclusters.common.exceptions.VipCrException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.external.model.VipServiceReq
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceMetadataRequestDTO
import com.alibaba.normandy.actor.client.v1.vip.data.VipServiceTupleParameter
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals


class VipServiceApiTest {

    @Test
    fun queryVipService_success_json() {

        val objectMapper = ObjectMapper()
        val api = spyk(VipServiceCrApi(objectMapper))
            .also {
                it.vipServiceHost = "http://fake.it"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("vipservice-cr", "getVipServiceCr") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
                "data": {
                    "unit": "center",
                    "usage": "PUBLISH",
                    "site": "na610",
                    "services": [
                        "{\"apiVersion\":\"v1\",\"kind\":\"Service\",\"metadata\":\"meta\",\"spec\":\"spec\"}"
                    ]
                },
                "code": 0,
                "message": null,
                "success": true
            }
        """.trimIndent()

        val mockReq = VipServiceMetadataRequestDTO().apply {
            namespace = "fake-ns"
            targetClusterId = "fake-cluster"
            vipServiceTupleParam = VipServiceTupleParameter().apply {
                this.appName = "fake-app"
                this.groupName = "fake-group"
                this.unit = "nt12"
                this.site = "CENTER"
                this.useType = "DAILY"
            }
        }
        val objects = api.getVipServiceCr(mockReq, "JSON")
        assertEquals(objects.size, 1)
        assertEquals(objects.get(0).apiVersion, "v1")
        assertEquals(objects.get(0).kind, "Service")
        assertEquals(
            objects.get(0).resourceObject,
            "{\"apiVersion\":\"v1\",\"kind\":\"Service\",\"metadata\":\"meta\",\"spec\":\"spec\"}"
        )
        unmockkAll()
    }

    @Test
    fun queryVipService_success_json_no_services() {

        val objectMapper = ObjectMapper()
        val api = spyk(VipServiceCrApi(objectMapper))
            .also {
                it.vipServiceHost = "http://fake.it"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("vipservice-cr", "getVipServiceCr") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
                "data": {
                    "unit": "center",
                    "usage": "PUBLISH",
                    "site": "na610",
                    "services": null
                },
                "code": 0,
                "message": null,
                "success": true
            }
        """.trimIndent()

        val mockReq = VipServiceMetadataRequestDTO().apply {
            namespace = "fake-ns"
            targetClusterId = "fake-cluster"
            vipServiceTupleParam = VipServiceTupleParameter().apply {
                this.appName = "fake-app"
                this.groupName = "fake-group"
                this.unit = "nt12"
                this.site = "CENTER"
                this.useType = "DAILY"
            }
        }
        val objects = api.getVipServiceCr(mockReq, "JSON")
        assertEquals(objects.size, 0)

        unmockkAll()
    }

    @Test
    fun queryVipService_success_yaml() {

        val objectMapper = ObjectMapper()
        val api = spyk(VipServiceCrApi(objectMapper))
            .also {
                it.vipServiceHost = "http://fake.it"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("vipservice-cr", "getVipServiceCr") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
                "data": {
                    "unit": "center",
                    "usage": "PUBLISH",
                    "site": "na610",
                    "services": [
                        "{\"apiVersion\":\"v1\",\"kind\":\"Service\",\"metadata\":\"meta\",\"spec\":\"spec\"}"
                    ]
                },
                "code": 0,
                "message": null,
                "success": true
            }
        """.trimIndent()

        val mockReq = VipServiceMetadataRequestDTO().apply {
            namespace = "fake-ns"
            targetClusterId = "fake-cluster"
            vipServiceTupleParam = VipServiceTupleParameter().apply {
                this.appName = "fake-app"
                this.groupName = "fake-group"
                this.unit = "nt12"
                this.site = "CENTER"
                this.useType = "DAILY"
            }
        }
        val objects = api.getVipServiceCr(mockReq, "YAML")
        assertEquals(objects.size, 1)
        assertEquals(objects.get(0).apiVersion, "v1")
        assertEquals(objects.get(0).kind, "Service")
        assertEquals(objects.get(0).resourceObject, "apiVersion: v1\n" +
                "kind: Service\n" +
                "metadata: meta\n" +
                "spec: spec\n")
        unmockkAll()
    }

    @Test
    fun queryVipService_fail() {

        val objectMapper = ObjectMapper()
        val api = spyk(VipServiceCrApi(objectMapper))
            .also {
                it.vipServiceHost = "http://fake.it"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("vipservice-cr", "getVipServiceCr") } returns false
                }
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
                "data": {
                    "unit": "center",
                    "usage": "PUBLISH",
                    "site": "na610",
                    "services": [
                        "fail"
                    ]
                },
                "code": 0,
                "message": null,
                "success": false
            }
        """.trimIndent()

        val mockReq = VipServiceMetadataRequestDTO().apply {
            namespace = "fake-ns"
            targetClusterId = "fake-cluster"
            vipServiceTupleParam = VipServiceTupleParameter().apply {
                this.appName = "fake-app"
                this.groupName = "fake-group"
                this.unit = "nt12"
                this.site = "CENTER"
                this.useType = "DAILY"
            }
        }
        assertThrows<VipCrException> { api.getVipServiceCr(mockReq, "JSON") }
        unmockkAll()
    }

    @Test
    fun queryVipService_downgrade() {

        val objectMapper = ObjectMapper()
        val api = spyk(VipServiceCrApi(objectMapper))
            .also {
                it.vipServiceHost = "http://fake.it"
                it.externalCallDowngradeProperties = mockk<ExternalCallDowngradeProperties>().also {
                    every { it.isDowngrade("vipservice-cr", "getVipServiceCr") } returns true
                }
            }

        val mockReq = VipServiceMetadataRequestDTO().apply {
            namespace = "fake-ns"
            targetClusterId = "fake-cluster"
            vipServiceTupleParam = VipServiceTupleParameter().apply {
                this.appName = "fake-app"
                this.groupName = "fake-group"
                this.unit = "nt12"
                this.site = "CENTER"
                this.useType = "DAILY"
            }
        }
        val objects = api.getVipServiceCr(mockReq, "JSON")
        assertEquals(objects.size, 0)
        unmockkAll()
    }


}