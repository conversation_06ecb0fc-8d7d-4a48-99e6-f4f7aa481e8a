package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREAttorneyService
import com.alibaba.koastline.multiclusters.apre.attorney.ApREBindingService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingData
import com.alibaba.koastline.multiclusters.apre.model.ApREBindingTerm
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDefaultFeatureDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.Attorney
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.Required
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.APRE
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.RESOURCE_POOL
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType
import com.alibaba.koastline.multiclusters.apre.params.IncludeScopeType.INHERIT
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.PageData
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import testutils.BaseTest
import java.time.Instant
import java.util.*

class ApREAttorneyServiceTest : BaseTest() {
    private var objectMapper = ObjectMapperFactory.newTolerant()

    @InjectMockKs
    lateinit var apREAttorneyService: ApREAttorneyService

    @MockK
    lateinit var matchScopeService: MatchScopeService

    @MockK
    lateinit var apREBindingService: ApREBindingService

    @MockK
    lateinit var apREService: ApREService

    @MockK
    lateinit var apRELabelService: ApRELabelService

    @Test
    fun createAttorneyTest(){
        // 绑定的ApRE
        val apRE = getComplexApREDTO().copy(
            name = getString(),
            id = getLong(),
            runtimeEnvKey = getString()
        )
        // 授权的ApREBindingTerm
        val apREBindingTerm = manufacturePojo(ApREBindingTerm::class.java).copy(
            allSupported = true,
            required = Required(
                allLabelSupported = true,
                allClustersSupported = true
            )
        )
        val apREBindingData = ApREBindingData(
            runtimeEnvKey = apRE.runtimeEnvKey!!,
            apREBindingTerm = apREBindingTerm,
            id = null,
        )
        // 限定的范围ms
        val matchScopeData = MatchScopeDataDO(
            externalId = getString(),
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
            targetId = null,
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            creator = getString(),
            modifier = getString()
        )
        // 授权
        val attorney = Attorney(
            apREBindingData = apREBindingData,
            matchScopeData = matchScopeData
        )
        val apREBindingDataId = getLong()
        every {
            apREBindingService.listApREBindingDataByApREKey(apRE.runtimeEnvKey!!)
        }returns emptyList()
        every {
            apREBindingService.createApREBindingData(apREBindingData)
        }returns apREBindingData.copy(
            id = apREBindingDataId
        )
        every {
            matchScopeService.createMatchScopeIgnoreWhileExist(any())
        }just runs
        apREAttorneyService.createAttorney(attorney)
    }

    @Test
    fun batchCreateAttorneyTest() {
        val productLine = getString()
        val apRE1 = getComplexApREDTO().copy(
            name = getString(),
            id = getLong(),
            runtimeEnvKey = getString()
        )

        val apRE2 = getComplexApREDTO().copy(
            name = getString(),
            id = getLong(),
            unit = getString(),
            az = getString(),
            stage = "PRE_PUBLISH",
            runtimeEnvKey = getString()
        )

        //全量授权
        val apREBindingTerm = manufacturePojo(ApREBindingTerm::class.java).copy(
            allSupported = true,
            required = Required(
                allLabelSupported = true,
                allClustersSupported = true
            )
        )

        val matchScopeData = MatchScopeDataDO(
            externalId = productLine,
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            targetId = null,
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            creator = getString(),
            modifier = getString()
        )

        // 创建新的授权范围
        val attorneyList = listOf(
            Attorney(
                apREBindingData = ApREBindingData(
                    runtimeEnvKey = apRE1.runtimeEnvKey!!,
                    apREBindingTerm = apREBindingTerm,
                    id = null,
                ),
                matchScopeData = matchScopeData
            ),
            Attorney(
                apREBindingData = ApREBindingData(
                    runtimeEnvKey = apRE2.runtimeEnvKey!!,
                    apREBindingTerm = apREBindingTerm,
                    id = null,
                ),
                matchScopeData = matchScopeData
            )
        )

        // case1: 创建不冲突
        val apREAttorneyService1 = spyk(ApREAttorneyService()) {
            // ApRE 未绑定ApREBindingData
            every {
                apREBindingService.listApREBindingDataByApREKey(any())
            } returns emptyList()

            apRE2.runtimeEnvKey!!
            every {
                createAttorney(
                    attorney = or(
                        attorneyList[0], attorneyList[1]
                    )
                )
            } just runs
        }
        apREAttorneyService1.batchCreateAttorney(attorneyList)

        verify(exactly = 2) {
            apREAttorneyService1.createAttorney(any())
        }

        //case2: 创建冲突 已经存在
        val apREAttorneyService2 = spyk(ApREAttorneyService()) {
            // ApRE 未绑定ApREBindingData
            every {
                apREBindingService.listApREBindingDataByApREKey(any())
            } answers {
                val apREKey = call.invocation.args[0] as String
                if (apREKey == apRE1.runtimeEnvKey || apREKey == apRE2.runtimeEnvKey) {
                    mutableListOf(
                        ApREBindingData(
                            runtimeEnvKey = apREKey,
                            apREBindingTerm = apREBindingTerm,
                            id = getLong()
                        )
                    )
                } else {
                    emptyList()
                }
            }

            every {
                matchScopeService.findByTargetAndExternal(
                    MatchScopeTargetTypeEnum.ApREBindingData.name,
                    any(),
                    productLine,
                    MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
                )
            } returns manufacturePojo(MatchScopeDataDO::class.java)
        }

        exceptionTest({ e -> softly.assertThat(e.message?.contains("重复授权行为，请检查授权动作")).isTrue }) {
            apREAttorneyService2.batchCreateAttorney(attorneyList)
        }
    }

    @Test
    fun listApREAttorneyByApREKeyTest(){
        val apREkey = getString()
        val apREBindingData = manufacturePojo(ApREBindingData::class.java).copy(
            runtimeEnvKey = apREkey,
            id = getLong()
        )
        every {
            apREBindingService.listApREBindingDataByApREKey(apREkey)
        } returns listOf(apREBindingData)

        every {
            matchScopeService.listByTarget(
                targetId = apREBindingData.id!!,
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name
            )
        } returns listOf(
            manufacturePojo(MatchScopeDataDO::class.java).copy(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                targetId = apREBindingData.id,
                externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
            )
        )
        val attorneys = apREAttorneyService.listAttorneyByApREKey(apREKey = apREkey)
        softly.assertThat(attorneys.size).isEqualTo(1)
        softly.assertThat(attorneys[0].apREBindingData).isEqualTo(apREBindingData)
    }

    @Test
    fun listAttorneyByPropertiesTest() {
        val site = getString()
        val stage = getString()
        val unit = getString()
        val buId = 4L
        val productLineFullPath = "1_2_3"
        val externalId = "$buId#$productLineFullPath"
        val externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        val includeScopeType = IncludeScopeType.ALL
        val pageSize = 10
        val pageNumber = 1
        val totalCount = 1
        val runtimeEnvKey = getString()
        val apREBindingData = ApREBindingData(
            id = getLong(), apREBindingTerm = ApREBindingTerm(
                allSupported = true
            ), runtimeEnvKey = runtimeEnvKey
        )
        val matchScopeData = manufacturePojo(MatchScopeData::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            targetId = apREBindingData.id!!,
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            externalId = "4#1_2"
        )
        val matchScopeDataDO = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            targetId = apREBindingData.id!!,
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
            externalId = "4#1_2"
        )
        every {
            matchScopeService.findMatchScopesByTargetAndExternalForProductLine(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                buId = buId, productFullLineIdPath = productLineFullPath
            )
        } returns listOf(matchScopeDataDO)

        every {
            apREBindingService.listApREBindingDataByProperties(
                site = site, stage = stage, unit = unit, ids = listOf(apREBindingData.id!!),
                pageSize = pageSize, pageNumber = pageNumber
            )
        } returns PageData(
            totalCount = totalCount.toLong(),
            pageNumber = pageNumber,
            pageSize = pageSize,
            data = listOf(apREBindingData)
        )

        val fullApRE = getComplexApREDTO().copy(runtimeEnvKey = runtimeEnvKey)
        every {
            apREService.listApREDetailsByApREKeys(listOf(runtimeEnvKey))
        } returns listOf(fullApRE)

        every {
            apREService.maskApRE(
                apREDO = fullApRE, any()
            )
        } returns fullApRE

        every {
            matchScopeService.convertMatchScopeData(matchScopeData)
        }returns matchScopeDataDO

        every {
            matchScopeService.getIncludeType(any(), any(), any(), any())
        }returns INHERIT

        val rs = apREAttorneyService.findAttorneyScopeByProperties(
            site = site, stage = stage, unit = unit, externalId = externalId, externalType = externalType,
            includeScopeType = includeScopeType, pageSize = pageSize, pageNumber = pageNumber
        )

        softly.assertThat(rs.externalId).isEqualTo(externalId)
        softly.assertThat(rs.externalType).isEqualTo(externalType)
        softly.assertThat(rs.pageData.pageSize).isEqualTo(pageSize)
        softly.assertThat(rs.pageData.totalCount).isEqualTo(1)
        softly.assertThat(rs.pageData.data?.get(0)?.attorney?.apREBindingData).isEqualTo(apREBindingData)
        softly.assertThat(rs.pageData.data?.get(0)?.attorney?.matchScopeData).isEqualTo(matchScopeDataDO)
        softly.assertThat(rs.pageData.data?.get(0)?.apREName).isEqualTo(fullApRE.name)
        softly.assertThat(rs.pageData.data?.get(0)?.supportedCluster?.size)
            .isEqualTo(fullApRE.resources.filter { it.clusterId != null }.size)
    }

    @Test
    fun findAttorneyByApREBindingDataIdTest(){
        val apREBindingDataId = getLong()
        val apREBindingData = manufacturePojo(ApREBindingData::class.java).copy(
            id = apREBindingDataId
        )
        val ms = manufacturePojo(MatchScopeDataDO::class.java).copy(
            targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
            targetId = apREBindingDataId,
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        )
        every {
            apREBindingService.findApREBindingData(apREBindingDataId)
        }returns apREBindingData
        every {
            matchScopeService.listByTarget(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                targetId = apREBindingDataId
            )
        }returns listOf(ms)
        val rs = apREAttorneyService.findAttorneyByApREBindingDataId(apREBindingDataId)
        softly.assertThat(rs?.apREBindingData).isEqualTo(apREBindingData)
        softly.assertThat(rs?.matchScopeData).isEqualTo(ms)
    }

    @Test
    fun findApRELabelFromApREAndResourcePoolWithDefaultTest() {
        val runtimeEnvKey = getString()

        val selfApRELabel = getApRELabelDO(
            APRE,
        ).copy(targetKey = runtimeEnvKey)

        val resourcePoolLabel = getApRELabelDO(
            RESOURCE_POOL
        ).copy(
            targetKey = runtimeEnvKey
        )

        val apREDefaultFeatures = mutableListOf(
            manufacturePojo(ApREDefaultFeatureDO::class.java)
        )

        val apREService = spyk(ApREService(objectMapper)) {
            every {
                apRELabelService.findApRELabelByTarget(
                    runtimeEnvKey, APRE.name
                )
            } returns mutableListOf(selfApRELabel)

            every {
                resourcePoolService.findApRERelativeResourcePoolLabelsByRuntimeEnvKey(
                    runtimeEnvKey,
                )
            } returns mutableListOf(resourcePoolLabel)

            every {
                apREDefaultFeatureService.findApREDefaultFeatureDetailWithDefaultImportUsage()
            } returns apREDefaultFeatures

            every {
                apREFeatureSpecService.convertDefaultFeatureSpec(any())
            } answers {
                callOriginal()
            }

            every {
                apREFeatureSpecService.transformToDefaultApREFeatureSpecDO(any())
            } answers {
                callOriginal()
            }

            every {
                apRELabelDefinitionService.fillApRELabelTitles(any())
            } just runs
        }

        val apRElabels = apREService.findApRELabelFromApREAndResourcePoolWithDefault(runtimeEnvKey)

        softly.assertThat(apRElabels.size).isEqualTo(3)
        softly.assertThat(apRElabels[0].targetType == APRE)
        softly.assertThat(apRElabels[1].targetType == RESOURCE_POOL)
        softly.assertThat(apRElabels[2].targetType == APRE)
    }

    /**
     * 测试过滤Coordinate来自动进行授权创建
     *
     */
    @Test
    fun createAttorneyApREByCoordinateTest() {
        val apRE = getComplexApREDTO()
        val site = apRE.az
        val unit = apRE.unit
        val stage = apRE.stage
        val externalId = getString()
        val externalType = MatchScopeExternalTypeEnum.APPLICATION.name
        val creator = getString()

        val apREBindingData = manufacturePojo(ApREBindingData::class.java).copy(
            id = getLong(),
            apREBindingTerm = manufacturePojo(ApREBindingTerm::class.java).copy(
                allSupported = true
            ),
            runtimeEnvKey = apRE.runtimeEnvKey!!
        )

        val msList = listOf(
            manufacturePojo(MatchScopeDataDO::class.java).copy(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                externalId = externalId,
                externalType = externalType,
                targetId = apREBindingData.id!!
            )
        )

        every {
            apREService.listBaseApREBySiteAndStageAndUnit(
                unit = unit, site = site, stage = stage
            )
        } returns listOf(apRE)

        every {
            matchScopeService.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ApREBindingData.name,
                externalId = externalId, externalType = externalType
            )
        } returns msList

        // 已经存在对应级别的授权
        every {
            apREBindingService.listApREBindingDataByIdList(
                listOf(apREBindingData.id!!)
            )
        } returns listOf(apREBindingData)

        apREAttorneyService.createAttorneyApREByCoordinate(
            unit = unit,
            site = site,
            stage = stage,
            creator = creator,
            externalId = externalId,
            externalTypeEnum = MatchScopeExternalTypeEnum.valueOf(externalType)
        )
    }

    private fun getApRELabelDO(apRELabelTargetTypeEnum: ApRELabelTargetTypeEnum): ApRELabelDO {
        return manufacturePojo(ApRELabelDO::class.java).copy(
            targetType = apRELabelTargetTypeEnum,
            apREFeatureSpecs = null,
            isDeleted = "N"
        )
    }

    private fun getComplexApREDTO(): ApREDO {
        return ApREDO(
            2911,
            getString(),
            getString(),
            "kostaline",
            "GfOvUwFDaydexVqs",
            "cn-zhangjiakou",
            "na610",
            "PUBLISH",
            "CENTER_UNIT.center",
            "ONLINE",
            null,
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N",
            listOf(
                ApRELabelDO(
                    null,
                    null,
                    ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                    "serverless/tpp",
                    null,
                    null,
                    null,
                    null,
                    listOf(
                        ApREFeatureSpecDO(
                            null,
                            null,
                            "serverless_runtime_1.0",
                            null,
                            null,
                            "publish",
                            "online",
                            "appstack_runtime",
                            "1111",
                            "appstack_version",
                            "1111",
                            "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            null,
                            null,
                            null,
                            null
                        ),
                        ApREFeatureSpecDO(
                            null,
                            null,
                            "serverless_runtime_1.0",
                            null,
                            null,
                            "publish",
                            "online",
                            "appstack_runtime",
                            "2222",
                            "appstack_version",
                            "2222",
                            "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            null,
                            null,
                            null,
                            null
                        )
                    ),
                    APRE,
                    SERVERLESS
                )
            ),
            emptyList()
        )
    }
}