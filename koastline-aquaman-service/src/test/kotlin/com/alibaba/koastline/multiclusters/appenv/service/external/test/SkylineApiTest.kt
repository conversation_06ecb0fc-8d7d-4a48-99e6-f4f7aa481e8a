package com.alibaba.koastline.multiclusters.appenv.service.external.test

import com.alibaba.ais.skyline.common.service.Result
import com.alibaba.ais.skyline.domain.search.response.ItemQueryResponse
import com.alibaba.fastjson.JSONArray
import com.alibaba.koastline.multiclusters.apre.params.MetadataStageEnum
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_APP_NAME
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_APP_SERVER_STATE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_CLUSTER_ID
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_CNT
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_IP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_NAMESPACE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_RESOURCE_GROUP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_RESOURCE_SUB_GROUP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_SITE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_SN
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_STAGE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_TAGS
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.SERVER_SERVERLESS_BACKUP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.SERVER_SERVERLESS_SURGE
import io.mockk.every
import io.mockk.spyk
import java.util.*
import kotlin.test.assertEquals
import org.junit.Test

class SkylineApiTest {

    @Test
    fun testListWorkloadMetadataConstraintThroughServerList_contains_surge() {
        val scheduleFacade = spyk<SkylineApi>() {
            every {
                skylineApi.queryServerByCondition(any(), any(), any(), any(), true, 1,
                    SkylineApi.QUERY_SERVER_LIMIT_NUM
                )
            } returns ItemQueryResponse().apply {
                this.isHasMore = false
                this.totalCount = 7
                this.itemList = listOf(
                    fakeCommonServer(),fakeCommonServer(),fakeCommonServer(),fakeCommonServer(),fakeCommonServer(),
                    fakeBackUpServer(),fakeBackUpServer(),fakeSurgeServer(),fakeSurgeServer()
                )
            }
        }
        val workloadMetadataConstraintAssembleList = scheduleFacade.listWorkloadMetadataConstraintThroughServerList(
            appName = "normandy-test-app4",
            envStackId = UUID.randomUUID().toString(),
            resourceFilteredTags = listOf(SERVER_SERVERLESS_SURGE)
        )
        assertEquals(5, workloadMetadataConstraintAssembleList[0].num)
    }

    @Test
    fun testListAppOfRuntimeBase() {
        val skylineApi = spyk<SkylineApi>() {
            every {
                skylineHttpClient!!.itemSearch().query(any())
            }returns Result.createSucc(ItemQueryResponse().apply {
                this.itemList = listOf(
                    mapOf(
                        FIELD_NAME_APP_NAME to "app1",
                        FIELD_NAME_APP_SERVER_STATE to "work_online",
                        FIELD_NAME_CNT to 10
                    ),
                    mapOf(
                        FIELD_NAME_APP_NAME to "app1",
                        FIELD_NAME_APP_SERVER_STATE to "work_offline",
                        FIELD_NAME_CNT to 15
                    ),
                    mapOf(
                        FIELD_NAME_APP_NAME to "app2",
                        FIELD_NAME_APP_SERVER_STATE to "work_online",
                        FIELD_NAME_CNT to 17
                    ),
                    mapOf(
                        FIELD_NAME_APP_NAME to "app3",
                        FIELD_NAME_APP_SERVER_STATE to "work_online",
                        FIELD_NAME_CNT to 19
                    )
                )
            })
        }
        val appResourceList = skylineApi.listAppOfRuntimeBase(listOf("runtime.1","runtime.2"))
        assertEquals(3, appResourceList.size)
        assertEquals("app1", appResourceList[0].appName)
        assertEquals(25, appResourceList[0].totalResourceNum)
        assertEquals(2, appResourceList[0].appResourceStatusList.size)

        assertEquals("app2", appResourceList[1].appName)
        assertEquals(17, appResourceList[1].totalResourceNum)
        assertEquals(1, appResourceList[1].appResourceStatusList.size)

        assertEquals("app3", appResourceList[2].appName)
        assertEquals(19, appResourceList[2].totalResourceNum)
        assertEquals(1, appResourceList[2].appResourceStatusList.size)
    }

    @Test
    fun testFilterIpsByServerProperties() {
        val skylineApi = spyk<SkylineApi>() {
            every {
                skylineHttpClient!!.itemSearch().query(any())
            }returns Result.createSucc(ItemQueryResponse().apply {
                this.itemList = listOf(
                    mapOf(
                        FIELD_NAME_IP to "***********",
                        FIELD_NAME_STAGE to "SMALLFLOW",
                        FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.center")),
                    ),
                    mapOf(
                        FIELD_NAME_IP to "***********",
                        FIELD_NAME_STAGE to "SMALLFLOW",
                        FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.center")),
                    ),
                    mapOf(
                        FIELD_NAME_IP to "***********",
                        FIELD_NAME_STAGE to "SMALLFLOW",
                        FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.unsh")),
                    )
                )
            })
        }
        val ips = skylineApi.filterIpsByServerProperties(listOf("***********","***********","***********"), MetadataStageEnum.SMALLFLOW, listOf("CENTER_UNIT.center"))
        assertEquals(2, ips.size)
        assertEquals("***********", ips[0])
        assertEquals("***********", ips[1])
    }

    private fun fakeCommonServer(): Map<String, Any> {
        return mapOf(
            FIELD_NAME_IP to "***********",
            FIELD_NAME_SN to UUID.randomUUID().toString(),
            FIELD_NAME_RESOURCE_GROUP to FIELD_NAME_RESOURCE_GROUP,
            FIELD_NAME_SITE to "na610",
            FIELD_NAME_STAGE to "PUBLISH",
            FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.center")),
            FIELD_NAME_CLUSTER_ID to "zjk_core_a01",
            FIELD_NAME_RESOURCE_SUB_GROUP to "default",
            FIELD_NAME_NAMESPACE to "normandy-test-app4",
        )
    }

    private fun fakeBackUpServer(): Map<String, Any> {
        return mapOf(
            FIELD_NAME_IP to "***********",
            FIELD_NAME_SN to UUID.randomUUID().toString(),
            FIELD_NAME_RESOURCE_GROUP to FIELD_NAME_RESOURCE_GROUP,
            FIELD_NAME_SITE to "na610",
            FIELD_NAME_STAGE to "PUBLISH",
            FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.center", SERVER_SERVERLESS_BACKUP)),
            FIELD_NAME_CLUSTER_ID to "zjk_core_a01",
            FIELD_NAME_RESOURCE_SUB_GROUP to "default",
            FIELD_NAME_NAMESPACE to "normandy-test-app4",
        )
    }

    private fun fakeSurgeServer(): Map<String, Any> {
        return mapOf(
            FIELD_NAME_IP to "***********",
            FIELD_NAME_SN to UUID.randomUUID().toString(),
            FIELD_NAME_RESOURCE_GROUP to FIELD_NAME_RESOURCE_GROUP,
            FIELD_NAME_SITE to "na610",
            FIELD_NAME_STAGE to "PUBLISH",
            FIELD_NAME_TAGS to JSONArray(listOf("CENTER_UNIT.center", SERVER_SERVERLESS_SURGE)),
            FIELD_NAME_CLUSTER_ID to "zjk_core_a01",
            FIELD_NAME_RESOURCE_SUB_GROUP to "default",
            FIELD_NAME_NAMESPACE to "normandy-test-app4",
        )
    }
}