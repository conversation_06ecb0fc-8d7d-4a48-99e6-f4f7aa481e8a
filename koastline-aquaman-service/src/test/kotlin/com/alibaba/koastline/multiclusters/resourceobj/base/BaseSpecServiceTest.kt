package com.alibaba.koastline.multiclusters.resourceobj.base

import io.kubernetes.client.openapi.models.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class BaseSpecServiceTest {

    private val baseSpecService = BaseSpecService()

    @Test
    fun `patchAffinityAndTolerations should remove is-ecs affinity and add is-ecs toleration`() {
        // Given
        val podTemplateSpec = V1PodTemplateSpec().apply {
            spec = V1PodSpec().apply {
                affinity = V1Affinity().apply {
                    nodeAffinity = V1NodeAffinity().apply {
                        requiredDuringSchedulingIgnoredDuringExecution = V1NodeSelector().apply {
                            nodeSelectorTerms = mutableListOf(
                                V1NodeSelectorTerm().apply {
                                    matchExpressions = mutableListOf(
                                        V1NodeSelectorRequirement().apply {
                                            key = "sigma.ali/is-ecs"
                                            operator = "In"
                                            values = listOf("true")
                                        },
                                        V1NodeSelectorRequirement().apply {
                                            key = "another-key"
                                            operator = "In"
                                            values = listOf("value")
                                        }
                                    )
                                }
                            )
                        }
                    }
                }
            }
        }

        // When
        baseSpecService.patchAffinityAndTolerations(podTemplateSpec)

        // Then
        // Verify is-ecs affinity is removed
        val matchExpressions = podTemplateSpec.spec!!.affinity!!.nodeAffinity!!
            .requiredDuringSchedulingIgnoredDuringExecution!!.nodeSelectorTerms[0].matchExpressions
        assertEquals(1, matchExpressions!!.size)
        assertEquals("another-key", matchExpressions[0].key)

        // Verify is-ecs toleration is added
        val tolerations = podTemplateSpec.spec!!.tolerations
        assertEquals(1, tolerations!!.size)
        assertEquals("sigma.ali/is-ecs", tolerations[0].key)
        assertEquals("Exists", tolerations[0].operator)
    }

    @Test
    fun `patchAffinityAndTolerations should handle null affinity`() {
        // Given
        val podTemplateSpec = V1PodTemplateSpec().apply {
            spec = V1PodSpec()
        }

        // When
        baseSpecService.patchAffinityAndTolerations(podTemplateSpec)

        // Then
        val tolerations = podTemplateSpec.spec!!.tolerations
        assertEquals(1, tolerations!!.size)
        assertEquals("sigma.ali/is-ecs", tolerations[0].key)
        assertEquals("Exists", tolerations[0].operator)
    }

    @Test
    fun `patchAffinityAndTolerations should handle existing tolerations`() {
        // Given
        val podTemplateSpec = V1PodTemplateSpec().apply {
            spec = V1PodSpec().apply {
                tolerations = mutableListOf(
                    V1Toleration().apply {
                        key = "existing-key"
                        operator = "Equal"
                        value = "value"
                    }
                )
            }
        }

        // When
        baseSpecService.patchAffinityAndTolerations(podTemplateSpec)

        // Then
        val tolerations = podTemplateSpec.spec!!.tolerations
        assertEquals(2, tolerations!!.size)
        assertTrue(tolerations.any { it.key == "existing-key" })
        assertTrue(tolerations.any { it.key == "sigma.ali/is-ecs" })
    }

    @Test
    fun `patchAffinityAndTolerations should handle existing same ecs toleration`() {
        // Given
        val podTemplateSpec = V1PodTemplateSpec().apply {
            spec = V1PodSpec().apply {
                tolerations = mutableListOf(
                    V1Toleration().apply {
                        key = "sigma.ali/is-ecs"
                        operator = "Exists"
                    }
                )
            }
        }

        // When
        baseSpecService.patchAffinityAndTolerations(podTemplateSpec)

        // Then
        val tolerations = podTemplateSpec.spec!!.tolerations
        assertEquals(1, tolerations!!.size)
        assertTrue(tolerations.any { it.key == "sigma.ali/is-ecs" })
        assertTrue(tolerations.any { it.operator == "Exists" })
    }

    @Test
    fun `patchAffinityAndTolerations should handle existing ecs toleration but different operator`() {
        // Given
        val podTemplateSpec = V1PodTemplateSpec().apply {
            spec = V1PodSpec().apply {
                tolerations = mutableListOf(
                    V1Toleration().apply {
                        key = "sigma.ali/is-ecs"
                        operator = "Equal"
                        value = "true"
                    }
                )
            }
        }

        // When
        baseSpecService.patchAffinityAndTolerations(podTemplateSpec)

        // Then
        val tolerations = podTemplateSpec.spec!!.tolerations
        assertEquals(2, tolerations!!.size)
        assertTrue(tolerations.any { it.operator == "Exists" })
        assertTrue(tolerations.any { it.operator == "Equal" })
    }
}