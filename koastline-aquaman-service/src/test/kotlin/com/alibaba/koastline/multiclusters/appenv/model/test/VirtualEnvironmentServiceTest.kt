package com.alibaba.koastline.multiclusters.appenv.model.test

import junit.framework.Assert.assertEquals
import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * <AUTHOR>
 */
class VirtualEnvironmentServiceTest {
    @Test
    fun createVirtualEnvironmentTest() {
        assertEquals(true, true)
    }

    @Test
    fun testRex() {
        val reg = "[_.]".toRegex()
        assertTrue { reg.containsMatchIn("center.unit") }
        assertTrue { reg.containsMatchIn("center_unit") }
        assertTrue { reg.containsMatchIn("center_unit.center") }
        assertFalse { reg.containsMatchIn("center-unit") }
    }
}