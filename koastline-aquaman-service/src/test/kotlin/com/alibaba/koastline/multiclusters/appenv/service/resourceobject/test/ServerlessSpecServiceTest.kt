package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.apre.model.ServerlessBaseAppOfStackExtraParams
import com.alibaba.koastline.multiclusters.apre.model.StackServerlessBaseAppBindingDataDO
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.external.model.CmdbStack
import com.alibaba.koastline.multiclusters.external.model.ServerlessBaseAppInfo
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.ServerlessSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.WorkloadSpecContext
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum.JSON
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import testutils.BaseTest

class ServerlessSpecServiceTest: BaseTest() {

    @Test
    fun testGetBaseSpec() {
        val now = Date(Instant.now().toEpochMilli())
        val context = makeFakeWorkloadSpecContext()
        val serverlessSpecService = spyk(ServerlessSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                getEnvBaselineJarUrl(any())
            } returns "http://ossproxy.XXXXXX"
            every {
                getEnvBaselineScenePlan(any())
            } returns "customSystemProperties:\n" +
                    "  tttf: eee\n" +
                    "  key: value\n" +
                    "  spring.profiles.active: staging,staging1\n" +
                    "onlineOfflineProperties:\n" +
                    "  customOnlineProperties:\n" +
                    "  - path: /postStart.htm\n" +
                    "    port: 7002\n" +
                    "    timeoutSeconds: 1\n" +
                    "    periodSeconds: 5\n" +
                    "    failureThreshold: 10\n" +
                    "    successThreshold: 2\n" +
                    "  customOfflineProperties:\n" +
                    "  - path: /preStop.htm\n" +
                    "    port: 7001\n" +
                    "    timeoutSeconds: 1\n" +
                    "    periodSeconds: 5\n" +
                    "    failureThreshold: 10\n" +
                    "    successThreshold: 2"
            every {
                defaultClusterService.getSimpleClusterProfileDataByClusterId(context.workloadMetadataConstraint.clusterId)
            } returns ClusterProfileData(
                id = 1L,
                clusterId = context.workloadMetadataConstraint.clusterId,
                clusterName = "cluster_a01",
                clusterProvider = "alibaba",
                clusterType = "alibaba-asi",
                region = "alibaba",
                site = "na610",
                useType = "publish",
                gmtCreate = now,
                gmtModified = now,
                isDeleted = "N",
                creator = "SYSTEM_ADMIN",
                modifier = "SYSTEM_ADMIN",
            )
        }
        val specObj = serverlessSpecService.getBaseSpec(
            context
        )
        assertEquals("""
            {"app":"normandy-test-app4","group":"normandy-test-app4_prehost","k8sId":"cluster_a01","site":"na610","unit":"CENTER_UNIT.center","stage":"PUBLISH","runtimeId":"runtime_01","jarURI":"http://ossproxy.XXXXXX","pauseMode":0,"serviceConfigs":[{"name":"skylineReg","masked":false,"async":true,"type":11,"configStr":"{\"host\":\"intra-sky.alibaba-inc.com\",\"appUseType\":\"PUBLISH\",\"group\":\"normandy-test-app4_prehost\",\"buffGroup\":\"c2_app_buffer\",\"hostnameTemplate\":\"normandy-test-app4{{.IpAddress}}.center.na610\"}"}],"customSystemProperties":{"tttf":"eee","key":"value","spring.profiles.active":"staging,staging1"},"onlineOfflineProperties":{"customOnlineProperties":[{"path":"/postStart.htm","port":"7002","timeoutSeconds":1,"periodSeconds":5,"failureThreshold":10,"successThreshold":2}],"customOfflineProperties":[{"path":"/preStop.htm","port":"7001","timeoutSeconds":1,"periodSeconds":5,"failureThreshold":10,"successThreshold":2}]}}
        """.trimIndent(),
        JsonUtils.writeValueAsString(specObj))
    }

    @Test
    fun `testGetDeploySpec while has serverless base migration`() {
        val stackPKId = getString()
        val resourceObjectSpec = makeCurrentResourceObject()
        val originalWorkloadMetadataConstraint = makeFakeWorkloadSpecContext().workloadMetadataConstraint
        val clusterProfileData = manufacturePojo(ClusterProfileData::class.java)
        val serverlessBaseAppInfo = ServerlessBaseAppInfo(
            baseAppName = getString(),
            baseEnvStackId = getString()
        )
        val targetWorkloadMetadataConstraint = originalWorkloadMetadataConstraint.copy(
            runtimeId = getString(),
            workloadName = "aone-upgrade-serverless-test03.e0722203eee57129"
        )
        val serverlessSpecService = spyk(ServerlessSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                getEnvBaselineJarUrlByStackPkId(
                    stackPkId = stackPKId
                )
            } returns "http://ossproxy.XXXXXX"
            every {
                getScenePlanByStackPkId(
                    stackPkId = stackPKId
                )
            } returns "customSystemProperties:\n" +
                    "  tttf: eee\n" +
                    "  key: value\n" +
                    "  spring.profiles.active: staging,staging1\n" +
                    "onlineOfflineProperties:\n" +
                    "  customOnlineProperties:\n" +
                    "  - path: /postStart.htm\n" +
                    "    port: 7002\n" +
                    "    timeoutSeconds: 1\n" +
                    "    periodSeconds: 5\n" +
                    "    failureThreshold: 10\n" +
                    "    successThreshold: 2\n" +
                    "  customOfflineProperties:\n" +
                    "  - path: /preStop.htm\n" +
                    "    port: 7001\n" +
                    "    timeoutSeconds: 1\n" +
                    "    periodSeconds: 5\n" +
                    "    failureThreshold: 10\n" +
                    "    successThreshold: 2"
            every {
                getAoneServerlessBaseAppInfo(
                    stackPkId = stackPKId
                )
            } returns serverlessBaseAppInfo
            every {
                defaultClusterService.getSimpleClusterProfileDataByClusterId(targetWorkloadMetadataConstraint.clusterId)
            } returns clusterProfileData
            every {
                serverlessRunningScheduleService.seekServerlessAppMigrationBaseApp(
                    originalWorkloadMetadataConstraint = originalWorkloadMetadataConstraint.copy(
                        runtimeId = "pro-runtime-test01.102565",
                        workloadName = "aone-upgrade-serverless-test03.e0722203eee57129"
                    ),
                    migrationToBaseApp = serverlessBaseAppInfo.baseAppName,
                    migrationToEnvStackId = serverlessBaseAppInfo.baseEnvStackId
                )
            } returns targetWorkloadMetadataConstraint
        }
        val specObj = serverlessSpecService.getDeploySpec(
            stackPkId = stackPKId,
            currentResourceObjectSpecStr = resourceObjectSpec,
            currentResourceObjectFormatEnum = JSON,
            workloadMetadataConstraint = originalWorkloadMetadataConstraint
        )
        assertEquals("aone-upgrade-serverless-test03.e0722203eee57129",specObj["workloadName"])
        assertEquals("normandy-test-app4",specObj["app"])
        assertEquals(clusterProfileData.clusterName,specObj["k8sId"])
        assertEquals("na610",specObj["site"])
        assertEquals("normandy-test-app4_prehost",specObj["group"])
        assertEquals("CENTER_UNIT.center",specObj["unit"])
        assertEquals("PUBLISH",specObj["stage"])
        assertEquals(targetWorkloadMetadataConstraint.runtimeId,specObj["runtimeId"])
        assertEquals("http://ossproxy.XXXXXX",specObj["jarURI"])
        assertEquals("value",(specObj["customSystemProperties"] as Map<String, String>)["key"])
    }

    @Test
    fun `test when base app info is found by CMDB`() {
        // Mock the API response
        val expectedBaseAppInfo = ServerlessBaseAppInfo("expectedAppName", "expectedEnvStackId")
        val stackPKId = getString()
        val serverlessSpecService = spyk(ServerlessSpecService()) {
            every {
                cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(stackPKId)
            } returns expectedBaseAppInfo
        }
        // Call the method under test
        val actualBaseAppInfo = serverlessSpecService.getAoneServerlessBaseAppInfo(stackPKId)

        // Verify the result
        assertEquals(expectedBaseAppInfo, actualBaseAppInfo)
    }

    @Test
    fun `test when base app info is found through binding service`() {
        val stackPKId = getString()
        val stackWithBox = manufacturePojo(CmdbStack::class.java)
        val stackServerlessBaseAppBindingDataDO = manufacturePojo(StackServerlessBaseAppBindingDataDO::class.java).copy(
            serverlessBaseAppName = getString(),
            extraParams = ServerlessBaseAppOfStackExtraParams(
                runtimeBaseEnvStackId = getString()
            )
        )
        val serverlessSpecService = spyk(ServerlessSpecService()) {
            every {
                cloudCmdbApi.getBindingServerlessBaseAppInfoByStackPKId(stackPKId)
            } returns null
            every {
                cloudCmdbApi.getStackWithBox(stackPKId)
            } returns stackWithBox
            every {
                stackServerlessBaseAppBindingService.getStackServerlessBaseAppBindingData(stackWithBox.categoryId)
            } returns stackServerlessBaseAppBindingDataDO
        }
        // Call the method under test
        val actualBaseAppInfo = serverlessSpecService.getAoneServerlessBaseAppInfo(stackPKId)

        // Verify the result
        assertEquals(stackServerlessBaseAppBindingDataDO.serverlessBaseAppName, actualBaseAppInfo!!.baseAppName)
        assertEquals(stackServerlessBaseAppBindingDataDO.extraParams!!.runtimeBaseEnvStackId, actualBaseAppInfo!!.baseEnvStackId)
    }

    private fun makeFakeWorkloadSpecContext(): WorkloadSpecContext {
        return WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na610",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "normandy-test-app4",
                runtimeId = "runtime_01"
            ),
            envStackId = "324a215d6bec6bffc99895f0d8fe5580",
            ResourceObjectProtocolEnum.ServerlessApp
        )
    }

    private fun makeCurrentResourceObject() : String {
        return """
            {
                "distrPlan":{
                    "control":{
                        "stepDelaySec":0,
                        "stepCount":0
                    },
                    "location":{
                        "label":"pro-runtime-test01.102565",
                        "priority":0
                    }
                },
                "globalPlan":{
                    "count":5,
                    "deferStop":5000,
                    "latestVersionRatio":10,
                    "maxSurge":100,
                    "minHealthCapacity":10,
                    "pauseRolling":false,
                    "requirePreload":false,
                    "serviceConfigs":[
                        {
                            "async":true,
                            "configStr":"{\"host\":\"intra-sky.alibaba-inc.com\",\"appUseType\":\"PRE_PUBLISH\",\"group\":\"aone-upgrade-serverless-test03_svlZexing_prehost\",\"buffGroup\":\"c2_app_buffer\"}",
                            "masked":false,
                            "name":"skylineReg",
                            "type":11
                        },
                        {
                            "async":false,
                            "configStr":"{\"cm2_server_zookeeper_host\":\"search-zk-cm2-serverless-na61.alibaba-inc.com:2187\",\"cm2_server_leader_path\":\"/cm_server_common\",\"cm2_server_cluster_name\":\"com.alibaba.glaucus.scene.RecEngineService.36191.PRE_PUBLISH\",\"tcpPort\":0,\"httpPort\":9420}",
                            "masked":false,
                            "name":"cm2",
                            "type":1
                        }
                    ],
                    "stopNodeTimeout":0,
                    "waitSuccessTimeout":600000,
                    "warmupTime":5000
                },
                "name":"aone-upgrade-serverless-test03.e0722203eee57129",
                "versionPlan":{
                    "resourcePlan":{
                        "loc":{
                            "group":"pro-runtime-test01.102565",
                            "role":"pro-runtime-test01.102565"
                        }
                    }
                }
            }
        """.trimIndent()
    }
}