package com.alibaba.koastline.multiclusters.apre.model

import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue


/**
 *
 * Date: 2023-02-21 Time: 22:41
 *
 * <AUTHOR>
 */
class RestrictionTest {

    @Test
    fun identity() {
        val r1 = Restriction("na610", "DAILY", "center", listOf("b", "a"), "XXXXXX")
        assertEquals("XXXXXX#center#na610#DAILY#a,b", r1.identity())
    }

    @Test
    fun `restriction list equalsIgnoreOrder`() {
        val restrictionList1 = listOf(
            Restriction("na610", "DAILY", "center", listOf("b", "a")),
            Restriction("na620", "DAILY", "center", listOf("a", "b")),
        )
        val restrictionList2 = listOf(
            Restriction("na620", "DAILY", "center", listOf("b", "a")),
            Restriction("na610", "DAILY", "center", listOf("a", "b")),
        )
        assertTrue(restrictionList1.equalsIgnoreOrder(restrictionList2))
        assertTrue(restrictionList2.equalsIgnoreOrder(restrictionList1))
    }

    @Test
    fun `restriction list equalsIgnoreOrder - not equal`() {
        val restrictionList1 = listOf(
            Restriction("na610", "DAILY", "center", listOf("b", "c")),
            Restriction("na620", "DAILY", "center", listOf("a", "b")),
        )
        val restrictionList2 = listOf(
            Restriction("na620", "DAILY", "center", listOf("b", "a")),
            Restriction("na610", "DAILY", "center", listOf("a", "b")),
        )
        assertFalse(restrictionList1.equalsIgnoreOrder(restrictionList2))
        assertFalse(restrictionList2.equalsIgnoreOrder(restrictionList1))
    }
}