package com.alibaba.koastline.multiclusters.appenv.utils.test

import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.common.utils.Base64Utils
import org.junit.Test
import kotlin.test.assertEquals

class Base64UtilsTest {

    @Test
    fun testBase64() {
        val source = ApREDeedDO(
            "aaaaaaaaa",
            null,
            null
        )
        val content = Base64Utils.encode(source)
        val target = Base64Utils.decode<ApREDeedDO>(content)
        assertEquals(source, target)
    }
}