package com.alibaba.koastline.multiclusters.appenv.service.external.test

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.external.AppCenterApi
import com.alibaba.koastline.multiclusters.external.model.AppTag
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.spyk
import kotlin.test.assertEquals
import org.junit.Test

/**
 * @author:    <EMAIL>
 * @description:  TODO
 * @date:    2023/12/2 3:11 PM
 */
class AppCenterApiTest {

    @Test
    fun testGetTestingResourceSpec() {
        val appName = "normandy-test-app4"
        val appCenterApi = spyk(AppCenterApi(ObjectMapper())) {
            every {
                getAppTagByAppName(appName, "testing_spec")
            }returns AppTag(
                id = 1L,
                appId = 110,
                tagKey = "testing_spec",
                tagValue = "4-8192-60"
            )
        }
        val resourceSpec = appCenterApi.getTestingResourceSpec(appName)
        assertEquals("4", resourceSpec!!.cpu)
        assertEquals("8", resourceSpec!!.memory)
        assertEquals("60", resourceSpec!!.disk)
    }

    @Test
    fun testGetAppInfoByNameV2() {
        val appName = "canfeng-test-927"
        val appCenterApi = spyk(AppCenterApi(JsonUtils.getObjectMapper())).also {
            it.appCenterHost = "mock"
            it.appCenterAccount = "mock"
            it.appCenterAccessKey = "mock"
        }
        mockkObject(HttpClientUtils)
        every {
            HttpClientUtils.httpGet(
                any(),
                any(),
                any()
            )
        } returns "{\"object\":{\"buildPlatform\":\"aone\",\"codeConfigs\":[{\"createType\":\"create\",\"creator\":\"368136\",\"deployType\":\"APP\",\"gmtCreate\":*************,\"gmtModified\":*************,\"id\":********,\"isDeleted\":\"N\",\"modifier\":\"368136\",\"name\":\"canfeng-test-927\",\"objectId\":263970,\"objectType\":\"Application\",\"properties\":{}}],\"creator\":\"368136\",\"gmtCreate\":*************,\"gmtModified\":*************,\"id\":263970,\"imageSource\":\"external_build\",\"isDeleted\":\"N\",\"modifier\":\"368136\",\"name\":\"canfeng-test-927\",\"owner\":{\"email\":\"<EMAIL>\",\"name\":\"残风\",\"staffId\":\"368136\"},\"productLine\":{\"fullIdPath\":\"24330_24731\",\"fullNamePath\":\"研发效能-研发基础设施平台/CanfengTest\",\"gmtCreate\":1655122834000,\"gmtModified\":1702347780000,\"id\":24731,\"isDeleted\":\"N\",\"modifier\":\"366815\",\"name\":\"CanfengTest\",\"parentId\":24330,\"rootId\":155,\"rootName\":\"集团基础设施\",\"userRoleMappings\":{}},\"publishPlatform\":\"aol\",\"status\":\"READY_TO_ONLINE\",\"subtype\":{\"subtype\":\"ALGORITHM_MODEL\",\"type\":\"Application\"},\"tags\":{\"techStackType\":{\"gmtCreate\":1730701875000,\"gmtModified\":1730701875000,\"id\":760662,\"isDeleted\":\"N\",\"key\":\"techStackType\",\"objectId\":\"263970\",\"objectType\":\"Application\",\"publicLevel\":1,\"sysName\":\"app-center\",\"value\":\"framework\",\"writerSysName\":\"app-center\"},\"imageSource\":{\"gmtCreate\":1730701875000,\"gmtModified\":1730701875000,\"id\":760661,\"isDeleted\":\"N\",\"key\":\"imageSource\",\"objectId\":\"263970\",\"objectType\":\"Application\",\"publicLevel\":1,\"sysName\":\"app-center\",\"value\":\"external_build\",\"writerSysName\":\"app-center\"},\"devStageEnable\":{\"creator\":\"368136\",\"gmtCreate\":1730703254000,\"gmtModified\":1730703254000,\"id\":760665,\"isDeleted\":\"N\",\"key\":\"devStageEnable\",\"modifier\":\"368136\",\"objectId\":\"263970\",\"objectType\":\"Application\",\"publicLevel\":1,\"sysName\":\"aone-mix\",\"value\":\"true\",\"writerSysName\":\"aone-mix\"}},\"techStackType\":\"framework\",\"userRoleMappings\":{\"ao.biz.ops\":[{\"email\":\"<EMAIL>\",\"name\":\"残风\",\"staffId\":\"368136\"}],\"ao.app.ops\":[{\"email\":\"<EMAIL>\",\"name\":\"残风\",\"staffId\":\"368136\"},{\"email\":\"<EMAIL>\",\"name\":\"东幕\",\"staffId\":\"434934\"},{\"email\":\"<EMAIL>\",\"name\":\"张振东\",\"staffId\":\"WB381477\"},{\"email\":\"<EMAIL>\",\"name\":\"荀易\",\"staffId\":\"356446\"},{\"email\":\"<EMAIL>\",\"name\":\"幻唐\",\"staffId\":\"117238\"},{\"email\":\"<EMAIL>\",\"name\":\"沙羽\",\"staffId\":\"241867\"},{\"email\":\"<EMAIL>\",\"name\":\"王娜娜\",\"staffId\":\"WB01777702\"},{\"email\":\"<EMAIL>\",\"name\":\"安辰\",\"staffId\":\"203221\"},{\"email\":\"<EMAIL>\",\"name\":\"万鹳\",\"staffId\":\"77454\"},{\"email\":\"<EMAIL>\",\"name\":\"苄卡\",\"staffId\":\"369655\"},{\"email\":\"<EMAIL>\",\"name\":\"忱明\",\"staffId\":\"326345\"},{\"email\":\"<EMAIL>\",\"name\":\"木理\",\"staffId\":\"137285\"}],\"ao.dev.tl\":[{\"email\":\"<EMAIL>\",\"name\":\"王娜娜\",\"staffId\":\"WB01777702\"},{\"email\":\"<EMAIL>\",\"name\":\"安辰\",\"staffId\":\"203221\"},{\"email\":\"<EMAIL>\",\"name\":\"万鹳\",\"staffId\":\"77454\"},{\"email\":\"<EMAIL>\",\"name\":\"残风\",\"staffId\":\"368136\"},{\"email\":\"<EMAIL>\",\"name\":\"东幕\",\"staffId\":\"434934\"},{\"email\":\"<EMAIL>\",\"name\":\"荀易\",\"staffId\":\"356446\"},{\"email\":\"<EMAIL>\",\"name\":\"幻唐\",\"staffId\":\"117238\"},{\"email\":\"<EMAIL>\",\"name\":\"忱明\",\"staffId\":\"326345\"},{\"email\":\"<EMAIL>\",\"name\":\"沙羽\",\"staffId\":\"241867\"},{\"email\":\"<EMAIL>\",\"name\":\"海丹\",\"staffId\":\"10873\"},{\"email\":\"<EMAIL>\",\"name\":\"木理\",\"staffId\":\"137285\"}],\"ao.codereviewer\":[{\"email\":\"<EMAIL>\",\"name\":\"东幕\",\"staffId\":\"434934\"}],\"ao.pe\":[{\"email\":\"<EMAIL>\",\"name\":\"残风\",\"staffId\":\"368136\"},{\"email\":\"<EMAIL>\",\"name\":\"东幕\",\"staffId\":\"434934\"},{\"email\":\"<EMAIL>\",\"name\":\"张振东\",\"staffId\":\"WB381477\"},{\"email\":\"<EMAIL>\",\"name\":\"荀易\",\"staffId\":\"356446\"},{\"email\":\"<EMAIL>\",\"name\":\"幻唐\",\"staffId\":\"117238\"},{\"email\":\"<EMAIL>\",\"name\":\"沙羽\",\"staffId\":\"241867\"},{\"email\":\"<EMAIL>\",\"name\":\"王娜娜\",\"staffId\":\"WB01777702\"},{\"email\":\"<EMAIL>\",\"name\":\"安辰\",\"staffId\":\"203221\"},{\"email\":\"<EMAIL>\",\"name\":\"万鹳\",\"staffId\":\"77454\"},{\"email\":\"<EMAIL>\",\"name\":\"忱明\",\"staffId\":\"326345\"},{\"email\":\"<EMAIL>\",\"name\":\"木理\",\"staffId\":\"137285\"}],\"ao.intg.ld\":[{\"email\":\"<EMAIL>\",\"name\":\"王娜娜\",\"staffId\":\"WB01777702\"},{\"email\":\"<EMAIL>\",\"name\":\"安辰\",\"staffId\":\"203221\"},{\"email\":\"<EMAIL>\",\"name\":\"万鹳\",\"staffId\":\"77454\"},{\"email\":\"<EMAIL>\",\"name\":\"东幕\",\"staffId\":\"434934\"},{\"email\":\"<EMAIL>\",\"name\":\"荀易\",\"staffId\":\"356446\"},{\"email\":\"<EMAIL>\",\"name\":\"忱明\",\"staffId\":\"326345\"},{\"email\":\"<EMAIL>\",\"name\":\"沙羽\",\"staffId\":\"241867\"},{\"email\":\"<EMAIL>\",\"name\":\"木理\",\"staffId\":\"137285\"}],\"ao.dba\":[{\"email\":\"<EMAIL>\",\"name\":\"屹峡\",\"staffId\":\"436665\"},{\"email\":\"<EMAIL>\",\"name\":\"长莫\",\"staffId\":\"437546\"},{\"email\":\"<EMAIL>\",\"name\":\"相勖\",\"staffId\":\"318811\"}]}},\"successful\":true}"
        val appInfo = appCenterApi.getAppInfoByNameV2(appName)
        assertEquals("ALGORITHM_MODEL", appInfo.subtype.subtype.name)
    }
}