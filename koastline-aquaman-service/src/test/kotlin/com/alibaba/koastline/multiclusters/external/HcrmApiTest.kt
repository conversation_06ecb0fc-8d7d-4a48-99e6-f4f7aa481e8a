package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import com.alibaba.koastline.multiclusters.external.model.UnitGroup
import com.alibaba.koastline.multiclusters.external.model.UnitLocation
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockkObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * <AUTHOR>
 * date 2024/4/22 15:35
 */
class HcrmApiTest {

    @Before
    fun setUpMockK() = MockKAnnotations.init(this, relaxed = true)

    @Test
    fun testListUnitizationUnitGroup() {
        mockkObject(HttpClientUtils)
        every {
            HttpClientUtils.httpGetWithHeaders(
                url = any(), headers = any(), params = any())
        } returns "{\n" +
                "    \"traceId\": \"2150042d17137716250658606e1618\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": 1,\n" +
                "            \"unitType\": \"trade\",\n" +
                "            \"description\": \"电商交易单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通在线+混部部署；适合电商交易类应用，切流规则为交易单元化用户切流规则，详见https://www.atatech.org/articles/136782\\n以下机房+单元为大促混部单元，仅在618、双11、双12大促期间启用：\\nNA62-CENTER_UNIT.unzbmix25g、EA118--CENTER_UNIT.une1、EA118--CENTER_UNIT.unn1\\n\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"张北\\\",\\\"id\\\":2,\\\"idc\\\":\\\"na620\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"},{\\\"area\\\":\\\"张北\\\",\\\"id\\\":3,\\\"idc\\\":\\\"na610\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"}]\",\n" +
                "            \"status\": \"EFFECTED\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 2,\n" +
                "            \"unitType\": \"guide\",\n" +
                "            \"description\": \"电商导购单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通两地在线+混部单元部署，导购类业务如推荐业务，与交易下单无关\\n与交易单元化同样使用unzbmix25g/unn1/une1三个离在线混部单元。\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"南通\\\",\\\"id\\\":14,\\\"idc\\\":\\\"ea119\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.unsh\\\",\\\"unitType\\\":\\\"guide\\\"},{\\\"area\\\":\\\"南通\\\",\\\"id\\\":15,\\\"idc\\\":\\\"ea118\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.une1\\\",\\\"unitType\\\":\\\"guide\\\"}]\",\n" +
                "            \"status\": \"EFFECTED\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"code\": 0,\n" +
                "    \"success\": true,\n" +
                "    \"message\": null\n" +
                "}"
        val result = HcrmApi("", "", "")
            .listUnitizationUnitGroup("test-app", "test-group")
        Assert.assertEquals(2, result.size)
        Assert.assertEquals(UnitGroup(unitType = "trade", description = "电商交易单元化", unitLocationList = listOf(
            UnitLocation("na620", "CENTER_UNIT.center", "trade", "true"),
            UnitLocation("na610", "CENTER_UNIT.center", "trade", "true"),
        )), result[0])
        Assert.assertEquals(UnitGroup(unitType = "guide", description = "电商导购单元化", unitLocationList = listOf(
            UnitLocation("ea119", "CENTER_UNIT.unsh", "guide", "true"),
            UnitLocation("ea118", "CENTER_UNIT.une1", "guide", "true"),
        )), result[1])
    }

    @Test
    fun `testListUnitizationUnitLocation while filter one unit group`() {
        mockkObject(HttpClientUtils)
        every {
            HttpClientUtils.httpGetWithHeaders(
                url = any(), headers = any(), params = any())
        } returns "{\n" +
                "    \"traceId\": \"2150042d17137716250658606e1618\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": 1,\n" +
                "            \"unitType\": \"trade\",\n" +
                "            \"description\": \"电商交易单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通在线+混部部署；适合电商交易类应用，切流规则为交易单元化用户切流规则，详见https://www.atatech.org/articles/136782\\n以下机房+单元为大促混部单元，仅在618、双11、双12大促期间启用：\\nNA62-CENTER_UNIT.unzbmix25g、EA118--CENTER_UNIT.une1、EA118--CENTER_UNIT.unn1\\n\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"张北\\\",\\\"id\\\":2,\\\"idc\\\":\\\"na620\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"},{\\\"area\\\":\\\"张北\\\",\\\"id\\\":3,\\\"idc\\\":\\\"na610\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"}]\",\n" +
                "            \"status\": \"EFFECTED\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 2,\n" +
                "            \"unitType\": \"guide\",\n" +
                "            \"description\": \"电商导购单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通两地在线+混部单元部署，导购类业务如推荐业务，与交易下单无关\\n与交易单元化同样使用unzbmix25g/unn1/une1三个离在线混部单元。\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"南通\\\",\\\"id\\\":14,\\\"idc\\\":\\\"ea119\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.unsh\\\",\\\"unitType\\\":\\\"guide\\\"},{\\\"area\\\":\\\"南通\\\",\\\"id\\\":15,\\\"idc\\\":\\\"ea118\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.une1\\\",\\\"unitType\\\":\\\"guide\\\"}]\",\n" +
                "            \"status\": \"INVALID\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"code\": 0,\n" +
                "    \"success\": true,\n" +
                "    \"message\": null\n" +
                "}"
        val result = HcrmApi("", "", "")
            .listUnitizationUnitLocation("test-app", "test-group")
        Assert.assertEquals(2, result.size)
        Assert.assertEquals(UnitLocation("na620", "CENTER_UNIT.center", "trade", "true"), result[0])
        Assert.assertEquals(UnitLocation("na610", "CENTER_UNIT.center", "trade", "true"), result[1])
    }

    @Test
    fun `testListUnitizationUnitLocation while exist multi unit group`() {
        mockkObject(HttpClientUtils)
        every {
            HttpClientUtils.httpGetWithHeaders(
                url = any(), headers = any(), params = any())
        } returns "{\n" +
                "    \"traceId\": \"2150042d17137716250658606e1618\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": 1,\n" +
                "            \"unitType\": \"trade\",\n" +
                "            \"description\": \"电商交易单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通在线+混部部署；适合电商交易类应用，切流规则为交易单元化用户切流规则，详见https://www.atatech.org/articles/136782\\n以下机房+单元为大促混部单元，仅在618、双11、双12大促期间启用：\\nNA62-CENTER_UNIT.unzbmix25g、EA118--CENTER_UNIT.une1、EA118--CENTER_UNIT.unn1\\n\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"张北\\\",\\\"id\\\":2,\\\"idc\\\":\\\"na620\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"},{\\\"area\\\":\\\"张北\\\",\\\"id\\\":3,\\\"idc\\\":\\\"na610\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.center\\\",\\\"unitType\\\":\\\"trade\\\"}]\",\n" +
                "            \"status\": \"EFFECTED\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 2,\n" +
                "            \"unitType\": \"guide\",\n" +
                "            \"description\": \"电商导购单元化\",\n" +
                "            \"empId\": \"38640\",\n" +
                "            \"empNick\": \"杨仪\",\n" +
                "            \"remark\": \"张北、南通两地在线+混部单元部署，导购类业务如推荐业务，与交易下单无关\\n与交易单元化同样使用unzbmix25g/unn1/une1三个离在线混部单元。\",\n" +
                "            \"unitLocation\": \"[{\\\"area\\\":\\\"南通\\\",\\\"id\\\":14,\\\"idc\\\":\\\"ea119\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.unsh\\\",\\\"unitType\\\":\\\"guide\\\"},{\\\"area\\\":\\\"南通\\\",\\\"id\\\":15,\\\"idc\\\":\\\"ea118\\\",\\\"isEcs\\\":\\\"true\\\",\\\"unit\\\":\\\"CENTER_UNIT.une1\\\",\\\"unitType\\\":\\\"guide\\\"}]\",\n" +
                "            \"status\": \"EFFECTED\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"code\": 0,\n" +
                "    \"success\": true,\n" +
                "    \"message\": null\n" +
                "}"
        val result = HcrmApi("", "", "")
            .listUnitizationUnitLocation("test-app", "test-group")
        Assert.assertEquals(4, result.size)
        Assert.assertEquals(UnitLocation("na620", "CENTER_UNIT.center", "trade", "true"), result[0])
        Assert.assertEquals(UnitLocation("na610", "CENTER_UNIT.center", "trade", "true"), result[1])
        Assert.assertEquals(UnitLocation("ea119", "CENTER_UNIT.unsh", "guide", "true"), result[2])
        Assert.assertEquals(UnitLocation("ea118", "CENTER_UNIT.une1", "guide", "true"), result[3])
    }
}