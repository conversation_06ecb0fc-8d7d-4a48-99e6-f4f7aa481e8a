package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.DefaultClusterService
import com.alibaba.koastline.multiclusters.apre.ApRELabelDefineService
import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.req.ResourcePoolCreateOrUpdateReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.dao.env.AppRuntimeEnvironmentDataRepo
import com.alibaba.koastline.multiclusters.data.dao.env.ResourcePoolRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.data.vo.env.ResourcePoolData
import io.mockk.Runs
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.verify
import org.junit.Test
import testutils.BaseTest

class ResourcePoolTest: BaseTest() {

    private var objectMapper = ObjectMapperFactory.newTolerant()
    @InjectMockKs
    lateinit var resourcePoolService: ResourcePoolService
    @MockK
    lateinit var resourcePoolRepo: ResourcePoolRepo
    @MockK
    lateinit var defaultClusterService: DefaultClusterService
    @MockK
    lateinit var apRELabelService: ApRELabelService
    @MockK
    lateinit var appRuntimeEnvironmentDataRepo: AppRuntimeEnvironmentDataRepo
    @MockK
    lateinit var apRELabelDefinitionService: ApRELabelDefineService

    @Test
    fun updateResourcePoolIgnoreWhileExistWithLabelTest(){
        val resourcePoolUpdateReqDto = manufacturePojo(ResourcePoolCreateOrUpdateReqDto::class.java)

        val clusterId = resourcePoolUpdateReqDto.clusterId
        val kManagedClusterKey = resourcePoolUpdateReqDto.managedClusterKey

        val existedResourcePool = manufacturePojo(ResourcePoolData::class.java).copy(
            clusterId = clusterId,
            managedClusterKey = kManagedClusterKey
        )

        every {
            resourcePoolRepo.findByManagedClusterKeyAndClusterId(
                clusterId = clusterId,
                managedClusterKey = kManagedClusterKey
            )
        }returns existedResourcePool

        every {
            apRELabelService.deleteApRELabelByTargetAndType(
                existedResourcePool.resourcePoolKey,
                ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                CONSOLE
            )
        }just Runs

        every {
            apRELabelService.createApRELabelIgnoreWhileExistWithFeatureSpec(any())
        }returns manufacturePojo(ApRELabelDO::class.java).copy(
            targetKey = existedResourcePool.resourcePoolKey
        )
        resourcePoolService.updateResourcePoolIgnoreWhileExistWithLabel(resourcePoolUpdateReqDto)

        verify(exactly = 1) {
            resourcePoolRepo.findByManagedClusterKeyAndClusterId(
                clusterId = clusterId, managedClusterKey = kManagedClusterKey
            )
        }

        verify(exactly = 1) {
            apRELabelService.deleteApRELabelByTargetAndType(
                existedResourcePool.resourcePoolKey,
                ApRELabelTargetTypeEnum.RESOURCE_POOL.name,
                CONSOLE
            )
        }
    }
}