package com.alibaba.koastline.multiclusters.appenv.service.runtime.test

import com.alibaba.koastline.multiclusters.appenv.service.apre.test.ApREMockDataUtils.manufacturePojo
import com.alibaba.koastline.multiclusters.common.exceptions.RuntimeDataException
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeData
import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkload
import com.alibaba.koastline.multiclusters.runtime.RuntimeBaseService
import com.alibaba.koastline.multiclusters.runtime.RuntimeCreateReqDto
import com.alibaba.koastline.multiclusters.runtime.RuntimeWorkloadCreateReqDto
import com.alibaba.koastline.multiclusters.runtime.params.RuntimeType
import io.mockk.every
import io.mockk.justRun
import io.mockk.spyk
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import org.junit.Test

class RuntimeBaseServiceTest {

    @Test(expected = RuntimeDataException::class)
    fun `testRegistryRuntime while is invalid exist`() {
        val runtimeCreateReqDto = fakeRuntimeCreateReqDto()
        val runtimeBaseService = spyk<RuntimeBaseService>() {
            every {
                runtimeRepo.findByRuntimeKey(runtimeCreateReqDto.runtimeKey)
            } returns RuntimeData(
                appName = runtimeCreateReqDto.appName,
                runtimeKey = runtimeCreateReqDto.runtimeKey,
                envStackId = runtimeCreateReqDto.envStackId,
                resourceGroupName = "this is different resource group name",
                type = runtimeCreateReqDto.type.name,
                creator = runtimeCreateReqDto.operator,
                modifier = runtimeCreateReqDto.operator
            )
        }
        runtimeBaseService.registryRuntime(runtimeCreateReqDto)
    }

    @Test(expected = RuntimeDataException::class)
    fun `testUnRegistryRuntime while have running workloads`() {
        val now = Date(Instant.now().toEpochMilli())
        val appName = "normandy-test-app4"
        val runtimeKey = UUID.randomUUID().toString()
        val runtimeBaseService = spyk<RuntimeBaseService>() {
            every {
                findRuntime(appName, runtimeKey)
            } returns RuntimeData(
                appName = appName,
                runtimeKey = runtimeKey,
                envStackId = UUID.randomUUID().toString(),
                resourceGroupName = "normandy-test-app4host",
                type = RuntimeType.COMMON.toString(),
                creator = "10000",
                modifier = "10000"
            )
            every {
                runtimeWorkloadRepo.listByRuntimeKey(runtimeKey)
            } returns listOf(
                RuntimeWorkload(
                    runtimeKey = runtimeKey,
                    site = "na610",
                    unit = "center",
                    stage = "PUBLISH",
                    clusterId = "asi_zjk_core_a01",
                    status = "ONLINE",
                    runningStatus = "INSTALLED",
                    gmtCreate = now,
                    gmtModified = now,
                    creator = "10000",
                    modifier = "10000",
                    isDeleted = "N"
                )
            )
        }
        runtimeBaseService.unRegistryRuntime(appName, runtimeKey, "10000")
    }

    @Test
    fun `testRegistryRuntimeWorkload while workload existed`() {
        val runtimeWorkloadCreateReqDto = manufacturePojo(RuntimeWorkloadCreateReqDto::class.java)
        val runtimeBaseService = spyk<RuntimeBaseService>(){
            every {
                runtimeWorkloadRepo.listByRuntimeKey(runtimeWorkloadCreateReqDto.runtimeKey)
            } returns listOf(
                RuntimeWorkload(
                    id = 1L,
                    runtimeKey = runtimeWorkloadCreateReqDto.runtimeKey,
                    site = runtimeWorkloadCreateReqDto.site,
                    stage = runtimeWorkloadCreateReqDto.stage,
                    unit = runtimeWorkloadCreateReqDto.unit,
                    clusterId = runtimeWorkloadCreateReqDto.clusterId,
                    status = "ONLINE",
                    runningStatus = "INSTALLED",
                    creator = "",
                    modifier = ""
                )
            )
            every {
                runtimeWorkloadRepo.updateBothStatusById(
                    id = 1L,
                    modifier = runtimeWorkloadCreateReqDto.operator,
                    status = runtimeWorkloadCreateReqDto.status.name,
                    runningStatus = runtimeWorkloadCreateReqDto.runningStatus.name
                )
            }returns 1
        }
        val rs = runtimeBaseService.registryRuntimeWorkload(runtimeWorkloadCreateReqDto)
        assertEquals(1L, rs)
    }

    private fun fakeRuntimeCreateReqDto(): RuntimeCreateReqDto {
        return RuntimeCreateReqDto(
            appName = "normandy-test-app4",
            envStackId = "d13f890e-d4f4-48c6-81f4-6e0b8f595244",
            runtimeKey = UUID.randomUUID().toString(),
            resourceGroupName = "normandy-test-app4host",
            type = RuntimeType.COMMON,
            operator = "10000"
        )
    }
}