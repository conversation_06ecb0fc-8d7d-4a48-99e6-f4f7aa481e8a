package com.alibaba.koastline.multiclusters.appenv

import com.alibaba.koastline.multiclusters.appenv.model.ClusterInstanceDto
import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ClusterProfileUniqueException
import com.alibaba.koastline.multiclusters.data.dao.env.KManagedClusterRepo
import com.alibaba.koastline.multiclusters.data.dao.env.KoastlineClusterProfileRepo
import com.alibaba.koastline.multiclusters.data.dao.env.KoastlineGatewayRepo
import com.alibaba.koastline.multiclusters.data.dao.env.SystemComponentsRepo
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import com.alibaba.koastline.multiclusters.data.vo.env.ClusterStatus
import com.alibaba.koastline.multiclusters.data.vo.env.KManagedClusterData
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.slot
import io.mockk.verify
import org.junit.Test
import testutils.BaseTest
import java.time.Instant
import java.util.*


class DefaultClusterServiceTest : BaseTest() {

    @InjectMockKs
    lateinit var defaultClusterService: DefaultClusterService

    @MockK
    lateinit var clusterProfileRepo: KoastlineClusterProfileRepo

    @MockK
    lateinit var gatewayRepo: KoastlineGatewayRepo

    @MockK
    lateinit var kManagedClusterRepo: KManagedClusterRepo

    @MockK
    lateinit var systemComponentsRepo: SystemComponentsRepo

    @MockK
    lateinit var objectMapper: ObjectMapper

    /**
     * 验证集群唯一性
     * 1.重复集群Id
     * 2.重复集群Name
     * 3.插入成功
     */
    @Test
    fun registerSimpleClusterProfileDataTest() {

        // case 1: 重复插入测试 clusterId
        val clusterId = getString()
        every {
            clusterProfileRepo.findClusterProfileByClusterId(clusterId)
        } answers {
            throw ClusterProfileUniqueException(clusterId = clusterId)
        }
        val expectErrorMsg = "cluster info has been record in database"

        val clusterInstanceDto = manufacturePojo(ClusterInstanceDto::class.java).copy(
            clusterId = clusterId,
            useType = ClusterProfileUseTypeEnum.test.name
        )
        exceptionTest({ e ->
            softly.assertThat(e.message).contains(expectErrorMsg)
        }) {
            defaultClusterService.registerSimpleClusterProfileData(clusterInstanceDto)
        }
        // case 2: 重复插入测试 clusterName

        val clusterName2 = getString()
        val clusterId2 = getString()
        val clusterInstanceDto2 = manufacturePojo(ClusterInstanceDto::class.java).copy(
            clusterName = clusterName2,
            clusterId = clusterId2,
            useType = ClusterProfileUseTypeEnum.test.name
        )

        every {
            clusterProfileRepo.findClusterProfileByClusterId(clusterId2)
        } answers {
            throw ClusterProfileUniqueException(clusterId = clusterInstanceDto2.clusterId)
        }

        every {
            clusterProfileRepo.findClusterProfileByClusterName(clusterName2)
        } answers {
            throw ClusterProfileUniqueException(clusterId = clusterInstanceDto2.clusterId)
        }

        exceptionTest({ e ->
            softly.assertThat(e.message).contains(expectErrorMsg)
        }) {
            defaultClusterService.registerSimpleClusterProfileData(clusterInstanceDto2)
        }
        // case 3:正常插入成功测试

        val newClusterName = getString()
        val newClusterId = getString()
        val clusterInstanceDto3 = manufacturePojo(ClusterInstanceDto::class.java).copy(
            clusterName = newClusterName,
            clusterId = newClusterId,
            useType = ClusterProfileUseTypeEnum.test.name
        )

        every {
            clusterProfileRepo.findClusterProfileByClusterName(newClusterName)
        } returns null

        every {
            clusterProfileRepo.findClusterProfileByClusterId(newClusterId)
        } returns null

        every {
            clusterProfileRepo.insertClusterProfile(any())
        } returns 1

        softly.assertThat(
            defaultClusterService.registerSimpleClusterProfileData(
                clusterInstanceDto = clusterInstanceDto3
            )
        ).isTrue
    }

    @Test
    fun updateSimpleClusterProfileDataTest() {
        val clusterId = getString()
        val clusterInstanceDto = manufacturePojo(ClusterInstanceDto::class.java).copy(
            clusterId = clusterId,
            useType = ClusterProfileUseTypeEnum.test.name
        )
        val clusterProfileData = manufacturePojo(ClusterProfileData::class.java).copy(
            clusterId = clusterId
        )

        every {
            clusterProfileRepo.findClusterProfileByClusterId(clusterId)
        } returns clusterProfileData

        val newClusterProfileData = clusterProfileData.copy(
            id = null,
            clusterName = clusterInstanceDto.clusterName,
            clusterProvider = clusterInstanceDto.clusterProvider,
            clusterType = clusterInstanceDto.clusterType,
            region = clusterInstanceDto.region,
            site = clusterInstanceDto.site ?: "",
            annotations = clusterInstanceDto.annotations,
            useType = clusterInstanceDto.useType,
            status = clusterInstanceDto.status,
            gmtModified = Date(Instant.now().toEpochMilli()),
            isDeleted = Constants.IS_NOT_DELETED,
            modifier = clusterInstanceDto.operator
        )

        val slot = slot<ClusterProfileData>()

        every {
            clusterProfileRepo.updateClusterProfile(capture(slot))
        } returns 1

        val result = defaultClusterService.updateSimpleClusterProfileData(clusterInstanceDto)

        verify {
            clusterProfileRepo.updateClusterProfile(any())
        }
        //忽略更新时间
        val missingTimeRealArgument = slot.captured.copy(
            gmtModified = newClusterProfileData.gmtModified
        )
        softly.assertThat(missingTimeRealArgument).isEqualTo(newClusterProfileData)
        softly.assertThat(result).isTrue
    }

    @Test
    fun createSimpleKManageClusterTest() {
        val region = getString()
        val status = ClusterStatus.ONLINE.name

        val slot = slot<KManagedClusterData>()

        every {
            kManagedClusterRepo.createKManagedCluster(capture(slot))
        } returns 1

        val result = defaultClusterService.createSimpleKManageCluster(region, status)

        val realArgumentMissingTime = slot.captured

        //验证调用和参数是否一致
        verify {
            kManagedClusterRepo.createKManagedCluster(any())
        }
        softly.assertThat(result.region).isEqualTo(region)
        softly.assertThat(result.status).isEqualTo(status)
        softly.assertThat(realArgumentMissingTime.status).isEqualTo(status)
        softly.assertThat(realArgumentMissingTime.region).isEqualTo(region)
    }
}