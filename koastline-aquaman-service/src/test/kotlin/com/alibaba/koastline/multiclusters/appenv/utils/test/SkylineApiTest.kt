package com.alibaba.koastline.multiclusters.appenv.utils.test

import com.alibaba.ais.skyline.common.service.Result
import com.alibaba.ais.skyline.domain.search.response.ItemQueryResponse
import com.alibaba.fastjson.JSON
import com.alibaba.koastline.multiclusters.external.SkylineApi
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_APP_NAME
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_CLUSTER_ID
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_IP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_NAMESPACE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_RESOURCE_GROUP
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_SITE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_SN
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_STAGE
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.FIELD_NAME_TAGS
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals

class SkylineApiTest {
    @Test
    fun testQueryServerListByResourceGroup() {
        val skylineApi = spyk(SkylineApi()) {
            val itemQueryResponse = ItemQueryResponse()
            itemQueryResponse.itemList = listOf(
                mutableMapOf(
                    FIELD_NAME_IP to "***********",
                    FIELD_NAME_SN to "11111111111111",
                    FIELD_NAME_APP_NAME to "normandy-test-app4",
                    FIELD_NAME_RESOURCE_GROUP to "normandy-test-app4host",
                    FIELD_NAME_SITE to "na610",
                    FIELD_NAME_STAGE to "PUBLISH",
                    FIELD_NAME_TAGS to JSON.parseArray("[\"CENTER_UNIT.center\"]"),
                    FIELD_NAME_CLUSTER_ID to "3a68318e-f82a-4646-aba7-6f5a5fff3b90",
                    FIELD_NAME_NAMESPACE to "normandy-test-app4"
                ),
                mutableMapOf(
                    FIELD_NAME_IP to "***********",
                    FIELD_NAME_SN to "22222222222222",
                    FIELD_NAME_APP_NAME to "normandy-test-app4",
                    FIELD_NAME_RESOURCE_GROUP to "normandy-test-app4host",
                    FIELD_NAME_SITE to "na620",
                    FIELD_NAME_STAGE to "",
                    FIELD_NAME_TAGS to JSON.parseArray("[\"CENTER_UNIT.center\"]"),
                    FIELD_NAME_CLUSTER_ID to "3a68318e-f82a-4646-aba7-6f5a5fff3b91",
                    FIELD_NAME_NAMESPACE to "normandy-test-app4"
                ),
                // IP is null
                mutableMapOf(
                    FIELD_NAME_SN to "33333333333333",
                    FIELD_NAME_APP_NAME to "normandy-test-app4",
                    FIELD_NAME_RESOURCE_GROUP to "normandy-test-app4host",
                    FIELD_NAME_SITE to "na620",
                    FIELD_NAME_STAGE to "PUBLISH",
                    FIELD_NAME_TAGS to JSON.parseArray("[\"CENTER_UNIT.center\"]"),
                    FIELD_NAME_CLUSTER_ID to "3a68318e-f82a-4646-aba7-6f5a5fff3b91",
                    FIELD_NAME_NAMESPACE to "normandy-test-app4"
                )
            )
            every {
                skylineHttpClient!!.itemSearch().query(any())
            } returns Result<ItemQueryResponse>().succ(itemQueryResponse)
        }
        val servers = skylineApi.convertItemQueryResponseToServerList("normandy-test-app4",
            skylineApi.queryServerByCondition("normandy-test-app4","3a68318e-f82a-4646-aba7-6f5a5fff3b90","normandy-test-app4host", null,false,1,1000)
        )
        // 第二项FIELD_NAME_STAGE为空，被过滤
        assertEquals(2, servers.size)
        assertEquals("***********", servers[0].ip)
        assertEquals("11111111111111", servers[0].sn)
        assertEquals("normandy-test-app4", servers[0].appName)
        assertEquals("PUBLISH", servers[0].stage)
        assertEquals("na610", servers[0].site)
        assertEquals("CENTER_UNIT.center", servers[0].unit)
        assertEquals("3a68318e-f82a-4646-aba7-6f5a5fff3b90", servers[0].clusterId)

        assertEquals("33333333333333", servers[1].sn)
    }
}