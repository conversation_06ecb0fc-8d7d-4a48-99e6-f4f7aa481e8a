package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.service.schedule.test.DeployScheduleServiceTest.Companion
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum.DEPLOY
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum.NON_DECLARATIVE
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.ASI
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleEnvType.SERVERLESS_APP
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.service.schedule.PodListScheduleService
import io.mockk.every
import io.mockk.spyk
import kotlin.test.assertEquals
import org.junit.Test

/**
 * @author:    <EMAIL>
 * @date:    2023/12/5 12:29 PM
 */
class PodListScheduleServiceTest {

    @Test
    fun testDoSchedule_while_is_not_serverless() {
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(
                scheduleEnvType = ASI
            )
        )
        val podListScheduleService = spyk<PodListScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            }returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_only_asi()
        }
        val scheduleResult = podListScheduleService.doSchedule(content)
        assertEquals(2, scheduleResult.workloadExpectedStates.size)
    }

    @Test
    fun testDoSchedule_while_is_serverless_and_has_serverless_resources() {
        val content = getBaseContent().copy(
            scheduleRequestParam = ScheduleRequestParam(
                scheduleEnvType = SERVERLESS_APP
            )
        )
        val podListScheduleService = spyk<PodListScheduleService>() {
            every {
                envHostResourceScopeService.findByCurrentEnvStackId(content.resourceScope.envStackId!!)
            }returns null
            every {
                runningStateScheduleService.doSchedule(content)
            } returns getScheduleResult_contains_serverless()
        }
        val scheduleResult = podListScheduleService.doSchedule(content)
        assertEquals(1, scheduleResult.workloadExpectedStates.size)
        assertEquals("runtime_id", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.runtimeId)
    }

    private fun getBaseContent(): ScheduleRequestContent {
        return ScheduleRequestContent(
            resourceScope = ResourceScope(
                DeployScheduleServiceTest.APP_NAME,
                DeployScheduleServiceTest.ENV_STACK_ID, DeployScheduleServiceTest.RESOURCE_GROUP
            ),
            scheduleType = ScheduleType(NON_DECLARATIVE, DEPLOY),
            scheduleRequestParam = ScheduleRequestParam()
        )
    }

    private fun getScheduleResult_only_asi(): ScheduleResult {
        return ScheduleResult(
            workloadExpectedStates = listOf(
                WorkloadExpectedState(
                    workloadMetadataConstraint = WorkloadMetadataConstraint(Companion.APP_NAME, Companion.RESOURCE_GROUP, "na610", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_a01", Companion.APP_NAME, null),
                    params = mapOf(ScheduleResultParamConstants.RESOURCE_NUM to "4")
                ),
                WorkloadExpectedState(
                    workloadMetadataConstraint = WorkloadMetadataConstraint(Companion.APP_NAME, Companion.RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b01", Companion.APP_NAME, null),
                    params = mapOf(ScheduleResultParamConstants.RESOURCE_NUM to "2")
                ),
            )
        )
    }

    private fun getScheduleResult_contains_serverless(): ScheduleResult {
        return ScheduleResult(
            workloadExpectedStates = getScheduleResult_only_asi().workloadExpectedStates.toMutableList().apply {
                this.add(
                    WorkloadExpectedState(
                        workloadMetadataConstraint = WorkloadMetadataConstraint(Companion.APP_NAME, Companion.RESOURCE_GROUP, "na620", "CENTER_UNIT.center", "PUBLISH", "default", "cluster_core_b02", Companion.APP_NAME, "runtime_id"),
                        params = mapOf(ScheduleResultParamConstants.RESOURCE_NUM to "1")
                    )
                )
            }
        )
    }
}