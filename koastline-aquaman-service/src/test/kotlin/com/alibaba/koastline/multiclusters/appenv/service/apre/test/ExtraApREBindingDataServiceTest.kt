package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.attorney.ExtraApREBindingDataService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ExternalAndProperties
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.RuntimeProperties
import com.alibaba.koastline.multiclusters.apre.model.ServerlessAttorney
import com.alibaba.koastline.multiclusters.apre.model.ServerlessEnvType.PUBLISH
import com.alibaba.koastline.multiclusters.apre.model.ServerlessEnvType.TESTING
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.data.dao.env.ExtraApREBindingDataRepo
import com.alibaba.koastline.multiclusters.data.vo.GroupCount
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingDataType.SERVERLESS_BASE
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.SpyK
import io.mockk.just
import io.mockk.runs
import org.junit.Test
import testutils.BaseTest
import java.time.Instant
import java.util.*

class ExtraApREBindingDataServiceTest : BaseTest() {
    @SpyK
    @InjectMockKs
    lateinit var extraApREBindingDataService: ExtraApREBindingDataService

    @MockK
    lateinit var extraApREBindingDataRepo: ExtraApREBindingDataRepo

    @MockK
    lateinit var matchScopeService: MatchScopeService

    val objectMapper = ObjectMapper()

    @Test
    fun getScopeAttorneyByIdentityInfoTest() {
        val identityInfo = manufacturePojo(IdentityInfo::class.java).copy(
            productLineIdPath = null,
        )
        val extraApREBindingDataId = getLong()
        val matchScopeData = mutableListOf(
            manufacturePojo(MatchScopeDataDO::class.java).copy(
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                externalId = getRandomProductLinePath(3),
                targetId = extraApREBindingDataId
            )
        )
        val targetIds = matchScopeData.map { it.targetId!! }
        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            id = extraApREBindingDataId,
            properties = objectMapper.writeValueAsString(
                manufacturePojo(RuntimeProperties::class.java)
            )
        )

        every {
            matchScopeService.findMatchScopesByTargetAndExternalForApp(
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                appName = identityInfo.appName,
                resourceGroupList = listOf(identityInfo.nodeGroup!!)
            )
        } returns matchScopeData

        every {
            extraApREBindingDataRepo.findByIds(targetIds)
        } returns mutableListOf(extraApREBindingData)

        val result = extraApREBindingDataService.getScopeAttorneyByIdentityInfo(
            identityInfo = identityInfo
        )

        softly.assertThat(result.size).isEqualTo(1)
        softly.assertThat(result.keys.first()).isEqualTo(extraApREBindingData)
        softly.assertThat(result.values.first()).isEqualTo(matchScopeData[0])
    }

    @Test
    fun findScopeAttorneyByExternalTest() {
        val externalType = MatchScopeExternalTypeEnum.APPLICATION
        val externalId = getRandomProductLinePath(3)
        val extraApREBindingDataType = SERVERLESS_BASE
        val extraApREBindingDataId = getLong()

        val matchScopeDataList = mutableListOf(
            manufacturePojo(MatchScopeData::class.java).copy(
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                externalId = getRandomProductLinePath(3),
                targetId = extraApREBindingDataId
            )
        )

        every {
            matchScopeService.matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                externalId = externalId,
                externalType = externalType.name
            )
        } returns matchScopeDataList

        // 3.27版本 type 只有 serverless 分类
        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            id = extraApREBindingDataId,
            properties = objectMapper.writeValueAsString(
                manufacturePojo(RuntimeProperties::class.java)
            ),
            type = extraApREBindingDataType
        )

        every {
            extraApREBindingDataRepo.findByIds(matchScopeDataList.map { it.targetId })
        } returns mutableListOf(extraApREBindingData)

        val result = extraApREBindingDataService.findScopeAttorneyByExternal(
            externalId = externalId,
            externalType = externalType,
            extraApREBindingDataType = extraApREBindingDataType
        )

        softly.assertThat(result.size).isEqualTo(1)
        softly.assertThat(result.keys.first().type).isEqualTo(SERVERLESS_BASE)
    }

    @Test
    fun listServerlessAttorneyByPropertiesTest() {
        val pageNumber = 1
        val pageSize = 5

        // total 一共3个 采用pageSize 5 一共存在2页
        val groupCount = mutableListOf(
            GroupCount(count = getIntUnderScope(10), groupName = "serverless1"),
            GroupCount(count = getIntUnderScope(6), groupName = "serverless2"),
            GroupCount(count = getIntUnderScope(13), groupName = "serverless3"),
            GroupCount(count = getIntUnderScope(9), groupName = "serverless4"),
            GroupCount(count = getIntUnderScope(5), groupName = "serverless5"),
            GroupCount(count = getIntUnderScope(2), groupName = "serverless6"),
        )
        val totalCount = groupCount.size
        val targetIds = groupCount.map { it.groupName }.subList(0, 5)

        // 查询对应 ids 中的数据 index in 0...4 一个ExtraApREBind
        val mockExtraApREBindingDataList = mutableListOf<ExtraApREBindingData>()
        val mockMatchScopeDataList = mutableListOf<MatchScopeData>()
        for (countUnit in groupCount) {
            for (count in 1..countUnit.count) {
                val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
                    targetId = countUnit.groupName,
                    properties = objectMapper.writeValueAsString(manufacturePojo(RuntimeProperties::class.java)),
                )
                val extraApREBindingDataId = checkNotNull(extraApREBindingData.id)
                val matchScopeData = manufacturePojo(MatchScopeData::class.java).copy(
                    targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    externalId = getRandomProductLinePath(3),
                    targetId = extraApREBindingDataId
                )
                mockExtraApREBindingDataList.add(extraApREBindingData)
                mockMatchScopeDataList.add(matchScopeData)
            }
        }

        every {
            extraApREBindingDataRepo.findScopeGroupCountByType(null, any())
        } returns groupCount

        every {
            extraApREBindingDataRepo.findByTargetIds(targetIds)
        } returns mockExtraApREBindingDataList

        every {
            matchScopeService.matchScopeDataRepo.findByTargetIdsAndTargetType(
                targetIds = mockExtraApREBindingDataList.map { it.id!! },
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name
            )
        } returns mockMatchScopeDataList

        val result = extraApREBindingDataService.listServerlessAttorneyByProperties(
            pageNumber = pageNumber,
            pageSize = pageSize,
            keyWords = null
        )

        softly.assertThat(result.pageNumber).isEqualTo(pageNumber)
        softly.assertThat(result.pageSize).isEqualTo(pageSize)
        softly.assertThat(result.totalCount).isEqualTo(totalCount.toLong())
        softly.assertThat(result.data?.size).isEqualTo(pageSize)

        val data = checkNotNull(result.data) { "data cannot be null in result" }
        data.forEachIndexed { index, serverlessAttorneyGroup ->
            softly.assertThat(serverlessAttorneyGroup.externalAndPropertiesList.size).isEqualTo(groupCount[index].count)
        }
    }

    @Test
    fun buildServerlessAttorneyGroupTest() {
        // serverless app build
        val serverlessBaseApp = getString()
        val properties = manufacturePojo(RuntimeProperties::class.java)
        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            type = SERVERLESS_BASE,
            targetId = serverlessBaseApp,
            properties = objectMapper.writeValueAsString(properties)
        )
        val extraApREBindingDataId = checkNotNull(extraApREBindingData.id)
        val externalId = getRandomProductLinePath(4)
        val matchScopeData = manufacturePojo(MatchScopeData::class.java).copy(
            targetId = extraApREBindingDataId,
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            externalId = externalId,
            externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name
        )
        val result = extraApREBindingDataService.buildServerlessAttorneyGroup(
            serverlessBaseApps = mutableListOf(serverlessBaseApp),
            extraApREBindingDataList = mutableListOf(extraApREBindingData),
            matchScopeList = mutableListOf(matchScopeData)
        )
        softly.assertThat(result.size).isEqualTo(1)
        softly.assertThat(result[0].externalAndPropertiesList[0].runtimeProperties).isEqualTo(properties)
        softly.assertThat(result[0].externalAndPropertiesList[0].externalType).isEqualTo(MatchScopeExternalTypeEnum.AONE_PRODUCTLINE)
        softly.assertThat(result[0].externalAndPropertiesList[0].externalId).isEqualTo(externalId)
    }

    @Test
    fun findByServerlessAttorneyByExternal() {
        val externalId = getRandomProductLinePath(3)
        val externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
        val runtimeProperties = manufacturePojo(RuntimeProperties::class.java)
        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            id = getLong(),
            properties = objectMapper.writeValueAsString(runtimeProperties),
            type = SERVERLESS_BASE
        )
        val extraApREBindingDataMs = manufacturePojo(MatchScopeData::class.java).copy(
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            targetId = extraApREBindingData.id!!,
        )
        val attorneyScopeList = mutableMapOf(
            extraApREBindingData to extraApREBindingDataMs
        )
        // 只返回1个数组 现在只有一个 serverless
        every {
            extraApREBindingDataService.findScopeAttorneyByExternal(
                externalId = externalId,
                externalType = externalType,
                extraApREBindingDataType = SERVERLESS_BASE
            )
        } returns attorneyScopeList
        val result = extraApREBindingDataService.findByServerlessAttorneyByExternal(
            externalId = externalId, externalType = externalType
        )
        softly.assertThat(result.size).isEqualTo(1)
    }

    @Test
    fun createServerlessAttorneyWhileExisted2UpdateTest() {
        val serverlessBaseApp = getString()
        val externalId = getString()
        val externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
        val properties = RuntimeProperties(
            allRuntimeSupport = true,
            admissionEnvList = listOf(TESTING, PUBLISH)
        )

        every {
            extraApREBindingDataService.findByServerlessAttorneyByExternal(
                externalId = externalId,
                externalType = externalType
            )
        } returns mutableListOf(
            ServerlessAttorney(
                serverlessBaseApp = serverlessBaseApp,
                externalAndProperties = ExternalAndProperties(
                    runtimeProperties = properties,
                    externalType = externalType,
                    externalId = externalId,
                    description = getString(),
                    creator = "creator",
                    modifier = "modifier",
                    gmtCreate = Date(Instant.now().toEpochMilli()),
                    gmtModified = Date(Instant.now().toEpochMilli())
                )
            )
        )

        every {
            extraApREBindingDataService.updateServerlessAttorney(
                any<String>(), any<String>(), any<MatchScopeExternalTypeEnum>(),
                any<RuntimeProperties>(), any<String>(), any<String>()
            )
        } returns manufacturePojo(ServerlessAttorney::class.java)

        every {
            extraApREBindingDataRepo.insert(any())
        } returns 1

        every {
            matchScopeService.createMatchScopeIgnoreWhileExist(any())
        } just runs

        every {
            extraApREBindingDataService.findByServerlessAttorneyByExternal(
                externalId = externalId,
                externalType = externalType,
            )
        }
    }

    @Test
    fun updateServerlessAttorneyTest() {
        val serverlessBaseApp = getString()
        val externalId = getRandomProductLinePath(3)
        val externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
        val modifier: String = getString()
        val properties = manufacturePojo(RuntimeProperties::class.java)
        val description = getString()

        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            targetId = serverlessBaseApp,
            properties = objectMapper.writeValueAsString(properties),
            type = SERVERLESS_BASE
        )

        val extraApREBindingDataMs = manufacturePojo(MatchScopeData::class.java).copy(
            externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
            targetId = extraApREBindingData.id!!,
        )

        every {
            extraApREBindingDataService.findScopeAttorneyByExternal(
                externalId = externalId,
                externalType = externalType,
                extraApREBindingDataType = SERVERLESS_BASE
            )
        } returns mapOf(extraApREBindingData to extraApREBindingDataMs)

        every {
            extraApREBindingDataService.extraApREBindingDataRepo.updateExtraApREBindingData(any())
        } returns 1

        val serverlessAttorney = manufacturePojo(ServerlessAttorney::class.java).copy(
            serverlessBaseApp = serverlessBaseApp
        )

        every {
            extraApREBindingDataService.findByServerlessAttorneyByExternal(
                externalId = externalId,
                externalType = externalType
            )
        } returns listOf(serverlessAttorney)

        val result = extraApREBindingDataService.updateServerlessAttorney(
            serverlessBaseApp = serverlessBaseApp,
            externalId = externalId,
            externalType = externalType,
            properties = properties,
            description = description,
            modifier = modifier
        )
        softly.assertThat(result.serverlessBaseApp).isEqualTo(serverlessBaseApp)
    }

    @Test
    fun deleteServerlessAttorneyTest() {
        val serverlessBaseApp = getString()
        val externalId = getRandomProductLinePath(3)
        val externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE
        val modifier: String = getString()

        val deleteMatchScopeData = manufacturePojo(MatchScopeData::class.java).copy(
            externalType = externalType.name,
            externalId = externalId,
            targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name
        )
        every {
            matchScopeService.matchScopeDataRepo.listByTargetTypeAndExternal(
                targetType = MatchScopeTargetTypeEnum.ExtraApREBindingData.name,
                externalId = externalId,
                externalType = externalType.name
            )
        } returns mutableListOf(deleteMatchScopeData)

        val extraApREBindingData = manufacturePojo(ExtraApREBindingData::class.java).copy(
            targetId = serverlessBaseApp,
            id = deleteMatchScopeData.targetId
        )

        every {
            extraApREBindingDataRepo.findByTargetId(targetId = serverlessBaseApp)
        } returns listOf(extraApREBindingData)
        every {
            matchScopeService.deleteMatchScopeById(any(), any())
        } just runs
        every {
            extraApREBindingDataRepo.deleteById(any())
        } returns 1

        extraApREBindingDataService.deleteServerlessAttorney(
            serverlessBaseApp = serverlessBaseApp,
            externalId = externalId,
            externalType = externalType,
            modifier = modifier
        )
    }

    /**
     * 测试第一波 ExtraApREBindingData 的测试
     */
    @Test
    fun featureByScopeAttorneySelectorTest() {
        val serverlessBaseApp = getString()
        val minMask = extraApREBindingDataService.getMinLabelMask()
        val maskStrategy = extraApREBindingDataService.getMaskStrategy()
        val apRELabelList = mutableListOf(
            manufacturePojo(ApRELabelDO::class.java).copy(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = ApRELabelExt.SERVERLESS_PREFIX + serverlessBaseApp,
                type = SERVERLESS
            )
        )
        // 按照serverless mask 逻辑 没有授权之后的 过滤应该没有标签了
        val leftApRELabel = extraApREBindingDataService.featureByScopeAttorneySelector(
            originApRELabels = apRELabelList,
            minLabelMaskMap = minMask,
            masksStrategyMap = maskStrategy,
            extraScopeAttorneyMapByType = emptyMap()
        )

        softly.assertThat(leftApRELabel.isEmpty()).isTrue
    }

    /**
     * 过滤serverless 标签
     */
    @Test
    fun serverlessMaskTest() {
        // case1: 存量标签都是serverless 没有授权 全部过滤 非serverless不过滤
        val originApRELabelList1 = mutableListOf(
            manufacturePojo(ApRELabelDO::class.java).copy(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = ApRELabelExt.SERVERLESS_PREFIX + getString(),
                type = SERVERLESS
            ),
            manufacturePojo(ApRELabelDO::class.java).copy(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = ApRELabelExt.SERVERLESS_PREFIX + getString(),
                type = SERVERLESS
            ),
            manufacturePojo(ApRELabelDO::class.java).copy(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = getString(),
                type = CONSOLE
            )
        )

        val result1 = extraApREBindingDataService.serverlessMask(
            leftApRELabels = originApRELabelList1,
            attorneys = null
        )

        softly.assertThat(result1.size).isEqualTo(1)
        softly.assertThat(result1[0].name).isEqualTo(ApRELabelExt.APRE_LABEL_FEATURE_NAME)
        softly.assertThat(result1[0].type == CONSOLE).isTrue

        // case2:存量标签中serverless 的spec版本实现过滤
        val target = ApRELabelExt.SERVERLESS_PREFIX + getString()
        val properties = objectMapper.writeValueAsString(
            manufacturePojo(RuntimeProperties::class.java).copy(
                allRuntimeSupport = false,
                admissionEnvList = listOf(
                    PUBLISH
                )
            )
        )
        val originApRELabelList2 = mutableListOf(
            manufacturePojo(ApRELabelDO::class.java).copy(
                name = ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                value = target,
                type = SERVERLESS,
                apREFeatureSpecs = mutableListOf(
                    manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                        scope = "test"
                    ),
                    manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                        scope = "test"
                    ),
                    manufacturePojo(ApREFeatureSpecDO::class.java).copy(
                        scope = "publish"
                    )
                )
            )
        )

        val extraApREBindingDataList = mutableListOf(
            manufacturePojo(ExtraApREBindingData::class.java).copy(
                properties = properties,
                targetId = target,
            )
        )

        val result2 = extraApREBindingDataService.serverlessMask(
            leftApRELabels = originApRELabelList2,
            attorneys = extraApREBindingDataList
        )

        softly.assertThat(result2.size).isEqualTo(1)
        softly.assertThat(result2[0].apREFeatureSpecs!!.size).isEqualTo(1)
        softly.assertThat(result2[0].apREFeatureSpecs!![0].scope).isEqualTo("publish")

        // case3: 存在ExtraApREBindingData 但 label 不在授权范围内 只保留非 serverless label
        val result3 = extraApREBindingDataService.serverlessMask(
            leftApRELabels = originApRELabelList1,
            attorneys = extraApREBindingDataList
        )
        softly.assertThat(result3.size).isEqualTo(1)
    }

    @Test
    fun mergeServerlessExtraApREBindingDataTest() {
        // 归并ServerlessExtraApREBindingData
        val serverlessBaseApp = getString()
        val extraApREBindingData1 = manufacturePojo(ExtraApREBindingData::class.java).copy(
            targetId = serverlessBaseApp,
            type = SERVERLESS_BASE,
            properties = objectMapper.writeValueAsString(
                RuntimeProperties(
                    allRuntimeSupport = false,
                    admissionEnvList = listOf(TESTING)
                )
            )
        )
        val extraApREBindingData2 = manufacturePojo(ExtraApREBindingData::class.java).copy(
            targetId = serverlessBaseApp,
            type = SERVERLESS_BASE,
            properties = objectMapper.writeValueAsString(
                RuntimeProperties(
                    allRuntimeSupport = true,
                    admissionEnvList = listOf(PUBLISH)
                )
            )
        )
        val result = extraApREBindingDataService.mergeServerlessExtraApREBindingData(
            listOf(
                extraApREBindingData1, extraApREBindingData2
            )
        )

        softly.assertThat(result?.size).isEqualTo(1)
        val properties = objectMapper.readValue(result!![0].properties, RuntimeProperties::class.java)
        softly.assertThat(properties.allRuntimeSupport).isTrue
        softly.assertThat(properties.admissionEnvList)
            .isEqualTo(listOf(TESTING, PUBLISH))
    }
}