package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.appstack.AppStackApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.MetadataOfSiteDO
import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class AppStackApREServiceTest {

    @Test
    fun testQueryClustersByApREDeedKey_with_declaration_region_is_null() {
        val region = "cn-zhang<PERSON>akou"
        val az = "na610"
        val stage = "SMALLFLOW"
        val unit = "CENTER_UNIT.center"
        val apREDeedKey = "apREDeedKey"
        val now = Date(Instant.now().toEpochMilli())
        val appStackApREService = spyk(AppStackApREService()) {
            every {
                apREDeedService.findApREDeedByKey(any())
            }returns ApREDeedDO(
                declarations = mutableListOf(Declaration(
                    az = az,
                    stage = stage,
                    unit = unit
                ))
            )
            every {
                metadataService.getMetadataOfSite(az)
            }returns MetadataOfSiteDO(
                site = az,
                region = region
            )
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region,az,stage,unit)
            }returns listOf(
                AppRuntimeEnvironmentData(
                    1L, "runtimeEnvKey", "", "",
                    "managedClusterKey",region,az,stage,unit,"status",null,now,now,"N"
                )
            )
            every {
                apREService.findCombinedApREBaseDetailByKey("runtimeEnvKey",null)
            }returns ApREDO(
                1L, "runtimeEnvKey", "", "",
                "managedClusterKey",region,region,az,az,stage,unit,"status",null,now,now,"N",
                emptyList(), emptyList()
            )
            every {
                clusterService.queryManagedClusterByManagedClusterKey("managedClusterKey")
            }returns null
        }
        val apREDeedResult = appStackApREService.queryClustersByApREDeedKey(apREDeedKey)
        assertEquals(1, apREDeedResult.matchDeclarations[0].apres.size)
        assertEquals(region, apREDeedResult.matchDeclarations[0].apres[0].region)
    }

    @Test
    fun testQueryClustersByApREDeedKey_with_declaration_to_multi_apre() {
        val region = "cn-zhangjiakou"
        val az = "na610"
        val stage = "SMALLFLOW"
        val unit = "CENTER_UNIT.center"
        val apREDeedKey = "apREDeedKey"
        val now = Date(Instant.now().toEpochMilli())
        val appStackApREService = spyk(AppStackApREService()) {
            every {
                apREDeedService.findApREDeedByKey(any())
            }returns ApREDeedDO(
                declarations = mutableListOf(Declaration(
                    az = az,
                    stage = stage,
                    unit = unit
                ))
            )
            every {
                metadataService.getMetadataOfSite(az)
            }returns MetadataOfSiteDO(
                site = az,
                region = region
            )
            every {
                appRuntimeEnvironmentDataRepo.findByRegionAndAzAndStageAndUnit(region,az,stage,unit)
            }returns listOf(
                AppRuntimeEnvironmentData(
                    1L, "runtimeEnvKey", "", "",
                    "managedClusterKey",region,az,stage,unit,"status",null,now,now,"N"
                ),
                AppRuntimeEnvironmentData(
                        2L, "runtimeEnvKey2", "", "",
                "managedClusterKey2",region,az,stage,unit,"status",null,now,now,"N"
                )
            )
            every {
                apREService.findCombinedApREBaseDetailByKey("runtimeEnvKey",null)
            }returns ApREDO(
                1L, "runtimeEnvKey", "", "",
                "managedClusterKey",region,region,az,az,stage,unit,"status",null,now,now,"N",
                emptyList(), emptyList()
            )
            every {
                clusterService.queryManagedClusterByManagedClusterKey("managedClusterKey")
            }returns null
        }
        val apREDeedResult = appStackApREService.queryClustersByApREDeedKey(apREDeedKey)
        assertEquals(1, apREDeedResult.matchDeclarations[0].apres.size)
        assertEquals(region, apREDeedResult.matchDeclarations[0].apres[0].region)
    }
}