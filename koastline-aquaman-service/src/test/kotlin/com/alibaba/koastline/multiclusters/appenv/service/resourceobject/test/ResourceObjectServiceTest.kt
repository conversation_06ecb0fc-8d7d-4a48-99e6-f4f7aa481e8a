package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.apre.ResourcePoolService
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.exceptions.BizException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.external.model.AppInfo
import com.alibaba.koastline.multiclusters.external.model.AppLevelEnum
import com.alibaba.koastline.multiclusters.external.model.AppStatusEnum
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService.Companion.PUBLISH_ID
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService.Companion.ROLLOUT_ID
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService.Companion.UPDATE_PARTITION
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.StatefulSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.facade.WorkloadSpecFacade
import com.alibaba.koastline.multiclusters.resourceobj.model.*
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceRequirementRequest
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceSpec
import com.alibaba.koastline.multiclusters.resourceobj.specific.ResourceSpecFeatureService
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.*
import org.apache.catalina.core.StandardService
import org.junit.Assert
import org.junit.Test
import org.yaml.snakeyaml.Yaml
import java.io.StringReader
import java.time.Instant
import java.util.*
import javax.json.Json
import javax.json.JsonObject
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ResourceObjectServiceTest {

    @Test
    fun testGetResourceRequirementSpec_with_resource_group() {
        val appName = "app-name"
        val resourceGroup = "app-name_prehost"
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup)
                }returns ResourceSpec("4","8Gi","60Gi",null)
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 4000
            every {
                getFeatureImportByResourceGroup(any())
            } returns ResourceObjectGetFeatureImportDO(

                formData = YamlUtils.load(
                    """
                        gpu:
                          models

                    """.trimIndent()
                ),
                resourceObjectFeatureKey = "GPU_AFFINITY_SPEC",
                gmtCreate = Date(Instant.now().toEpochMilli()),
                version = "0.0.1"
            )
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
        assertEquals(null, resourceRequirementResp.gpuCardModel)
    }

    @Test
    fun testGetResourceRequirementSpec_with_resource_group_and_compute_unit() {
        val appName = "app-name"
        val resourceGroup = "app-name_prehost"
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup)
                } returns ResourceSpec("4","8Gi","60Gi",null)
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 4000
            every {
                getFeatureImportByResourceGroup(any())
            } returns ResourceObjectGetFeatureImportDO(

                formData = emptyMap(),
                resourceObjectFeatureKey = "GPU_AFFINITY_SPEC",
                gmtCreate = Date(Instant.now().toEpochMilli()),
                version = "0.0.1"
            )
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
        assertEquals(null, resourceRequirementResp.gpuCardModel)
    }

    @Test
    fun testGetResourceRequirementSpec_with_app_and_no_group() {
        val appName = "app-name"
        val resourceGroup = ""
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    userLabelService.getAppResourceSpec(appName)
                }returns ResourceSpec(cpu = "4", memory = "8", disk = "60")
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 4000
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
    }

    @Test
    fun testGetResourceRequirementSpec_with_app_and_has_group() {
        val appName = "app-name"
        val resourceGroup = "app-name_prehost"
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup)
                } returns null
                every {
                    userLabelService.getAppResourceSpec(appName)
                }returns ResourceSpec(cpu = "4", memory = "8", disk = "60")
                every {
                    getFeatureImportByResourceGroup(any())
                } returns ResourceObjectGetFeatureImportDO(

                    formData = YamlUtils.load(
                        """
                        gpu:
                          models:
                          - Tesla-T4

                    """.trimIndent()
                    ),
                    resourceObjectFeatureKey = "GPU_AFFINITY_SPEC",
                    gmtCreate = Date(Instant.now().toEpochMilli()),
                    version = "0.0.1"
                )
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 4000
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
        assertEquals("Tesla-T4", resourceRequirementResp.gpuCardModel)
    }

    @Test
    fun testGetResourceRequirementSpec_with_atom() {
        val appName = "app-name"
        val resourceGroup = "app-name_prehost"
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup)
                } returns null
                every {
                    userLabelService.getAppResourceSpec(appName)
                }returns ResourceSpec(cpu = "4", memory = "8", disk = "60")
                every {
                    getFeatureImportByResourceGroup(any())
                } returns null
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 4000
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
        assertEquals(null, resourceRequirementResp.gpuCardModel)
    }

    @Test
    fun testGetResourceRequirementSpec_with_vpa() {
        val appName = "app-name"
        val resourceGroup = "app-name_prehost"
        val resourceObjectService = spyk(ResourceObjectService()) {
            this.resourceSpecFeatureService = spyk(ResourceSpecFeatureService()){
                every {
                    getResourceSpecByMatchScope(MatchScopeExternalTypeEnum.RESOURCE_GROUP.name, resourceGroup)
                }returns ResourceSpec("4","8Gi","60Gi",null)
            }
            every {
                capacityApi.queryVpaCurrentSpec(appName, resourceGroup, null,null,null, 4000)
            } returns 1500
            every {
                getFeatureImportByResourceGroup(any())
            } returns ResourceObjectGetFeatureImportDO(

                formData = null,
                resourceObjectFeatureKey = "GPU_AFFINITY_SPEC",
                gmtCreate = Date(Instant.now().toEpochMilli()),
                version = "0.0.1"
            )
        }
        val resourceRequirementResp = resourceObjectService.getResourceRequirementSpecWithExtra(
            ResourceRequirementRequest(appName = appName, resourceGroup = resourceGroup)
        )
        verify(exactly = 0) {
            resourceObjectService.capacityApi.queryVpaCurrentSpec(
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }

        assertEquals("4", resourceRequirementResp.resourceRequest.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceRequest.disk)
        assertEquals(null, resourceRequirementResp.resourceRequest.gpu)

        assertEquals("4", resourceRequirementResp.resourceLimit.cpu)
        assertEquals((8L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.memory)
        assertEquals((60L*1024*1024*1024).toString(), resourceRequirementResp.resourceLimit.disk)
        assertEquals(null, resourceRequirementResp.resourceLimit.gpu)
        assertEquals(null, resourceRequirementResp.gpuCardModel)
    }


    @Test
    fun `testMergeResourceByCluster while different cluster`() {
        val resourcePoolService = spyk<ResourcePoolService>() {}
        val originalResources = listOf(
            ResourceDO(
                clusterProfileNew = fakeClusterProfile("clusterId_a"),
                resourcePoolKey = "clusterId_a",
                apRELabels = listOf(
                    ApRELabelDO(
                        name = "label_name_a",
                        value = "label_value_a",
                        apRELabelKey = "label_key_1",
                        apREFeatureSpecs = listOf(
                            fakeApreLabelSpec("label_a_spec01"),
                            fakeApreLabelSpec("label_a_spec02")
                        )
                    )
                )
            ),
            ResourceDO(
                clusterProfileNew = fakeClusterProfile("clusterId_b"),
                resourcePoolKey = "clusterId_b",
                apRELabels = listOf(
                    ApRELabelDO(
                        name = "label_name_b",
                        value = "label_value_b",
                        apRELabelKey = "label_key_2",
                        apREFeatureSpecs = listOf(
                            fakeApreLabelSpec("label_a_spec01"),
                            fakeApreLabelSpec("label_a_spec02")
                        )
                    )
                )
            )
        )
        val sources = resourcePoolService.mergeResourceByCluster(originalResources)

        assertEquals(2, sources.size)
        assertEquals(1, sources[0]!!.apRELabels.size)
        assertEquals(2, sources[0]!!.apRELabels[0]!!.apREFeatureSpecs!!.size)
        assertEquals(1, sources[1]!!.apRELabels.size)
        assertEquals(2, sources[1]!!.apRELabels[0]!!.apREFeatureSpecs!!.size)
    }

    @Test
    fun `testMergeResourceByCluster while same cluster`() {
        val resourcePoolService = spyk<ResourcePoolService>() {}
        val originalResources = listOf(
            ResourceDO(
                clusterProfileNew = fakeClusterProfile("clusterId_a"),
                resourcePoolKey = "clusterId_a",
                apRELabels = listOf(
                    ApRELabelDO(
                        name = "label_name_a",
                        value = "label_value_a",
                        apRELabelKey = "label_key_1",
                        apREFeatureSpecs = listOf(
                            fakeApreLabelSpec("label_a_spec01"),
                            fakeApreLabelSpec("label_a_spec02")
                        )
                    )
                )
            ),
            ResourceDO(
                clusterProfileNew = fakeClusterProfile("clusterId_a"),
                resourcePoolKey = "clusterId_a",
                apRELabels = listOf(
                    ApRELabelDO(
                        name = "label_name_a",
                        value = "label_value_a",
                        apRELabelKey = "label_key_2",
                        apREFeatureSpecs = listOf(
                            fakeApreLabelSpec("label_a_spec02"),
                            fakeApreLabelSpec("label_a_spec03")
                        )
                    ),
                    ApRELabelDO(
                        name = "label_name_b",
                        value = "label_value_b",
                        apRELabelKey = "label_key_3",
                        apREFeatureSpecs = listOf(
                            fakeApreLabelSpec("label_b_spec01"),
                            fakeApreLabelSpec("label_b_spec02")
                        )
                    )
                )
            )
        )
        val sources = resourcePoolService.mergeResourceByCluster(originalResources)

        assertEquals(1, sources.size)
        assertEquals(2, sources[0]!!.apRELabels.size)
        assertEquals(3, sources[0]!!.apRELabels[0]!!.apREFeatureSpecs!!.size)
        assertEquals(2, sources[0]!!.apRELabels[1]!!.apREFeatureSpecs!!.size)
    }

    private fun fakeClusterProfile(clusterId: String): ClusterProfileNew {
        return ClusterProfileNew(
            clusterId = clusterId,
            clusterName = clusterId,
            clusterProvider = "alibaba",
            clusterType = "alibaba-asi",
            siteList = emptyList(),
            componentDataList = emptyList(),
            useType = "publish"
        )
    }

    private fun fakeApreLabelSpec(specCode: String): ApREFeatureSpecDO {
        return ApREFeatureSpecDO(
            null,null,null,null,specCode,null,null,null,null,null,null,null,null
        )
    }
    val originalResourceObjecSpec = """
        {
            "kind": "StatefulSet",
            "apiVersion": "apps/v1",
            "metadata": {
                "name": "alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3",
                "namespace": "alsc-adv-l-model",
                "uid": "910b81ce-80b5-4ac9-9ea4-fd32ed360542",
                "resourceVersion": "38039603784",
                "generation": 9,
                "creationTimestamp": "2023-07-21T02:37:29Z",
                "labels": {
                    "apps.kruise.io/rollout-batch-id": "0",
                    "apps.kruise.io/rollout-id": "127318589",
                    "inplaceset.sigma.ali/proxy": "CloneSet",
                    "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
                    "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
                    "sigma.ali/app-name": "alsc-adv-l-model",
                    "sigma.ali/disable-default-pdb-strategy": "true",
                    "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                    "sigma.ali/site": "na610",
                    "sigma.ali/subgroup": "default",
                    "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                    "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
                    "statefulset.sigma.ali/mode": "sigma"
                },
                "annotations": {
                    "cloneset.beta1.sigma.ali/app-fail-count": "0",
                    "cloneset.beta1.sigma.ali/expected-updated-replicas": "1",
                    "cloneset.beta1.sigma.ali/image-fail-count": "0",
                    "cloneset.beta1.sigma.ali/publish-success-replicas": "1",
                    "cloneset.beta1.sigma.ali/scheduled-fail-count": "0",
                    "inplaceset.beta1.sigma.ali/upgrade-scatter": "pod.beta1.sigma.ali/naming-register-state=working_online",
                    "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{\"statefulset.beta1.sigma.ali/partition\":\"0%\",\"statefulset.beta1.sigma.ali/pod-upgrade-timeout\":\"1800\",\"statefulset.beta1.sigma.ali/publish-id\":\"127318589\",\"statefulset.beta1.sigma.ali/upgrade-max-unavailable\":\"1\"},\"labels\":{\"apps.kruise.io/rollout-batch-id\":\"0\",\"apps.kruise.io/rollout-id\":\"127318589\"},\"name\":\"alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3\",\"namespace\":\"alsc-adv-l-model\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1800\\\", \\\"ImagePullTimeoutSeconds\\\":\\\"600\\\", \\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"beta\"},{\"name\":\"trafficRouteLabel\",\"value\":\"beta\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish\",\"name\":\"main\",\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/alsc-adv-l-model/logs\",\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":\"1\",\"volumes\":[{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}},\"updateStrategy\":{\"rollingUpdate\":{\"partition\":\"*********\"}}}}\n",
                    "kubectl.kubernetes.io/last-applied-configuration-scale": "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"alibabacloud.com/ip-stack\":\"ipv4\"}},\"spec\":{\"containers\":[{\"name\":\"main\",\"env\":[{\"name\":\"trafficRouteLabel\",\"value\":\"beta\"}]}]}}}}",
                    "kubectl.kubernetes.io/last-applier": "scale",
                    "sigma.ali/disable-cascading-deletion": "true",
                    "statefulset.beta1.sigma.ali/apprules-update-required": "false",
                    "statefulset.beta1.sigma.ali/partition": "0%",
                    "statefulset.beta1.sigma.ali/pod-upgrade-timeout": "1800",
                    "statefulset.beta1.sigma.ali/pods-to-delete": "9d46d207-0e36-432d-ab5c-62fd5ba969f4",
                    "statefulset.beta1.sigma.ali/publish-id": "127318589",
                    "statefulset.beta1.sigma.ali/updated-ready-replicas": "1",
                    "statefulset.beta1.sigma.ali/upgrade-max-unavailable": "1"
                },
                "finalizers": [
                    "alibabacloud.com/proxy-to-cloneset"
                ]
            },
            "spec": {
                "replicas": "1",
                "selector": {
                    "matchLabels": {
                        "sigma.ali/app-name": "alsc-adv-l-model",
                        "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                        "sigma.ali/site": "na610",
                        "sigma.ali/subgroup": "default",
                        "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                        "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "normandy.alibaba-inc.com/stack-id": "ce9afefd-36ee-4555-bb75-538b0278af1a",
                            "normandy.alibabacloud.com/order-id": "17613435000074-1-1",
                            "pod.beta1.sigma.ali/naming-register-state": "working_online",
                            "sigma.ali/app-name": "alsc-adv-l-model",
                            "sigma.ali/container-model": "dockervm",
                            "sigma.ali/inject-staragent-sidecar": "true",
                            "sigma.ali/instance-group": "alsc-adv-l-model_gpu2_host",
                            "sigma.ali/site": "na610",
                            "sigma.ali/subgroup": "default",
                            "sigma.ali/upstream-component": "normandy",
                            "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                            "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
                        },
                        "annotations": {
                            "alibabacloud.com/ip-stack": "ipv4",
                            "pod.beta1.alibabacloud.com/container-cpu-quota-unlimit": "true",
                            "pod.beta1.alibabacloud.com/zoom-in-cpu-quota": "1.2",
                            "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
                            "pod.beta1.sigma.ali/apprules": "{\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"constraints\":\"{}\",\"extConfig\":\"{}\",\"hostConfig\":\"{}\",\"monopolize\":\"{}\",\"prohibit\":\"{}\",\"resource_pool\":\"sigma_public\",\"spread\":\"{}\"}",
                            "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
                            "pod.beta1.sigma.ali/hostname-template": "alsc-adv-l-model{{.IpAddress}}.center.na610",
                            "pod.beta1.sigma.ali/naming-register-state": "working_online",
                            "sigma.ali/enable-apprules-injection": "true",
                            "sigma.ali/use-unified-pv": "true"
                        }
                    },
                    "spec": {
                        "volumes": [
                            {
                                "name": "44cde429260b721f6a8c26b99ab1601b",
                                "emptyDir": {}
                            },
                            {
                                "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                "emptyDir": {}
                            },
                            {
                                "name": "79e9ba5367e6c330f6e45be47de0bfad",
                                "emptyDir": {}
                            }
                        ],
                        "containers": [
                            {
                                "name": "main",
                                "image": "hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish",
                                "env": [
                                    {
                                        "name": "ali_aone_timestamp",
                                        "value": "*************"
                                    },
                                    {
                                        "name": "ali_start_app",
                                        "value": "no"
                                    },
                                    {
                                        "name": "ali_run_mode",
                                        "value": "common_vm"
                                    },
                                    {
                                        "name": "envSign",
                                        "value": "beta"
                                    },
                                    {
                                        "name": "trafficRouteLabel",
                                        "value": "beta"
                                    }
                                ],
                                "resources": {
                                    "limits": {
                                        "cpu": "64",
                                        "ephemeral-storage": "1099511627776",
                                        "memory": "206158430208",
                                        "nvidia.com/gpu": "8"
                                    },
                                    "requests": {
                                        "cpu": "64",
                                        "ephemeral-storage": "1099511627776",
                                        "memory": "206158430208",
                                        "nvidia.com/gpu": "8"
                                    }
                                },
                                "volumeMounts": [
                                    {
                                        "name": "44cde429260b721f6a8c26b99ab1601b",
                                        "mountPath": "/home/<USER>/logs"
                                    },
                                    {
                                        "name": "85cfbbfa3aefacdb2f49532dbb652151",
                                        "mountPath": "/home/<USER>/cai/logs"
                                    },
                                    {
                                        "name": "79e9ba5367e6c330f6e45be47de0bfad",
                                        "mountPath": "/home/<USER>/alsc-adv-l-model/logs"
                                    }
                                ],
                                "terminationMessagePath": "/dev/termination-log",
                                "livenessProbe": {
                                    "exec": {
                                        "command": [
                                            "sh",
                                            "-c",
                                            "date"
                                        ]
                                    },
                                    "failureThreshold": 10,
                                    "initialDelaySeconds": 10,
                                    "periodSeconds": 10,
                                    "successThreshold": 1,
                                    "timeoutSeconds": 1
                                },
                                "terminationMessagePolicy": "File",
                                "imagePullPolicy": "IfNotPresent"
                            }
                        ],
                        "restartPolicy": "Always",
                        "terminationGracePeriodSeconds": "1",
                        "dnsPolicy": "Default",
                        "securityContext": {},
                        "affinity": {
                            "nodeAffinity": {
                                "requiredDuringSchedulingIgnoredDuringExecution": {
                                    "nodeSelectorTerms": [
                                        {
                                            "matchExpressions": [
                                                {
                                                    "key": "sigma.ali/resource-pool",
                                                    "operator": "In",
                                                    "values": [
                                                        "sigma_public"
                                                    ]
                                                },
                                                {
                                                    "key": "sigma.ali/is-ecs",
                                                    "operator": "In",
                                                    "values": [
                                                        "true"
                                                    ]
                                                },
                                                {
                                                    "key": "alibabacloud.com/gpu-card-model-detail",
                                                    "operator": "In",
                                                    "values": [
                                                        "Tesla-V100-SXM2-32GB"
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        },
                        "schedulerName": "default-scheduler",
                        "enableServiceLinks": false
                    }
                },
                "serviceName": "",
                "podManagementPolicy": "OrderedReady",
                "updateStrategy": {
                    "type": "RollingUpdate",
                    "rollingUpdate": {
                        "partition": "*********"
                    }
                },
                "revisionHistoryLimit": 10
            }
        }
    """.trimIndent()



    @Test
    fun testThreeWayMerge_ScalePatch() {
        // ! kubectl.kubernetes.io/last-applied-configuration-scale
        /*
{
	"spec": {
		"template": {
			"metadata": {
				"annotations": {
					"alibabacloud.com/ip-stack": "ipv4"
				}
			},
			"spec": {
				"containers": [{
					"name": "main",
					"env": [{
						"name": "trafficRouteLabel",
						"value": "beta"
					}]
				}]
			}
		}
	}
}
         */
        val currentResourceObjectConfig = """
        {
          "spec": {
            "template": {
              "spec": {
                "containers": [
                  {
                    "name": "main",
                    "env": [
                      {
                        "name": "envSign",
                        "value": "prod"
                      }
                    ]
                  }
                ]
              }
            }
          }
        }
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }
        /*
        after deploy, scale inject an annotation "alibabacloud.com/ip-stack": "ipv4" to workload
        last-appied-configuration-scale contains annotation "alibabacloud.com/ip-stack": "ipv4", but last-applied-configuration does not
         */

        /*
        currentResourceObjectConfig delete trafficRouteLabel env, which is contains in last-applied-configuration, thus the deletion can not take effect
        currentResourceObjectConfig modify value of envSign env from beta to prod, which can take effect
        currentResourceObjectConfig delete "alibabacloud.com/ip-stack": "ipv4", which can take effect
         */

        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            originalResourceObjecSpec,
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.SCALE_OUT,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.JSON
        )

        val diff = Json.createDiff(
            Json.createReader(StringReader(originalResourceObjecSpec)).readValue() as JsonObject,
            Json.createReader(StringReader(finalPost)).readValue() as JsonObject
        );

        Assert.assertEquals(
            """

[
    {
        "op": "replace",
        "path": "/metadata/annotations/kubectl.kubernetes.io/last-applied-configuration-scale",
        "value": "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"main\",\"env\":[{\"name\":\"envSign\",\"value\":\"prod\"},{\"name\":\"trafficRouteLabel\",\"value\":\"beta\"}]}]}}}}"
    },
    {
        "op": "remove",
        "path": "/spec/template/metadata/annotations/alibabacloud.com/ip-stack"
    },
    {
        "op": "replace",
        "path": "/spec/template/spec/containers/0/env/3/value",
        "value": "prod"
    }
]""".trimIndent(),
            JsonUtils.format(diff.toJsonArray())
        )
    }

    @Test
    fun testThreeWayMerge_ScalePatch2() {

        val currentResourceObjectConfig = """
        {
          "spec": {
            "template": {
              "spec": {
                "affinity": {
                  "nodeAffinity": {
                    "requiredDuringSchedulingIgnoredDuringExecution": {
                      "nodeSelectorTerms": [
                        {
                          "matchExpressions": [
                            {
                              "key": "alibabacloud.com/gpu-card-model-detail",
                              "operator": "In",
                              "values": [
                                "Tesla-T4",
                                "T5"
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }

        val originalResourceObjecSpec = """
{
  "apiVersion": "apps/v1",
  "kind": "StatefulSet",
  "spec": {
    "template": {
      "spec": {
        "affinity": {
          "nodeAffinity": {
            "requiredDuringSchedulingIgnoredDuringExecution": {
              "nodeSelectorTerms": [
                {
                  "matchExpressions": [
                    {
                      "key": "sigma.ali/resource-pool",
                      "operator": "In",
                      "values": [
                        "sigma_public"
                      ]
                    },
                    {
                      "key": "alibabacloud.com/gpu-card-model-detail",
                      "operator": "In",
                      "values": [
                        "T4"
                      ]
                    }
                  ]
                },
                {
                  "matchExpressions": [
                    {
                      "key": "sigma.ali/resource-pool",
                      "operator": "In",
                      "values": [
                        "compute_platform"
                      ]
                    },
                    {
                      "key": "alibabacloud.com/gpu-card-model-detail",
                      "operator": "In",
                      "values": [
                        "T5"
                      ]
                    }
                  ]
                }
              ]
            }
          }
        }
      }
    }
  }
}
        """.trimIndent()
        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            originalResourceObjecSpec,
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.SCALE_OUT,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.JSON
        )


        Assert.assertEquals(
            """{"apiVersion":"apps/v1","kind":"StatefulSet","spec":{"template":{"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"sigma.ali/resource-pool","operator":"In","values":["sigma_public"]},{"key":"alibabacloud.com/gpu-card-model-detail","operator":"In","values":["Tesla-T4","T5"]}]},{"matchExpressions":[{"key":"sigma.ali/resource-pool","operator":"In","values":["compute_platform"]},{"key":"alibabacloud.com/gpu-card-model-detail","operator":"In","values":["Tesla-T4","T5"]}]}]}}}}}},"metadata":{"annotations":{"kubectl.kubernetes.io/last-applied-configuration-scale":"{\"spec\":{\"template\":{\"spec\":{\"affinity\":{\"nodeAffinity\":{\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"alibabacloud.com/gpu-card-model-detail\",\"operator\":\"In\",\"values\":[\"Tesla-T4\",\"T5\"]}]}]}}}}}}}","kubectl.kubernetes.io/last-applier":"scale"}}}""".trimIndent(),
            finalPost
        )
    }

    @Test
    fun testThreeWayMerge_ScalePatch_AfterDeploy() {
        val currentResourceObjectConfig = """
        {
          "spec": {
            "template": {
              "spec": {
                "containers": [
                  {
                    "name": "main",
                    "env": [
                      {
                        "name": "envSign",
                        "value": "prod"
                      }
                    ]
                  }
                ]
              }
            }
          }
        }
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }
        var originalResourceObjecSpecObj = YamlUtils.load(originalResourceObjecSpec)
        ResourceObjectService.getAnnotationsFromWorkloadSpec(originalResourceObjecSpecObj)?.apply {
            this.put(ResourceObjectService.LAST_APPLIER, ResourceObjectService.LAST_APPLIER_DEPLOY)
        }

        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            JsonUtils.writeValueAsString(originalResourceObjecSpecObj),
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.SCALE_OUT,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.JSON
        )

        val diff = Json.createDiff(
            Json.createReader(StringReader(originalResourceObjecSpec)).readValue() as JsonObject,
            Json.createReader(StringReader(finalPost)).readValue() as JsonObject
        );

        Assert.assertEquals(
            """

[
    {
        "op": "replace",
        "path": "/metadata/annotations/kubectl.kubernetes.io/last-applied-configuration-scale",
        "value": "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"main\",\"env\":[{\"name\":\"envSign\",\"value\":\"prod\"}]}]}}}}"
    },
    {
        "op": "replace",
        "path": "/spec/template/spec/containers/0/env/3/value",
        "value": "prod"
    }
]""".trimIndent(),
            JsonUtils.format(diff.toJsonArray())
        )
    }


    @Test
    fun `test_filteredLastApplied`() {
        var currentResourceObjectConfig = YamlUtils.load(originalResourceObjecSpec)
        ResourceObjectService.getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll{
            ResourceObjectService.PUBLISH_ANNOS.contains(it)
        }

        ResourceObjectService.getLabelsFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll{
            ResourceObjectService.PUBLISH_LABELS.contains(it)
        }

        ResourceObjectService.getSpecFromWorkloadSpec(currentResourceObjectConfig)?.keys?.removeAll{
            ResourceObjectService.PUBLISH_STRATEGY.contains(it)
        }

        Assert.assertFalse(
            ResourceObjectService.getAnnotationsFromWorkloadSpec(currentResourceObjectConfig)!!.keys.contains(PUBLISH_ID)
        )
        Assert.assertFalse(
            ResourceObjectService.getSpecFromWorkloadSpec(currentResourceObjectConfig)!!.keys.contains(UPDATE_PARTITION)
        )
        Assert.assertFalse(
            ResourceObjectService.getLabelsFromWorkloadSpec(currentResourceObjectConfig)!!.keys.contains(ROLLOUT_ID)
        )
    }

    @Test
    fun `test_filteredLastApplied_koastline-lib-container`() {
        var currentResourceObjectConfig = YamlUtils.load("""
            {
              "apiVersion": "apps/v1",
              "spec": {
                "template": {
                  "spec": {
                    "containers": [
                      {
                        "env": [
                          {
                            "name": "SIGMA_IGNORE_RESOURCE",
                            "value": "true"
                          },
                          {
                            "name": "SIGMA_IGNORE_READY",
                            "value": "true"
                          }
                        ],
                        "image": "reg-zhangbei.docker.alibaba-inc.com/aone/koastline-lib-injector_daily:20220317162653_daily",
                        "imagePullPolicy": "IfNotPresent",
                        "lifecycle": {
                          "postStart": {
                            "exec": {
                              "command": [
                                "/bin/sh",
                                "-c",
                                "cp -R -f /home/<USER>/app-lib-injector/* /home/<USER>/koastline/;"
                              ]
                            }
                          }
                        },
                        "name": "koastline-lib-injector",
                        "resources": {
                          "limits": {
                            "cpu": "500m",
                            "memory": "1Gi"
                          },
                          "requests": {
                            "cpu": "500m",
                            "memory": "1Gi"
                          }
                        },
                        "terminationMessagePath": "/dev/termination-log",
                        "terminationMessagePolicy": "File",
                        "volumeMounts": [
                          {
                            "mountPath": "/home/<USER>/koastline",
                            "name": "koastline-lib-injector"
                          }
                        ]
                      },
                      {
                        "env": [
                          {
                            "name": "ali_aone_timestamp",
                            "value": "1706500394173"
                          }
                        ],
                        "image": "hub.docker.alibaba-inc.com/aone/alsc-ks-basic-center:20240130190621794145_daily",
                        "imagePullPolicy": "IfNotPresent",
                        "name": "main",
                        "resources": {
                          "limits": {
                            "cpu": "4",
                            "ephemeral-storage": "**********0",
                            "memory": "8589934592"
                          },
                          "requests": {
                            "cpu": "4",
                            "ephemeral-storage": "**********0",
                            "memory": "8589934592"
                          }
                        },
                        "terminationMessagePath": "/dev/termination-log",
                        "terminationMessagePolicy": "File"
                      }
                    ]
                  }
                }
              }
            }
        """.trimIndent())
        ResourceObjectService.getContainersFromWorkloadSpec(currentResourceObjectConfig)?.removeAll{
            ResourceObjectService.PUBLISH_CONTAINERS.contains(it?.get("name"))
        }
        Assert.assertEquals("{\"apiVersion\":\"apps/v1\",\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"1706500394173\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/alsc-ks-basic-center:20240130190621794145_daily\",\"imagePullPolicy\":\"IfNotPresent\",\"name\":\"main\",\"resources\":{\"limits\":{\"cpu\":\"4\",\"ephemeral-storage\":\"**********0\",\"memory\":\"8589934592\"},\"requests\":{\"cpu\":\"4\",\"ephemeral-storage\":\"**********0\",\"memory\":\"8589934592\"}},\"terminationMessagePath\":\"/dev/termination-log\",\"terminationMessagePolicy\":\"File\"}]}}}}", JsonUtils.writeValueAsString(currentResourceObjectConfig))

    }


    @Test
    fun testThreeWayMerge_ScalePatch_mergeOptions() {
        val currentResourceObjectConfig = """
        {"spec":{"template":{"spec":{"containers":[{"name":"main","resources":{"requests":{"cpu":"2","memory":"**********","ephemeral-storage":"***********"},"limits":{"cpu":"2","memory":"**********","ephemeral-storage":"***********"}}}]}}}}
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }

        val originalResourceObjectSpecStr = """
                {"apiVersion":"apps/v1","kind":"StatefulSet","metadata":{"annotations":{"cloneset.beta1.sigma.ali/app-fail-count":"0","cloneset.beta1.sigma.ali/expected-updated-replicas":"69","cloneset.beta1.sigma.ali/image-fail-count":"0","cloneset.beta1.sigma.ali/publish-success-replicas":"69","cloneset.beta1.sigma.ali/scheduled-fail-count":"0","inplaceset.beta1.sigma.ali/upgrade-scatter":"pod.beta1.sigma.ali/naming-register-state=working_online","kubectl.kubernetes.io/last-applied-configuration":"{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{\"sigma.ali/upgrade-merge-annotations\":\"pod.beta1.sigma.ali/disable-lifecycle-hook-inject\",\"statefulset.beta1.sigma.ali/partition\":\"90%\",\"statefulset.beta1.sigma.ali/pod-upgrade-timeout\":\"1800\",\"statefulset.beta1.sigma.ali/publish-id\":\"*********\",\"statefulset.beta1.sigma.ali/upgrade-max-unavailable\":\"17\"},\"labels\":{\"apps.kruise.io/rollout-batch-id\":\"0\",\"apps.kruise.io/rollout-id\":\"*********\"},\"name\":\"pill-env-44f53a90-6bfa-4262-9f22-2d8cb6c5--n3\",\"namespace\":\"pill-env\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1860\\\",\\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\",\\\"ImagePullTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/env-sign\":\"public-base\",\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"SIGMA_IGNORE_RESOURCE\",\"value\":\"true\"},{\"name\":\"SIGMA_IGNORE_READY\",\"value\":\"true\"}],\"image\":\"reg-zhangbei.docker.alibaba-inc.com/aone/koastline-lib-injector_daily:20220317162653_daily\",\"lifecycle\":{\"postStart\":{\"exec\":{\"command\":[\"/bin/sh\",\"-c\",\"cp -R -f /home/<USER>/app-lib-injector/* /home/<USER>/koastline/;\"]}}},\"name\":\"koastline-lib-injector\",\"resources\":{\"limits\":{\"cpu\":\"500m\",\"memory\":\"1Gi\"},\"requests\":{\"cpu\":\"500m\",\"memory\":\"1Gi\"}},\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/koastline\",\"name\":\"koastline-lib-injector\"}]},{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"public-base\"},{\"name\":\"trafficRouteLabel\",\"value\":\"public-base\"},{\"name\":\"crIds\",\"value\":\"********\"},{\"name\":\"exec_scm_hook\",\"value\":\"yes\"},{\"name\":\"ali_app_name\",\"value\":\"pill-env\"},{\"name\":\"ali_env_sign\",\"value\":\"public-base\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/pill-env:20231102184135968996\",\"name\":\"main\",\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/workspace\",\"name\":\"3f6275bd8aad7e35d71db930051eec5c\"},{\"mountPath\":\"/opt/pill-env/aerial\",\"name\":\"b134d16e67082c4a7ff8c0c64e361347\"},{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/pill-env/logs\",\"name\":\"c47cd56dd06d8ff1663d53b2c148a602\"},{\"mountPath\":\"/home/<USER>/koastline\",\"name\":\"koastline-lib-injector\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":1,\"volumes\":[{\"emptyDir\":{},\"name\":\"3f6275bd8aad7e35d71db930051eec5c\"},{\"emptyDir\":{},\"name\":\"b134d16e67082c4a7ff8c0c64e361347\"},{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"c47cd56dd06d8ff1663d53b2c148a602\"},{\"emptyDir\":{},\"name\":\"koastline-lib-injector\"}]}},\"updateStrategy\":{\"rollingUpdate\":{\"partition\":*********}}}}\n","kubectl.kubernetes.io/last-applied-configuration-scale":"{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"main\",\"resources\":{\"requests\":{\"cpu\":\"2\",\"memory\":\"**********\",\"ephemeral-storage\":\"***********\"},\"limits\":{\"cpu\":\"2\",\"memory\":\"**********\",\"ephemeral-storage\":\"***********\"}}}],\"automountServiceAccountToken\":false}}}}","kubectl.kubernetes.io/last-applier":"scale","sigma.ali/disable-cascading-deletion":"true","sigma.ali/upgrade-merge-annotations":"pod.beta1.sigma.ali/disable-lifecycle-hook-inject","statefulset.beta1.sigma.ali/apprules-update-required":"false","statefulset.beta1.sigma.ali/partition":"0%","statefulset.beta1.sigma.ali/pod-upgrade-timeout":"1800","statefulset.beta1.sigma.ali/pods-to-delete":"pill-env--3f8f9986-1628-4f0a-aeff-6a67e1fa4624","statefulset.beta1.sigma.ali/publish-id":"*********","statefulset.beta1.sigma.ali/updated-ready-replicas":"69","statefulset.beta1.sigma.ali/upgrade-max-unavailable":"17"},"creationTimestamp":"2022-07-15T09:04:51.000000Z","finalizers":["alibabacloud.com/proxy-to-cloneset"],"generation":2379,"labels":{"apps.kruise.io/rollout-batch-id":"9","apps.kruise.io/rollout-id":"*********","inplaceset.sigma.ali/proxy":"CloneSet","normandy.alibabacloud.com/order-id":"**************-1-1","sigma.ali/app-name":"pill-env","sigma.ali/disable-default-pdb-strategy":"true","sigma.ali/instance-group":"pill-env_public-base_3594596_testhost","sigma.ali/site":"na131","sigma.ali/subgroup":"default","sigma.alibaba-inc.com/app-stage":"DAILY","sigma.alibaba-inc.com/app-unit":"CENTER_UNIT.center","statefulset.sigma.ali/mode":"sigma"},"name":"pill-env-44f53a90-6bfa-4262-9f22-2d8cb6c5--n3","namespace":"pill-env","resourceVersion":"33122263762","uid":"1713b896-9544-4488-9f1c-135a910c41c8"},"spec":{"podManagementPolicy":"OrderedReady","replicas":69,"revisionHistoryLimit":10,"selector":{"matchLabels":{"sigma.ali/app-name":"pill-env","sigma.ali/instance-group":"pill-env_public-base_3594596_testhost","sigma.ali/site":"na131","sigma.ali/subgroup":"default","sigma.alibaba-inc.com/app-stage":"DAILY","sigma.alibaba-inc.com/app-unit":"CENTER_UNIT.center"}},"serviceName":"","template":{"metadata":{"annotations":{"alibabacloud.com/ip-stack":"ipv4","pod.beta1.sigma.ali/alarming-off-upgrade":"true","pod.beta1.sigma.ali/apprules":"{\"allocSpec\":\"{}\",\"constraints\":\"{}\",\"extConfig\":\"{}\",\"hostConfig\":\"{}\",\"isOverQuota\":\"true\",\"monopolize\":\"{}\",\"prohibit\":\"{}\",\"resource_pool\":\"sigma_public\",\"spread\":\"{}\"}","pod.beta1.sigma.ali/container-extra-config":"{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1860\",\"PreStopHookTimeoutSeconds\":\"600\",\"ImagePullTimeoutSeconds\":\"600\"}}}","pod.beta1.sigma.ali/hostname-template":"pill-env{{.IpAddress}}.na131","pod.beta1.sigma.ali/naming-register-state":"working_online","sigma.ali/enable-apprules-injection":"true","sigma.ali/use-unified-pv":"true"},"labels":{"normandy.alibaba-inc.com/stack-id":"7c346186-a010-41d6-bd70-e459c37e529c","normandy.alibabacloud.com/order-id":"**************-1-1","pod.beta1.sigma.ali/naming-register-state":"working_online","sigma.ali/app-name":"pill-env","sigma.ali/container-model":"dockervm","sigma.ali/env-sign":"public-base","sigma.ali/inject-staragent-sidecar":"true","sigma.ali/instance-group":"pill-env_public-base_3594596_testhost","sigma.ali/site":"na131","sigma.ali/subgroup":"default","sigma.ali/upstream-component":"normandy","sigma.alibaba-inc.com/app-stage":"DAILY","sigma.alibaba-inc.com/app-unit":"CENTER_UNIT.center"}},"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"sigma.ali/resource-pool","operator":"In","values":["sigma_public"]},{"key":"sigma.ali/is-ecs","operator":"In","values":["true"]}]}]}}},"containers":[{"env":[{"name":"SIGMA_IGNORE_RESOURCE","value":"true"},{"name":"SIGMA_IGNORE_READY","value":"true"}],"image":"reg-zhangbei.docker.alibaba-inc.com/aone/koastline-lib-injector_daily:20220317162653_daily","imagePullPolicy":"IfNotPresent","lifecycle":{"postStart":{"exec":{"command":["/bin/sh","-c","cp -R -f /home/<USER>/app-lib-injector/* /home/<USER>/koastline/;"]}}},"name":"koastline-lib-injector","resources":{"limits":{"cpu":"500m","memory":"1Gi"},"requests":{"cpu":"500m","memory":"1Gi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/home/<USER>/koastline","name":"koastline-lib-injector"}]},{"env":[{"name":"ali_aone_timestamp","value":"*************"},{"name":"ali_start_app","value":"no"},{"name":"ali_run_mode","value":"common_vm"},{"name":"envSign","value":"public-base"},{"name":"trafficRouteLabel","value":"public-base"},{"name":"crIds","value":"********"},{"name":"exec_scm_hook","value":"yes"},{"name":"ali_app_name","value":"pill-env"},{"name":"ali_env_sign","value":"public-base"}],"image":"hub.docker.alibaba-inc.com/aone/pill-env:20231102184135968996","imagePullPolicy":"IfNotPresent","name":"main","resources":{"requests":{"cpu":"2","memory":"**********","ephemeral-storage":"***********"},"limits":{"cpu":"2","memory":"**********","ephemeral-storage":"***********"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/home/<USER>/workspace","name":"3f6275bd8aad7e35d71db930051eec5c"},{"mountPath":"/opt/pill-env/aerial","name":"b134d16e67082c4a7ff8c0c64e361347"},{"mountPath":"/home/<USER>/logs","name":"44cde429260b721f6a8c26b99ab1601b"},{"mountPath":"/home/<USER>/cai/logs","name":"85cfbbfa3aefacdb2f49532dbb652151"},{"mountPath":"/home/<USER>/pill-env/logs","name":"c47cd56dd06d8ff1663d53b2c148a602"},{"mountPath":"/home/<USER>/koastline","name":"koastline-lib-injector"}]}],"dnsPolicy":"Default","enableServiceLinks":false,"restartPolicy":"Always","schedulerName":"default-scheduler","securityContext":{},"terminationGracePeriodSeconds":1,"volumes":[{"emptyDir":{},"name":"3f6275bd8aad7e35d71db930051eec5c"},{"emptyDir":{},"name":"b134d16e67082c4a7ff8c0c64e361347"},{"emptyDir":{},"name":"44cde429260b721f6a8c26b99ab1601b"},{"emptyDir":{},"name":"85cfbbfa3aefacdb2f49532dbb652151"},{"emptyDir":{},"name":"c47cd56dd06d8ff1663d53b2c148a602"},{"emptyDir":{},"name":"koastline-lib-injector"}]}},"updateStrategy":{"rollingUpdate":{"partition":*********},"type":"RollingUpdate"}},"status":{"collisionCount":0,"currentReplicas":69,"currentRevision":"pill-env-44f53a90-6bfa-4262-9f22-2d8cb6c5--n3-774cc49767","observedGeneration":2379,"readyReplicas":69,"replicas":69,"updateRevision":"pill-env-44f53a90-6bfa-4262-9f22-2d8cb6c5--n3-774cc49767","updatedReplicas":69}}
            """.trimIndent()
        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            originalResourceObjectSpecStr,
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.SCALE_OUT,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.JSON
        )

        val diff = Json.createDiff(
            Json.createReader(StringReader(originalResourceObjectSpecStr)).readValue() as JsonObject,
            Json.createReader(StringReader(finalPost)).readValue() as JsonObject
        );

        Assert.assertEquals(
            """

[
    {
        "op": "add",
        "path": "/spec/template/spec/automountServiceAccountToken",
        "value": false
    }
]""".trimIndent(),
            JsonUtils.format(diff.toJsonArray())
        )
    }

    @Test
    fun testThreeWayMerge_DeployPatch() {
        /*
       after deploy, scale inject an annotation "alibabacloud.com/ip-stack": "ipv4" to workload
       last-appied-configuration-scale contains annotation "alibabacloud.com/ip-stack": "ipv4", but last-applied-configuration does not
        */

        /*
        currentResourceObjectConfig delete trafficRouteLabel env, which can take effect
        currentResourceObjectConfig modify value of envSign env from beta to prod, which can take effect
        currentResourceObjectConfig delete "alibabacloud.com/ip-stack": "ipv4", which can take effect
         */
        val currentResourceObjectConfig = """
        {
          "apiVersion": "apps/v1",
          "kind": "StatefulSet",
          "metadata": {
            "annotations": {
              "statefulset.beta1.sigma.ali/partition": "0%",
              "statefulset.beta1.sigma.ali/pod-upgrade-timeout": "1800",
              "statefulset.beta1.sigma.ali/publish-id": "127318589",
              "statefulset.beta1.sigma.ali/upgrade-max-unavailable": "1"
            },
            "labels": {
              "apps.kruise.io/rollout-batch-id": "0",
              "apps.kruise.io/rollout-id": "127318589"
            },
            "name": "alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3",
            "namespace": "alsc-adv-l-model"
          },
          "spec": {
            "template": {
              "metadata": {
                "annotations": {
                  "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
                  "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
                  "pod.beta1.sigma.ali/naming-register-state": "working_online",
                  "sigma.ali/enable-apprules-injection": "true",
                  "sigma.ali/use-unified-pv": "true"
                },
                "labels": {
                  "sigma.ali/inject-staragent-sidecar": "true"
                }
              },
              "spec": {
                "automountServiceAccountToken": false,
                "containers": [
                  {
                    "env": [
                      {
                        "name": "ali_aone_timestamp",
                        "value": "*************"
                      },
                      {
                        "name": "ali_start_app",
                        "value": "no"
                      },
                      {
                        "name": "ali_run_mode",
                        "value": "common_vm"
                      },
                      {
                        "name": "envSign",
                        "value": "prod"
                      }
                    ],
                    "image": "hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish",
                    "name": "main",
                    "livenessProbe": {
                        "failureThreshold": 10,
                        "httpGet": {
                            "path": "/actuator/appHealth/liveness",
                            "port": 7002,
                            "scheme": "HTTP"
                        },
                        "initialDelaySeconds": 10,
                        "periodSeconds": 10,
                        "successThreshold": 1,
                        "timeoutSeconds": 1
                    },
                    "volumeMounts": [
                      {
                        "mountPath": "/home/<USER>/logs",
                        "name": "44cde429260b721f6a8c26b99ab1601b"
                      },
                      {
                        "mountPath": "/home/<USER>/cai/logs",
                        "name": "85cfbbfa3aefacdb2f49532dbb652151"
                      },
                      {
                        "mountPath": "/home/<USER>/alsc-adv-l-model/logs",
                        "name": "79e9ba5367e6c330f6e45be47de0bfad"
                      }
                    ]
                  }
                ],
                "dnsPolicy": "Default",
                "enableServiceLinks": false,
                "terminationGracePeriodSeconds": "1",
                "volumes": [
                  {
                    "emptyDir": {},
                    "name": "44cde429260b721f6a8c26b99ab1601b"
                  },
                  {
                    "emptyDir": {},
                    "name": "85cfbbfa3aefacdb2f49532dbb652151"
                  },
                  {
                    "emptyDir": {},
                    "name": "79e9ba5367e6c330f6e45be47de0bfad"
                  }
                ]
              }
            },
            "updateStrategy": {
              "rollingUpdate": {
                "partition": "*********"
              }
            }
          }
        }
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }


        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            originalResourceObjecSpec,
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.DEPLOY,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.YAML
        )





        val diff = Json.createDiff(
            Json.createReader(StringReader(originalResourceObjecSpec)).readValue() as JsonObject,
            Json.createReader(StringReader(YamlUtils.yaml2json(finalPost))).readValue() as JsonObject
        );



        Assert.assertEquals(
            """

[
    {
        "op": "replace",
        "path": "/metadata/annotations/kubectl.kubernetes.io/last-applied-configuration",
        "value": "{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{},\"labels\":{},\"name\":\"alsc-adv-l-model-62c20f6c-dcbe-41d2-900e---n3\",\"namespace\":\"alsc-adv-l-model\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1800\\\", \\\"ImagePullTimeoutSeconds\\\":\\\"600\\\", \\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"prod\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/alsc-adv-l-model:20230728100449889241_publish\",\"name\":\"main\",\"livenessProbe\":{\"failureThreshold\":10,\"httpGet\":{\"path\":\"/actuator/appHealth/liveness\",\"port\":7002,\"scheme\":\"HTTP\"},\"initialDelaySeconds\":10,\"periodSeconds\":10,\"successThreshold\":1,\"timeoutSeconds\":1},\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/alsc-adv-l-model/logs\",\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":\"1\",\"volumes\":[{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"79e9ba5367e6c330f6e45be47de0bfad\"}]}}}}"
    },
    {
        "op": "replace",
        "path": "/metadata/annotations/kubectl.kubernetes.io/last-applier",
        "value": "deploy"
    },
    {
        "op": "remove",
        "path": "/spec/template/metadata/annotations/alibabacloud.com/ip-stack"
    },
    {
        "op": "replace",
        "path": "/spec/template/spec/containers/0/env/4/name",
        "value": "envSign"
    },
    {
        "op": "replace",
        "path": "/spec/template/spec/containers/0/env/4/value",
        "value": "prod"
    },
    {
        "op": "remove",
        "path": "/spec/template/spec/containers/0/env/3"
    },
    {
        "op": "remove",
        "path": "/spec/template/spec/containers/0/livenessProbe/exec"
    },
    {
        "op": "add",
        "path": "/spec/template/spec/containers/0/livenessProbe/httpGet",
        "value": {
            "path": "/actuator/appHealth/liveness",
            "port": 7002,
            "scheme": "HTTP"
        }
    },
    {
        "op": "add",
        "path": "/spec/template/spec/automountServiceAccountToken",
        "value": false
    }
]""".trimIndent(),
            JsonUtils.format(diff.toJsonArray())
        )
    }

    @Test
    fun testThreeWayMerge_DeployPatch_Init() {

        val currentResourceObjectConfig = """
{
  "apiVersion": "apps/v1",
  "kind": "StatefulSet",
  "metadata": {
    "name": "fc-promotion-system-5f85ed1e-6aa9-46b5-9d--n3",
    "namespace": "fc-promotion-system"
  },
  "spec": {
    "template": {
      "metadata": {
        "annotations": {
          "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
          "pod.beta1.sigma.ali/naming-register-state": "working_online",
          "sigma.ali/use-unified-pv": "true",
          "sigma.ali/enable-apprules-injection": "true",
          "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
          "alibabacloud.com/inject-security-atp": "{\"initContainerName\":\"security-atp-init\",\"initContainerImage\":\"hub.docker.alibaba-inc.com/aone-base-global/sec_atp_1.3.9.5:20240308103025\",\"initContainerCommand\":[\"sh\", \"-c\", \"/home/<USER>/atp/install.sh\"],\"volumeName\":\"security-atp\",\"volumePath\":\"/opt/security-atp\",\"loaderFilePath\":\"/opt/security-atp/file/agent_so/libatp_preload.so\"}"
        },
        "labels": {
          "sigma.ali/inject-staragent-sidecar": "true"
        }
      },
      "spec": {
        "affinity": {
          "nodeAffinity": {
            "requiredDuringSchedulingIgnoredDuringExecution": {
              "nodeSelectorTerms": [
                {
                  "matchExpressions": [
                    {
                      "key": "sigma.ali/resource-pool",
                      "operator": "In",
                      "values": [
                        "sigma_public"
                      ]
                    },
                    {
                      "key": "sigma.ali/is-ecs",
                      "operator": "In",
                      "values": [
                        "true"
                      ]
                    }
                  ]
                }
              ]
            }
          }
        },
        "automountServiceAccountToken": false,
        "containers": [
          {
            "env": [
              {
                "name": "ali_aone_timestamp",
                "value": "*************"
              },
              {
                "name": "ali_start_app",
                "value": "no"
              },
              {
                "name": "ali_run_mode",
                "value": "common_vm"
              },
              {
                "name": "envSign",
                "value": "staging"
              },
              {
                "name": "trafficRouteLabel",
                "value": "staging"
              },
              {
                "name": "ALIYUN_LOGTAIL_USER_DEFINED_ID",
                "value": "acni_v1_normandy_fc-promotion-system_online_d70362ae-4d04-48fa-bb2d-21f7de604571"
              },
              {
                "name": "ALIYUN_LOGTAIL_USER_ID",
                "value": ****************
              }
            ],
            "image": "hub.docker.alibaba-inc.com/aone/fc-promotion-system_prepub:20240418111730532607_prepub",
            "name": "main",
            "volumeMounts": [
              {
                "mountPath": "/home/<USER>/liaoyuan-out",
                "name": "26f907fb3bf70c5f850d72be0d60db4a"
              },
              {
                "mountPath": "/home/<USER>/notify",
                "name": "98e3f9a7853ee1e66fa98766fa1af604"
              },
              {
                "mountPath": "/home/<USER>/snapshots",
                "name": "d043ab5abf87f2109e0ed379d4fbb275"
              },
              {
                "mountPath": "/home/<USER>/.rocketmq_offsets",
                "name": "fdca7e870b8663f2088472f477f40170"
              },
              {
                "mountPath": "/home/<USER>/configclient",
                "name": "f1c341b95cb59b00f2a72dbfabb2c4cd"
              },
              {
                "mountPath": "/home/<USER>/vipsrv-dns",
                "name": "4594f9cc2b6414e0c27ea07d5b2646f7"
              },
              {
                "mountPath": "/home/<USER>/amsdata",
                "name": "b28dce6991f47354f1a8f2383fe95695"
              },
              {
                "mountPath": "/home/<USER>/vipsrv-failover",
                "name": "e8efefb1cdce1d55487529d6e6c8fed8"
              },
              {
                "mountPath": "/home/<USER>/amsdata_all",
                "name": "5d141dda47411ed9f2cacc42433e1e16"
              },
              {
                "mountPath": "/home/<USER>/catserver",
                "name": "6426549ef68729f5b5679988daab4051"
              },
              {
                "mountPath": "/home/<USER>/cai/logs",
                "name": "85cfbbfa3aefacdb2f49532dbb652151"
              },
              {
                "mountPath": "/home/<USER>/vipsrv-cache",
                "name": "d995b338ee85c4415fa3306a90587664"
              },
              {
                "mountPath": "/home/<USER>/diamond",
                "name": "324a215d6bec6bffc99895f0d8fe5580"
              },
              {
                "mountPath": "/home/<USER>/fc-promotion-system/logs",
                "name": "5102f36195e1f1332e8885065f210331"
              },
              {
                "mountPath": "/home/<USER>/csp",
                "name": "dad113c337563f905a93c0793b880d9d"
              },
              {
                "mountPath": "/home/<USER>/logs",
                "name": "44cde429260b721f6a8c26b99ab1601b"
              },
              {
                "name": "acni-crevol-cmms-measure-info",
                "readOnly": "true",
                "mountPath": "/etc/cmms"
              },
              {
                "name": "acni-crevol-safebox-meta",
                "readOnly": "true",
                "mountPath": "/etc/acni/safebox-meta/target"
              }
            ],
            "resources": {
              "requests": {
                "cpu": "1",
                "memory": "8589934592",
                "ephemeral-storage": "***********"
              },
              "limits": {
                "cpu": "1",
                "memory": "8589934592",
                "ephemeral-storage": "***********"
              }
            }
          }
        ],
        "dnsPolicy": "Default",
        "enableServiceLinks": false,
        "terminationGracePeriodSeconds": 1,
        "volumes": [
          {
            "emptyDir": {},
            "name": "4594f9cc2b6414e0c27ea07d5b2646f7"
          },
          {
            "emptyDir": {},
            "name": "fdca7e870b8663f2088472f477f40170"
          },
          {
            "emptyDir": {},
            "name": "b28dce6991f47354f1a8f2383fe95695"
          },
          {
            "emptyDir": {},
            "name": "5d141dda47411ed9f2cacc42433e1e16"
          },
          {
            "emptyDir": {},
            "name": "85cfbbfa3aefacdb2f49532dbb652151"
          },
          {
            "emptyDir": {},
            "name": "6426549ef68729f5b5679988daab4051"
          },
          {
            "emptyDir": {},
            "name": "f1c341b95cb59b00f2a72dbfabb2c4cd"
          },
          {
            "emptyDir": {},
            "name": "dad113c337563f905a93c0793b880d9d"
          },
          {
            "emptyDir": {},
            "name": "324a215d6bec6bffc99895f0d8fe5580"
          },
          {
            "emptyDir": {},
            "name": "5102f36195e1f1332e8885065f210331"
          },
          {
            "emptyDir": {},
            "name": "26f907fb3bf70c5f850d72be0d60db4a"
          },
          {
            "emptyDir": {},
            "name": "44cde429260b721f6a8c26b99ab1601b"
          },
          {
            "emptyDir": {},
            "name": "98e3f9a7853ee1e66fa98766fa1af604"
          },
          {
            "emptyDir": {},
            "name": "d043ab5abf87f2109e0ed379d4fbb275"
          },
          {
            "emptyDir": {},
            "name": "d995b338ee85c4415fa3306a90587664"
          },
          {
            "emptyDir": {},
            "name": "e8efefb1cdce1d55487529d6e6c8fed8"
          },
          {
            "name": "acni-crevol-cmms-measure-info",
            "secret": {
              "secretName": "sec-acni.v2.app-group-sys.fc-promotion-system.online.fc-promotion-system.prehost-mgcsym-cmms-measure-info",
              "defaultMode": 420
            }
          },
          {
            "name": "acni-crevol-safebox-meta",
            "secret": {
              "secretName": "sec-acni.v1.normandy.fc-promotion-system.online.d70362ae-4d04-48fa-bb2d-21f7de604571-mgcsym-safebox-meta",
              "defaultMode": 420
            }
          }
        ]
      }
    }
  }
}
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }


        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            """
 {
   "apiVersion": "apps/v1",
   "kind": "StatefulSet",
   "metadata": {
     "annotations": {
       "cloneset.beta1.sigma.ali/app-fail-count": "0",
       "cloneset.beta1.sigma.ali/expected-updated-replicas": "1",
       "cloneset.beta1.sigma.ali/image-fail-count": "0",
       "cloneset.beta1.sigma.ali/publish-success-replicas": "1",
       "cloneset.beta1.sigma.ali/scheduled-fail-count": "0",
       "inplaceset.beta1.sigma.ali/upgrade-scatter": "pod.beta1.sigma.ali/naming-register-state=working_online",
       "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"StatefulSet\",\"metadata\":{\"annotations\":{\"sigma.ali/upgrade-merge-annotations\":\"pod.beta1.sigma.ali/disable-lifecycle-hook-inject\",\"statefulset.beta1.sigma.ali/partition\":\"0%\",\"statefulset.beta1.sigma.ali/pod-upgrade-timeout\":\"1800\",\"statefulset.beta1.sigma.ali/publish-id\":\"*********\",\"statefulset.beta1.sigma.ali/upgrade-max-unavailable\":\"100%\"},\"labels\":{\"apps.kruise.io/rollout-batch-id\":\"0\",\"apps.kruise.io/rollout-id\":\"*********\"},\"name\":\"fc-promotion-system-5f85ed1e-6aa9-46b5-9d--n3\",\"namespace\":\"fc-promotion-system\"},\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"alibabacloud.com/inject-security-atp\":\"{\\\"initContainerName\\\":\\\"security-atp-init\\\",\\\"initContainerImage\\\":\\\"hub.docker.alibaba-inc.com/aone-base-global/sec_atp_1.3.9.5:20240308103025\\\",\\\"initContainerCommand\\\":[\\\"sh\\\", \\\"-c\\\", \\\"/home/<USER>/atp/install.sh\\\"],\\\"volumeName\\\":\\\"security-atp\\\",\\\"volumePath\\\":\\\"/opt/security-atp\\\",\\\"loaderFilePath\\\":\\\"/opt/security-atp/file/agent_so/libatp_preload.so\\\"}\",\"pod.beta1.sigma.ali/alarming-off-upgrade\":\"true\",\"pod.beta1.sigma.ali/container-extra-config\":\"{\\\"containerConfigs\\\":{\\\"main\\\":{\\\"PostStartHookTimeoutSeconds\\\":\\\"1800\\\", \\\"ImagePullTimeoutSeconds\\\":\\\"600\\\", \\\"PreStopHookTimeoutSeconds\\\":\\\"600\\\"}}}\",\"pod.beta1.sigma.ali/naming-register-state\":\"working_online\",\"sigma.ali/enable-apprules-injection\":\"true\",\"sigma.ali/use-unified-pv\":\"true\"},\"labels\":{\"sigma.ali/inject-staragent-sidecar\":\"true\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"sigma.ali/resource-pool\",\"operator\":\"In\",\"values\":[\"sigma_public\"]},{\"key\":\"sigma.ali/is-ecs\",\"operator\":\"In\",\"values\":[\"true\"]}]}]}}},\"automountServiceAccountToken\":false,\"containers\":[{\"env\":[{\"name\":\"ali_aone_timestamp\",\"value\":\"*************\"},{\"name\":\"ali_start_app\",\"value\":\"no\"},{\"name\":\"ali_run_mode\",\"value\":\"common_vm\"},{\"name\":\"envSign\",\"value\":\"staging\"},{\"name\":\"trafficRouteLabel\",\"value\":\"staging\"},{\"name\":\"ALIYUN_LOGTAIL_CONFIG\",\"value\":\"/etc/ilogtail/conf/cn-zhangjiakou-internet/ilogtail_config.json\"},{\"name\":\"ALIYUN_LOGTAIL_USER_DEFINED_ID\",\"value\":\"acni_v1_normandy_fc-promotion-system_online_d70362ae-4d04-48fa-bb2d-21f7de604571\"},{\"name\":\"ALIYUN_LOGTAIL_USER_ID\",\"value\":\"****************\"},{\"name\":\"exec_scm_hook\",\"value\":\"yes\"},{\"name\":\"ali_app_name\",\"value\":\"fc-promotion-system\"},{\"name\":\"ali_env_sign\",\"value\":\"staging\"}],\"image\":\"hub.docker.alibaba-inc.com/aone/fc-promotion-system_prepub:20240415155017500847_prepub\",\"name\":\"main\",\"resources\":{\"limits\":{\"cpu\":\"1\",\"ephemeral-storage\":\"***********\",\"memory\":\"8589934592\"},\"requests\":{\"cpu\":\"1\",\"ephemeral-storage\":\"***********\",\"memory\":\"8589934592\"}},\"volumeMounts\":[{\"mountPath\":\"/home/<USER>/liaoyuan-out\",\"name\":\"26f907fb3bf70c5f850d72be0d60db4a\"},{\"mountPath\":\"/home/<USER>/notify\",\"name\":\"98e3f9a7853ee1e66fa98766fa1af604\"},{\"mountPath\":\"/home/<USER>/snapshots\",\"name\":\"d043ab5abf87f2109e0ed379d4fbb275\"},{\"mountPath\":\"/home/<USER>/.rocketmq_offsets\",\"name\":\"fdca7e870b8663f2088472f477f40170\"},{\"mountPath\":\"/home/<USER>/configclient\",\"name\":\"f1c341b95cb59b00f2a72dbfabb2c4cd\"},{\"mountPath\":\"/home/<USER>/vipsrv-dns\",\"name\":\"4594f9cc2b6414e0c27ea07d5b2646f7\"},{\"mountPath\":\"/home/<USER>/amsdata\",\"name\":\"b28dce6991f47354f1a8f2383fe95695\"},{\"mountPath\":\"/home/<USER>/vipsrv-failover\",\"name\":\"e8efefb1cdce1d55487529d6e6c8fed8\"},{\"mountPath\":\"/home/<USER>/amsdata_all\",\"name\":\"5d141dda47411ed9f2cacc42433e1e16\"},{\"mountPath\":\"/home/<USER>/catserver\",\"name\":\"6426549ef68729f5b5679988daab4051\"},{\"mountPath\":\"/home/<USER>/cai/logs\",\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"mountPath\":\"/home/<USER>/vipsrv-cache\",\"name\":\"d995b338ee85c4415fa3306a90587664\"},{\"mountPath\":\"/home/<USER>/diamond\",\"name\":\"324a215d6bec6bffc99895f0d8fe5580\"},{\"mountPath\":\"/home/<USER>/fc-promotion-system/logs\",\"name\":\"5102f36195e1f1332e8885065f210331\"},{\"mountPath\":\"/home/<USER>/csp\",\"name\":\"dad113c337563f905a93c0793b880d9d\"},{\"mountPath\":\"/home/<USER>/logs\",\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"mountPath\":\"/etc/cmms\",\"name\":\"acni-crevol-cmms-measure-info\",\"readOnly\":true},{\"mountPath\":\"/etc/acni/safebox-meta/target\",\"name\":\"acni-crevol-safebox-meta\",\"readOnly\":true},{\"mountPath\":\"/home/<USER>/fc-promotion-system/logs\",\"name\":\"********************************\"}]}],\"dnsPolicy\":\"Default\",\"enableServiceLinks\":false,\"terminationGracePeriodSeconds\":1,\"volumes\":[{\"emptyDir\":{},\"name\":\"4594f9cc2b6414e0c27ea07d5b2646f7\"},{\"emptyDir\":{},\"name\":\"fdca7e870b8663f2088472f477f40170\"},{\"emptyDir\":{},\"name\":\"b28dce6991f47354f1a8f2383fe95695\"},{\"emptyDir\":{},\"name\":\"5d141dda47411ed9f2cacc42433e1e16\"},{\"emptyDir\":{},\"name\":\"85cfbbfa3aefacdb2f49532dbb652151\"},{\"emptyDir\":{},\"name\":\"6426549ef68729f5b5679988daab4051\"},{\"emptyDir\":{},\"name\":\"f1c341b95cb59b00f2a72dbfabb2c4cd\"},{\"emptyDir\":{},\"name\":\"dad113c337563f905a93c0793b880d9d\"},{\"emptyDir\":{},\"name\":\"324a215d6bec6bffc99895f0d8fe5580\"},{\"emptyDir\":{},\"name\":\"5102f36195e1f1332e8885065f210331\"},{\"emptyDir\":{},\"name\":\"26f907fb3bf70c5f850d72be0d60db4a\"},{\"emptyDir\":{},\"name\":\"44cde429260b721f6a8c26b99ab1601b\"},{\"emptyDir\":{},\"name\":\"98e3f9a7853ee1e66fa98766fa1af604\"},{\"emptyDir\":{},\"name\":\"d043ab5abf87f2109e0ed379d4fbb275\"},{\"emptyDir\":{},\"name\":\"d995b338ee85c4415fa3306a90587664\"},{\"emptyDir\":{},\"name\":\"e8efefb1cdce1d55487529d6e6c8fed8\"},{\"name\":\"acni-crevol-cmms-measure-info\",\"secret\":{\"defaultMode\":420,\"secretName\":\"sec-acni.v2.app-group-sys.fc-promotion-system.online.fc-promotion-system.prehost-mgcsym-cmms-measure-info\"}},{\"name\":\"acni-crevol-safebox-meta\",\"secret\":{\"defaultMode\":420,\"secretName\":\"sec-acni.v1.normandy.fc-promotion-system.online.d70362ae-4d04-48fa-bb2d-21f7de604571-mgcsym-safebox-meta\"}},{\"emptyDir\":{},\"name\":\"********************************\"}]}},\"updateStrategy\":{\"rollingUpdate\":{\"partition\":*********}}}}\n",
       "sigma.ali/disable-cascading-deletion": "true",
       "sigma.ali/upgrade-merge-annotations": "pod.beta1.sigma.ali/disable-lifecycle-hook-inject",
       "statefulset.beta1.sigma.ali/apprules-update-required": "false",
       "statefulset.beta1.sigma.ali/partition": "0%",
       "statefulset.beta1.sigma.ali/pod-upgrade-timeout": "1800",
       "statefulset.beta1.sigma.ali/pods-to-delete": "8e1de82f-2543-4f64-ac52-8f017e178381,fe6497fa-d057-4b5b-ae15-301f5410c89b,0ee8c957-0432-4540-aa97-476c0309b8ef",
       "statefulset.beta1.sigma.ali/publish-id": "*********",
       "statefulset.beta1.sigma.ali/updated-ready-replicas": "1",
       "statefulset.beta1.sigma.ali/upgrade-max-unavailable": "100%"
     },
     "creationTimestamp": "2021-12-06T09:49:21.000000Z",
     "finalizers": [
       "alibabacloud.com/proxy-to-cloneset"
     ],
     "generation": 3071,
     "labels": {
       "apps.kruise.io/rollout-batch-id": "0",
       "apps.kruise.io/rollout-id": "*********",
       "inplaceset.sigma.ali/proxy": "CloneSet",
       "sigma.ali/app-name": "fc-promotion-system",
       "sigma.ali/disable-default-pdb-strategy": "true",
       "sigma.ali/instance-group": "fc-promotion-system_prehost",
       "sigma.ali/site": "na620",
       "sigma.ali/subgroup": "default",
       "sigma.alibaba-inc.com/app-stage": "PRE_PUBLISH",
       "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
       "statefulset.sigma.ali/mode": "sigma"
     },
     "name": "fc-promotion-system-5f85ed1e-6aa9-46b5-9d--n3",
     "namespace": "fc-promotion-system",
     "resourceVersion": "46690776505",
     "uid": "bbc094e7-733f-4e56-8bb0-6732a19632e8"
   },
   "spec": {
     "podManagementPolicy": "Parallel",
     "replicas": 1,
     "revisionHistoryLimit": 10,
     "selector": {
       "matchLabels": {
         "sigma.ali/app-name": "fc-promotion-system",
         "sigma.ali/instance-group": "fc-promotion-system_prehost",
         "sigma.ali/site": "na620",
         "sigma.ali/subgroup": "default",
         "sigma.alibaba-inc.com/app-stage": "PRE_PUBLISH",
         "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
       }
     },
     "serviceName": "",
     "template": {
       "metadata": {
         "annotations": {
           "alibabacloud.com/inject-security-atp": "{\"initContainerName\":\"security-atp-init\",\"initContainerImage\":\"hub.docker.alibaba-inc.com/aone-base-global/sec_atp_1.3.9.5:20240308103025\",\"initContainerCommand\":[\"sh\", \"-c\", \"/home/<USER>/atp/install.sh\"],\"volumeName\":\"security-atp\",\"volumePath\":\"/opt/security-atp\",\"loaderFilePath\":\"/opt/security-atp/file/agent_so/libatp_preload.so\"}",
           "alibabacloud.com/ip-stack": "ipv4",
           "pod.beta1.alibabacloud.com/container-cpu-quota-unlimit": "true",
           "pod.beta1.alibabacloud.com/zoom-in-cpu-quota": "1.2",
           "pod.beta1.sigma.ali/alarming-off-upgrade": "true",
           "pod.beta1.sigma.ali/apprules": "{\"allocSpec\":\"{\\\"CpuSetMode\\\":\\\"share\\\"}\",\"constraints\":\"{}\",\"extConfig\":\"{}\",\"hostConfig\":\"{}\",\"monopolize\":\"{}\",\"prohibit\":\"{}\",\"resource_pool\":\"sigma_public\",\"spread\":\"{}\"}",
           "pod.beta1.sigma.ali/container-extra-config": "{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"600\", \"PreStopHookTimeoutSeconds\":\"600\"}}}",
           "pod.beta1.sigma.ali/hostname-template": "fc-promotion-system{{.IpAddress}}.pre.na620",
           "pod.beta1.sigma.ali/naming-register-state": "working_online",
           "sigma.ali/enable-apprules-injection": "true",
           "sigma.ali/use-unified-pv": "true"
         },
         "labels": {
           "normandy.alibabacloud.com/order-id": "58337684",
           "pod.beta1.sigma.ali/naming-register-state": "working_online",
           "sigma.ali/app-name": "fc-promotion-system",
           "sigma.ali/container-model": "dockervm",
           "sigma.ali/inject-staragent-sidecar": "true",
           "sigma.ali/instance-group": "fc-promotion-system_prehost",
           "sigma.ali/site": "na620",
           "sigma.ali/subgroup": "default",
           "sigma.ali/upstream-component": "normandy",
           "sigma.alibaba-inc.com/app-stage": "PRE_PUBLISH",
           "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center"
         }
       },
       "spec": {
         "affinity": {
           "nodeAffinity": {
             "requiredDuringSchedulingIgnoredDuringExecution": {
               "nodeSelectorTerms": [
                 {
                   "matchExpressions": [
                     {
                       "key": "sigma.ali/resource-pool",
                       "operator": "In",
                       "values": [
                         "sigma_public"
                       ]
                     },
                     {
                       "key": "sigma.ali/is-ecs",
                       "operator": "In",
                       "values": [
                         "true"
                       ]
                     }
                   ]
                 }
               ]
             }
           }
         },
         "automountServiceAccountToken": false,
         "containers": [
           {
             "env": [
               {
                 "name": "ali_aone_timestamp",
                 "value": "*************"
               },
               {
                 "name": "ali_start_app",
                 "value": "no"
               },
               {
                 "name": "ali_run_mode",
                 "value": "common_vm"
               },
               {
                 "name": "envSign",
                 "value": "staging"
               },
               {
                 "name": "trafficRouteLabel",
                 "value": "staging"
               },
               {
                 "name": "ALIYUN_LOGTAIL_CONFIG",
                 "value": "/etc/ilogtail/conf/cn-zhangjiakou-internet/ilogtail_config.json"
               },
               {
                 "name": "ALIYUN_LOGTAIL_USER_DEFINED_ID",
                 "value": "acni_v1_normandy_fc-promotion-system_online_d70362ae-4d04-48fa-bb2d-21f7de604571"
               },
               {
                 "name": "ALIYUN_LOGTAIL_USER_ID",
                 "value": "****************"
               },
               {
                 "name": "exec_scm_hook",
                 "value": "yes"
               },
               {
                 "name": "ali_app_name",
                 "value": "fc-promotion-system"
               },
               {
                 "name": "ali_env_sign",
                 "value": "staging"
               }
             ],
             "image": "hub.docker.alibaba-inc.com/aone/fc-promotion-system_prepub:20240415155017500847_prepub",
             "imagePullPolicy": "IfNotPresent",
             "name": "main",
             "resources": {
               "limits": {
                 "cpu": "1",
                 "ephemeral-storage": "***********",
                 "memory": "8589934592"
               },
               "requests": {
                 "cpu": "1",
                 "ephemeral-storage": "***********",
                 "memory": "8589934592"
               }
             },
             "terminationMessagePath": "/dev/termination-log",
             "terminationMessagePolicy": "File",
             "volumeMounts": [
               {
                 "mountPath": "/home/<USER>/liaoyuan-out",
                 "name": "26f907fb3bf70c5f850d72be0d60db4a"
               },
               {
                 "mountPath": "/home/<USER>/notify",
                 "name": "98e3f9a7853ee1e66fa98766fa1af604"
               },
               {
                 "mountPath": "/home/<USER>/snapshots",
                 "name": "d043ab5abf87f2109e0ed379d4fbb275"
               },
               {
                 "mountPath": "/home/<USER>/.rocketmq_offsets",
                 "name": "fdca7e870b8663f2088472f477f40170"
               },
               {
                 "mountPath": "/home/<USER>/configclient",
                 "name": "f1c341b95cb59b00f2a72dbfabb2c4cd"
               },
               {
                 "mountPath": "/home/<USER>/vipsrv-dns",
                 "name": "4594f9cc2b6414e0c27ea07d5b2646f7"
               },
               {
                 "mountPath": "/home/<USER>/amsdata",
                 "name": "b28dce6991f47354f1a8f2383fe95695"
               },
               {
                 "mountPath": "/home/<USER>/vipsrv-failover",
                 "name": "e8efefb1cdce1d55487529d6e6c8fed8"
               },
               {
                 "mountPath": "/home/<USER>/amsdata_all",
                 "name": "5d141dda47411ed9f2cacc42433e1e16"
               },
               {
                 "mountPath": "/home/<USER>/catserver",
                 "name": "6426549ef68729f5b5679988daab4051"
               },
               {
                 "mountPath": "/home/<USER>/cai/logs",
                 "name": "85cfbbfa3aefacdb2f49532dbb652151"
               },
               {
                 "mountPath": "/home/<USER>/vipsrv-cache",
                 "name": "d995b338ee85c4415fa3306a90587664"
               },
               {
                 "mountPath": "/home/<USER>/diamond",
                 "name": "324a215d6bec6bffc99895f0d8fe5580"
               },
               {
                 "mountPath": "/home/<USER>/fc-promotion-system/logs",
                 "name": "5102f36195e1f1332e8885065f210331"
               },
               {
                 "mountPath": "/home/<USER>/csp",
                 "name": "dad113c337563f905a93c0793b880d9d"
               },
               {
                 "mountPath": "/home/<USER>/logs",
                 "name": "44cde429260b721f6a8c26b99ab1601b"
               },
               {
                 "mountPath": "/etc/cmms",
                 "name": "acni-crevol-cmms-measure-info",
                 "readOnly": true
               },
               {
                 "mountPath": "/etc/acni/safebox-meta/target",
                 "name": "acni-crevol-safebox-meta",
                 "readOnly": true
               }
             ]
           }
         ],
         "dnsPolicy": "Default",
         "enableServiceLinks": false,
         "priorityClassName": "unified-prod",
         "restartPolicy": "Always",
         "schedulerName": "default-scheduler",
         "securityContext": {},
         "terminationGracePeriodSeconds": 1,
         "volumes": [
           {
             "emptyDir": {},
             "name": "4594f9cc2b6414e0c27ea07d5b2646f7"
           },
           {
             "emptyDir": {},
             "name": "fdca7e870b8663f2088472f477f40170"
           },
           {
             "emptyDir": {},
             "name": "b28dce6991f47354f1a8f2383fe95695"
           },
           {
             "emptyDir": {},
             "name": "5d141dda47411ed9f2cacc42433e1e16"
           },
           {
             "emptyDir": {},
             "name": "85cfbbfa3aefacdb2f49532dbb652151"
           },
           {
             "emptyDir": {},
             "name": "6426549ef68729f5b5679988daab4051"
           },
           {
             "emptyDir": {},
             "name": "f1c341b95cb59b00f2a72dbfabb2c4cd"
           },
           {
             "emptyDir": {},
             "name": "dad113c337563f905a93c0793b880d9d"
           },
           {
             "emptyDir": {},
             "name": "324a215d6bec6bffc99895f0d8fe5580"
           },
           {
             "emptyDir": {},
             "name": "5102f36195e1f1332e8885065f210331"
           },
           {
             "emptyDir": {},
             "name": "26f907fb3bf70c5f850d72be0d60db4a"
           },
           {
             "emptyDir": {},
             "name": "44cde429260b721f6a8c26b99ab1601b"
           },
           {
             "emptyDir": {},
             "name": "98e3f9a7853ee1e66fa98766fa1af604"
           },
           {
             "emptyDir": {},
             "name": "d043ab5abf87f2109e0ed379d4fbb275"
           },
           {
             "emptyDir": {},
             "name": "d995b338ee85c4415fa3306a90587664"
           },
           {
             "emptyDir": {},
             "name": "e8efefb1cdce1d55487529d6e6c8fed8"
           },
           {
             "name": "acni-crevol-cmms-measure-info",
             "secret": {
               "defaultMode": 420,
               "secretName": "sec-acni.v2.app-group-sys.fc-promotion-system.online.fc-promotion-system.prehost-mgcsym-cmms-measure-info"
             }
           },
           {
             "name": "acni-crevol-safebox-meta",
             "secret": {
               "defaultMode": 420,
               "secretName": "sec-acni.v1.normandy.fc-promotion-system.online.d70362ae-4d04-48fa-bb2d-21f7de604571-mgcsym-safebox-meta"
             }
           },
           {
             "emptyDir": {},
             "name": "********************************"
           }
         ]
       }
     },
     "updateStrategy": {
       "rollingUpdate": {
         "partition": *********
       },
       "type": "RollingUpdate"
     }
   },
   "status": {
     "collisionCount": 0,
     "currentReplicas": 1,
     "currentRevision": "fc-promotion-system-5f85ed1e-6aa9-46b5-9d--n3-74b6dfdf7b",
     "observedGeneration": 3071,
     "readyReplicas": 1,
     "replicas": 1,
     "updateRevision": "fc-promotion-system-5f85ed1e-6aa9-46b5-9d--n3-74b6dfdf7b",
     "updatedReplicas": 1
   }
 }
            """.trimIndent(),
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.DEPLOY,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.YAML
        )

        val mount = (((((YamlUtils.load(finalPost).get("spec") as Map<String, Any>).get("template") as Map<String, Any>).get("spec") as Map<String, Any>).get("containers") as List<Map<String, Any>>).get(0).get("volumeMounts") as List<Map<String, Any>>).find{
            mount ->
            mount.get("name")!!.equals("********************************")
        }

        Assert.assertNull(mount)

    }

    @Test
    fun testThreeWayMerge_Init_ScalePatch() {
        val newOriginalResourceObjecSpec = YamlUtils.dump(
            YamlUtils.load(originalResourceObjecSpec).apply {
                ResourceObjectService.getAnnotationsFromWorkloadSpec(this)?.keys?.removeAll {
                    ResourceObjectService.NEW_LAST_APPLIED_CONF_ANNOTATIONS.contains(it)

                }
            }
        )

        val currentResourceObjectConfig = """
        {
          "spec": {
            "template": {
              "spec": {
                "containers": [
                  {
                    "name": "main",
                    "env": [
                      {
                        "name": "envSign",
                        "value": "prod"
                      }
                    ]
                  }
                ]
              }
            }
          }
        }
""".trimIndent()

        val resourceObjectService = spyk<ResourceObjectService>() {
        }

        val finalPost = resourceObjectService.threeWayMerge(
            currentResourceObjectConfig,
            newOriginalResourceObjecSpec,
            ResourceObjectProtocolEnum.StatefulSet,
            ResourceObjectSceneEnum.SCALE_OUT,
            ResourceObjectBuildTypeEnum.PATCH,
            ResourceObjectFormatEnum.JSON
        )

        val diff = Json.createDiff(
            Json.createReader(StringReader(originalResourceObjecSpec)).readValue() as JsonObject,
            Json.createReader(StringReader(finalPost)).readValue() as JsonObject
        );

        Assert.assertEquals(
            """

[
    {
        "op": "replace",
        "path": "/metadata/annotations/kubectl.kubernetes.io/last-applied-configuration-scale",
        "value": "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"main\",\"env\":[{\"name\":\"envSign\",\"value\":\"prod\"}]}]}}}}"
    },
    {
        "op": "replace",
        "path": "/spec/template/spec/containers/0/env/3/value",
        "value": "prod"
    }
]""".trimIndent(),
            JsonUtils.format(diff.toJsonArray())
        )
    }

    @Test
    fun testThreeWayMerge_remove_whenExclude() {
        val spec = ResourceObjectService.filterLastAppliedConf(
            originalResourceObjecSpec,
            ResourceObjectFormatEnum.YAML,
            ResourceObjectProtocolEnum.StatefulSet
        )
        val remove = ResourceObjectService.getAnnotationsFromWorkloadSpec(YamlUtils.load(spec))?.keys?.removeAll {
            ResourceObjectService.NEW_LAST_APPLIED_CONF_ANNOTATIONS.contains(it)

        }
        Assert.assertFalse(remove!!)

    }

    @Test
    fun testPreloadResourceSpecAnnotationsAndSetAnnotation() {
        val map1 = mapOf(
            "dog" to "cat"
        )
        val map2 = ResourceObjectService.preLoadResourceSpecAnnotations(map1)
        Assert.assertEquals("""dog: cat
metadata:
  annotations: {}

""".trimIndent(),YamlUtils.dump(map2))
        ResourceObjectService.getAnnotationsFromWorkloadSpec(map2)?.put("k1", "v1")
        Assert.assertEquals("""dog: cat
metadata:
  annotations:
    k1: v1

""".trimIndent(),YamlUtils.dump(map2))
    }

    @Test(expected = BizException::class)
    fun `testBatchQueryResourceGroupBaselineSpec while app group size is more than max`() {
        val resourceObjectService = spyk<ResourceObjectService>() {
        }
        val resourceGroupList = (1..251).map {
            "rg-$it"
        }
        val result = resourceObjectService.batchQueryResourceGroupBaselineSpec("appName", resourceGroupList)
        Assert.assertEquals(250, result.size)
    }

    @Test
    fun testGetDeployVersionFromOriginalResourceObject() {
        val originalResourceObject = """
kind: StatefulSet
apiVersion: apps/v1
spec:
  template:
    metadata:
      labels:
        tags.sunfire.com/app-deploy-version: V178132626""".trimIndent()
        Assert.assertEquals(
            "V178132626",
            ResourceObjectService.getDeployVersionFromOriginalResourceObject(YamlUtils.load(originalResourceObject))
        )
    }

    @Test
    fun testDeployAssembledVersionRequest() {
        val requestJsonStr = """
            {
                "appName": "aone-qa-test-new02",
                "envStackId": "bd6c9a24-3566-41c4-be9b-3d3c08669a37",
                "versionProtocol": "ServerlessApp",
                "workload": null,
                "extendedParams": {
                    "envSign": "staging",
                    "mixFlowInstId": "183684830",
                    "envVars": [
                        {
                            "key": "AONE_ENV_SIGN_TEST",
                            "value": "staging_test"
                        }
                    ],
                    "systemProperties": [
                        {
                            "key": "AONE_ENV_SIGN",
                            "value": "staging"
                        }
                    ]
                },
                "userTraits": [

                ]
            }
        """.trimIndent()
        val requestObj = JsonUtils.readValue(requestJsonStr, DeployAssembledVersionRequest::class.java)
        assertEquals(4, requestObj.extendedParams!!.size)
        assertEquals("AONE_ENV_SIGN_TEST", (requestObj.extendedParams!!["envVars"]!! as List<Map<String, String>>)[0]["key"])
        assertEquals("staging_test", (requestObj.extendedParams!!["envVars"]!! as List<Map<String, String>>)[0]["value"])

        assertEquals("AONE_ENV_SIGN", (requestObj.extendedParams!!["systemProperties"]!! as List<Map<String, String>>)[0]["key"])
        assertEquals("staging", (requestObj.extendedParams!!["systemProperties"]!! as List<Map<String, String>>)[0]["value"])
    }

    @Test
    fun `testGetResourceObjectStr postModifyBaseSpec always`() {
        // Given
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            every { preCheck(any(), any()) } just Runs
            every { postModifyBaseSpec(any(),any())} returns YamlUtils.load("""
apiVersion: postModified
            """.trimIndent())
        }
        val resourceObjectService = spyk<ResourceObjectService>() {
            every { scheduleStandardService.checkMultiProtocol(any(), any(), any()) } just Runs
            every { workloadSpecFactory.getWorkloadSpecFacade(any()) } returns statefulSetSpecService
            every { resourceObjectFeatureService.listResourceObjectFeatureImportSpec(any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { appCenterApi.getAppInfoByName(any()) } returns AppInfo(
                id = 1L,
                buId = 4L,
                name = "normandy-test-app4",
                productId = 5L,
                productFullLineIdPath = "3#4_5_6",
                status = AppStatusEnum.valueOf("ONLINE"),
                level = AppLevelEnum.GRADE4
            )
            every { threeWayMergeProperties.whetherInThreeWayMerge(any(), any(), any(), any()) } returns false
            every { commonProperties.firstOrNull(CommonProperties.SCALE_OUT_POST_CHECK) } returns "false"

        }

        val request = ScaleOutAssembledResourceObjectRequest(
            workloadMetadataConstraint = WorkloadMetadataConstraint(
                appName = "gt-training",
                resourceGroup = "gt-training_group2",
                site = "na61",
                unit = "CENTER_UNIT.center",
                stage = "PRE_PUBLISH",
                clusterId = "c85e3f175d19a41d09824b338ca6a8a53"
            ),
            resourceObjectProtocol = "StatefulSet",
            envStackId = "test-env",
            resourceObjectFormatEnum = ResourceObjectFormatEnum.YAML,
            currentResourceObject = """
apiVersion: apps/v1
            """.trimIndent(),
            inputParams = emptyMap()
        )

        // When
        val result = resourceObjectService.getAssembledResourceObjectToScaleOut(request)

        // Then
        assertEquals("""apiVersion: postModified

""".trimIndent(), result.resourceObject)

    }
}