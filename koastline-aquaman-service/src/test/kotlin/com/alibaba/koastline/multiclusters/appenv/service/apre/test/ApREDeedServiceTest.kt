package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.model.Constants
import com.alibaba.koastline.multiclusters.apre.attorney.ApREDeedService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedNotFoundException
import com.alibaba.koastline.multiclusters.common.utils.Base64Utils
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeed
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import java.security.MessageDigest
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ApREDeedServiceTest {
    var messageDigest: MessageDigest = MessageDigest.getInstance("MD5")

    @Test(expected = ApREDeedNotFoundException::class)
    fun testFindApREDeedByKey_with_deed_not_found() {
        val key = "vwMImMwRy0xFsdo+JbdJKg=="
        val apREDeedService = spyk(ApREDeedService()) {
            every {
                apREDeedRepo.findByKey(key)
            } returns null
        }
        apREDeedService.findApREDeedByKey(key)
    }

    @Test
    fun testCreateApREDeedWhileNotExist_with_key_exist() {
        val deedDO = getSimpleInitialDeedDO(null)
        val apREDeedService = spyk(ApREDeedService()) {
            every {
                apREDeedRepo.findByKey(any())
            } returns getSimpleDeed()
        }
        val result = apREDeedService.createApREDeedWhileNotExist(deedDO)
        assertEquals(
            IdentityInfo(
            "production",
            "1#2_3",
            "app_name",
            "env_id",
            "env_stack_id",
            "node_group"
        ), result.identityInfo)
        assertEquals(1, result.declarations!!.size)
        assertEquals(
            Declaration(
                null,
            "cn-zhangjiakou",
            "na610",
            "PUBLISH",
            "CENTER_UNIT.center",
            null
        ), result.declarations!![0].copy(id = null))
    }

    @Test
    fun testCreateApREDeedWhileNotExist_with_not_key_and_has_declarations() {
        val deedDO = getSimpleInitialDeedDO(null)
        val apREDeedService = spyk(ApREDeedService()) {
            every {
                apREDeedRepo.findByKey(any())
            } returns null
            every {
                apREDeedRepo.insert(any())
            } returns 1
        }
        val result = apREDeedService.createApREDeedWhileNotExist(deedDO)
        assertTrue(result.key!!.isNotBlank())
        assertEquals(1, result.declarations!!.size)
        assertTrue(result.declarations!![0].id!!.isNotBlank())
    }

    @Test
    fun testCreateApREDeedWhileNotExist_with_not_key_and_do_not_has_declarations() {
        val deedDO = getSimpleInitialDeedDO(null)
        deedDO.declarations = null
        val apREDeedService = spyk(ApREDeedService()) {
            every {
                apREDeedRepo.findByKey(any())
            } returns null
            every {
                apREDeedRepo.insert(any())
            } returns 1
        }
        val result = apREDeedService.createApREDeedWhileNotExist(deedDO)
        assertTrue(result.key!!.isNotBlank())
        assertEquals(1, result.declarations!!.size)
        assertTrue(result.declarations!![0].id!!.isNotBlank())
    }

    private fun getSimpleDeed(): ApREDeed {
        val content = Base64Utils.encode(getSimpleInitialDeedDO(null))
        val key = Base64Utils.encode(messageDigest.digest(content.toByteArray(Charsets.UTF_8)))
        val now = Date(Instant.now().toEpochMilli())
        val deedDO = getSimpleInitialDeedDO(key)
        return ApREDeed(
            1,
            key,
            deedDO.identityInfo!!.envLevel,
            deedDO.identityInfo!!.appName!!,
            deedDO.identityInfo!!.envId,
            deedDO.identityInfo!!.envStackId,
            deedDO.identityInfo!!.nodeGroup,
            content,
            now,
            now,
            Constants.IS_NOT_DELETED
        )
    }
    private fun getSimpleInitialDeedDO(key: String?): ApREDeedDO {
        return ApREDeedDO(
            key,
            IdentityInfo(
                "production",
                "1#2_3",
                "app_name",
                "env_id",
                "env_stack_id",
                "node_group"
            ),
            mutableListOf<Declaration>(
                Declaration(
                    "id",
                    "cn-zhangjiakou",
                    "na610",
                    "PUBLISH",
                    "CENTER_UNIT.center",
                    null
                )
            )
        )
    }
}