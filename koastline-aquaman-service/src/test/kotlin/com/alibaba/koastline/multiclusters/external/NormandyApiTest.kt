package com.alibaba.koastline.multiclusters.external

import com.alibaba.koastline.multiclusters.common.exceptions.NormandyException
import com.alibaba.koastline.multiclusters.common.utils.HttpClientUtils
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class NormandyApiTest {
    @Test
    fun queryDataSetAndSecretCrList_empty() {
        val api = spyk(NormandyApi())
            .also {
                it.host = "mock"
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {"success":true,"code":200,"message":"OK","data":{"crResourceObjectList":[],"formatEnum":"YAML"},"errorDetail":null,"traceId":"******************************"}
        """.trimIndent()

        assertTrue(
            api.queryDataSetAndSecretCrList(
                "appName",
                "stackId",
                "stackPkId",
                "clusterId",
                "YAML"
            ).crResourceObjectList!!.isEmpty()
        )
        unmockkAll()
    }

    @Test
    fun queryDataSetAndSecretCrList_NotEmpty() {
        val api = spyk(NormandyApi())
            .also {
                it.host = "mock"
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
              "success": true,
              "code": 200,
              "message": "OK",
              "data": {
                "crResourceObjectList": [
                  {
                    "apiVersion": "v1",
                    "kind": "Secret",
                    "resourceObject": "apiVersion: v1\ndata:\n  AccessKeyId: TFRBSTV0U0FYQTVWR0dIZnI2bXVHUzdZ\n  AccessKeySecret: ****************************************\n  SecurityToken: ''\n  LastUpdate: MjAyNC0xMi0xOFQwNjozOTo0My4wMDArMDg6MDA=\n  Expiration: MjAyNC0xMi0xOFQxNjozOTo0My4wMDArMDg6MDA=\nkind: Secret\nmetadata:\n  name: normandy-resource--c425a147bee28c46a068bcf145efa63d\n  namespace: canfeng-test-1091\ntype: Opaque\n"
                  },
                  {
                    "apiVersion": "data.kubedl.io/v1alpha1",
                    "kind": "DataSet",
                    "resourceObject": "apiVersion: data.kubedl.io/v1alpha1\nkind: DataSet\nmetadata:\n  annotations: {}\n  labels:\n    alibabacloud.com/quota-name: kubedl\n    serverless.io/user-id: '264071'\n    alibabacloud.com/resource-account-name: kubedl\n  name: ds-2b129c387b5454d98e2d446a529980f8\n  namespace: canfeng-test-1091\nspec:\n  enableCache: true\n  enablePreload: true\n  dataSources:\n  - mountPath: /home/<USER>/app_datas/models\n    name: ''\n    readOnly: false\n    oss:\n      endpoint: cn-zhangjiakou.oss.aliyuncs.com\n      mountPoint: oss://aone-algorithm-model/test/models/LLM-Research/Meta-Llama-3___1-8B/\n      options:\n      - name: accessKeyId\n        valueFrom:\n          secretKeyRef:\n            key: accessKeyId\n            name: normandy-resource--c425a147bee28c46a068bcf145efa63d\n      - name: accessKeySecret\n        valueFrom:\n          secretKeyRef:\n            key: accessKeySecret\n            name: normandy-resource--c425a147bee28c46a068bcf145efa63d\n  cacheEngine:\n    fluid:\n      jindoRuntime: {}\n"
                  }
                ],
                "formatEnum": "YAML"
              },
              "errorDetail": null,
              "traceId": "******************************"
            }
        """.trimIndent()

        assertTrue(
            api.queryDataSetAndSecretCrList(
                "appName",
                "stackId",
                "stackPkId",
                "clusterId",
                "YAML"
            ).crResourceObjectList!!.size == 2
        )
        unmockkAll()
    }

    @org.junit.Test(expected = NormandyException::class)
    fun queryDataSetAndSecretCrList_Fail() {
        val api = spyk(NormandyApi())
            .also {
                it.host = "mock"
            }
        mockkObject(HttpClientUtils)
        every { HttpClientUtils.httpPost(any(), any<String>(), any()) } returns """
            {
              "success": false,
              "code": 500,
              "message": "getAoneOssAccessTokenFailed: Internal Server Error",
              "data": null
            }
        """.trimIndent()

        api.queryDataSetAndSecretCrList(
            "appName",
            "stackId",
            "stackPkId",
            "clusterId",
            "YAML"
        ).crResourceObjectList!!.size == 2
        unmockkAll()
    }

}