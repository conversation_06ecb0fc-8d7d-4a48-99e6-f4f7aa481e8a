package com.alibaba.koastline.multiclusters.resourceobj.base.facade

import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectPostCheckException
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectService
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.LAST_POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.POD_TEMPLATE_HASH
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService.Companion.ROLLBACKINDEPLOY_ALARM
import com.alibaba.koastline.multiclusters.resourceobj.base.RollingSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.StatefulSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class AbstractWorkloadSpecServiceTest {

  @Test
  fun test_postApply_hash_change_or_not() {
    val statefulSetSpecService = spyk(StatefulSetSpecService()) {
      baseSpecService = BaseSpecService()
    }
    val after = statefulSetSpecService.postApply(
      """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
    sigma.ali/app-name: tbmc-algo-decision
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: >-
          {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm
          volumeMounts: null
        - name: data-container
          env:
            - name: IS_SIDECAR
              value: 'true'
            - name: SIGMA_IGNORE_READY
              value: 'true'
            - name: SIGMA_IGNORE_RESOURCE
              value: 'true'
          command:
            - tail
            - '-f'
            - /dev/null
          dockerfile: APP-META/docker-config/Dockerfile_data_container
          volumeMounts: null
      volumes: null""".trimIndent(), ResourceObjectFormatEnum.YAML
    )
    val hash = ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after))?.get(POD_TEMPLATE_HASH) as String
      assertEquals("77631bca7ace42c34c902cc8202c5fba", hash)
    val after2 = statefulSetSpecService.postApply(
      """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/app-name: tbmc-algo-decision
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: >-
          {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm
          volumeMounts: null
        - name: data-container
          env:
            - name: IS_SIDECAR
              value: 'true'
            - name: SIGMA_LOG_SUFFIX
              value: 'test'
            - name: SIGMA_IGNORE_READY
              value: 'true'
            - name: SIGMA_IGNORE_RESOURCE
              value: 'true'
          command:
            - tail
            - '-f'
            - /dev/null
          dockerfile: APP-META/docker-config/Dockerfile_data_container
          volumeMounts: null
      volumes: null""".trimIndent(), ResourceObjectFormatEnum.YAML
    )
    val hash2 =
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after2))?.get(POD_TEMPLATE_HASH) as String
      // only SIGMA_LOG_SUFFIX & metadata change
      assertEquals(hash, hash2)

    val after3 = statefulSetSpecService.postApply(
      """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/app-name: tbmc-algo-decision
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: >-
          {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 63
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm
          volumeMounts: null
        - name: data-container
          env:
            - name: IS_SIDECAR
              value: 'true'
            - name: SIGMA_LOG_SUFFIX
              value: 'test'
            - name: SIGMA_IGNORE_READY
              value: 'true'
            - name: SIGMA_IGNORE_RESOURCE
              value: 'true'
          command:
            - tail
            - '-f'
            - /dev/null
          dockerfile: APP-META/docker-config/Dockerfile_data_container
          volumeMounts: null
      volumes: null""".trimIndent(), ResourceObjectFormatEnum.YAML
    )
    val hash3 =
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after3))?.get(POD_TEMPLATE_HASH) as String
      // only terminationGracePeriodSeconds changes
      assertEquals(hash, hash3)

    val after4 = statefulSetSpecService.postApply(
      """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/app-name: tbmc-algo-decision
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: >-
          {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 63
      containers:
        - name: main
          env:
            - name: ali_run_mode
              value: common_vm
          volumeMounts: null
        - name: data-container
          env:
            - name: IS_SIDECAR
              value: 'true'
            - name: SIGMA_LOG_SUFFIX
              value: 'test'
            - name: SIGMA_IGNORE_READY
              value: 'true'
            - name: SIGMA_IGNORE_RESOURCE
              value: 'true'
          command:
            - tail
            - '-f'
            - /dev/null
          dockerfile: APP-META/docker-config/Dockerfile_data_container
          volumeMounts: null
      volumes: null""".trimIndent(), ResourceObjectFormatEnum.YAML
    )
    val hash4 =
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after4))?.get(POD_TEMPLATE_HASH) as String
      // ali_start_app is removed
      assertNotEquals(hash, hash4)

  }

  @Test
  fun test_postApply_alarm_or_not() {
    val rollingSetSpecService = spyk(RollingSetSpecService()) {
      baseSpecService = BaseSpecService()
    }
    val after = rollingSetSpecService.postApply(
      """
apiVersion: carbon.taobao.com/v1
kind: RollingSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: "true"
  labels:
    app.c2.io/platform: serverless
    app.hippo.io/pod-version: v3.0
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: "true"
        serverless.io/skyline-tags: serverless.pod
      labels:
        sigma.ali/inject-staragent-sidecar: "true"
        app.hippo.io/maxInstancePerNode: "5"
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 1
      containers:
      - env:
        - name: ali_start_app
          value: "no"
        - name: ali_run_mode
          value: "common_vm"
        name: main
        args:
        - -c
        - /home/<USER>/start_supervisor.sh
        command:
        - /bin/sh""".trimIndent(), ResourceObjectFormatEnum.YAML
    )
    val hash = ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after))?.get(POD_TEMPLATE_HASH) as String
      assertEquals("4e68e0b631375d5a03f376bb8590a6b6", hash)
    val after2 = rollingSetSpecService.postApply(
      after, ResourceObjectFormatEnum.YAML
    )
    val hash2 =
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after2))?.get(LAST_POD_TEMPLATE_HASH) as String
      assertEquals(hash, hash2)
    val hash22 =
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after2))?.get(POD_TEMPLATE_HASH) as String
      assertEquals(hash, hash22)
    assertEquals(
      null,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after2))?.get(ROLLBACKINDEPLOY_ALARM)
    )

    val after3 = rollingSetSpecService.postApply(
      after2, ResourceObjectFormatEnum.YAML
    )

    assertEquals(
        hash,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after3))?.get(POD_TEMPLATE_HASH) as String
    )
    assertEquals(
        hash,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after3))?.get(LAST_POD_TEMPLATE_HASH) as String
    )
    assertEquals(
      null,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after2))?.get(ROLLBACKINDEPLOY_ALARM)
    )

    val modified = YamlUtils.load(after3)
    ResourceObjectService.getPodSpecFromWorkloadSpec(modified)?.put("containerModel", "VM")
    ResourceObjectService.getPodSpecFromWorkloadSpec(modified)?.remove("dnsPolicy")
    val after4 = rollingSetSpecService.postApply(
      YamlUtils.dump(modified), ResourceObjectFormatEnum.YAML
    )

    assertEquals(
        hash,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after4))?.get(POD_TEMPLATE_HASH) as String
    )
    assertEquals(
        hash,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after4))?.get(LAST_POD_TEMPLATE_HASH) as String
    )
    assertEquals(
        null,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after4))?.get(ROLLBACKINDEPLOY_ALARM)
    )

    val after5 = rollingSetSpecService.postApply(
      after4, ResourceObjectFormatEnum.YAML
    )
    assertEquals(
      null,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after5))?.get(ROLLBACKINDEPLOY_ALARM)
    )

    val after6 = rollingSetSpecService.postApply(
      after5, ResourceObjectFormatEnum.YAML
    )
    assertEquals(
      null,
      ResourceObjectService.getLabelsFromWorkloadSpec(YamlUtils.load(after6))?.get(ROLLBACKINDEPLOY_ALARM)
    )


  }

    @Test
    fun test_postCheckForScaleOut_success() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        statefulSetSpecService.postCheckForScaleOut(
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'false'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            ResourceObjectFormatEnum.YAML
        )
    }

    @Test
    fun test_postCheckForScaleOut_success_affinity_difference() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        statefulSetSpecService.postCheckForScaleOut(
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution: {nodeSelectorTerms: [{matchExpressions: [{key: sigma.ali/machine-model, operator: In, values: [ecs.ebmc6-inc.26xlarge, ecs.ebmg6-inc.26xlarge, ecs.ebmg6e-inc.26xlarge]}]}]}
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'false'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution: {nodeSelectorTerms: [{matchExpressions: [{key: sigma.ali/resource-pool, operator: In, values: [sigma_public]}]}]}
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            ResourceObjectFormatEnum.YAML
        )
    }

    @Test(expected = ResourceObjectPostCheckException::class)
    fun test_postCheckForScaleOut_fail() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        statefulSetSpecService.postCheckForScaleOut(
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'false'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'yes'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            ResourceObjectFormatEnum.YAML
        )
    }

    @Test
    fun test_postCheckForScaleOut_skip() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        statefulSetSpecService.postCheckForScaleOut(
            null,
            """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
spec:
  template:
    metadata:
      annotations:
        sigma.ali/use-unified-pv: 'false'
        pod.beta1.sigma.ali/naming-register-state: working_online
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: tbmc-algo-decision
    spec:
      dnsPolicy: Default
      terminationGracePeriodSeconds: 60
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'yes'
            - name: ali_run_mode
              value: common_vm""".trimIndent(),
            ResourceObjectFormatEnum.YAML
        )
    }


}