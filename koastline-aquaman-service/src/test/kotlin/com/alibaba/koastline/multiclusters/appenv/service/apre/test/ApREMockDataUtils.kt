package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREFeatureSpecDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ResourcePoolDataDO
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum.APRE
import com.alibaba.koastline.multiclusters.common.utils.KeyGenerator
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import testutils.BaseTest
import java.time.Instant
import java.util.*

object ApREMockDataUtils : BaseTest() {

    /**
     * 获取一个随机ApREDO
     *
     * @param apRELabelTargetTypeEnum
     * @return
     */
    fun getApRELabelDO(apRELabelTargetTypeEnum: ApRELabelTargetTypeEnum): ApRELabelDO {
        return manufacturePojo(ApRELabelDO::class.java).copy(
            targetType = apRELabelTargetTypeEnum,
            apREFeatureSpecs = null,
            isDeleted = "N"
        )
    }

    /**
     * 获取一个构造好复杂属性的ApREDTO
     *
     * @return
     */
    fun getComplexApREDTO(): ApREDO {
        return ApREDO(
            2911,
            null,
            null,
            "kostaline",
            "GfOvUwFDaydexVqs",
            "cn-zhangjiakou",
            "na610",
            "PUBLISH",
            "CENTER_UNIT.center",
            "ONLINE",
            null,
            Date(Instant.now().toEpochMilli()),
            Date(Instant.now().toEpochMilli()),
            "N",
            listOf(
                ApRELabelDO(
                    null,
                    null,
                    ApRELabelExt.APRE_LABEL_FEATURE_NAME,
                    "serverless/tpp",
                    null,
                    null,
                    null,
                    null,
                    listOf(
                        ApREFeatureSpecDO(
                            null,
                            null,
                            "serverless_runtime_1.0",
                            null,
                            null,
                            "publish",
                            "online",
                            "appstack_runtime",
                            "1111",
                            "appstack_version",
                            "1111",
                            "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            null,
                            null,
                            null,
                            null
                        ),
                        ApREFeatureSpecDO(
                            null,
                            null,
                            "serverless_runtime_1.0",
                            null,
                            null,
                            "publish",
                            "online",
                            "appstack_runtime",
                            "2222",
                            "appstack_version",
                            "2222",
                            "{\"roleName\":\"tao-guide_PRE_PUBLISH\",\"foundationName\":\"dosa_foundation\",\"usageTag\":\"PRE_PUBLISH\",\"unit\":\"CENTER_UNIT.center\",\"region\":\"张北\",\"idc\":\"na610\",\"jdk\":\"1.8\",\"image\":\"xxx\",\"cpu\":\"800\",\"memory\":\"16g\"}",
                            null,
                            null,
                            null,
                            null
                        )
                    ),
                    APRE,
                    SERVERLESS
                )
            ),
            emptyList()
        )
    }

    /**
     * 获取指定属性下AppRuntimeEnvironmentData
     *
     * @return
     */
    fun getAppRuntimeEnvironmentData(
        stage: String = "PUBLISH",
        unit: String = "CENTER_UNIT.center",
        site: String = "na610",
        region: String = "cn-zhangjiakou",
        runtimeEnvKey: String = KeyGenerator.generateAlphanumericKey(ApREService.ENVIRONMENT_KEY_LENGTH),
    ): AppRuntimeEnvironmentData {
        return AppRuntimeEnvironmentData(
            id = getLong(),
            runtimeEnvKey = runtimeEnvKey,
            creator = "kostaline",
            managedClusterKey = getString(),
            name = getString(),
            region = region,
            az = site,
            stage = stage,
            unit = unit,
            status = "ONLINE",
            metaData = null,
            gmtCreate = Date(Instant.now().toEpochMilli()),
            gmtModified = Date(Instant.now().toEpochMilli()),
            isDeleted = "N",
        )
    }

    /**
     * 获取随机的ResourcePool资源
     *
     * @param manageClusterKey
     * @param clusterId
     * @return
     */
    fun getRandomResourcePool(
        manageClusterKey: String = getString(), clusterId: String = getString()
    ): ResourcePoolDataDO {
        return manufacturePojo(ResourcePoolDataDO::class.java).copy(
            managedClusterKey = manageClusterKey,
            clusterId = clusterId,
            isDeleted = "N"
        )
    }
}