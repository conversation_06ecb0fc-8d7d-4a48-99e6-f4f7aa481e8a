package com.alibaba.koastline.multiclusters.resourceobj

import org.junit.Test
import org.junit.jupiter.api.Assertions.*

class TempServiceTest {
  @Test
  fun `test regular expression replace`() {
    val tempService = TempService()
    val result = tempService.regExReplace("""
      APPSTACK_ENV_LEVEL\s*(=|!)=\s*"(\w+(?!-ncloud))"
    """.trimIndent(), """
    if APPSTACK_ENV_LEVEL=="staging" || APPSTACK_ENV_LEVEL=="staging1" || APPSTACK_ENV_LEVEL=="testing-ncloud" {
        mainContainer: container.#Main & {
		    env: pre_env //环境变量
		}
    if APPSTACK_ENV_ST == "staging" {
        replica: 1
    }
    if APPSTACK_ENV_LEVEL != "production" {
        mainContainer: container.#Main & {
            env: common_env
        }
        replica: 2
    }
    """.trimIndent(), """
      APPSTACK_ENV_LEVEL ${'$'}1= "${'$'}2-ncloud"
    """.trimIndent())
    assertEquals("""
        if APPSTACK_ENV_LEVEL == "staging-ncloud" || APPSTACK_ENV_LEVEL == "staging1-ncloud" || APPSTACK_ENV_LEVEL=="testing-ncloud" {
            mainContainer: container.#Main & {
          env: pre_env //环境变量
      }
        if APPSTACK_ENV_ST == "staging" {
            replica: 1
        }
        if APPSTACK_ENV_LEVEL != "production-ncloud" {
            mainContainer: container.#Main & {
                env: common_env
            }
            replica: 2
        }
    """.trimIndent(), result)
  }

}