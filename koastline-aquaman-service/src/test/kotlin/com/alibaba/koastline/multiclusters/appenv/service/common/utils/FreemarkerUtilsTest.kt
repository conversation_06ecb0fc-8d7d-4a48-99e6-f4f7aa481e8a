package com.alibaba.koastline.multiclusters.appenv.service.common.utils

import com.alibaba.koastline.multiclusters.common.utils.FreemarkerUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import org.junit.Test
import kotlin.test.assertEquals

class FreemarkerUtilsTest {

    @Test
    fun testParseTemplate_with_full_params() {
        val tpl = """
<#if user.version?has_content>
metadata:
  annotations:
    sigma.ali/upgrade-merge-annotations: 'pod.sigma.ali/container-start-policy'
spec:
  template:
    metadata:
      annotations:
        pod.sigma.ali/container-start-policy: parallel
    spec:
      containers:
        - name: daprd
          env: 
            - name: K8S_CONTAINER_NAME
              value: daprd
            - name: IS_SIDECAR
              value: "true"
            - name: SIGMA_IGNORE_RESOURCE
              value: "true"
            - name: ALIMETRICS_ENABLE
              value: "true"
            - name: ALIMETRICS_SERVER_PORT
              value: "8007"
            - name: ALI_METRICS_URL_PORT
              value: "8007"
            - name: PROMETHEUS_METRICS_TENANT
              value: sunfire_metric
            - name: SCRAPE_PROMETHEUS_METRICS
              value: "yes"
            - name: PROMETHEUS_METRICS_URL_PORT_PATH
              value: "9090/metrics"
            - name: ali_aone_timestamp
              value: ${'$'}{.now?long?c} 
          image: hub.docker.alibaba-inc.com/dapr/daprd:${'$'}{user.version}
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: v1.0/healthz
              port: 3500
              scheme: HTTP
            initialDelaySeconds: 2
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
                cpu: 1
                memory: 128Mi
            requests:
                cpu: 1
                memory: 128Mi
          ports:
            - containerPort: 50001
              name: grpc
              protocol: TCP
            - containerPort: 3500
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              path: v1.0/healthz
              port: 3500
              scheme: HTTP
            initialDelaySeconds: 2
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/logs
              name: 44cde429260b721f6a8c26b99ab1601b
</#if>
<#if user.type?has_content>
spec:
  template:
    metadata:
      labels:
        middleware.alibaba-inc.com/dapr: ${'$'}{user.type}
</#if>"""
        val paramMap = YamlUtils.load("""
version: v1.15.4.8
        """)
        assertEquals("""
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
          nvidia.com/gpu: 1
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }

    @Test
    fun test_resourceSpec_number_presentation() {
        val tpl = """
<#setting number_format="computer">
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
        val paramMap = YamlUtils.load(
            """
resources:
  requests:
    cpu: '32'
    memory: '256'
    disk: '60'
    gpu: '16'
  limits:
    cpu: '32'
    memory: '256'
    disk: '60'
    gpu: '16'
        """
        )
        assertEquals(
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "60"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1600"
            limits:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1600"
""", FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap))
        )
        val tpl2 = """
spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "${'$'}{user.resources.requests.disk}"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${'$'}{user.resources.requests.cpu}"
              memory: "${'$'}{user.resources.requests.memory}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.requests.gpu?number)*100)?int}"
              </#if>
            limits:
              cpu: "${'$'}{user.resources.limits.cpu}"
              memory: "${'$'}{user.resources.limits.memory}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              alibabacloud.com/gpu: "${'$'}{((user.resources.limits.gpu?number)*100)?int}"
              </#if>
              """.trimIndent()
        assertEquals(
            """spec:
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: "60"
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1,600"
            limits:
              cpu: "32"
              memory: "256"
              alibabacloud.com/gpu: "1,600"
""", FreemarkerUtils.parseTemplate(tpl2, mapOf("user" to paramMap))
        )
    }

    @Test
    fun testParseTemplate_with_check_template_condition() {
        val tpl = """
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: ${'$'}{user.resources.requests.cpu}
          memory: ${'$'}{user.resources.requests.memory}
          ephemeral-storage: ${'$'}{user.resources.requests.disk}
          <#if user.resources.requests.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.requests.gpu}
          </#if>
        limits:
          cpu: ${'$'}{user.resources.limits.cpu}
          memory: ${'$'}{user.resources.limits.memory}
          ephemeral-storage: ${'$'}{user.resources.limits.disk}
          <#if user.resources.limits.gpu??>
          nvidia.com/gpu: ${'$'}{user.resources.limits.gpu}
          </#if>"""
        val paramMap = YamlUtils.load("""
            resources:
                requests:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
                limits:
                  cpu: '4'
                  memory: 8Gi
                  disk: 60Gi
        """)
        assertEquals("""
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }

    @Test
    fun testParseTemplate_with_empty_map() {
        val tpl = """
<#if user.customSystemProperties?has_content>
customSystemProperties: 
<#list user.customSystemProperties as key, value>
  ${'$'}{key}: ${'$'}{value}
</#list>
</#if>
"""
        val paramMap = YamlUtils.load("""{}""".trimIndent())
        assertEquals("""
""",FreemarkerUtils.parseTemplate(tpl, mapOf("user" to paramMap)))
    }


    @Test
    fun innerYamlPodTest(){
        val paddingNumber = 1
        val originalYaml = """
spec:
  containers:
    - name: main
      resources:
        requests:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        limits:
          cpu: 4
          memory: 8Gi
          ephemeral-storage: 60Gi
        """.trimIndent()

        val finalYaml = """
spec:
    containers:
      - name: main
        resources:
          requests:
            cpu: 4
            memory: 8Gi
            ephemeral-storage: 60Gi
          limits:
            cpu: 4
            memory: 8Gi
            ephemeral-storage: 60Gi
        """.trimIndent()

        val trimYaml = FreemarkerUtils.innerYamlPad(paddingNumber, originalYaml)
        assertEquals(trimYaml, finalYaml)
    }
}