package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ALL
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectPatchService
import com.alibaba.koastline.multiclusters.resourceobj.model.PatchStrategyDefinition
import com.alibaba.koastline.multiclusters.resourceobj.model.Protocol
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectSceneEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.Strategy
import com.alibaba.koastline.multiclusters.resourceobj.params.ResourceObjectPatchStrategyEnum.ignore
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.kubernetes.client.openapi.models.V1StatefulSet
import io.mockk.every
import io.mockk.spyk
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertThrows
import org.springframework.stereotype.Component
import kotlin.test.assertEquals


@Component
class ResourceObjectPatchServiceTest {

    @Test
    fun `testPatchFeature -- upgrade-merge-annotations not found`() {
        val protocol  = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-annotations: a
        """.trimIndent()
        val resourceObjectPatchService =  getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(resourceObjectPatchService.patchFeature(protocol, null, getBaseResourceObjectSpec(), featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
            ResourceObjectSceneEnum.DEPLOY))

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- existed upgrade-merge-annotations is empty`() {
        val protocol  = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-annotations: a
        """.trimIndent()
        val resourceObjectPatchService =  getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = ""
        val assembledResourceObjectStr = YamlUtils.dump(resourceObjectPatchService.patchFeature(protocol, null,
            baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
            ResourceObjectSceneEnum.DEPLOY))

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- merge upgrade-merge-annotations`() {
        val protocol  = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-annotations: a
        """.trimIndent()
        val resourceObjectPatchService =  getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "c,b"
        val assembledResourceObjectStr = YamlUtils.dump(resourceObjectPatchService.patchFeature(protocol, null,
            baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
            ResourceObjectSceneEnum.DEPLOY))

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "c,b,a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- merge upgrade-merge-annotations with existed value`() {
        val protocol  = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-annotations: a,b
        """.trimIndent()
        val resourceObjectPatchService =  getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "b,c,b,b,b"
        val assembledResourceObjectStr = YamlUtils.dump(resourceObjectPatchService.patchFeature(protocol, null,
            baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
            ResourceObjectSceneEnum.DEPLOY))

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-annotations"] = "b,c,a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- upgrade-merge-labels not found`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-labels: a
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                getBaseResourceObjectSpec(),
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- existed upgrade-merge-labels is empty`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-labels: a
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = ""
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null,
                baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- merge upgrade-merge-labels`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-labels: a
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "c,b"
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null,
                baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "c,b,a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }


    @Test
    fun `testPatchFeature -- merge upgrade-merge-labels with existed value`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
            metadata:
              annotations:
                sigma.ali/upgrade-merge-labels: a,b
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecToPatch = getBaseResourceObjectSpec()
        baseResourceObjectSpecToPatch["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "c,b,c,b,b,c"
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null,
                baseResourceObjectSpecToPatch, featureSpecStr, null,"resourceObjectFeatureKey", APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["metadata"]
            .run { this as Map<String, Any> }["annotations"]
            .run { this as MutableMap<String, Any> }["sigma.ali/upgrade-merge-labels"] = "c,b,a"
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }


    @Test
    fun testPatchFeature_with_obj_property_replace() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      containers:
        - name: main
          resources:
            limits:
              cpu: '4'
              ephemeral-storage: '64424509440'
              memory: '8589934592'
            requests:
              cpu: '4'
              ephemeral-storage: '64424509440'
              memory: '8589934592'"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                getBaseResourceObjectSpec(),
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["spec"]
            .run { this as Map<String, Any> }["containers"]
            .run { this as List<Any> }[0]
            .run { this as MutableMap<String, Any> }["resources"] = featureSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["spec"]
            .run { this as Map<String, Any> }["containers"]
            .run { this as List<Any> }[0]
            .run { this as Map<String, Any> }["resources"] as Any
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun testPatchFeature_with_obj_property_merge() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    metadata:
      labels:
        sigma.ali/site: na610
        sigma.ali/subgroup: default
        sigma.ali/upstream-component: normandy
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      annotations:
        alibabacloud.com/ip-stack: ipv4
          """
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                getBaseResourceObjectSpec(),
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["metadata"].apply {
            //patch template.metadata.labels
            this.run { this as Map<String, Any> }["labels"]
                .run { this as MutableMap<String, Any> }.putAll(
                    featureSpec["spec"]
                        .run { this as Map<String, Any> }["template"]
                        .run { this as Map<String, Any> }["metadata"]
                        .run { this as Map<String, Any> }["labels"]
                        .run { this as Map<String, Any> })
        }.apply {
            //patch template.metadata.annotations
            this.run { this as Map<String, Any> }["annotations"]
                .run { this as MutableMap<String, Any> }.putAll(
                    featureSpec["spec"]
                        .run { this as Map<String, Any> }["template"]
                        .run { this as Map<String, Any> }["metadata"]
                        .run { this as Map<String, Any> }["annotations"]
                        .run { this as Map<String, Any> })
        }
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun testPatchFeature_with_obj_property_is_null() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: sigma.ali/resource-pool
                    operator: In
                    values:
                      - sigma_public
                  - key: sigma.ali/is-ecs
                    operator: In
                    values:
                      - 'true'"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                getBaseResourceObjectSpec(),
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["spec"]
            .run { this as MutableMap<String, Any> }["affinity"] = featureSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["spec"]
            .run { this as Map<String, Any> }["affinity"] as Any
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)

    }

    @Test
    fun `testAffinityInjectCase1--No Affinity Declaration`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T5
"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
    spec:"""
        val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
        ).toMutableMap()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                patchStrategyDefinitionOfGpu,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata: null
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
"""

        assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

    }

    @Test
    fun `testAffinityInjectCase1--One MatchExpression`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T5"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: sigma.ali/resource-pool
                    operator: In
                    values:
                      - sigma_public"""
        val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
        ).toMutableMap()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                patchStrategyDefinitionOfGpu,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
"""

        assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

    }

    @Test
    fun `testAffinityInjectCase1--Two MatchExpression`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T5"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: sigma.ali/resource-pool
                    operator: In
                    values:
                      - sigma_public
              - matchExpressions:
                  - key: sigma.ali/resource-pool
                    operator: In
                    values:
                      - compute_platform
 """
        val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
        ).toMutableMap()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                patchStrategyDefinitionOfGpu,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - compute_platform
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
"""

        assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

    }

  @Test
  fun `testMW_ENV  one container add`() {
    val protocol = "CloneSet"
    val featureSpecStr = """spec:
  template:
    spec:
      containers:
        - env:
            - name: MW_ENV
              value: changren"""
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'"""
    val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
    ).toMutableMap()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfMW_ENV,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )


    val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
      - name: main
        env:
        - name: ali_start_app
          value: 'no'
        - name: MW_ENV
          value: changren
"""

    assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

  }

  @Test
  fun `testMW_ENV  two container add`() {
    val protocol = "CloneSet"
    val featureSpecStr = """spec:
  template:
    spec:
      containers:
        - env:
            - name: MW_ENV
              value: changren"""
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
        - name: tengine
          env:
            - name: ali_start_app
              value: 'no'"""
    val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
    ).toMutableMap()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfMW_ENV,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )


    val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
      - name: main
        env:
        - name: ali_start_app
          value: 'no'
        - name: MW_ENV
          value: changren
      - name: tengine
        env:
        - name: ali_start_app
          value: 'no'
        - name: MW_ENV
          value: changren
"""

    assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

  }

  @Test
  fun `testMW_ENV  two container overwrite`() {
    val protocol = "CloneSet"
    val featureSpecStr = """spec:
  template:
    spec:
      containers:
        - env:
            - name: MW_ENV
              value: changren"""
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
        - name: main
          env:
            - name: MW_ENV
              value: 123
        - name: tengine
          env:
            - name: ali_start_app
              value: 'no'"""
    val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
    ).toMutableMap()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfMW_ENV,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )


    val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: CloneSet
spec:
  template:
    spec:
      containers:
      - name: main
        env:
        - name: MW_ENV
          value: changren
      - name: tengine
        env:
        - name: ali_start_app
          value: 'no'
        - name: MW_ENV
          value: changren
"""

    assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

  }

    @Test
    fun `testAffinityInjectCase1--Overwrite One matchExpression`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T5"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - T4
 """
        val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
        ).toMutableMap()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                patchStrategyDefinitionOfGpu,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
"""

        assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

    }

    @Test
    fun `testAffinityInjectCase1--Overwrite Two matchExpressions`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      affinity: 
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: alibabacloud.com/gpu-card-model-detail
                    operator: In
                    values:
                      - Tesla-V100-SXM2-16GB
                      - T5"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - T4
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - compute_platform
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - T5
 """
        val baseResourceObjectSpec = YamlUtils.load(baseResourceObjectSpecStr.trimIndent()
        ).toMutableMap()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                patchStrategyDefinitionOfGpu,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )


        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - compute_platform
              - key: alibabacloud.com/gpu-card-model-detail
                operator: In
                values:
                - Tesla-V100-SXM2-16GB
                - T5
"""

        assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

    }

  @Test
  fun `testJsonMergeValue`() {
    val protocol = "StatefulSet"
    val featureSpecStr = """spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"staragent":{"PreStopHookTimeoutSeconds":"600"}}}'
      """
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpecStr = """spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
"""
    val baseResourceObjectSpec = YamlUtils.load(
      baseResourceObjectSpecStr.trimIndent()
    ).toMutableMap()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfJsonMerge,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )


    val expectedPatchedResourceObjectSpecStr = """spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"},"staragent":{"PreStopHookTimeoutSeconds":"600"}}}'

""".trimIndent()

    assertEquals(expectedPatchedResourceObjectSpecStr, patchedResourceObjectSpecStr)

  }

    @Test
    fun `testBlocker -- blocker do not work`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  spec:
    volumeClaimTemplates:
      AQUAMAN_BLOKER
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        assertEquals(YamlUtils.dump(getBaseResourceObjectSpec()), patchedResourceObjectSpecStr)

    }

    @Test
    fun `testBlocker -- validator do not work`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  metadata:
    labels:
      sigma.ali/app-name:
        AQUAMAN_BLOKER.*-test-app4
  spec:
    template:
      spec:
        volumes:
          AQUAMAN_BLOKER.*-test-app4
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        val patchedResourceObjectSpecStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        assertEquals(YamlUtils.dump(getBaseResourceObjectSpec()), patchedResourceObjectSpecStr)

    }

  @Test
  fun `testBlocker -- hostPath blocker do not work`() {
    val protocol = "StatefulSet"
    val featureSpecStr = """
spec:
  template:
    spec:
      volumes:
        - hostPath: AQUAMAN_BLOKER
        """.trimIndent()
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpec = getBaseResourceObjectSpec()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfHostPathBlocker,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )

    assertEquals(YamlUtils.dump(getBaseResourceObjectSpec()), patchedResourceObjectSpecStr)

  }

    @Test
    fun `testResourceObjectChecker -- label blocker`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  metadata:
    labels:
      sigma.ali/app-name:
        AQUAMAN_BLOKER
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        var exception: ResourceObjectException = assertThrows(
            ResourceObjectException::class.java,
            { YamlUtils.dump(
                resourceObjectPatchService.patchFeature(
                    protocol,
                    null,
                    baseResourceObjectSpec,
                    featureSpecStr,
                    null,
                    "resourceObjectFeatureKey",
                    APP_NAME,
                    ResourceObjectSceneEnum.DEPLOY
                )
            ) }
        )

        assertEquals("资源对象路径非法,path:metadata.labels.sigma.ali/app-name", exception.message)

    }

    @Test
    fun `testResourceObjectChecker -- volume blocker`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  spec:
    template:
      spec:
        volumes:
        - name: e6e7d04f2c902940a90aad534532bac9
          AQUAMAN_BLOKER: true
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        var exception: ResourceObjectException = assertThrows(
            ResourceObjectException::class.java,
            { YamlUtils.dump(
                resourceObjectPatchService.patchFeature(
                    protocol,
                    null,
                    baseResourceObjectSpec,
                    featureSpecStr,
                    null,
                    "resourceObjectFeatureKey",
                    APP_NAME,
                    ResourceObjectSceneEnum.DEPLOY
                )
            ) }
        )

        assertEquals("资源对象路径非法,path:spec.template.spec.volumes,value:{name=e6e7d04f2c902940a90aad534532bac9, emptyDir={}}", exception.message)

    }

  @Test
  fun `testResourceObjectChecker -- volume host path blocker work`() {
    val protocol = "StatefulSet"
    val featureSpecStr = """
  spec:
    template:
      spec:
        volumes:
          - hostPath: AQUAMAN_BLOKER
        """.trimIndent()
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpec = YamlUtils.load(
      """
spec:
  template:
    spec:
      volumes:
        - name: valid
          emptyDir:  {}
        - name: test
          hostPath: 
            path: /data
            type: Directory
""".trimIndent()
    ).toMutableMap()
    var exception: ResourceObjectException = assertThrows(
      ResourceObjectException::class.java,
      {
        YamlUtils.dump(
          resourceObjectPatchService.patchFeature(
            protocol,
            null,
            baseResourceObjectSpec,
            featureSpecStr,
            patchStrategyDefinitionOfHostPathBlocker,
            "resourceObjectFeatureKey",
            APP_NAME,
            ResourceObjectSceneEnum.DEPLOY
          )
        )
      }
    )

    assertEquals(
      "资源对象路径非法,path:spec.template.spec.volumes.hostPath",
      exception.message
    )

  }

  @Test
  fun `testResourceObjectChecker -- do not inject to empty resource object`() {
    val protocol = "StatefulSet"
    val featureSpecStr = """
  spec:
    template:
      spec:
        volumes:
          - hostPath: AQUAMAN_BLOKER
        """.trimIndent()
    val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
    val baseResourceObjectSpec = getSlimResourceObject()
    val patchedResourceObjectSpecStr = YamlUtils.dump(
      resourceObjectPatchService.patchFeature(
        protocol,
        null,
        baseResourceObjectSpec,
        featureSpecStr,
        patchStrategyDefinitionOfHostPathBlocker,
        "resourceObjectFeatureKey",
        APP_NAME,
        ResourceObjectSceneEnum.DEPLOY
      )
    )
    assertEquals(YamlUtils.dump(getSlimResourceObject()), patchedResourceObjectSpecStr)

  }

  private fun getSlimResourceObject() = YamlUtils.load(
    """
    spec:
      template:
        spec:
          """.trimIndent()
  ).toMutableMap()

  @Test
    fun `testResourceObjectChecker -- label validator`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  metadata:
    labels:
      sigma.ali/app-name:
        AQUAMAN_BLOKER.*-test-app3
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        var exception: ResourceObjectException = assertThrows(
            ResourceObjectException::class.java,
            { YamlUtils.dump(
                resourceObjectPatchService.patchFeature(
                    protocol,
                    null,
                    baseResourceObjectSpec,
                    featureSpecStr,
                    null,
                    "resourceObjectFeatureKey",
                    APP_NAME,
                    ResourceObjectSceneEnum.DEPLOY
                )
            ) }
        )

        assertEquals("资源对象取值非法,path:metadata.labels.sigma.ali/app-name,value:normandy-test-app4,regEx:AQUAMAN_BLOKER.*-test-app3", exception.message)

    }

    @Test
    fun `testResourceObjectChecker -- PVC blocker`() {
        val protocol = "StatefulSet"
        val featureSpecStr = """
  spec:
    volumeClaimTemplates:
      AQUAMAN_BLOKER
        """.trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val baseResourceObjectSpec = YamlUtils.load(
            """
spec:
  volumeClaimTemplates:
  - metadata:
      annotations:
        sigma.ali/as-root-disk: "true"
      creationTimestamp: null
      name: 1effb2475fcfba4f9e8b8a1dbc8f3caf
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 480Gi
      storageClassName: csi-ultron-efficiency
    status: {}
""".trimIndent()
        ).toMutableMap()
        var exception: ResourceObjectException = assertThrows(
            ResourceObjectException::class.java,
            { YamlUtils.dump(
                resourceObjectPatchService.patchFeature(
                    protocol,
                    null,
                    baseResourceObjectSpec,
                    featureSpecStr,
                    null,
                    "resourceObjectFeatureKey",
                    APP_NAME,
                    ResourceObjectSceneEnum.DEPLOY
                )
            ) }
        )

        assertEquals("资源对象路径非法,path:spec.volumeClaimTemplates", exception.message)

    }

    @Test
    fun testPatchFeature_with_list_merge() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    spec:
      containers:
        - name: main
          env:
            - name: ali_start_app
              value: 'no'
            - name: ali_run_mode
              value: common_vm"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                getBaseResourceObjectSpec(),
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        val featureSpec = YamlUtils.load(featureSpecStr)
        val baseResourceObjectSpec = getBaseResourceObjectSpec()
        baseResourceObjectSpec["spec"]
            .run { this as Map<String, Any> }["template"]
            .run { this as Map<String, Any> }["spec"]
            .run { this as Map<String, Any> }["containers"]
            .run { this as List<Any> }[0]
            .run { this as MutableMap<String, Any> }["env"]
            .run { this as MutableList<Any> }.addAll(
                featureSpec["spec"]
                    .run { this as Map<String, Any> }["template"]
                    .run { this as Map<String, Any> }["spec"]
                    .run { this as Map<String, Any> }["containers"]
                    .run { this as List<Any> }[0]
                    .run { this as Map<String, Any> }["env"] as List<Any>
            )
        assertEquals(YamlUtils.dump(baseResourceObjectSpec), assembledResourceObjectStr)
    }

    @Test
    fun testPatchFeature_with_obj_property_ignore_by_additionalPatchStrategyDefinition() {
        val protocol = "StatefulSet"
        val featureSpecStr = """spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'false'
        """
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null, getBaseResourceObjectSpec(), featureSpecStr, PatchStrategyDefinition(
                    listOf(Protocol(protocol)),
                    listOf(
                        Strategy(
                            ref = "spec.template.metadata.annotations.pod.beta1.sigma.ali/alarming-off-upgrade",
                            patchStrategy = ignore.name
                        )
                    ),
                    emptyList()
                ),
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        )

        //转V1StatefulSet校验
        JsonUtils.gsonReadValue(
            JsonUtils.writeValueAsString(YamlUtils.load(assembledResourceObjectStr)),
            V1StatefulSet::class.java
        )

        assertEquals(YamlUtils.dump(getBaseResourceObjectSpec()), assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- merge cloneset volumeMounts by path`() {
        val protocol  = "CloneSet"
        val featureSpecStr = """
spec:
  template:
    spec:
      containers:
      - name: main
        image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20221130190655566578_publish
        volumeMounts:
        - {name: another_equal_name, mountPath: /home/<USER>/normandy-test-app4/logs}
        - {name: another_not_equal_name, mountPath: /home/<USER>/cai/logs}
      volumes:
      - name: another_equal_name
        emptyDir: {}
      - name: another_not_equal_name
        emptyDir: {}""".trimIndent()
        val resourceObjectPatchService =  getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null, getBaseResourceObjectSpec(), featureSpecStr, null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        ).trimIndent()
        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    statefulset.sigma.ali/mode: sigma
    sigma.ali/app-name: normandy-test-app4
  creationTimestamp: '2022-01-05T03:57:35Z'
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4
    spec:
      containers:
      - env:
        - name: envSign
          value: production
        image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20221130190655566578_publish
        name: main
        volumeMounts:
        - name: e6e7d04f2c902940a90aad534532bac9
          mountPath: /home/<USER>/normandy-test-app4/logs
        - name: another_not_equal_name
          mountPath: /home/<USER>/cai/logs
        livenessProbe:
          initialDelaySeconds: 300
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
          exec:
            command:
            - /bin/sh
            - -c
            - '[ ! -x /home/<USER>/liveness.sh ] && exit 1; sudo -u admin /home/<USER>/liveness.sh
              2>&1;'
      volumes:
      - name: e6e7d04f2c902940a90aad534532bac9
        emptyDir: {}
      - name: another_equal_name
        emptyDir: {}
      - name: another_not_equal_name
        emptyDir: {}""".trimIndent()
        assertEquals(expectedPatchedResourceObjectSpecStr, assembledResourceObjectStr)
    }

    @Test
    fun `testPatchFeature -- merge cloneset volumeMounts by path acni case`() {
        val protocol = "CloneSet"
        val featureSpecStr = """
spec:
  template:
    spec:
      volumes:
        - name: acni-crevol-cmms-measure-info
          secret:
            secretName: s1
            defaultMode: 420
        - name: acni-crevol-daraz-reverse-oss
          secret:
            secretName: s2
            defaultMode: 420
        - name: acni-crevol-safebox-meta-v2
          secret:
            secretName: s3
            defaultMode: 420
      containers:
        - name: main
          env:
          volumeMounts:
            - name: acni-crevol-cmms-measure-info
              readOnly: true
              mountPath: /etc/cmms
            - name: acni-crevol-daraz-reverse-oss
              readOnly: true
              mountPath: /etc/acni/resources/aliyun/daraz-reverse-oss
            - name: acni-crevol-safebox-meta-v2
              readOnly: true
              mountPath: /etc/acni/safebox-meta-v2/target""".trimIndent()
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()
        val assembledResourceObjectStr = YamlUtils.dump(
            resourceObjectPatchService.patchFeature(
                protocol, null, getBaseResourceObjectSpecCloneSet(), featureSpecStr, null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )
        ).trimIndent()
        val expectedPatchedResourceObjectSpecStr = """apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
spec:
  template:
    spec:
      containers:
      - name: rasp-init-sidecar
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /home/<USER>/cai/mtopapirepo
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: global-reverse-center-s
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /tmp
          name: shared-tmp
        - mountPath: /home/<USER>/code-share
          name: ncee-share-dir-0
        - mountPath: /home/<USER>/daraz-reverse-ns-s/logs
          name: logdir-0
        - mountPath: /home/<USER>/logs
          name: logdir-1
        - mountPath: /home/<USER>/cai/mtopapirepo
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: main
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /tmp
          name: shared-tmp
        - mountPath: /home/<USER>/code-share
          name: ncee-share-dir-0
        - mountPath: /home/<USER>/daraz-reverse-ns-s/logs
          name: logdir-0
        - mountPath: /home/<USER>/logs
          name: logdir-1
        - mountPath: /home/<USER>/cai/mtopapirepo
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
        - mountPath: /etc/cmms
          name: acni-crevol-cmms-measure-info
          readOnly: true
        - mountPath: /etc/acni/resources/aliyun/daraz-reverse-oss
          name: acni-crevol-daraz-reverse-oss
          readOnly: true
        - mountPath: /etc/acni/safebox-meta-v2/target
          name: acni-crevol-safebox-meta-v2
          readOnly: true
      - name: rasp-sidecar
        volumeMounts:
        - mountPath: /home/<USER>/cai/mtopapirepo
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      volumes:
      - emptyDir: {}
        name: shared-tmp
      - emptyDir: {}
        name: appstack-iac-vol
      - downwardAPI:
          defaultMode: 420
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/sn']
            path: staragent_sn
        name: cse-staragent-sn
      - emptyDir: {}
        name: ncee-share-dir-0
      - emptyDir: {}
        name: logdir-0
      - emptyDir: {}
        name: logdir-1
      - emptyDir: {}
        name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: acni-crevol-cmms-measure-info
        secret:
          defaultMode: 420
          secretName: s1
      - name: acni-crevol-daraz-reverse-oss
        secret:
          secretName: s2
          defaultMode: 420
      - name: acni-crevol-safebox-meta-v2
        secret:
          secretName: s3
          defaultMode: 420
          """.trimIndent()
        assertEquals(expectedPatchedResourceObjectSpecStr, assembledResourceObjectStr)
    }

    @Test
    fun `testServerlessAppFeatureInject`() {
        val protocol = "ServerlessApp"
        val featureSpecStr = """appHooks:
- beforeStartApp:
  - bash /home/<USER>/xxx/bin/on.sh
  afterStartApp:
  - xxx
  beforeStopApp:
  - bash /home/<USER>/xxx/bin/off.sh
  afterStopApp: []
  name: safety-out
  priority: 100
- beforeStartApp:
  - sudo /usr/alisys/dragoon/libexec/armory/bin/safetyout.sh 1
  afterStopApp:
  - sudo /usr/alisys/dragoon/libexec/armory/bin/safetyout.sh 0 
  name: safetyout
  priority: 100
"""
        val resourceObjectPatchService = getResourceObjectPatchServiceSpy()

        val baseResourceObjectSpec = getServerlessAppBaseResourceObjectSpec()
        val patchedResourceObjectSpec =
            resourceObjectPatchService.patchFeature(
                protocol,
                null,
                baseResourceObjectSpec,
                featureSpecStr,
                null,
                "resourceObjectFeatureKey",
                APP_NAME,
                ResourceObjectSceneEnum.DEPLOY
            )


        val expectedPatchedResourceObjectSpecStr = """app: dosa
k8sId: asi_zjk_core_a01
site: na610
group: dosa_serverless_host
unit: CENTER_UNIT.center
stage: PUBLISH
runtimeId: 基座Id
envLevel: 环境级别 例如 production/staging/staging1/staging2/...
jarURI: 构建产物的地址，存取协议待定，比如OSS file path，容器使用该值获取App包文件
appVersion: '{构建的app版本信息}'
steps:
- 10
- 25
- 50
- 70
- 100
pauseMode: 1
uniqueId: 英文数字串 不超64字符
customLabels:
  k1: v1
  k2: v2
markInfos:
  '{someKeyForDetermineStep}': prefix{{.Step}}
  '{someKeyForAppVersion}': '18'
  otherKey: otherValue
settings:
  maxSurge: 0
  minHealthCapacity: 80
  skipUpdateNodeCount: true
serviceConfigs:
- configStr: '{"domain":"changesvc.tpp.net","jmenvDom":"jmenv.tbsite.net:8080","jmenvUrl":"","token":"f65092e4aae6936aeeee2b504a14cae9","port":7001,"weight":1}'
  masked: false
  name: vipserverRegA
  type: 2
  async: false
- configStr: '{"domain":"somedomain","jmenvDom":"jmenv.tbsite.net:8080","jmenvUrl":"","token":"xxxxx","port":7001,"weight":1}'
  masked: false
  name: vipserverRegB
  type: 2
  async: false
- configStr: '{"host":"intra-sky.alibaba-inc.com","appUseType":"PUBLISH","group":"targe_host","buffGroup":"bufferhost_group"}'
  masked: false
  name: skylineReg
  type: 11
  async: true
appHooks:
- beforeStartApp:
  - bash /home/<USER>/xxx/bin/on.sh
  - sudo touch /etc/xxx
  - sudo chmod 777 /etc/xxx
  - sudo echo xxx > /etc/xxx
  afterStartApp:
  - xxx
  beforeStopApp:
  - bash /home/<USER>/xxx/bin/off.sh
  - sudo rm -f /etc/xxx
  afterStopApp: []
  name: string
  priority: 0
- beforeStartApp:
  - bash /home/<USER>/xxx/bin/on.sh
  afterStartApp:
  - xxx
  beforeStopApp:
  - bash /home/<USER>/xxx/bin/off.sh
  afterStopApp: []
  name: safety-out
  priority: 100
- beforeStartApp:
  - sudo /usr/alisys/dragoon/libexec/armory/bin/safetyout.sh 1
  afterStopApp:
  - sudo /usr/alisys/dragoon/libexec/armory/bin/safetyout.sh 0
  name: safetyout
  priority: 100
schedulerMetas:
  k1: v1
  k2: v2
"""

        assertEquals(
            expectedPatchedResourceObjectSpecStr,
            YamlUtils.dump(patchedResourceObjectSpec)
        )

    }
    private fun getBaseResourceObjectSpec(): MutableMap<String, Any> {
        return YamlUtils.load(
            """apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations: {sigma.ali/disable-cascading-deletion: 'true'}
  labels: {statefulset.sigma.ali/mode: sigma, sigma.ali/app-name: normandy-test-app4}
  "creationTimestamp": "2022-01-05T03:57:35Z"
spec:
  template:
    metadata:
      annotations: {pod.beta1.sigma.ali/alarming-off-upgrade: 'true'}
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
    spec:
      containers:
      - env:
        - {name: envSign, value: production}
        image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20221130190655566578_publish
        name: main
        volumeMounts:
        - {name: e6e7d04f2c902940a90aad534532bac9, mountPath: /home/<USER>/normandy-test-app4/logs}
        livenessProbe:
          initialDelaySeconds: 300
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
          exec:
            command: [/bin/sh, -c, '[ ! -x /home/<USER>/liveness.sh ] && exit 1; sudo
                -u admin /home/<USER>/liveness.sh 2>&1;']
      volumes:
      - name: e6e7d04f2c902940a90aad534532bac9
        emptyDir: {}""".trimIndent()
        ).toMutableMap()
    }

    private fun getBaseResourceObjectSpecCloneSet(): MutableMap<String, Any> {
        return YamlUtils.load(
            """apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
spec:
  template:
    spec:
      containers:
      - name: rasp-init-sidecar
        volumeMounts:
        - mountPath: "/home/<USER>/appstack-iac-vol"
          name: appstack-iac-vol
        - mountPath: "/home/<USER>/cai/mtopapirepo"
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: global-reverse-center-s
        volumeMounts:
        - mountPath: "/home/<USER>/appstack-iac-vol"
          name: appstack-iac-vol
        - mountPath: "/tmp"
          name: shared-tmp
        - mountPath: "/home/<USER>/code-share"
          name: ncee-share-dir-0
        - mountPath: "/home/<USER>/daraz-reverse-ns-s/logs"
          name: logdir-0
        - mountPath: "/home/<USER>/logs"
          name: logdir-1
        - mountPath: "/home/<USER>/cai/mtopapirepo"
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: main
        volumeMounts:
        - mountPath: "/home/<USER>/appstack-iac-vol"
          name: appstack-iac-vol
        - mountPath: "/tmp"
          name: shared-tmp
        - mountPath: "/home/<USER>/code-share"
          name: ncee-share-dir-0
        - mountPath: "/home/<USER>/daraz-reverse-ns-s/logs"
          name: logdir-0
        - mountPath: "/home/<USER>/logs"
          name: logdir-1
        - mountPath: "/home/<USER>/cai/mtopapirepo"
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
        - mountPath: "/etc/cmms"
          name: acni-crevol-cmms-measure-info
          readOnly: true
        - mountPath: "/etc/acni/resources/aliyun/daraz-reverse-oss"
          name: acni-crevol-daraz-reverse-oss
          readOnly: true
        - mountPath: "/etc/acni/safebox-meta-v2/target"
          name: acni-crevol-safebox-meta-v2
          readOnly: true
      - name: rasp-sidecar
        volumeMounts:
        - mountPath: "/home/<USER>/cai/mtopapirepo"
          name: 0e26b0513f4b0e0fde55f8febc76a0f9
      volumes:
      - emptyDir: {}
        name: shared-tmp
      - emptyDir: {}
        name: appstack-iac-vol
      - downwardAPI:
          defaultMode: 420
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/sn']
            path: staragent_sn
        name: cse-staragent-sn
      - emptyDir: {}
        name: ncee-share-dir-0
      - emptyDir: {}
        name: logdir-0
      - emptyDir: {}
        name: logdir-1
      - emptyDir: {}
        name: 0e26b0513f4b0e0fde55f8febc76a0f9
      - name: acni-crevol-cmms-measure-info
        secret:
          defaultMode: 420
          secretName: s1
        secret:
          defaultMode: 420
          secretName: s2
        secret:
          defaultMode: 420
          secretName: s3
""".trimIndent()
        ).toMutableMap()
    }

    private fun getServerlessAppBaseResourceObjectSpec(): MutableMap<String, Any> {
        return YamlUtils.load(
            """---
app: dosa
k8sId: asi_zjk_core_a01
site: na610
group: dosa_serverless_host
unit: CENTER_UNIT.center
stage: PUBLISH
runtimeId: 基座Id
envLevel: 环境级别 例如 production/staging/staging1/staging2/...
jarURI: 构建产物的地址，存取协议待定，比如OSS file path，容器使用该值获取App包文件
appVersion: "{构建的app版本信息}"
steps:
- 10
- 25
- 50
- 70
- 100
pauseMode: 1
uniqueId: 英文数字串 不超64字符
customLabels:
  k1: v1
  k2: v2
markInfos:
  "{someKeyForDetermineStep}": prefix{{.Step}}
  "{someKeyForAppVersion}": '18'
  otherKey: otherValue
settings:
  maxSurge: 0
  minHealthCapacity: 80
  skipUpdateNodeCount: true
serviceConfigs:
- configStr: '{"domain":"changesvc.tpp.net","jmenvDom":"jmenv.tbsite.net:8080","jmenvUrl":"","token":"f65092e4aae6936aeeee2b504a14cae9","port":7001,"weight":1}'
  masked: false
  name: vipserverRegA
  type: 2
  async: false
- configStr: '{"domain":"somedomain","jmenvDom":"jmenv.tbsite.net:8080","jmenvUrl":"","token":"xxxxx","port":7001,"weight":1}'
  masked: false
  name: vipserverRegB
  type: 2
  async: false
- configStr: '{"host":"intra-sky.alibaba-inc.com","appUseType":"PUBLISH","group":"targe_host","buffGroup":"bufferhost_group"}'
  masked: false
  name: skylineReg
  type: 11
  async: true
appHooks:
- beforeStartApp:
  - bash /home/<USER>/xxx/bin/on.sh
  - sudo touch /etc/xxx
  - sudo chmod 777 /etc/xxx
  - sudo echo xxx > /etc/xxx
  afterStartApp:
  - xxx
  beforeStopApp:
  - bash /home/<USER>/xxx/bin/off.sh
  - sudo rm -f /etc/xxx
  afterStopApp: []
  name: string
  priority: 0
schedulerMetas:
  k1: v1
  k2: v2
""".trimIndent()
        ).toMutableMap()
    }

    private val patchStrategyDefinitionOfGpu = ObjectMapper().readValue<PatchStrategyDefinition>(
        """
{
  "protocols":[
    {
      "kind":"StatefulSet"
    },
    {
      "kind":"CloneSet"
    }
  ],
  "strategies":[
    {
      "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms",
      "description":"笛卡尔积",
      "patchStrategy":"cartesian"
    },
    {
      "ref":"spec.template.spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms.matchExpressions",
      "patchStrategy":"merge",
      "patchMergeKey":"key"
    }
  ],
  "defaultStrategies":[
    {
      "ref":"default.list",
      "patchStrategy":"replace"
    },
    {
      "ref":"default.map",
      "patchStrategy":"merge"
    },
    {
      "ref":"default.value",
      "patchStrategy":"replace"
    }
  ]
}
""".trimIndent()
    )

  private val patchStrategyDefinitionOfJsonMerge = ObjectMapper().readValue<PatchStrategyDefinition>(
    """
{
  "protocols":[
    {
      "kind":"StatefulSet"
    },
    {
      "kind":"CloneSet"
    },
    {
      "kind":"RollingSet"
    }
  ],
  "strategies":[
    {
      "ref":"spec.template.metadata.annotations.pod.beta1.sigma.ali/container-extra-config",
      "description":"Json Merge",
      "patchStrategy":"jsonMerge"
    }
  ]
}
""".trimIndent()
  )

  private val patchStrategyDefinitionOfMW_ENV = ObjectMapper().readValue<PatchStrategyDefinition>(
    """
{
  "protocols":[
    {
      "kind":"CloneSet"
    }
  ],
  "strategies":[
    {
      "ref":"spec.template.spec.containers",
      "description":"笛卡尔积",
      "patchStrategy":"cartesian"
    },
    {
      "ref":"spec.template.spec.containers.env",
      "patchStrategy":"merge",
      "patchMergeKey":"name"
    }
  ],
  "defaultStrategies":[
    {
      "ref":"default.list",
      "patchStrategy":"replace"
    },
    {
      "ref":"default.map",
      "patchStrategy":"merge"
    },
    {
      "ref":"default.value",
      "patchStrategy":"replace"
    }
  ]
}
""".trimIndent()
  )

  private val patchStrategyDefinitionOfHostPathBlocker = ObjectMapper().readValue<PatchStrategyDefinition>(
    """
{
    "protocols": [
      {
        "kind": "StatefulSet"
      },
      {
        "kind": "CloneSet"
      },
      {
        "kind": "RollingSet"
      }
    ],
    "strategies": [
      {
        "ref": "spec.template.spec.volumes",
        "description": "校验",
        "patchStrategy": "verify"
      }
    ],
    "defaultStrategies": [
      {
        "ref": "default.list",
        "patchStrategy": "replace"
      },
      {
        "ref": "default.map",
        "patchStrategy": "merge"
      },
      {
        "ref": "default.value",
        "patchStrategy": "replace"
      }
    ]
  }
""".trimIndent()
  )



    private fun getResourceObjectPatchServiceSpy(): ResourceObjectPatchService {
        return spyk<ResourceObjectPatchService>() {
            every {
                featureImportListenerFactory.listListener(any())
            }returns emptyList()
            every {
                commonProperties.contains(FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG, APP_NAME)
            }returns false
            every {
                commonProperties.contains(FEATURE_CONFLICT_EXTRA_APP_NAME_LIST_CONFIG, ALL)
            }returns false
        }
    }

    companion object {
        private const val APP_NAME = "normandy-test-app4"
    }

}