package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.cse.models.v1alpha1.cloneset.CloneSet
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectPatchService
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService.Companion.CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME
import com.alibaba.koastline.multiclusters.resourceobj.base.WorkloadSpecContext
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import io.mockk.every
import io.mockk.spyk
import org.junit.Assert
import org.junit.Test
import kotlin.test.assertEquals

class CloneSetSpecServiceTest {

    @Test
    fun testUnMarshalWithGson() {
        val stsStr = """
            {
            	"apiVersion": "apps/v1",
            	"kind": "CloneSet",
            	"spec": {
            		"template": {
            			"spec": {
            				"containers": [{
            					"name": "main",
            					"resources": {
            						"requests": {
            							"cpu": 60
            						}
            					}
            				}]
            			}
            		}
            	}
            }
        """.trimIndent()
        val sts = JsonUtils.gsonReadValue(stsStr, CloneSet::class.java)
        Assert.assertEquals(
            java.math.BigDecimal(60),
            sts.spec.template.spec!!.containers[0].resources!!.requests!!.get("cpu")!!.number
        )
    }

    @Test(expected = com.fasterxml.jackson.databind.exc.MismatchedInputException::class)
    fun testUnMarshalWithJackson() {
        val stsStr = """
            {
            	"apiVersion": "apps/v1",
            	"kind": "CloneSet",
            	"spec": {
            		"template": {
            			"spec": {
            				"containers": [{
            					"name": "main",
            					"resources": {
            						"requests": {
            							"cpu": 60
            						}
            					}
            				}]
            			}
            		}
            	}
            }
        """.trimIndent()
        JsonUtils.readValue(stsStr, CloneSet::class.java)
    }

    @Test
    fun testGetBaseSpec() {
        val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
        val cloneSetSpecService = spyk(CloneSetSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                cloudCmdbApi.getBaseline(envStackId).id
            } returns "stack-pk-id"
            every {
                cloudCmdbApi.getEnvBaselineSpec(envStackId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: "2023-03-09 10:06:14"
  creationTimestamp: null
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
  name: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
spec:
  replicas: 1
  scaleStrategy: {}
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: "true"
        pod.beta1.sigma.ali/alarming-off-upgrade: "true"
      creationTimestamp: null
      labels:
        alibabacloud.com/inject-staragent-sidecar: "true"
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - -c
        - su admin -c '/home/<USER>/start.sh & sleep 999999'
        command:
        - /bin/sh
        env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        name: main
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "1"
            memory: 1Gi
          requests:
            cpu: "1"
            memory: 1Gi
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /tmp
          name: shared-tmp
      dnsPolicy: Default
      shareProcessNamespace: true
      terminationGracePeriodSeconds: 60
      volumes:
      - emptyDir: {}
        name: shared-tmp
      - emptyDir: {}
        name: appstack-iac-vol
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/sn']
            path: staragent_sn
        name: cse-staragent-sn
  updateStrategy:
    type: InPlaceOnly
status:
  availableReplicas: 0
  readyReplicas: 0
  replicas: 0
  updatedReadyReplicas: 0
  updatedReplicas: 0
""".trimIndent()
        }
        cloneSetSpecService.resourceObjectPatchService = spyk(ResourceObjectPatchService())
        val specObj = cloneSetSpecService.getBaseSpec(WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na610",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "normandy-test-app4"
            ),
            envStackId = envStackId,
            ResourceObjectProtocolEnum.CloneSet
        ))
        assertEquals("""apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: '2023-03-09 10:06:14'
    apps.kruise.io/stack-pk-id: stack-pk-id
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    sigma.ali/upstream-component: normandy
  namespace: normandy-test-app4
spec:
  replicas: 1
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: 'true'
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
      labels:
        alibabacloud.com/inject-staragent-sidecar: 'true'
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.ali/app-name: normandy-test-app4
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/upstream-component: normandy
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - -c
        - su admin -c '/home/<USER>/start.sh & sleep 999999'
        command:
        - /bin/sh
        env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        name: main
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: '1'
            memory: 1Gi
          requests:
            cpu: '1'
            memory: 1Gi
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /tmp
          name: shared-tmp
      dnsPolicy: Default
      shareProcessNamespace: true
      terminationGracePeriodSeconds: 60
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      - effect: NoSchedule
        key: sigma.ali/resource-pool
        operator: Equal
        value: sigma_public
      - effect: NoSchedule
        key: sigma.alibaba-inc.com/app-stage
        operator: Equal
        value: PUBLISH
      volumes:
      - emptyDir: {}
        name: shared-tmp
      - emptyDir: {}
        name: appstack-iac-vol
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/sn']
            path: staragent_sn
        name: cse-staragent-sn
  scaleStrategy: {}
  updateStrategy:
    type: InPlaceOnly
status:
  observedGeneration: 0
  replicas: 0
  readyReplicas: 0
  updatedReplicas: 0
  updatedReadyReplicas: 0
  collisionCount: 0
  expectedUpdatedReplicas: 0""".trimIndent(), YamlUtils.dump(specObj.apply { (this["metadata"] as MutableMap<String,Any>).remove("name") }).trimIndent())
    }

    @Test
    fun `testGetDeploySpec workload from appstack`() {
        val envStackPkId = "324a215d6bec6bffc99895f0d8fe5580"
        val cloneSetSpecService = spyk(CloneSetSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                cloudCmdbApi.getEnvBaselineSpecByStackPKId(envStackPkId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: "2023-03-09 10:06:14"
  creationTimestamp: null
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
  name: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
spec:
  replicas: 1
  scaleStrategy: {}
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: "true"
        pod.beta1.sigma.ali/alarming-off-upgrade: "true"
      creationTimestamp: null
      labels:
        alibabacloud.com/inject-staragent-sidecar: "true"
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
    spec:
      containers:
      - name: main
        image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        
            """
        }
        val specObj = cloneSetSpecService.getDeploySpec(envStackPkId, """
            {
                "apiVersion":"apps.kruise.io/v1alpha1",
                "kind":"CloneSet",
                "metadata":{
                    "creationTimestamp":"2022-12-21T09:32:56.000000Z",
                    "generation":123,
                    "labels":{
                        "normandy.alibabacloud.com/order-id":"*********"
                    },
                    "name":"global-voyage690115e248adb20f9d0cd8d46aa1c8ae",
                    "namespace":"cse-default",
                    "resourceVersion":"19090332922",
                    "uid":"73b3eab2-6a21-4e91-af8e-9ffb9262e637"
                },
                "spec":{
                    "minReadySeconds":20,
                    "replicas":2,
                    "selector":{
                        "matchLabels":{
                            "oam.cse.alibaba-inc.com/object-id": "aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_workload"
                        }
                    },
                    "template":{
                        "metadata":{
                            "labels":{
                                "normandy.alibaba-inc.com/stack-id":"global-voyage690115e248adb20f9d0cd8d46aa1c8ae",
                                "normandy.alibabacloud.com/order-id":"*********",
                                "oam.cse.alibaba-inc.com/object-id": "aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_workload",
                                "alibabacloud.com/quota-name": "test",
                                "alibabacloud.com/resource-account-name": "test2"
                            },
                            "annotations":{
                                "alibabacloud.com/linked-quota-name": true
                            }
                        },
                        "spec":{
                            "automountServiceAccountToken":false,
                            "containers":[
                                {
                                    "image":"hub.docker.alibaba-inc.com/aone/global-voyager-platform-deliver2:20230526133726551624",
                                    "name":"main",
                                    "imagePullPolicy":"Always"
                                }
                            ]
                        }
                    },
                    "scaleStrategy":{
                        "podsToDelete":["*******"]
                    },
                    "updateStrategy":{
                        "type":"InPlaceOnly",
                        "partition":"100%",
                        "maxUnavailable":2
                    }
                }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON, WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        ))
        assertEquals("""apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: '2023-03-09 10:06:14'
    apps.kruise.io/stack-pk-id: 324a215d6bec6bffc99895f0d8fe5580
  generation: 123
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
    normandy.alibabacloud.com/order-id: '*********'
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    sigma.ali/upstream-component: normandy
  name: global-voyage690115e248adb20f9d0cd8d46aa1c8ae
  namespace: cse-default
  resourceVersion: '19090332922'
spec:
  replicas: 2
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_workload
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: 'true'
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        alibabacloud.com/linked-quota-name: 'true'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
      labels:
        alibabacloud.com/inject-staragent-sidecar: 'true'
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_workload
        alibabacloud.com/quota-name: test
        alibabacloud.com/resource-account-name: test2
        normandy.alibaba-inc.com/stack-id: global-voyage690115e248adb20f9d0cd8d46aa1c8ae
        normandy.alibabacloud.com/order-id: '*********'
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.ali/app-name: normandy-test-app4
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/upstream-component: normandy
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        name: main
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      - effect: NoSchedule
        key: sigma.ali/resource-pool
        operator: Equal
        value: sigma_public
      - effect: NoSchedule
        key: sigma.alibaba-inc.com/app-stage
        operator: Equal
        value: PUBLISH
  scaleStrategy:
    podsToDelete:
    - *******
  updateStrategy:
    type: InPlaceOnly
    partition: 100%
    maxUnavailable: 2
status:
  observedGeneration: 0
  replicas: 0
  readyReplicas: 0
  updatedReplicas: 0
  updatedReadyReplicas: 0
  collisionCount: 0
  expectedUpdatedReplicas: 0
        """.trimIndent(), YamlUtils.dump(specObj).trimIndent())
    }

    @Test
    fun `testGetDeploySpec workload from aone`() {
        val envStackPkId = "324a215d6bec6bffc99895f0d8fe5580"
        val cloneSetSpecService = spyk(CloneSetSpecService()) {
            baseSpecService = BaseSpecService()
            every {
                cloudCmdbApi.getEnvBaselineSpecByStackPKId(envStackPkId, CLONE_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: "2023-03-09 10:06:14"
  creationTimestamp: null
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
  name: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
spec:
  replicas: 1
  scaleStrategy: {}
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: "true"
        pod.beta1.sigma.ali/alarming-off-upgrade: "true"
      creationTimestamp: null
      labels:
        alibabacloud.com/inject-staragent-sidecar: "true"
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
    spec:
      containers:
      - name: main
        image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        
            """
        }
        val specObj = cloneSetSpecService.getDeploySpec(envStackPkId, """
            {
                "apiVersion":"apps.kruise.io/v1alpha1",
                "kind":"CloneSet",
                "metadata":{
                    "creationTimestamp":"2022-12-21T09:32:56.000000Z",
                    "generation":123,
                    "labels":{
                        "normandy.alibabacloud.com/order-id":"*********"
                    },
                    "name":"global-voyage690115e248adb20f9d0cd8d46aa1c8ae",
                    "namespace":"cse-default",
                    "resourceVersion":"19090332922",
                    "uid":"73b3eab2-6a21-4e91-af8e-9ffb9262e637"
                },
                "spec":{
                    "minReadySeconds":20,
                    "replicas":2,
                    "selector":{
                        "matchLabels":{
                            "sigma.ali/site": "na610",
                            "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.center",
                            "sigma.ali/instance-group": "normandy-test-app4_prehost",
                            "sigma.ali/app-name": "normandy-test-app4",
                            "sigma.alibaba-inc.com/app-stage": "PUBLISH",
                            "sigma.ali/subgroup": "default"
                        }
                    },
                    "template":{
                        "metadata":{
                            "labels":{
                                "normandy.alibaba-inc.com/stack-id":"global-voyage690115e248adb20f9d0cd8d46aa1c8ae",
                                "normandy.alibabacloud.com/order-id":"*********",
                                "oam.cse.alibaba-inc.com/object-id": "aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_workload"
                            }
                        },
                        "spec":{
                            "automountServiceAccountToken":false,
                            "containers":[
                                {
                                    "image":"hub.docker.alibaba-inc.com/aone/global-voyager-platform-deliver2:20230526133726551624",
                                    "name":"main",
                                    "imagePullPolicy":"Always"
                                }
                            ]
                        }
                    },
                    "scaleStrategy":{
                        "podsToDelete":["*******"]
                    },
                    "updateStrategy":{
                        "type":"InPlaceOnly",
                        "partition":"100%",
                        "maxUnavailable":2
                    }
                }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON, WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        ))
        assertEquals("""apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: '2023-03-09 10:06:14'
    apps.kruise.io/stack-pk-id: 324a215d6bec6bffc99895f0d8fe5580
  generation: 123
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
    normandy.alibabacloud.com/order-id: '*********'
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    sigma.ali/upstream-component: normandy
  name: global-voyage690115e248adb20f9d0cd8d46aa1c8ae
  namespace: cse-default
  resourceVersion: '19090332922'
spec:
  replicas: 2
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
      sigma.ali/subgroup: default
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: 'true'
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
      labels:
        alibabacloud.com/inject-staragent-sidecar: 'true'
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        normandy.alibaba-inc.com/stack-id: global-voyage690115e248adb20f9d0cd8d46aa1c8ae
        normandy.alibabacloud.com/order-id: '*********'
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.ali/app-name: normandy-test-app4
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/upstream-component: normandy
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        name: main
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      - effect: NoSchedule
        key: sigma.ali/resource-pool
        operator: Equal
        value: sigma_public
      - effect: NoSchedule
        key: sigma.alibaba-inc.com/app-stage
        operator: Equal
        value: PUBLISH
  scaleStrategy:
    podsToDelete:
    - *******
  updateStrategy:
    type: InPlaceOnly
    partition: 100%
    maxUnavailable: 2
status:
  observedGeneration: 0
  replicas: 0
  readyReplicas: 0
  updatedReplicas: 0
  updatedReadyReplicas: 0
  collisionCount: 0
  expectedUpdatedReplicas: 0
        """.trimIndent(), YamlUtils.dump(specObj).trimIndent())
    }

  @Test
  fun `whetherThreeWayMergeInitialized true`() {
    assertEquals(true, CloneSetSpecService.whetherThreeWayMergeInitialized(cloneSetYaml))
    assertEquals(false, CloneSetSpecService.whetherThreeWayMergeInitialized("""
apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: "2023-03-09 10:06:14"
""".trimIndent()))
  }

  @Test
  fun `lastAppliedConfigInit`() {
    val result = CloneSetSpecService.lastAppliedConfigInit(cloneSetYaml, ResourceObjectFormatEnum.YAML)
    val lastApplied = ((YamlUtils.load(result).get("metadata") as Map<String, Any>).get("annotations") as Map<String, Any>).get("kubectl.kubernetes.io/last-applied-configuration")
    var lastAppliedMap = YamlUtils.load(lastApplied.toString())
    ((lastAppliedMap.get("metadata") as Map<String, Any>).get("annotations") as MutableMap<String, Any>).put("kubectl.kubernetes.io/last-applier", "scale")
    ((lastAppliedMap.get("spec") as MutableMap<String, Any>).put("updateStrategy", mapOf("type" to "InPlaceOnly")))
    assertEquals(cloneSetYaml, YamlUtils.dump(lastAppliedMap))
    val resultObj = YamlUtils.load(result)
    ((resultObj.get("metadata") as MutableMap<String, Any>).get("annotations") as MutableMap<String, Any>).remove("kubectl.kubernetes.io/last-applied-configuration")
    assertEquals(cloneSetYaml, YamlUtils.dump(resultObj))
  }

  @Test
  fun `lastAppliedConfigInit2`() {
    val result = CloneSetSpecService.lastAppliedConfigInit(cloneSetJson, ResourceObjectFormatEnum.JSON)
    val lastApplied = ((YamlUtils.load(result).get("metadata") as Map<String, Any>).get("annotations") as Map<String, Any>).get("kubectl.kubernetes.io/last-applied-configuration")
    val resultComp = YamlUtils.load(result) as MutableMap
    (resultComp.get("spec") as MutableMap<String, Any>).remove("updateStrategy")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("apps.kruise.io/rollout-batch-id")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("apps.kruise.io/rollout-id")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("sigma.ali/app-name")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("sigma.ali/instance-group")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("sigma.ali/site")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("sigma.alibaba-inc.com/app-stage")
    ((resultComp.get("metadata") as Map<String, Any>).get("labels") as MutableMap<String, Any>).remove("sigma.alibaba-inc.com/app-unit")
    ((resultComp.get("metadata") as Map<String, Any>).get("annotations") as MutableMap<String, Any>).remove("kubectl.kubernetes.io/last-applied-configuration")
    assertEquals(JsonUtils.writeValueAsString(resultComp), JsonUtils.writeValueAsString(YamlUtils.load(lastApplied as String)))
    val resultObj = YamlUtils.load(result)
    ((resultObj.get("metadata") as MutableMap<String, Any>).get("annotations") as MutableMap<String, Any>).remove("kubectl.kubernetes.io/last-applied-configuration")
    assertEquals(cloneSetJson, JsonUtils.writeValueAsString(resultObj))
  }

  val cloneSetJson = """
    {"apiVersion":"apps.kruise.io/v1alpha1","kind":"CloneSet","metadata":{"annotations":{"apps.kruise.io/stack-pk-id":"73531725-011a-4e4d-8e30-e29de6b6a4b7","appstack.aone.alibaba-inc.com/resource-name":"global-voyager-platform-deliver-daily-ncloud-ac","cloneset.beta1.sigma.ali/app-fail-count":"0","cloneset.beta1.sigma.ali/image-fail-count":"0","cloneset.beta1.sigma.ali/publish-success-replicas":"1","cloneset.beta1.sigma.ali/scheduled-fail-count":"0","koastline.alibaba-inc.com/modified-datetime":"2024-01-11 14:48:23","oam.cse.alibaba-inc.com/environment":"{\"stage\":\"DAILY\",\"unit\":\"CENTER_UNIT.center\",\"az\":\"na131\",\"region\":\"cn-wulanchabu\"}","sigma.ali/upgrade-merge-annotations":"alibabacloud.com/lightweight-container"},"creationTimestamp":"2022-12-06T09:36:30.000000Z","generation":177,"labels":{"apps.kruise.io/rollout-batch-id":"0","apps.kruise.io/rollout-id":"134945642","appstack.aone.alibaba-inc.com/appname":"global-voyager-platform-deliver","appstack.aone.alibaba-inc.com/envlevel":"testing-ncloud","appstack.aone.alibaba-inc.com/envname":"daily-ncloud","appstack.aone.alibaba-inc.com/iac-revision":"v1","cloneset.asi/mode":"asi","koastline.alibaba-inc.com/controlled-by":"aone","oam.cse.alibaba-inc.com/ac-spec-hash":"2452159736","oam.cse.alibaba-inc.com/appconf-generation":"0","oam.cse.alibaba-inc.com/application-configuration":"global-voyager-platform-deliver-daily-ncloud-ac","oam.cse.alibaba-inc.com/component-instance":"latest","oam.cse.alibaba-inc.com/component-schematic":"global-voyager-platform-deliver-daily-ncloud-component","sigma.ali/app-name":"global-voyager-platform-deliver","sigma.ali/instance-group":"global-voyager-platform-deliver_center_daily-ncloud_dailyhost","sigma.ali/site":"na131","sigma.ali/subgroup":"default","sigma.alibaba-inc.com/app-stage":"DAILY","sigma.alibaba-inc.com/app-unit":"CENTER_UNIT.center"},"name":"global-voyage24e9bcc3d4414c0274c7605be97f4823","namespace":"cse-default","resourceVersion":"34401948467","uid":"9576fe2f-83db-4570-907d-dce6e0d89fb0"},"spec":{"minReadySeconds":20,"replicas":1,"selector":{"matchLabels":{"oam.cse.alibaba-inc.com/object-id":"global-voyage24e9bcc3d4414c0274c7605be97f4823"}},"template":{"metadata":{"annotations":{"alibabacloud.com/lightweight-container":"{\"initContainerImage\": \"reg.docker.alibaba-inc.com/ali/init:1.0.2\"}","pod.beta1.alibabacloud.com/container-cpu-quota-unlimit":"true","pod.beta1.alibabacloud.com/sshd-in-staragent":"true","pod.beta1.sigma.ali/alarming-off-upgrade":"true","pod.beta1.sigma.ali/container-extra-config":"{\"containerConfigs\":{\"main\":{\"PostStartHookTimeoutSeconds\":\"1800\", \"ImagePullTimeoutSeconds\":\"300\"}}}","pod.beta1.sigma.ali/disable-cpuset-mode-injection":"true","pod.beta1.sigma.ali/hostname-template":"global-voyager-platform-deliver{{.IpAddress}}.na131","pod.beta1.sigma.ali/naming-register-state":"working_online","pod.beta1.sigma.ali/request-alloc-spec":"{}","pods.sigma.alibaba-inc.com/inject-pod-sn":"true","sigma.ali/app-storage-mode":"yundisk-pv","sigma.ali/app-storage-size":"42949672960","sigma.ali/disable-over-quota-filter":"true","sigma.ali/enable-apprules-injection":"true"},"labels":{"alibabacloud.com/inject-staragent-sidecar":"true","oam.cse.alibaba-inc.com/application-configuration":"global-voyager-platform-deliver-daily-ncloud-ac","oam.cse.alibaba-inc.com/component-instance":"latest","oam.cse.alibaba-inc.com/component-revision":"global-voyager-platform-deliver-daily-ncloud-component","oam.cse.alibaba-inc.com/component-schematic":"global-voyager-platform-deliver-daily-ncloud-component","oam.cse.alibaba-inc.com/object-id":"global-voyage24e9bcc3d4414c0274c7605be97f4823","quota.alibabacloud.com/disable-admission":"true","sigma.ali/app-name":"global-voyager-platform-deliver","sigma.ali/instance-group":"global-voyager-platform-deliver_center_daily-ncloud_dailyhost","sigma.ali/resource-pool":"sigma_public","sigma.ali/site":"na131","sigma.ali/subgroup":"default","sigma.ali/upstream-component":"normandy","sigma.alibaba-inc.com/app-stage":"DAILY","sigma.alibaba-inc.com/app-unit":"CENTER_UNIT.center"}},"spec":{"automountServiceAccountToken":false,"containers":[{"command":["sleep","***********"],"env":[{"name":"STATIC_CONF_HASH","value":"81d729e97c28d9382380b280ed4d57a8"},{"name":"KRUISE_CONTAINER_PRIORITY","value":"1"},{"name":"IS_SIDECAR","value":"true"},{"name":"SIGMA_IGNORE_RESOURCE","value":"true"}],"image":"reg.docker.alibaba-inc.com/koastline/static-config:********-v0.2","imagePullPolicy":"IfNotPresent","lifecycle":{"postStart":{"exec":{"command":["/static-config/main","-static-conf-info=gitops/static_config/global-voyager-platform-deliver/cf/ef/cfefb709451af4b17e73f04f1f5c1cbc|/appstack-iac/static-config/home/<USER>/global-voyager-platform-deliver/target/global-voyager-platform-deliver/BOOT-INF/classes/application.properties"]}}},"name":"static-config-sidecar","resources":{"limits":{"cpu":"1","memory":"128Mi"},"requests":{"cpu":"1","memory":"128Mi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/appstack-iac/static-config","name":"static-config-initcontainer-volume"},{"mountPath":"/etc/oss","name":"static-config-secret","readOnly":true}]},{"args":["-c","sudo chmod 1777 /tmp & sudo chown -R admin:admin /home/<USER>/ && /home/<USER>/start.sh & sleep 99999999"],"command":["/bin/sh"],"env":[{"name":"CSE_INSTANCE_ID","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"metadata.uid"}}},{"name":"CSE_NODE_NAME","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"spec.nodeName"}}},{"name":"CSE_NODE_IP","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"status.hostIP"}}},{"name":"CSE_POD_NAME","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"metadata.name"}}},{"name":"CSE_POD_NAMESPACE","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"metadata.namespace"}}},{"name":"CSE_POD_IP","valueFrom":{"fieldRef":{"apiVersion":"v1","fieldPath":"status.podIP"}}},{"name":"JPDA_ENABLE","value":"1"},{"name":"SERVICE_OPTS","value":"-Dspring.profiles.active=daily -Dpandora.fast.classloader=true -DlimitTime=100 -Dhsf.consumer.init.accumulate.timeout=10000 -Dhsf.client.localcall=false -Ddruid.load.spifilter.skip=true -Dcom.alibaba.introspector.findCustomizerClass.skipStrategy=SKIP_ALL -Dspring.beaninfo.ignore=true"},{"name":"ALIYUN_LOGTAIL_USER_ID","value":"1304331102723435"},{"name":"ALIYUN_LOGTAIL_USER_DEFINED_ID","value":"voyager-daily"},{"name":"ALIYUN_LOGTAIL_CONFIG","value":"/etc/ilogtail/conf/cn-wulanchabu/ilogtail_config.json"},{"name":"ALIYUN_LOGTAIL_CONFIG_CONFIGURED_BY_TRAIT"},{"name":"ALIYUN_LOG_ENV_TAGS","value":"SN"},{"name":"TZ","value":"Asia/Shanghai"},{"name":"ARTHAS_LIB_DIR","value":"/tmp/arthas"},{"name":"MW_ENV","value":"daily"},{"name":"STATIC_CONF_HASH","value":"81d729e97c28d9382380b280ed4d57a8"},{"name":"JAVA_AGENT","value":"-javaagent:/home/<USER>/appstack-iac-vol/rasp/rasp-agent.jar=1,/home/<USER>/appstack-iac-vol/rasp/jam-framework"}],"image":"hub.docker.alibaba-inc.com/aone/global-voyager-platform-deliver:20240111144659102971","imagePullPolicy":"Always","lifecycle":{"postStart":{"exec":{"command":["/home/<USER>/poststart.sh"]}},"preStop":{"exec":{"command":["/home/<USER>/stop.sh"]}}},"livenessProbe":{"exec":{"command":["/home/<USER>/liveness.sh"]},"failureThreshold":3,"initialDelaySeconds":1800,"periodSeconds":10,"successThreshold":1,"timeoutSeconds":2},"name":"main","resources":{"limits":{"cpu":"8","memory":"17179869184"},"requests":{"cpu":"8","memory":"17179869184"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/home/<USER>/appstack-iac-vol","name":"appstack-iac-vol"},{"mountPath":"/tmp","name":"shared-tmp"},{"mountPath":"/home/<USER>/global-voyager-platform-deliver/logs","name":"logdir-0"},{"mountPath":"/home/<USER>/logs","name":"logdir-1"},{"mountPath":"/appstack-iac/static-config","name":"static-config-initcontainer-volume"}]},{"args":["-c","sudo chmod +x /home/<USER>/rasp/RASP.sh && /home/<USER>/rasp/RASP.sh start"],"command":["/bin/sh"],"env":[{"name":"IS_SIDECAR","value":"true"},{"name":"SIGMA_IGNORE_RESOURCE","value":"true"}],"image":"hub.docker.alibaba-inc.com/aone-base-global/rasp_asi_image:20230912141344","imagePullPolicy":"Always","name":"rasp-sidecar","resources":{"limits":{"cpu":"100m","memory":"300Mi"},"requests":{"cpu":"100m","memory":"300Mi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File"}],"dnsPolicy":"Default","initContainers":[{"args":["-c","sudo chmod +x /home/<USER>/rasp/RASP.sh && /home/<USER>/rasp/RASP.sh install"],"command":["/bin/sh"],"image":"hub.docker.alibaba-inc.com/aone-base-global/rasp_asi_image:20230912141344","imagePullPolicy":"Always","name":"rasp-init","resources":{"limits":{"cpu":"300m","memory":"128Mi"},"requests":{"cpu":"300m","memory":"128Mi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/home/<USER>/appstack-iac-vol","name":"appstack-iac-vol"}]}],"restartPolicy":"Always","schedulerName":"default-scheduler","securityContext":{},"shareProcessNamespace":false,"terminationGracePeriodSeconds":60,"tolerations":[{"effect":"NoSchedule","key":"sigma.ali/is-ecs","operator":"Equal","value":"true"},{"effect":"NoSchedule","key":"sigma.ali/resource-pool","operator":"Equal","value":"sigma_public"},{"effect":"NoSchedule","key":"sigma.alibaba-inc.com/app-stage","operator":"Equal","value":"DAILY"}],"volumes":[{"emptyDir":{},"name":"shared-tmp"},{"emptyDir":{},"name":"appstack-iac-vol"},{"downwardAPI":{"defaultMode":420,"items":[{"fieldRef":{"apiVersion":"v1","fieldPath":"metadata.labels['sigma.ali/sn']"},"path":"staragent_sn"}]},"name":"cse-staragent-sn"},{"emptyDir":{},"name":"logdir-0"},{"emptyDir":{},"name":"logdir-1"},{"emptyDir":{},"name":"static-config-initcontainer-volume"},{"name":"static-config-secret","secret":{"defaultMode":420,"secretName":"koastline-oss-static-config"}}]}},"scaleStrategy":{},"updateStrategy":{"type":"InPlaceOnly","partition":"0%","maxUnavailable":"100%"}},"status":{"observedGeneration":177,"replicas":1,"readyReplicas":1,"updatedReplicas":1,"updatedReadyReplicas":1,"updateRevision":"global-voyage24e9bcc3d4414c0274c7605be97f4823-654d96f7b9","currentRevision":"global-voyage24e9bcc3d4414c0274c7605be97f4823-654d96f7b9","collisionCount":0,"expectedUpdatedReplicas":1}}
  """.trimIndent()


  val cloneSetYaml = """
apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/resource-name: aone-mix-test-191811.3759293-ac
    koastline.alibaba-inc.com/modified-datetime: '2023-03-09 10:06:14'
    kubectl.kubernetes.io/last-applier: scale
  labels:
    appstack.aone.alibaba-inc.com/appname: aone-mix-test
    appstack.aone.alibaba-inc.com/envlevel: testing
  name: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
spec:
  replicas: 1
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441
  template:
    metadata:
      annotations:
        pod.beta1.alibabacloud.com/sshd-in-staragent: 'true'
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
      labels:
        alibabacloud.com/inject-staragent-sidecar: 'true'
        oam.cse.alibaba-inc.com/application-configuration: aone-mix-test-191811.3759293-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/object-id: aone-mix-test9c1c47ab0075bd15c26367c6e146a441_from_box
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - -c
        - su admin -c '/home/<USER>/start.sh & sleep 999999'
        command:
        - /bin/sh
        env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        image: reg-zhangbei.docker.alibaba-inc.com/aone/aone-mix-test_testing:20230309100533614048_daily
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        name: main
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - date
          failureThreshold: 1
          initialDelaySeconds: 1
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: '1'
            memory: 1Gi
          requests:
            cpu: '1'
            memory: 1Gi
        volumeMounts:
        - mountPath: /home/<USER>/appstack-iac-vol
          name: appstack-iac-vol
        - mountPath: /tmp
          name: shared-tmp
      dnsPolicy: Default
      shareProcessNamespace: true
      terminationGracePeriodSeconds: 60
      volumes:
      - emptyDir: {}
        name: shared-tmp
      - emptyDir: {}
        name: appstack-iac-vol
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/sn']
            path: staragent_sn
        name: cse-staragent-sn
  scaleStrategy: {}
  updateStrategy:
    type: InPlaceOnly
status:
  observedGeneration: 0
  replicas: 0
  readyReplicas: 0
  updatedReplicas: 0
  updatedReadyReplicas: 0
  collisionCount: 0
  expectedUpdatedReplicas: 0

""".trimIndent()
}