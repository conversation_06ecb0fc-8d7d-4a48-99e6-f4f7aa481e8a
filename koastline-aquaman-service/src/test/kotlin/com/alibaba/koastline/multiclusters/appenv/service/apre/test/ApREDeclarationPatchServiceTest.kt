package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.appenv.model.Constants.IS_NOT_DELETED
import com.alibaba.koastline.multiclusters.apre.ApREDeclarationPatchService
import com.alibaba.koastline.multiclusters.apre.common.MatchScopeService
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelector
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.IdentityInfo
import com.alibaba.koastline.multiclusters.apre.model.MatchScopeDataDO
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.model.req.ApREDeclarationPatchCreateReqDto
import com.alibaba.koastline.multiclusters.apre.model.req.MatchScopeDataReqDto
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchPriorityEnum
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType.BALANCE_CLUSTER
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeExternalTypeEnum
import com.alibaba.koastline.multiclusters.apre.params.MatchScopeTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeclarationPatchException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeclarationPatchData
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import io.mockk.InternalPlatformDsl
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.junit.Rule
import org.junit.Test
import org.junit.jupiter.api.assertThrows
import org.junit.rules.ExpectedException
import testutils.BaseTest
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class ApREDeclarationPatchServiceTest : BaseTest() {
    private var objectMapper = ObjectMapperFactory.newTolerant()
    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    @Test
    fun `testCreateApREDeclarationPatchWithMatchScope -- duplicate`() {
        val matchScopeDataReqDto = MatchScopeDataReqDto(
            externalId = "app-test-host1",
            externalType = MatchScopeExternalTypeEnum.RESOURCE_GROUP.name
        )
        val apREDeclarationPatchCreateReqDto = ApREDeclarationPatchCreateReqDto(
            balanceType = BALANCE_CLUSTER,
            site = "na610",
            stage = "PRE_PUBLISH",
            unit = "CENTER_UNIT.center",
            creator = "admin",
            declarationPatch = DeclarationPatch(),
            matchScopeDataReqDtoList = listOf(matchScopeDataReqDto)
        )
        val apREDeclarationPatchService = spyk(ApREDeclarationPatchService(objectMapper)) {
            every {
                matchScopeDataRepo.listByTargetTypeAndExternal(
                    targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                    externalType = matchScopeDataReqDto.externalType,
                    externalId = matchScopeDataReqDto.externalId
                )
            } returns listOf(MatchScopeData(
                id = 1L,
                targetType = MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name,
                externalType = matchScopeDataReqDto.externalType,
                externalId = matchScopeDataReqDto.externalId,
                targetId = 10L, creator = "admin", modifier = "admin", exclusions = null, restrictions = null
            ))
            every {
                apREDeclarationPatchDataRepo.findById(10L)
            } returns ApREDeclarationPatchData(
                id = 1L,
                site = apREDeclarationPatchCreateReqDto.site,
                unit = apREDeclarationPatchCreateReqDto.unit,
                stage = apREDeclarationPatchCreateReqDto.stage,
                balanceType = apREDeclarationPatchCreateReqDto.balanceType!!.name,
                creator = "admin", modifier = "admin", declarationPatch = ""
            )
        }

        exceptionRule.expect(ApREDeclarationPatchException::class.java)
        exceptionRule.expectMessage("重复的均衡策略配置，apREDeclarationPatchCreateReqDto:{\"balanceType\":\"BALANCE_CLUSTER\",\"unit\":\"CENTER_UNIT.center\",\"stage\":\"PRE_PUBLISH\",\"site\":\"na610\",\"declarationPatch\":{\"patchItems\":[]},\"creator\":\"admin\",\"matchScopeDataDOs\":[{\"externalId\":\"app-test-host1\",\"externalType\":\"RESOURCE_GROUP\"}]}")
        apREDeclarationPatchService.createApREDeclarationPatchWithMatchScope(apREDeclarationPatchCreateReqDto)
    }

    @Test
    fun testExecuteApREDeclarationPatchForApREDeed_with_single_patch() {
        val appName = "app_name"
        val resourceGroup = "app_name-prehost"
        val now = Date(Instant.now().toEpochMilli())
        val apREDeclarationPatchService = spyk(ApREDeclarationPatchService(objectMapper), recordPrivateCalls = true) {

        }
        every {
            InternalPlatformDsl.dynamicCall(
                apREDeclarationPatchService,
                "queryApREDeclarationPatchDataByAppNameAndResourceGroup",
                arrayOf(appName, resourceGroup, ApREDeclarationPatchType.BALANCE_SITE.name)
            ) { mockk() }
        } returns mutableListOf<ApREDeclarationPatchDataDO>(
            ApREDeclarationPatchDataDO(1L,ApREDeclarationPatchType.BALANCE_SITE.name,null,"CENTER_UNIT.center","PRE_PUBLISH",null, DeclarationPatch(
                mutableListOf(
                    PatchItem(site = "na610", weight = 1),
                    PatchItem(site = "na620", weight = 2)
                )),"creator","moddifier",ApREDeclarationPatchPriorityEnum.MEDIUM.priority,now,now,IS_NOT_DELETED,null),
            ApREDeclarationPatchDataDO(2L,ApREDeclarationPatchType.BALANCE_SITE.name,null,"CENTER_UNIT.center","PUBLISH",null, DeclarationPatch(
                mutableListOf(
                    PatchItem(site = "ea119", weight = 1),
                    PatchItem(site = "ea120", weight = 2)
                )),"creator","moddifier",ApREDeclarationPatchPriorityEnum.HIGH.priority,now,now,IS_NOT_DELETED,null)
        )

        //start test
        val apREDO = apREDeclarationPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(ApREDeedDO(
            null,
            IdentityInfo("production",null,appName,null,null,resourceGroup),
            mutableListOf(
                Declaration(null,null,null,"PRE_PUBLISH","CENTER_UNIT.center", mutableListOf(),1)
            )
        ));
        assertEquals(2, apREDO.declarations!!.size)
        assertEquals("na610", apREDO.declarations!![0].az)
        assertEquals("PRE_PUBLISH", apREDO.declarations!![0].stage)
        assertEquals("CENTER_UNIT.center", apREDO.declarations!![0].unit)
        assertEquals(1, apREDO.declarations!![0].weight)
        assertEquals("na620", apREDO.declarations!![1].az)
        assertEquals("PRE_PUBLISH", apREDO.declarations!![1].stage)
        assertEquals("CENTER_UNIT.center", apREDO.declarations!![1].unit)
        assertEquals(2, apREDO.declarations!![1].weight)
    }

    @Test
    fun testQueryApREDeclarationPatchDataByAppNameAndResourceGroup_with_single_patch_multi_scope() {
        val appName = "app_name"
        val resourceGroup = "app_name-prehost"
        val apREDeclarationPatchService = spyk(ApREDeclarationPatchService(objectMapper)) {
            every {
                apREDeclarationPatchDataRepo.findByIds(listOf(1L))
            } returns listOf(
                ApREDeclarationPatchData(
                    id = 1L,
                    balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                    unit = "center",
                    stage = "PUBLISH",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(site = "na610", weight = 1),
                            PatchItem(site = "na620", weight = 2)
                        )))
                )
            )
        }
        apREDeclarationPatchService.matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                findMatchScopesByTargetAndExternalForApp(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName, listOf(resourceGroup))
            } returns listOf(
                MatchScopeDataDO(
                    externalId = "alibaba",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                ),
                MatchScopeDataDO(
                    externalId = appName,
                    externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                )
            )
        }
        val apREDeclarationPatchDataDOList = apREDeclarationPatchService.queryApREDeclarationPatchDataByAppNameAndResourceGroup(appName, resourceGroup, ApREDeclarationPatchType.BALANCE_SITE.name)
        assertEquals(1, apREDeclarationPatchDataDOList.size)
        assertEquals(1, apREDeclarationPatchDataDOList[0].matchScopeDataDOs!!.size)
        assertEquals(appName, apREDeclarationPatchDataDOList[0].matchScopeDataDOs!![0].externalId)
    }

    @Test
    fun testQueryApREDeclarationPatchDataByAppNameAndResourceGroup_with_multi_patch_multi_scope() {
        val appName = "app_name"
        val resourceGroup = "app_name-prehost"
        val apREDeclarationPatchService = spyk(ApREDeclarationPatchService(objectMapper)) {
            every {
                apREDeclarationPatchDataRepo.findByIds(listOf(1L,2L))
            } returns listOf(
                ApREDeclarationPatchData(
                    id = 1L,
                    balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                    unit = "center",
                    stage = "PUBLISH",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(site = "na610", weight = 1),
                            PatchItem(site = "na620", weight = 1)
                        )))
                ),
                ApREDeclarationPatchData(
                    id = 2L,
                    balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                    unit = "center",
                    stage = "PUBLISH",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(site = "na610", weight = 1),
                            PatchItem(site = "na620", weight = 2)
                        )))
                )
            )
        }
        apREDeclarationPatchService.matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                findMatchScopesByTargetAndExternalForApp(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName, listOf(resourceGroup))
            } returns listOf(
                MatchScopeDataDO(
                    externalId = "alibaba",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                ),
                MatchScopeDataDO(
                    externalId = "3#2_4",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                ),
                MatchScopeDataDO(
                    externalId = "alibaba",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 2L
                ),
                MatchScopeDataDO(
                    externalId = appName,
                    externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 2L
                )
            )
        }
        val apREDeclarationPatchDataDOList = apREDeclarationPatchService.queryApREDeclarationPatchDataByAppNameAndResourceGroup(appName, resourceGroup, ApREDeclarationPatchType.BALANCE_SITE.name)
        assertEquals(1, apREDeclarationPatchDataDOList.size)
        assertEquals(1, apREDeclarationPatchDataDOList[0].matchScopeDataDOs!!.size)
        assertEquals(2L, apREDeclarationPatchDataDOList[0].id)
        assertEquals(appName, apREDeclarationPatchDataDOList[0].matchScopeDataDOs!![0].externalId)
        assertEquals(2, apREDeclarationPatchDataDOList[0].declarationPatch.patchItems.size)
        assertEquals(mutableListOf(
            PatchItem(site = "na610", weight = 1),
            PatchItem(site = "na620", weight = 2)), apREDeclarationPatchDataDOList[0].declarationPatch.patchItems)
    }

    @Test
    fun testQueryApREDeclarationPatchDataByAppNameAndResourceGroup_with_both_type_and_multi_patch_multi_scope() {
        val appName = "app_name"
        val resourceGroup = "app_name-prehost"
        val apREDeclarationPatchService = spyk(ApREDeclarationPatchService(objectMapper)) {
            every {
                apREDeclarationPatchDataRepo.findByIds(listOf(1L,2L,3L,4L))
            } returns listOf(
                ApREDeclarationPatchData(
                    id = 1L,
                    balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                    unit = "center",
                    stage = "PUBLISH",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(site = "na610", weight = 1),
                            PatchItem(site = "na620", weight = 1)
                        )))
                ),
                ApREDeclarationPatchData(
                    id = 2L,
                    balanceType = ApREDeclarationPatchType.BALANCE_SITE.name,
                    unit = "center",
                    stage = "PUBLISH",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(site = "na610", weight = 1),
                            PatchItem(site = "na620", weight = 2)
                        )))
                ),
                ApREDeclarationPatchData(
                    id = 3L,
                    balanceType = ApREDeclarationPatchType.BALANCE_CLUSTER.name,
                    unit = "center",
                    stage = "PUBLISH",
                    site = "na610",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "m"))), weight = 1),
                            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "x86"))), weight = 1)
                        )))
                ),
                ApREDeclarationPatchData(
                    id = 4L,
                    balanceType = ApREDeclarationPatchType.BALANCE_CLUSTER.name,
                    unit = "center",
                    stage = "PUBLISH",
                    site = "na610",
                    creator = "admin",
                    modifier = "admin",
                    declarationPatch = JsonUtils.writeValueAsString(DeclarationPatch(
                        mutableListOf(
                            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "m"))), weight = 1),
                            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "x86"))), weight = 2)
                        )))
                )
            )
        }
        apREDeclarationPatchService.matchScopeService = spyk(MatchScopeService(objectMapper)) {
            every {
                findMatchScopesByTargetAndExternalForApp(MatchScopeTargetTypeEnum.ApREDeclarationPatchData.name, appName, listOf(resourceGroup))
            } returns listOf(
                MatchScopeDataDO(
                    externalId = "alibaba",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 1L
                ),
                MatchScopeDataDO(
                    externalId = appName,
                    externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 2L
                ),
                MatchScopeDataDO(
                    externalId = "alibaba",
                    externalType = MatchScopeExternalTypeEnum.AONE_PRODUCTLINE.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 3L
                ),
                MatchScopeDataDO(
                    externalId = appName,
                    externalType = MatchScopeExternalTypeEnum.APPLICATION.name,
                    creator = "admin",
                    modifier = "admin",
                    targetId = 4L
                )
            )
        }
        val apREDeclarationPatchDataDOListByBalanceSite = apREDeclarationPatchService.queryApREDeclarationPatchDataByAppNameAndResourceGroup(appName, resourceGroup, ApREDeclarationPatchType.BALANCE_SITE.name)
        assertEquals(1, apREDeclarationPatchDataDOListByBalanceSite.size)
        assertEquals(1, apREDeclarationPatchDataDOListByBalanceSite[0].matchScopeDataDOs!!.size)
        assertEquals(2L, apREDeclarationPatchDataDOListByBalanceSite[0].id)
        assertEquals(appName, apREDeclarationPatchDataDOListByBalanceSite[0].matchScopeDataDOs!![0].externalId)
        assertEquals(2, apREDeclarationPatchDataDOListByBalanceSite[0].declarationPatch.patchItems.size)
        assertEquals(mutableListOf(
            PatchItem(site = "na610", weight = 1),
            PatchItem(site = "na620", weight = 2)
        ), apREDeclarationPatchDataDOListByBalanceSite[0].declarationPatch.patchItems)

        val apREDeclarationPatchDataDOListByBalanceCluster = apREDeclarationPatchService.queryApREDeclarationPatchDataByAppNameAndResourceGroup(appName, resourceGroup, ApREDeclarationPatchType.BALANCE_CLUSTER.name)
        assertEquals(1, apREDeclarationPatchDataDOListByBalanceSite.size)
        assertEquals(1, apREDeclarationPatchDataDOListByBalanceCluster[0].matchScopeDataDOs!!.size)
        assertEquals(4L, apREDeclarationPatchDataDOListByBalanceCluster[0].id)
        assertEquals(appName, apREDeclarationPatchDataDOListByBalanceCluster[0].matchScopeDataDOs!![0].externalId)
        assertEquals(2, apREDeclarationPatchDataDOListByBalanceCluster[0].declarationPatch.patchItems.size)
        assertEquals(mutableListOf(
            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "m"))), weight = 1),
            PatchItem(clusterSelector = ClusterSelector( labels = listOf(ClusterLabel(name = "feature/type", value = "x86"))), weight = 2)
        ), apREDeclarationPatchDataDOListByBalanceCluster[0].declarationPatch.patchItems)
    }

    @Test
    fun test_DeclarationPatch_validate() {
        val patchItem1 = PatchItem(site = "site1", weight = 1)
        val patchItem2 =
            PatchItem(clusterSelector = ClusterSelector(labels = listOf(ClusterLabel("key1", "value1"))), weight = 2)
        val declarationPatch = DeclarationPatch(listOf(patchItem1, patchItem2))

        // 校验通过，不抛出异常
        declarationPatch.validate()

        // 测试校验不通过情况

        // 重复集群
        val patchItem3 = PatchItem(site = "site2", weight = 3)
        val patchItem4 = PatchItem(clusterSelector = ClusterSelector(clusterIds = listOf("cluster1")), weight = 4)
        val declarationPatch2 = DeclarationPatch(listOf(patchItem3, patchItem4, patchItem4))
        assertThrows<IllegalArgumentException> {
            declarationPatch2.validate()
        }

        // 重复特性
        val declarationPatch3 = DeclarationPatch(listOf(patchItem3, patchItem2, patchItem2))
        assertThrows<IllegalArgumentException> {
            declarationPatch3.validate()
        }

        // 重复站点
        val declarationPatch4 = DeclarationPatch(listOf(patchItem1, patchItem1))
        assertThrows<IllegalArgumentException> {
            declarationPatch4.validate()
        }
    }

    @Test
    fun test_patchItem_validate() {
        val patchItem1 = PatchItem(site = "site1", weight = 1)
        // 校验通过，不抛出异常
        patchItem1.validate()
        // 测试校验不通过情况
        val patchItem2 = PatchItem(site = "site2", clusterSelector = ClusterSelector(), weight = 2)
        assertThrows<IllegalArgumentException> {
            patchItem2.validate()
        }
        // 权重小于0
        val patchItem3 = PatchItem(site = "site1", weight = -1)
        assertThrows<IllegalArgumentException> {
            patchItem3.validate()
        }
    }

    @Test
    fun test_clusterSelector_identity() {
        val clusterSelector1 =
            ClusterSelector(selectType = ClusterSelectorType.CLUSTER, clusterIds = listOf("cluster1"))
        val clusterSelector2 = ClusterSelector(
            selectType = ClusterSelectorType.CLUSTER_LABEL,
            labels = listOf(ClusterLabel("key1", "value1"))
        )
        assertEquals("CLUSTER${'$'}cluster1", clusterSelector1.identity())
        assertEquals("CLUSTER_LABEL${'$'}key1=value1", clusterSelector2.identity())
    }
}