package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.common.ApRELabelService
import com.alibaba.koastline.multiclusters.apre.ApREService
import com.alibaba.koastline.multiclusters.apre.model.*
import com.alibaba.koastline.multiclusters.apre.params.ApRELabelTargetTypeEnum
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.ObjectMapperFactory
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.COMMON
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.SERVERLESS
import io.mockk.InternalPlatformDsl
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.junit.Test
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals

class ApREServiceMatchTest {
    private var objectMapper = ObjectMapperFactory.newTolerant()
    private val now = Date(Instant.now().toEpochMilli())

    @Test
    fun testMergeBindingApRE_with_target_null() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "region","az","stage","unit",null,null,null,null,null,null,emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "",",","","",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns null
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(sourceApRE.id, (apRE as ApREDO).id)
    }

    @Test
    fun testMergeBindingApRE_with_target_do_not_match_declaration() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null,null,emptyList())
        val targetApRE = ApREDO(2L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null,null,emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(sourceApRE.id, (apRE as ApREDO).id)
    }

    @Test
    fun testMergeBindingApRE_with_limited_cluster_merge() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels  = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList(),
            limitedClusterIdList = listOf("cluster_id_a01")
        )
        val targetApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList()
        )
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            apREBindingTerm = ApREBindingTerm(allSupported = false, Required(
                    allClustersSupported = false,
                    clusters = listOf("cluster_id_a02")
                )
            )
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(2, (apRE as ApREDO).limitedClusterIdList.size)
        assertEquals("cluster_id_a01", apRE.limitedClusterIdList[0])
        assertEquals("cluster_id_a02", apRE.limitedClusterIdList[1])
    }

    @Test
    fun testMergeBindingApRE_with_filter_serverless() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels  = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute", type = CONSOLE,apRELabelKey="XXXXX-a")
            ),resources = emptyList()
        )
        val targetApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "gpu", type = CONSOLE,apRELabelKey="XXXXX-b")
            ),resources = emptyList()
        )
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            apREBindingTerm = ApREBindingTerm(allSupported = true)
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(2, (apRE as ApREDO).apRELabels!!.size)
    }

    @Test
    fun testMergeBindingApRE_with_limited_cluster_ignore() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels  = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList(),
            limitedClusterIdList = listOf()
        )
        val targetApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList()
        )
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            apREBindingTerm = ApREBindingTerm(allSupported = false, Required(
                allClustersSupported = false,
                clusters = listOf("cluster_id_a02")
            )
            )
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(0, (apRE as ApREDO).limitedClusterIdList.size)
    }

    @Test
    fun testMergeBindingApRE_with_limited_cluster_distinct() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels  = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList(),
            limitedClusterIdList = listOf("cluster_id_a01","cluster_id_a02")
        )
        val targetApRE = ApREDO(id = 1L, runtimeEnvKey = runtimeEnvKey,name = null, creator = "creator", managedClusterKey = "MMMMMMM",
            region = "cn-zhangjiakou", az = "na610", stage = "PRE_PUBLISH", unit = "center", status = null, metaData = null, gmtCreate = null, gmtModified = null, isDeleted = null, apRELabels = listOf(
                ApRELabelDO(name = "apre/aliaba/feature", value = "compute")
            ),resources = emptyList()
        )
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            apREBindingTerm = ApREBindingTerm(allSupported = false, Required(
                    allClustersSupported = false,
                    clusters = listOf("cluster_id_a02")
                )
            )
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() }
        assertEquals(2, (apRE as ApREDO).limitedClusterIdList.size)
        assertEquals("cluster_id_a01", apRE.limitedClusterIdList[0])
        assertEquals("cluster_id_a02", apRE.limitedClusterIdList[1])
    }

    @Test
    fun testMergeBindingApRE_with_target_match_declaration_label_prefix() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, null,emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证重叠场景
                ApRELabelDO(2L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(4L,"bbb","runtime-2","common","runtime-2","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(5L,"bbb","runtime-3","common","runtime-3","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS),
                // 验证与场景
                ApRELabelDO(3L,runtimeEnvKey,"alibaba/feature","serverless/tpp2",null,null,null,"ccc", mutableListOf(
                    ApREFeatureSpecDO(7L,"ccc","runtime-tpp2-2","common","runtime-tpp2-2","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center", listOf(
            MatchApRELabel("alibaba/feature","serverless")
        ))
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        assertEquals(2, apRE.apRELabels!!.size)
        assertEquals("serverless/tpp", apRE.apRELabels!![0].value)
        assertEquals("serverless/tpp2", apRE.apRELabels!![1].value)
    }

    @Test
    fun testMergeBindingApRE_with_overlap_single() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证单方存在场景
                ApRELabelDO(1L,runtimeEnvKey,"alibaba/feature","serverless/compute",null,null,null,"aaa", mutableListOf(
                    ApREFeatureSpecDO(1L,"aaa","2C4G","common","2-4","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(2L,"aaa","4C8G","common","4-8","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, COMMON)
            ),emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
            ),emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        // 验证运行时环境合并属性
        assertEquals(1L, apRE.id)
        assertEquals("cn-zhangjiakou", apRE.region)
        assertEquals("na610", apRE.az)
        assertEquals("PRE_PUBLISH", apRE.stage)
        assertEquals("center", apRE.unit)
        // 验证Label合并属性
        assertEquals(1, apRE.apRELabels!!.size)
        assertEquals("serverless/compute", apRE.apRELabels!![0].value)

        // 验证Spec合并属性
        assertEquals(2, apRE.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("2-4", apRE.apRELabels!![0].apREFeatureSpecs!![0].specCode)
        assertEquals("4-8", apRE.apRELabels!![0].apREFeatureSpecs!![1].specCode)
    }

    @Test
    fun testMergeBindingApRE_with_overlap_repeated() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证重叠场景
                ApRELabelDO(2L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(3L,"bbb","runtime-1","common","runtime-1","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(4L,"bbb","runtime-2","common","runtime-2","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证重叠场景
                ApRELabelDO(2L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(4L,"bbb","runtime-2","common","runtime-2","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(5L,"bbb","runtime-3","common","runtime-3","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        // 验证运行时环境合并属性
        assertEquals(1L, apRE.id)
        assertEquals("cn-zhangjiakou", apRE.region)
        assertEquals("na610", apRE.az)
        assertEquals("PRE_PUBLISH", apRE.stage)
        assertEquals("center", apRE.unit)
        // 验证Label合并属性
        assertEquals(1, apRE.apRELabels!!.size)
        assertEquals("serverless/tpp", apRE.apRELabels!![0].value)

        // 验证Spec合并属性
        assertEquals(2, apRE.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("runtime-1", apRE.apRELabels!![0].apREFeatureSpecs!![0].specCode)
        assertEquals("runtime-2", apRE.apRELabels!![0].apREFeatureSpecs!![1].specCode)
    }

    @Test
    fun testMergeBindingApRE_with_overlap_combine() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证与场景
                ApRELabelDO(3L,runtimeEnvKey,"alibaba/feature","serverless/tpp2",null,null,null,"ccc", mutableListOf(
                    ApREFeatureSpecDO(6L,"ccc","runtime-tpp2-1","common","runtime-tpp2-1","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证与场景
                ApRELabelDO(3L,runtimeEnvKey,"alibaba/feature","serverless/tpp2",null,null,null,"ccc", mutableListOf(
                    ApREFeatureSpecDO(7L,"ccc","runtime-tpp2-2","common","runtime-tpp2-2","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE

            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        // 验证运行时环境合并属性
        assertEquals(1L, apRE.id)
        assertEquals("cn-zhangjiakou", apRE.region)
        assertEquals("na610", apRE.az)
        assertEquals("PRE_PUBLISH", apRE.stage)
        assertEquals("center", apRE.unit)
        // 验证Label合并属性
        assertEquals(1, apRE.apRELabels!!.size)
        assertEquals("serverless/tpp2", apRE.apRELabels!![0].value)

        // 验证Spec合并属性
        assertEquals(1, apRE.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("runtime-tpp2-1", apRE.apRELabels!![0].apREFeatureSpecs!![0].specCode)
    }

    @Test
    fun testMergeBindingApRE_with_overlap_multi_label() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证单方存在场景
                ApRELabelDO(1L,runtimeEnvKey,"alibaba/feature","serverless/compute",null,null,null,"aaa", mutableListOf(
                    ApREFeatureSpecDO(1L,"aaa","2C4G","common","2-4","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(2L,"aaa","4C8G","common","4-8","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS),
                // 验证重叠场景
                ApRELabelDO(2L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(3L,"bbb","runtime-1","common","runtime-1","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(4L,"bbb","runtime-2","common","runtime-2","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS),
                // 验证与场景
                ApRELabelDO(3L,runtimeEnvKey,"alibaba/feature","serverless/tpp2",null,null,null,"ccc", mutableListOf(
                    ApREFeatureSpecDO(6L,"ccc","runtime-tpp2-1","common","runtime-tpp2-1","publish","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                // 验证重叠场景
                ApRELabelDO(2L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(4L,"bbb","runtime-2","common","runtime-2","publish","online","",null,null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(5L,"bbb","runtime-3","common","runtime-3","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS),
                // 验证与场景
                ApRELabelDO(3L,runtimeEnvKey,"alibaba/feature","serverless/tpp2",null,null,null,"ccc", mutableListOf(
                    ApREFeatureSpecDO(7L,"ccc","runtime-tpp2-2","common","runtime-tpp2-2","test","online","",null,null,null,null,"DEFAULT"),
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS)
            ),emptyList())
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center",null)
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE

            every {
                apREFeatureSpecService.mergeApREFeatureSpecs(any(), any())
            }answers {
                callOriginal()
            }
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        // 验证运行时环境合并属性
        assertEquals(1L, apRE.id)
        assertEquals("cn-zhangjiakou", apRE.region)
        assertEquals("na610", apRE.az)
        assertEquals("PRE_PUBLISH", apRE.stage)
        assertEquals("center", apRE.unit)
        // 验证Label合并属性
        assertEquals(3, apRE.apRELabels!!.size)
        assertEquals("serverless/compute", apRE.apRELabels!![0].value)
        assertEquals("serverless/tpp", apRE.apRELabels!![1].value)
        assertEquals("serverless/tpp2", apRE.apRELabels!![2].value)

        // 验证Spec合并属性
        assertEquals(2, apRE.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("2-4", apRE.apRELabels!![0].apREFeatureSpecs!![0].specCode)
        assertEquals("4-8", apRE.apRELabels!![0].apREFeatureSpecs!![1].specCode)

        assertEquals(2, apRE.apRELabels!![1].apREFeatureSpecs!!.size)
        assertEquals("runtime-1", apRE.apRELabels!![1].apREFeatureSpecs!![0].specCode)
        assertEquals("runtime-2", apRE.apRELabels!![1].apREFeatureSpecs!![1].specCode)

        assertEquals(1, apRE.apRELabels!![2].apREFeatureSpecs!!.size)
        assertEquals("runtime-tpp2-1", apRE.apRELabels!![2].apREFeatureSpecs!![0].specCode)
    }

    @Test
    fun testMergeBindingApRE_with_feature_spec_label_match() {
        val runtimeEnvKey = "vwMImMwRy0xFsdo+JbdJKg=="
        val sourceApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "region","az","stage","unit",null,null,null,null,null,null,emptyList())
        val targetApRE = ApREDO(1L, runtimeEnvKey,null,"creator","MMMMMMM",
            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf(
                ApRELabelDO(11L,runtimeEnvKey,"alibaba/feature","serverless/tpp",null,null,null,"bbb", mutableListOf(
                    ApREFeatureSpecDO(11L,"bbb","runtime-1","common","runtime-1","publish","online","","{\"envStackId\":\"123\"}",null,null,null,"DEFAULT"),
                    ApREFeatureSpecDO(112L,"bbb","runtime-2","common","runtime-2","publish","online","","{\"envStackId\":\"456\"}",null,null,null,"DEFAULT")
                ),ApRELabelTargetTypeEnum.APRE, SERVERLESS),
            ),emptyList()
        )
        val targetApREBinding = ApREBindingData(
            runtimeEnvKey,
            null
        )
        val declaration = Declaration(null, "cn-zhangjiakou","na610","PRE_PUBLISH","center", listOf(
            MatchApRELabel(
                name = null, value = null,
                matchApREFeatureSpecs = listOf(MatchApREFeatureSpec(
                        matchFeatureSpecLabels = mapOf(
                            "envStackId" to "123"
                        )))
                )
            )
        )
        val apREService = spyk(ApREService(objectMapper), recordPrivateCalls = true) {
            this.apRELabelService = spyk(ApRELabelService())
            every {
                findCombinedApREBaseDetailByKey(runtimeEnvKey, targetApREBinding.apREBindingTerm)
            } returns targetApRE
        }

        val apRE = InternalPlatformDsl.dynamicCall(
            apREService,
            "mergeBindingApRE",
            arrayOf(sourceApRE, targetApREBinding, declaration, emptyList<ApRELabelType>())
        ) { mockk() } as ApREDO
        assertEquals(1, apRE.apRELabels!![0].apREFeatureSpecs!!.size)
        assertEquals("{\"envStackId\":\"123\"}", apRE.apRELabels!![0].apREFeatureSpecs!![0].labels)
    }
}