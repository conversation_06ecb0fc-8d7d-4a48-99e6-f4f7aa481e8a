package com.alibaba.koastline.multiclusters.appenv.service.apre.test

import com.alibaba.koastline.multiclusters.apre.base.EnvLevelStageMappingService
import io.mockk.spyk
import org.junit.Test
import kotlin.test.assertEquals

class EnvLevelStageMappingServiceTest {
    @Test
    fun testGetStandardEnvLevel() {
        val envLevelStageMappingService = spyk<EnvLevelStageMappingService>()
        assertEquals("staging", envLevelStageMappingService.getStandardEnvLevel("staging"))
        assertEquals("staging", envLevelStageMappingService.getStandardEnvLevel("staging1"))
        assertEquals("testing", envLevelStageMappingService.getStandardEnvLevel("testing"))
        assertEquals("testing", envLevelStageMappingService.getStandardEnvLevel("testing-trunk"))
        assertEquals("production", envLevelStageMappingService.getStandardEnvLevel("production"))
        assertEquals("production", envLevelStageMappingService.getStandardEnvLevel("production1"))
        assertEquals("gray", envLevelStageMappingService.getStandardEnvLevel("gray"))
        assertEquals("gray", envLevelStageMappingService.getStandardEnvLevel("gray1"))
        assertEquals("beta", envLevelStageMappingService.getStandardEnvLevel("beta"))
        assertEquals("beta", envLevelStageMappingService.getStandardEnvLevel("beta1"))
        assertEquals("production-runtime", envLevelStageMappingService.getStandardEnvLevel("production-runtime"))
        assertEquals("staging-runtime", envLevelStageMappingService.getStandardEnvLevel("staging-runtime"))
        assertEquals("SMALLFLOW-runtime", envLevelStageMappingService.getStandardEnvLevel("SMALLFLOW-runtime"))
        assertEquals("testing-runtime", envLevelStageMappingService.getStandardEnvLevel("testing-runtime"))

        assertEquals("staging", envLevelStageMappingService.getStandardEnvLevel("app-test-aone2-intgstaging-1"))
        assertEquals("testing", envLevelStageMappingService.getStandardEnvLevel("app-test-aone2-cr-1"))
        assertEquals("testing", envLevelStageMappingService.getStandardEnvLevel("app-test-aone2-appstack-1"))
    }
}