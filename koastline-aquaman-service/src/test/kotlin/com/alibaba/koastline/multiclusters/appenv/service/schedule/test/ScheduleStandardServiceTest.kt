package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.common.exceptions.ApREDeedResourceGroupBindingNotFoundException
import com.alibaba.koastline.multiclusters.external.SkylineApi.Companion.TESTING_ENV_COMMON_APP_NAME
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService.Companion.DEFAULT_NAME_SPACE
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.exception.ScheduleException
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleWeight
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraintAssemble
import com.alibaba.koastline.multiclusters.schedule.service.schedule.DeclarativeScaleOutScheduleService.Companion.RESOURCE_GROUP_USAGE_TYPE_DAILY
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import io.mockk.every
import io.mockk.justRun
import io.mockk.spyk
import java.util.UUID
import org.junit.Test
import kotlin.test.assertEquals
import org.junit.Rule
import org.junit.rules.ExpectedException
import testutils.BaseTest

class ScheduleStandardServiceTest: BaseTest() {

    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()

    @Test
    fun testDoComputeDeclarationExpectedReplicas_while_expectedReplicas_can_be_average() {
        val rs = ScheduleStandardService().doComputeDeclarationExpectedReplicas(
            listOf(
                ScheduleWeight(uniqueKey = "1", weight = 1),
                ScheduleWeight(uniqueKey = "2", weight = 1)
            ),
            10
        )
        assertEquals(2, rs.size)
        assertEquals(5, rs["1"])
        assertEquals(5, rs["2"])
    }

    @Test
    fun testDoComputeDeclarationExpectedReplicas_while_expectedReplicas_can_not_be_average() {
        val rs = ScheduleStandardService().doComputeDeclarationExpectedReplicas(
            listOf(
                ScheduleWeight(uniqueKey = "1", weight = 1),
                ScheduleWeight(uniqueKey = "2", weight = 1)
            ),
            11
        )
        assertEquals(2, rs.size)
        assertEquals(6, rs["1"])
        assertEquals(5, rs["2"])
    }

    @Test
    fun testDoComputeDeclarationExpectedReplicas_while_expectedReplicas_is_boundary_value_of_1() {
        val rs = ScheduleStandardService().doComputeDeclarationExpectedReplicas(
            listOf(
                ScheduleWeight(uniqueKey = "1", weight = 1),
                ScheduleWeight(uniqueKey = "2", weight = 1)
            ),
            1
        )
        assertEquals(2, rs.size)
        assertEquals(1, rs["1"])
        assertEquals(0, rs["2"])
    }

    @Test
    fun `testGetAssembledApREDeedByDeclarative while don't have declaration and resource group is not testing`() {
        val resourceGroup = "normandy-test-app4_prehost"
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup)
            }returns null
            every {
                skylineApi.getAppGroup(resourceGroup)
            }returns AppGroup(
                name = resourceGroup,
                appName = "normandy-test-app4",
                originalName = "normandy-test-app4",
                usageType = RESOURCE_GROUP_USAGE_TYPE_DAILY
            )
        }
        exceptionRule.expect(ApREDeedResourceGroupBindingNotFoundException::class.java)
        scheduleStandardService.getAssembledApREDeedByDeclarative(
            apREDeedKey = null,
            resourceGroup = resourceGroup,
            serverless = false,
            serverlessRuntimeTemplate = null,
        )
    }

    @Test
    fun `testGetAssembledApREDeedByDeclarative while don't have declaration and resource group is testing`() {
        val resourceGroup = "normandy-test-app4_prehost"
        val apREDeedDO = ApREDeedDO(key = UUID.randomUUID().toString())
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                apREDeedResourceGroupBindingService.getByResourceGroup(resourceGroup)
            }returns null
            every {
                skylineApi.getAppGroup(resourceGroup)
            }returns AppGroup(
                name = resourceGroup,
                appName = "normandy-test-app4",
                originalName = TESTING_ENV_COMMON_APP_NAME,
                usageType = RESOURCE_GROUP_USAGE_TYPE_DAILY
            )
            every {
                apREDeedService.createApREDeedWhileNotExist(any())
            }returns apREDeedDO
            justRun {
                apREDeedResourceGroupBindingService.createIgnoreWhileExist(any(),any(),any())
            }
            every {
                apREDeedService.findApREDeedByKey(any())
            }returns apREDeedDO
            every {
                apreDeedPatchService.fillSiteBalanceApREDeclarationPatchForApREDeed(any())
            }returns apREDeedDO
        }
        //不报异常
        scheduleStandardService.getAssembledApREDeedByDeclarative(
            apREDeedKey = null,
            resourceGroup = resourceGroup,
            serverless = false,
            serverlessRuntimeTemplate = null,
        )
    }

    @Test
    fun `testCheckMultiProtocol while exist cloneset`() {
        val appName = getString()
        val resourceGroup = getString()
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = appName,
                    resourceGroup = resourceGroup
                )
            } returns listOf(
                WorkloadMetadataConstraintAssemble(
                    manufacturePojo(WorkloadMetadataConstraint::class.java).copy(
                        namespace = DEFAULT_NAME_SPACE
                    ),
                    num = 1
                )
            )
        }
        exceptionRule.expect(ScheduleException::class.java)
        exceptionRule.expectMessage("当前分组:${resourceGroup}存在云原生资源，无法同时扩容ASI资源")
        scheduleStandardService.checkMultiProtocol(
            resourceObjectProtocol = ResourceObjectProtocolEnum.StatefulSet.name,
            appName = appName,
            resourceGroup = resourceGroup
        )
    }

    @Test
    fun `testCheckMultiProtocol while exist sts`() {
        val appName = getString()
        val resourceGroup = getString()
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = appName,
                    resourceGroup = resourceGroup
                )
            } returns listOf(
                WorkloadMetadataConstraintAssemble(
                    manufacturePojo(WorkloadMetadataConstraint::class.java).copy(
                        namespace = appName
                    ),
                    num = 1
                )
            )
        }
        exceptionRule.expect(ScheduleException::class.java)
        exceptionRule.expectMessage("当前分组:${resourceGroup}存在ASI资源，无法同时扩容云原生资源")
        scheduleStandardService.checkMultiProtocol(
            resourceObjectProtocol = ResourceObjectProtocolEnum.CloneSet.name,
            appName = appName,
            resourceGroup = resourceGroup
        )
    }
}