package com.alibaba.koastline.multiclusters.appenv.service.resourceobject.test

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ASI_MIX_CLUSTER_NAME_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.config.CommonProperties.Companion.ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectPostCheckException
import com.alibaba.koastline.multiclusters.common.exceptions.ResourceObjectPreCheckException
import com.alibaba.koastline.multiclusters.common.utils.JsonUtils
import com.alibaba.koastline.multiclusters.common.utils.YamlUtils
import com.alibaba.koastline.multiclusters.resourceobj.base.BaseSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.StatefulSetSpecService
import com.alibaba.koastline.multiclusters.resourceobj.base.StatefulSetSpecService.Companion.STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME
import com.alibaba.koastline.multiclusters.resourceobj.base.WorkloadSpecContext
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFormatEnum
import com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectProtocolEnum
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.google.gson.Gson
import io.kubernetes.client.openapi.models.V1StatefulSet
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.spyk
import org.junit.Assert
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertTrue


class StatefulSetSpecServiceTest {

    val gson = Gson()

    @Test
    fun testUnMarshalWithGson() {
        val stsStr = """
            {
            	"apiVersion": "apps/v1",
            	"kind": "StatefulSet",
            	"spec": {
            		"template": {
            			"spec": {
            				"containers": [{
            					"name": "main",
            					"resources": {
            						"requests": {
            							"cpu": 60
            						}
            					}
            				}]
            			}
            		}
            	}
            }
        """.trimIndent()
        val sts = JsonUtils.gsonReadValue(stsStr, V1StatefulSet::class.java)
        Assert.assertEquals(
            java.math.BigDecimal(60),
            sts.spec!!.template.spec!!.containers[0].resources!!.requests!!.get("cpu")!!.number
        )
    }

    @Test(expected = com.fasterxml.jackson.databind.exc.MismatchedInputException::class)
    fun testUnMarshalWithJackson() {
        val stsStr = """
            {
            	"apiVersion": "apps/v1",
            	"kind": "StatefulSet",
            	"spec": {
            		"template": {
            			"spec": {
            				"containers": [{
            					"name": "main",
            					"resources": {
            						"requests": {
            							"cpu": 60
            						}
            					}
            				}]
            			}
            		}
            	}
            }
        """.trimIndent()
        JsonUtils.readValue(stsStr, V1StatefulSet::class.java)
    }

    @Test
    fun testGetBaseSpec() {
        val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every {
                    commonProperties.contains(ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns false
            }
            every {
                cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent()
        }
        val specObj = statefulSetSpecService.getBaseSpec(WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4longlonglonglonglonglonglong",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na610",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "customed-namespace"
            ),
            envStackId = envStackId,
            ResourceObjectProtocolEnum.StatefulSet
        ))
        val name = (specObj!!["metadata"] as MutableMap<String, String>).run {
            this.remove("name")
        }
        assertEquals("""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: '{"spec":{"template":{"metadata":{"labels":{"sigma.ali/inject-staragent-sidecar":"true"}},"spec":{"containers":[{"image":"reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily","name":"main","volumeMounts":[{"mountPath":"/home/<USER>/diamond","name":"324a215d6bec6bffc99895f0d8fe5580"}]}],"volumes":[{"emptyDir":{},"name":"324a215d6bec6bffc99895f0d8fe5580"}]}}}}'
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4longlonglonglonglonglonglong
    sigma.alibaba-inc.com/app-stage: PUBLISH
    statefulset.sigma.ali/mode: sigma
    normandy.alibaba-inc.com/stack-id: 324a215d6bec6bffc99895f0d8fe5580
    sigma.ali/upstream-component: normandy
  namespace: customed-namespace
spec:
  replicas: 0
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4longlonglonglonglonglonglong
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4longlonglonglonglonglonglong{{.IpAddress}}.center.na610
        alibabacloud.com/ip-stack: ipv4
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4longlonglonglonglonglonglong
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/container-model: dockervm
        sigma.ali/upstream-component: normandy
        pod.beta1.sigma.ali/naming-register-state: working_online
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
              - key: sigma.ali/is-ecs
                operator: In
                values:
                - 'true'
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      dnsPolicy: default
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent(), YamlUtils.dump(specObj).trimIndent())
        assertTrue(name!!.startsWith("normandy-test-app4"))
        assertTrue(name!!.endsWith("-n3"))
        assertEquals(name!!.length, 45)

    }

    @Test
    fun `testGetBaseSpec -- is asi physical cluster`() {
        val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every {
                    commonProperties.contains(ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns true
                every {
                    commonProperties.contains(ASI_MIX_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns false
                every {
                    commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, any())
                } returns false
            }
            every {
                cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
      annotations: {sigma.ali/app-storage-size: 2000Gi}
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
        resources:
            limits:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
            requests:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent()
        }
        val context = WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na610",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "normandy-test-app4"
            ),
            envStackId = envStackId,
            resourceObjectProtocolEnum = ResourceObjectProtocolEnum.StatefulSet
        )
        val specObj = statefulSetSpecService.postModifyBaseSpec(
            resourceObjectSpec = statefulSetSpecService.getBaseSpec(context),
            context = context
        )
        val name = (specObj!!["metadata"] as MutableMap<String, String>).run {
            this.remove("name")
        }
        assertEquals("""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: '{"spec":{"template":{"metadata":{"annotations":{"sigma.ali/app-storage-size":"2000Gi"},"labels":{"sigma.ali/inject-staragent-sidecar":"true"}},"spec":{"containers":[{"image":"reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily","name":"main","resources":{"limits":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"},"requests":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"}},"volumeMounts":[{"mountPath":"/home/<USER>/diamond","name":"324a215d6bec6bffc99895f0d8fe5580"}]}],"volumes":[{"emptyDir":{},"name":"324a215d6bec6bffc99895f0d8fe5580"}]}}}}'
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    statefulset.sigma.ali/mode: sigma
    normandy.alibaba-inc.com/stack-id: 324a215d6bec6bffc99895f0d8fe5580
    sigma.ali/upstream-component: normandy
  namespace: normandy-test-app4
spec:
  replicas: 0
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: 2000Gi
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
        alibabacloud.com/ip-stack: ipv4
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/container-model: dockervm
        sigma.ali/upstream-component: normandy
        pod.beta1.sigma.ali/naming-register-state: working_online
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        resources:
          limits:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
          requests:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      dnsPolicy: default
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent(), YamlUtils.dump(specObj).trimIndent())
        assertTrue(name!!.startsWith("normandy-test-app4-"))
        assertTrue(name!!.endsWith("-n3"))
    }

    @Test
    fun `testGetBaseSpec -- lingjun`() {
        val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every {
                    commonProperties.contains(ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns false
                every {
                    commonProperties.contains(ASI_MIX_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns false
                every {
                    commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, "na132")
                } returns true
            }
            every {
                cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
      annotations: {sigma.ali/app-storage-size: 2000Gi}
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
        resources:
            limits:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
            requests:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent()
        }
        val context = WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na132",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "normandy-test-app4"
            ),
            envStackId = envStackId,
            resourceObjectProtocolEnum = ResourceObjectProtocolEnum.StatefulSet
        )
        val specObj = statefulSetSpecService.postModifyBaseSpec(
            resourceObjectSpec = statefulSetSpecService.getBaseSpec(context),
            context = context
        )
        val name = (specObj!!["metadata"] as MutableMap<String, String>).run {
            this.remove("name")
        }
        assertEquals("""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: '{"spec":{"template":{"metadata":{"annotations":{"sigma.ali/app-storage-size":"2000Gi"},"labels":{"sigma.ali/inject-staragent-sidecar":"true"}},"spec":{"containers":[{"image":"reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily","name":"main","resources":{"limits":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"},"requests":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"}},"volumeMounts":[{"mountPath":"/home/<USER>/diamond","name":"324a215d6bec6bffc99895f0d8fe5580"}]}],"volumes":[{"emptyDir":{},"name":"324a215d6bec6bffc99895f0d8fe5580"}]}}}}'
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/site: na132
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    statefulset.sigma.ali/mode: sigma
    normandy.alibaba-inc.com/stack-id: 324a215d6bec6bffc99895f0d8fe5580
    sigma.ali/upstream-component: normandy
  namespace: normandy-test-app4
spec:
  replicas: 0
  selector:
    matchLabels:
      sigma.ali/site: na132
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: 2000Gi
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na132
        alibabacloud.com/ip-stack: ipv4
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4
        sigma.ali/site: na132
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/container-model: dockervm
        sigma.ali/upstream-component: normandy
        pod.beta1.sigma.ali/naming-register-state: working_online
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        resources:
          limits:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
          requests:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      dnsPolicy: default
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent(), YamlUtils.dump(specObj).trimIndent())
        assertTrue(name!!.startsWith("normandy-test-app4-"))
        assertTrue(name!!.endsWith("-n3"))
    }

    @Test
    fun `testGetBaseSpec -- is asi physical cluster and mix cluster`() {
        val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every {
                    commonProperties.contains(ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns true
                every {
                    commonProperties.contains(ASI_MIX_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
                } returns true
                every {
                    commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, any())
                } returns false

            }
            every {
                cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
      annotations: {sigma.ali/app-storage-size: 2000Gi}
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
        resources:
            limits:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
            requests:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent()
        }
        val context = WorkloadSpecContext(
            WorkloadMetadataConstraint(
                appName = "normandy-test-app4",
                resourceGroup = "normandy-test-app4_prehost",
                unit = "CENTER_UNIT.center",
                stage = "PUBLISH",
                site = "na610",
                clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                subgroup = "default",
                namespace = "normandy-test-app4"
            ),
            envStackId = envStackId,
            resourceObjectProtocolEnum = ResourceObjectProtocolEnum.StatefulSet
        )
        val specObj = statefulSetSpecService.postModifyBaseSpec(
            resourceObjectSpec = statefulSetSpecService.getBaseSpec(context),
            context = context
        )
        val name = (specObj!!["metadata"] as MutableMap<String, String>).run {
            this.remove("name")
        }
        assertEquals("""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: '{"spec":{"template":{"metadata":{"annotations":{"sigma.ali/app-storage-size":"2000Gi"},"labels":{"sigma.ali/inject-staragent-sidecar":"true"}},"spec":{"containers":[{"image":"reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily","name":"main","resources":{"limits":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"},"requests":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"}},"volumeMounts":[{"mountPath":"/home/<USER>/diamond","name":"324a215d6bec6bffc99895f0d8fe5580"}]}],"volumes":[{"emptyDir":{},"name":"324a215d6bec6bffc99895f0d8fe5580"}]}}}}'
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    statefulset.sigma.ali/mode: sigma
    normandy.alibaba-inc.com/stack-id: 324a215d6bec6bffc99895f0d8fe5580
    sigma.ali/upstream-component: normandy
  namespace: normandy-test-app4
spec:
  replicas: 0
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: 2000Gi
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
        alibabacloud.com/ip-stack: ipv4
        alibabacloud.com/skip-kubelet-admission: '["cpu","memory","alibabacloud.com/acu"]'
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/container-model: dockervm
        sigma.ali/upstream-component: normandy
        pod.beta1.sigma.ali/naming-register-state: working_online
        alibabacloud.com/timesharing-promotion-type: trade
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        resources:
          limits:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
          requests:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      dnsPolicy: default
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent(), YamlUtils.dump(specObj).trimIndent())
        assertTrue(name!!.startsWith("normandy-test-app4-"))
        assertTrue(name!!.endsWith("-n3"))
    }

  @Test
  fun `testGetBaseSpec -- gpu specific action`() {
    val envStackId = "324a215d6bec6bffc99895f0d8fe5580"
    val statefulSetSpecService = spyk(StatefulSetSpecService()) {
      baseSpecService = spyk(BaseSpecService()) {
        every {
          commonProperties.contains(ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
        } returns true
        every {
          commonProperties.contains(ASI_MIX_CLUSTER_NAME_LIST_CONFIG, "324a215d6bec6bffc99895f0d8fe5580")
        } returns true
          every {
              commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, any())
          } returns true
      }
      every {
        cloudCmdbApi.getEnvBaselineSpec(envStackId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
      } returns """apiVersion: apps/v1
kind: StatefulSet
spec:
  template:
    metadata:
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
      annotations: {sigma.ali/app-storage-size: 2000Gi}
    spec:
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
        resources:
            limits:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
            requests:
              cpu: '8'
              ephemeral-storage: '214748364800'
              memory: '17179869184'
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent()
    }
    val context = WorkloadSpecContext(
      WorkloadMetadataConstraint(
        appName = "normandy-test-app4",
        resourceGroup = "normandy-test-app4_prehost",
        unit = "CENTER_UNIT.center",
        stage = "PUBLISH",
        site = "na610",
        clusterId = "324a215d6bec6bffc99895f0d8fe5580",
        subgroup = "default",
        namespace = "normandy-test-app4"
      ),
      envStackId = envStackId,
      resourceObjectProtocolEnum = ResourceObjectProtocolEnum.StatefulSet
    )
    val specObj = statefulSetSpecService.postModifyBaseSpec(
      resourceObjectSpec = statefulSetSpecService.getBaseSpec(context),
      context = context
    )
    val name = (specObj!!["metadata"] as MutableMap<String, String>).run {
      this.remove("name")
    }
    assertEquals("""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: '{"spec":{"template":{"metadata":{"annotations":{"sigma.ali/app-storage-size":"2000Gi"},"labels":{"sigma.ali/inject-staragent-sidecar":"true"}},"spec":{"containers":[{"image":"reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily","name":"main","resources":{"limits":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"},"requests":{"cpu":"8","ephemeral-storage":"214748364800","memory":"17179869184"}},"volumeMounts":[{"mountPath":"/home/<USER>/diamond","name":"324a215d6bec6bffc99895f0d8fe5580"}]}],"volumes":[{"emptyDir":{},"name":"324a215d6bec6bffc99895f0d8fe5580"}]}}}}'
    sigma.ali/disable-cascading-deletion: 'true'
  labels:
    sigma.ali/site: na610
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
    sigma.ali/subgroup: default
    sigma.ali/instance-group: normandy-test-app4_prehost
    sigma.ali/app-name: normandy-test-app4
    sigma.alibaba-inc.com/app-stage: PUBLISH
    statefulset.sigma.ali/mode: sigma
    normandy.alibaba-inc.com/stack-id: 324a215d6bec6bffc99895f0d8fe5580
    sigma.ali/upstream-component: normandy
  namespace: normandy-test-app4
spec:
  replicas: 0
  selector:
    matchLabels:
      sigma.ali/site: na610
      sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      sigma.ali/subgroup: default
      sigma.ali/instance-group: normandy-test-app4_prehost
      sigma.ali/app-name: normandy-test-app4
      sigma.alibaba-inc.com/app-stage: PUBLISH
  template:
    metadata:
      annotations:
        sigma.ali/app-storage-size: 2000Gi
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        sigma.ali/use-unified-pv: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800","ImagePullTimeoutSeconds":"600","PreStopHookTimeoutSeconds":"600"}}}'
        pod.beta1.sigma.ali/hostname-template: normandy-test-app4{{.IpAddress}}.center.na610
        alibabacloud.com/ip-stack: ipv4
        alibabacloud.com/skip-kubelet-admission: '["cpu","memory","alibabacloud.com/acu"]'
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
        sigma.ali/app-name: normandy-test-app4
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
        sigma.ali/subgroup: default
        sigma.ali/instance-group: normandy-test-app4_prehost
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.ali/container-model: dockervm
        sigma.ali/upstream-component: normandy
        pod.beta1.sigma.ali/naming-register-state: working_online
        alibabacloud.com/timesharing-promotion-type: trade
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - image: reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4_testing:20221205170310828655_daily
        name: main
        resources:
          limits:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
          requests:
            cpu: '8'
            ephemeral-storage: '214748364800'
            memory: '17179869184'
        volumeMounts:
        - mountPath: /home/<USER>/diamond
          name: 324a215d6bec6bffc99895f0d8fe5580
      dnsPolicy: default
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
      volumes:
      - emptyDir: {}
        name: 324a215d6bec6bffc99895f0d8fe5580""".trimIndent(), YamlUtils.dump(specObj).trimIndent())
    assertTrue(name!!.startsWith("normandy-test-app4-"))
    assertTrue(name!!.endsWith("-n3"))
  }

    @Test
    fun `testGetBaseSpec -- timeStamp`() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every { baseSpecService.patchAffinityAndTolerations(any()) } just Runs
                every { baseSpecService.modifyForMixDeployCluster(any(), any()) } just Runs
                every { baseSpecService.modifyForPhysicalMachineCluster(any(), any()) } just Runs
            }
        }
        val ret = statefulSetSpecService.postModifyBaseSpec(
            YamlUtils.load(
                """
apiVersion: apps/v1
kind: StatefulSet
metadata:
  creationTimestamp: '2025-02-27T07:41:14.000000Z'
spec:
  template:
    spec:
        """.trimIndent()
            ), context = WorkloadSpecContext(
                WorkloadMetadataConstraint(
                    appName = "normandy-test-app4",
                    resourceGroup = "normandy-test-app4_prehost",
                    unit = "CENTER_UNIT.center",
                    stage = "PUBLISH",
                    site = "na610",
                    clusterId = "324a215d6bec6bffc99895f0d8fe5580",
                    subgroup = "default",
                    namespace = "normandy-test-app4"
                ),
                envStackId = "envStackId",
                resourceObjectProtocolEnum = ResourceObjectProtocolEnum.StatefulSet
            )
        )
        assertEquals("2025-02-27T07:41:14.000000Z", (ret.get("metadata") as Map<String, Any>).get("creationTimestamp"))
    }

    @Test
    fun testGetDeploySpec() {
        val envStackPkId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
              every {
                commonProperties.contains(
                  CommonProperties.Companion.ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG,
                  "324a215d6bec6bffc99895f0d8fe5580"
                )
              } returns false
                every {
                    commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, any())
                } returns false
            }
            every {
                cloudCmdbApi.getEnvBaselineSpecByStackPKId(envStackPkId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations: {sigma.ali/upgrade-merge-labels: 'tags.sunfire.com/app-deploy-version', sigma.ali/upgrade-merge-annotations: 'sigma.ali/enable-apprules-injection'}
  labels: {statefulset.sigma.ali/mode: sigma, sigma.ali/app-name: normandy-test-app4, cloneset.beta1.sigma.ali/enable-pod-idx-gen: 'true'}
spec:
  template:
    metadata:
      annotations: {pod.beta1.sigma.ali/alarming-off-upgrade: 'true', pod.beta1.sigma.ali/naming-register-state: working_online,
        sigma.ali/use-unified-pv: 'true', sigma.ali/enable-apprules-injection: 'true',
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}'}
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
    spec:
      containers:
      - env:
        - {name: ali_aone_timestamp, value: '1684116732557'}
        - {name: ali_start_app, value: 'no'}
        - {name: ali_run_mode, value: common_vm}
        - {name: envSign, value: production}
        - {name: trafficRouteLabel, value: production}
        image: hub.docker.alibaba-inc.com/aone/normandy-test-app4:20230515142257182101_publish
        name: main
            """
        }
        val specObj = statefulSetSpecService.getDeploySpec(envStackPkId, """
            {
              "apiVersion": "apps/v1",
              "kind": "StatefulSet",
              "metadata": {
                "name": "normandy-test-app4-c0b7a852-8f72-493a-ba0--n3",
                "namespace": "normandy-test-app4",
                "labels": {
                  "normandy.alibabacloud.com/pod-template-hash": "4fb0c0212dbaa1266cc7d993dceace4f"
                }
              },
              "spec": {
                "podManagementPolicy": "OrderedReady",
                "replicas": 1,
                "revisionHistoryLimit": 10,
                "selector": {
                  "matchLabels": {
                    "sigma.ali/app-name": "normandy-test-app4",
                    "sigma.ali/instance-group": "normandy-test-app4_testing_3789405_testhost",
                    "sigma.ali/site": "na131",
                    "sigma.ali/subgroup": "default",
                    "sigma.alibaba-inc.com/app-stage": "DAILY",
                    "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.ant_zb"
                  }
                },
                "serviceName": "",
                "template": {
                  "spec": {
                    "containers": [
                      {
                        "image": "reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20230524103836251831_daily",
                        "name": "main",
                        "resources": {
                          "limits": {
                            "cpu": "8",
                            "ephemeral-storage": "42949672960",
                            "memory": "17179869184"
                          },
                          "requests": {
                            "cpu": "8",
                            "ephemeral-storage": "42949672960",
                            "memory": "17179869184"
                          }
                        }
                      }
                    ]
                  }
                },
                "updateStrategy": {
                  "rollingUpdate": {
                    "partition": 100000000
                  },
                  "type": "RollingUpdate"
                }
              }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON, WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        ))
        assertEquals("""apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/upgrade-merge-labels: tags.sunfire.com/app-deploy-version
    sigma.ali/upgrade-merge-annotations: sigma.ali/enable-apprules-injection
  labels:
    normandy.alibabacloud.com/pod-template-hash: 4fb0c0212dbaa1266cc7d993dceace4f
    cloneset.beta1.sigma.ali/enable-pod-idx-gen: 'true'
  name: normandy-test-app4-c0b7a852-8f72-493a-ba0--n3
  namespace: normandy-test-app4
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}'
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - env:
        - name: ali_aone_timestamp
          value: '1684116732557'
        - name: ali_start_app
          value: 'no'
        - name: ali_run_mode
          value: common_vm
        - name: envSign
          value: production
        - name: trafficRouteLabel
          value: production
        image: hub.docker.alibaba-inc.com/aone/normandy-test-app4:20230515142257182101_publish
        name: main
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
        """.trimIndent(), YamlUtils.dump(specObj).trimIndent())
    }

    @Test
    fun testPreCheckWorkload() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        val exception = assertFailsWith<ResourceObjectPreCheckException>(
            block = {
                statefulSetSpecService.preCheck(
                    """
            {
                "apiVersion": "apps/v1",
                "kind": "StatefulSet",
                "spec": {
                    "volumeClaimTemplates": "AQUAMAN_BLOKER"
                }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON
                )
            }
        )
        assertEquals("""preCheck spec error, msg:java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 5 column 34 path ${'$'}.spec.volumeClaimTemplates, resourceObjectSpecStr:{
    "apiVersion": "apps/v1",
    "kind": "StatefulSet",
    "spec": {
        "volumeClaimTemplates": "AQUAMAN_BLOKER"
    }
}""".trimIndent(), exception.message)

    }

    @Test
    fun testPostCheckWorkload() {
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = BaseSpecService()
        }
        val exception = assertFailsWith<ResourceObjectPostCheckException>(
            block = {
                statefulSetSpecService.postCheck(
                    """
            {
              "apiVersion": "apps/v1",
              "kind": "StatefulSet",
              "spec": {
                    "volumeClaimTemplates": "AQUAMAN_BLOKER"
              }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON
                )
            }
        )
        assertEquals("""postCheckSpec spec error, msg:java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 5 column 34 path ${'$'}.spec.volumeClaimTemplates, resourceObjectSpecStr:{
  "apiVersion": "apps/v1",
  "kind": "StatefulSet",
  "spec": {
        "volumeClaimTemplates": "AQUAMAN_BLOKER"
  }
}""".trimIndent(), exception.message)

    }

    @Test
    fun `testGetDeploySpec~workload labels`() {
        val envStackPkId = "324a215d6bec6bffc99895f0d8fe5580"
        val statefulSetSpecService = spyk(StatefulSetSpecService()) {
            baseSpecService = spyk(BaseSpecService()) {
                every {
                    commonProperties.contains(
                        CommonProperties.Companion.ASI_PHYSICAL_CLUSTER_NAME_LIST_CONFIG,
                        "324a215d6bec6bffc99895f0d8fe5580"
                    )
                } returns false
                every {
                    commonProperties.contains(ASI_LOCAL_INLINE_STORAGE_SITE_LIST_CONFIG, any())
                } returns false
            }
            every {
                cloudCmdbApi.getEnvBaselineSpecByStackPKId(envStackPkId, STATEFUL_SET_SPEC_YAML_ATTRIBUTE_NAME)
            } returns """apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations: {sigma.ali/upgrade-merge-labels: 'tags.sunfire.com/app-deploy-version', sigma.ali/upgrade-merge-annotations: 'sigma.ali/enable-apprules-injection',sigma.ali/upgrade-inherited-labels: 'tags.aone.alibaba-inc.com/deploy-stack-id,tags.aone.alibaba-inc.com/env-level,tags.sunfire.com/app-deploy-version'}
  labels: {statefulset.sigma.ali/mode: sigma, sigma.ali/app-name: normandy-test-app4, cloneset.beta1.sigma.ali/enable-pod-idx-gen: 'true',tags.aone.alibaba-inc.com/deploy-stack-id: '7b50ca97-ef9a-4b54-a51c-0b2e0260d72c',tags.aone.alibaba-inc.com/env-level: 'production',tags.sunfire.com/app-deploy-version: 'V191847870'}
spec:
  template:
    metadata:
      annotations: {pod.beta1.sigma.ali/alarming-off-upgrade: 'true', pod.beta1.sigma.ali/naming-register-state: working_online,
        sigma.ali/use-unified-pv: 'true', sigma.ali/enable-apprules-injection: 'true',
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}'}
      labels: {sigma.ali/inject-staragent-sidecar: 'true', sigma.ali/app-name: normandy-test-app4}
    spec:
      containers:
      - env:
        - {name: ali_aone_timestamp, value: '1684116732557'}
        - {name: ali_start_app, value: 'no'}
        - {name: ali_run_mode, value: common_vm}
        - {name: envSign, value: production}
        - {name: trafficRouteLabel, value: production}
        image: hub.docker.alibaba-inc.com/aone/normandy-test-app4:20230515142257182101_publish
        name: main
            """
        }
        val specObj = statefulSetSpecService.getDeploySpec(envStackPkId, """
            {
              "apiVersion": "apps/v1",
              "kind": "StatefulSet",
              "metadata": {
                "name": "normandy-test-app4-c0b7a852-8f72-493a-ba0--n3",
                "namespace": "normandy-test-app4",
                "labels": {
                  "normandy.alibabacloud.com/pod-template-hash": "4fb0c0212dbaa1266cc7d993dceace4f"
                }
              },
              "spec": {
                "podManagementPolicy": "OrderedReady",
                "replicas": 1,
                "revisionHistoryLimit": 10,
                "selector": {
                  "matchLabels": {
                    "sigma.ali/app-name": "normandy-test-app4",
                    "sigma.ali/instance-group": "normandy-test-app4_testing_3789405_testhost",
                    "sigma.ali/site": "na131",
                    "sigma.ali/subgroup": "default",
                    "sigma.alibaba-inc.com/app-stage": "DAILY",
                    "sigma.alibaba-inc.com/app-unit": "CENTER_UNIT.ant_zb"
                  }
                },
                "serviceName": "",
                "template": {
                  "spec": {
                    "containers": [
                      {
                        "image": "reg-zhangbei.docker.alibaba-inc.com/aone/normandy-test-app4:20230524103836251831_daily",
                        "name": "main",
                        "resources": {
                          "limits": {
                            "cpu": "8",
                            "ephemeral-storage": "42949672960",
                            "memory": "17179869184"
                          },
                          "requests": {
                            "cpu": "8",
                            "ephemeral-storage": "42949672960",
                            "memory": "17179869184"
                          }
                        }
                      }
                    ]
                  }
                },
                "updateStrategy": {
                  "rollingUpdate": {
                    "partition": 100000000
                  },
                  "type": "RollingUpdate"
                }
              }
            }
        """.trimIndent(), ResourceObjectFormatEnum.JSON, WorkloadMetadataConstraint(
            appName = "normandy-test-app4",
            resourceGroup = "normandy-test-app4_prehost",
            unit = "CENTER_UNIT.center",
            stage = "PUBLISH",
            site = "na610",
            clusterId = "324a215d6bec6bffc99895f0d8fe5580",
            subgroup = "default",
            namespace = "normandy-test-app4"
        ))
        assertEquals("""apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    sigma.ali/upgrade-merge-labels: tags.sunfire.com/app-deploy-version
    sigma.ali/upgrade-merge-annotations: sigma.ali/enable-apprules-injection
    sigma.ali/upgrade-inherited-labels: tags.aone.alibaba-inc.com/deploy-stack-id,tags.aone.alibaba-inc.com/env-level,tags.sunfire.com/app-deploy-version
  labels:
    normandy.alibabacloud.com/pod-template-hash: 4fb0c0212dbaa1266cc7d993dceace4f
    cloneset.beta1.sigma.ali/enable-pod-idx-gen: 'true'
    tags.aone.alibaba-inc.com/deploy-stack-id: 7b50ca97-ef9a-4b54-a51c-0b2e0260d72c
    tags.aone.alibaba-inc.com/env-level: production
    tags.sunfire.com/app-deploy-version: V191847870
  name: normandy-test-app4-c0b7a852-8f72-493a-ba0--n3
  namespace: normandy-test-app4
spec:
  template:
    metadata:
      annotations:
        pod.beta1.sigma.ali/alarming-off-upgrade: 'true'
        pod.beta1.sigma.ali/naming-register-state: working_online
        sigma.ali/use-unified-pv: 'true'
        sigma.ali/enable-apprules-injection: 'true'
        pod.beta1.sigma.ali/container-extra-config: '{"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"600", "PreStopHookTimeoutSeconds":"600"}}}'
      labels:
        sigma.ali/inject-staragent-sidecar: 'true'
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: sigma.ali/resource-pool
                operator: In
                values:
                - sigma_public
      containers:
      - env:
        - name: ali_aone_timestamp
          value: '1684116732557'
        - name: ali_start_app
          value: 'no'
        - name: ali_run_mode
          value: common_vm
        - name: envSign
          value: production
        - name: trafficRouteLabel
          value: production
        image: hub.docker.alibaba-inc.com/aone/normandy-test-app4:20230515142257182101_publish
        name: main
      tolerations:
      - key: sigma.ali/is-ecs
        operator: Exists
        """.trimIndent(), YamlUtils.dump(specObj).trimIndent())
    }
}