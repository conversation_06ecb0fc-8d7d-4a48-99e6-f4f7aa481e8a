package com.alibaba.koastline.multiclusters.resourceobj.hook

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import org.junit.Assert.assertEquals
import org.junit.Assert.assertSame
import org.junit.Test

import testutils.BaseTest

class FeatureImportHookManagerTest : BaseTest() {

    private val manager: FeatureImportHookManager
        get() {
            val tm = FeatureImportHookManager(listOf(HookA(), HookB()))
            tm.registryHooks()
            return tm
        }

    @Test
    fun `preProcess -- hooked`() {
        val featureImportA = manufacturePojo(ResourceObjectFeatureImport::class.java)
            .copy(
                resourceObjectFeatureKey = "FeatureA"
            )
        val gotA = manager.preProcess(featureImportA)
        assertEquals("A", gotA.paramMap)

        val featureImportB = manufacturePojo(ResourceObjectFeatureImport::class.java)
            .copy(
                resourceObjectFeatureKey = "FeatureB"
            )
        val gotB = manager.preProcess(featureImportB)
        assertEquals("B", gotB.paramMap)
    }

    @Test
    fun `preProcess -- not hooked`() {
        val featureImport = manufacturePojo(ResourceObjectFeatureImport::class.java)
        val got = manager.preProcess(featureImport)
        assertSame(featureImport, got)
    }
}

@FeatureImportHookMeta("FeatureA")
class HookA : FeatureImportHook {
    override fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport {
        return featureImport.copy(paramMap = "A")
    }
}

@FeatureImportHookMeta("FeatureB")
class HookB : FeatureImportHook {
    override fun preProcess(featureImport: ResourceObjectFeatureImport): ResourceObjectFeatureImport {
        return featureImport.copy(paramMap = "B")
    }
}