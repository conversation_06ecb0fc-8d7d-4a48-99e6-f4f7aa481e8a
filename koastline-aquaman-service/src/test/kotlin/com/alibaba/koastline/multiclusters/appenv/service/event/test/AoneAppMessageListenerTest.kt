package com.alibaba.koastline.multiclusters.appenv.service.event.test

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.event.consumer.app.AoneAppMessageListener
import com.alibaba.koastline.multiclusters.resourceobj.ResourceObjectFeatureService
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext
import com.alibaba.rocketmq.common.message.MessageExt
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test

class AoneAppMessageListenerTest {

    @Test
    fun testConsumeMessage() {
        val msgList = mutableListOf(makeFakeMessageExt())
        val aoneAppMessageListener = spyk<AoneAppMessageListener> {
            justRun {
                //从messageExt.body中解析应用名
                userLabelService.initAppLabels("app-name-test01")
                resourceObjectFeatureService.initLightContainerForNewApp("app-name-test01")
            }
        }
        val consumeConcurrentlyContext = mockk<ConsumeConcurrentlyContext>()
        aoneAppMessageListener.consumeMessage(msgList, consumeConcurrentlyContext)
    }


    @Test
    fun testConsumeMessageLightweightDefault() {
        val msgList = mutableListOf(makeFakeMessageExt())
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())).apply {
            every { createFeatureImport(any()) } returns 1L
            every { commonProperties.firstOrNull(CommonProperties.LIGHTWEIGHT_IMAGE) } returns null
        }
        val aoneAppMessageListener = spyk(AoneAppMessageListener()) {
            justRun {
                userLabelService.initAppLabels("app-name-test01")
            }
            this.resourceObjectFeatureService = resourceObjectFeatureService
        }

        val consumeConcurrentlyContext = mockk<ConsumeConcurrentlyContext>()
        aoneAppMessageListener.consumeMessage(msgList, consumeConcurrentlyContext)
        verify {
            resourceObjectFeatureService.createFeatureImport(
                match {
                    it.paramMap == emptyMap<String, String>()
                }
            )
        }
    }

    @Test
    fun testConsumeMessageLightweightSpecified() {
        val msgList = mutableListOf(makeFakeMessageExt())
        val resourceObjectFeatureService = spyk(ResourceObjectFeatureService(ObjectMapper())).apply {
            every { createFeatureImport(any()) } returns 1L
            every { commonProperties.firstOrNull(CommonProperties.LIGHTWEIGHT_IMAGE) } returns "testImage"
        }
        val aoneAppMessageListener = spyk(AoneAppMessageListener()) {
            justRun {
                userLabelService.initAppLabels("app-name-test01")
            }
            this.resourceObjectFeatureService = resourceObjectFeatureService
        }

        val consumeConcurrentlyContext = mockk<ConsumeConcurrentlyContext>()
        aoneAppMessageListener.consumeMessage(msgList, consumeConcurrentlyContext)
        verify {
            resourceObjectFeatureService.createFeatureImport(
                match {
                    it.paramMap == mapOf(
                        "initContainerImage" to "testImage"
                    )
                }
            )
        }
    }

    private fun makeFakeMessageExt(): MessageExt {
        val bodyStr = """
            {
                "appId":224558,
                "appInfo":{
                    "aone_id":224558,
                    "appType":"developmentApp",
                    "appops":[
                        {
                            "bucId":"310363",
                            "emailAddr":"XXXXX",
                            "emailPrefix":"XXXX",
                            "empId":"XXXXXX",
                            "firstName":"XXXXX",
                            "userName":"XXXXX"
                        }
                    ],
                    "buId":173,
                    "bu_group":"CPO产品技术部",
                    "dataSecureLevel":"L1",
                    "deployWay":"normal",
                    "description":"XXXXXXXXXXX",
                    "finSecureLevel":"L1",
                    "gmtCreate":1683164003000,
                    "id":224558,
                    "level":"GRADE2",
                    "name":"app-name-test01",
                    "oncall":[

                    ],
                    "product":"SSC",
                    "productId":24853,
                    "product_fullline":"CPO产品技术部/SSC",
                    "product_fullline_id_path":"24853",
                    "product_line":"SSC",
                    "status":"READY_TO_ONLINE"
                },
                "bizToken":"93ca598c-0836-448f-ad15-af569b7b3aa8",
                "type":"add"
            }
        """.trimIndent()
        return MessageExt().apply {
            this.body = bodyStr.toByteArray(Charsets.UTF_8)
        }
    }
}