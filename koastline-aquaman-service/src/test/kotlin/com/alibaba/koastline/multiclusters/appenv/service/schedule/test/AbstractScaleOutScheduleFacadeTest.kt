package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeclarationPatchDataDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedDO
import com.alibaba.koastline.multiclusters.apre.model.ApREDeedResult
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterLabel
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelector
import com.alibaba.koastline.multiclusters.apre.model.ClusterSelectorType
import com.alibaba.koastline.multiclusters.apre.model.Declaration
import com.alibaba.koastline.multiclusters.apre.model.DeclarationPatch
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.PatchItem
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.apre.params.ApREDeclarationPatchType
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType.CONSOLE
import com.alibaba.koastline.multiclusters.external.model.AppGroup
import com.alibaba.koastline.multiclusters.resourceobj.base.CloneSetSpecService
import com.alibaba.koastline.multiclusters.schedule.model.DeclarationData
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleResult
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadExpectedState
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraint
import com.alibaba.koastline.multiclusters.schedule.model.WorkloadMetadataConstraintAssemble
import com.alibaba.koastline.multiclusters.schedule.params.ScheduleResultParamConstants
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleFilterServiceFactory
import com.alibaba.koastline.multiclusters.schedule.service.ScheduleStrategyService
import com.alibaba.koastline.multiclusters.schedule.service.fiter.ClusterLoadScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.RandomScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.RunningStateScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.ValidApREScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.fiter.X86ClusterScheduleFilterProcessor
import com.alibaba.koastline.multiclusters.schedule.service.schedule.RunningStateScheduleService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.ScheduleStandardService
import com.alibaba.koastline.multiclusters.schedule.service.schedule.facade.AbstractScaleOutScheduleFacade
import io.mockk.every
import io.mockk.mockkClass
import io.mockk.spyk
import org.junit.Rule
import org.junit.Test
import org.junit.rules.ExpectedException
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AbstractScaleOutScheduleFacadeTest {
    @JvmField
    @Rule
    val exceptionRule: ExpectedException = ExpectedException.none()
    @Test
    fun testDoSchedule_while_has_running_state_cluster() {
        val content = getScheduleRequestContent()
        val apREDeedResult = makeFakeApREDeedResult()
        val runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", any())
            } returns listOf("clusterId_a","clusterId_d")
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            this.scheduleStandardService = scheduleStandardService
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = any(), resourceGroup = any(), stage = any(), unit = any(), site = any())
            } returns null
            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        assertEquals(2,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_a", scheduleResult.workloadExpectedStates[0].clusterProfile!!.clusterId)
        assertEquals("33", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[1].clusterProfile!!.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun testDoSchedule_while_do_not_has_running_state_cluster() {
        val content = getScheduleRequestContent()
        val apREDeedResult = makeFakeApREDeedResult()
        val runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", any())
            } returns listOf()
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = any(), resourceGroup = any(), stage = any(), unit = any(), site = any())
            } returns null

            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        assertEquals(2,scheduleResult.workloadExpectedStates.size)
        assertTrue(listOf("clusterId_a","clusterId_b","clusterId_c").contains(scheduleResult.workloadExpectedStates[0].clusterProfile!!.clusterId))
        assertEquals("33", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[1].clusterProfile!!.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun testDoSchedule_while_default_filter_arm() {
        val content = getScheduleRequestContent()
        val apREDeedResult = makeFakeApREDeedResultWithArm()
        val runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", any())
            } returns listOf()
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = any(), resourceGroup = any(), stage = any(), unit = any(), site = any())
            } returns null

            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        assertEquals(2,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[0].clusterProfile!!.clusterId)
        assertEquals("33", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[1].clusterProfile!!.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun testDoSchedule_while_has_cluster_balance_and_cluster_label_selector() {
        val content = getScheduleRequestContent()
        val apREDeedResult = makeFakeApREDeedResult()
        val runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", makeFakeDeclaration_na620())
            } returns listOf()
        }

        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            this.scheduleStrategyService = ScheduleStrategyService()
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na610")
            } returns makeFakeApREDeclarationPatchDataForClusterLabelSelector()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na620")
            } returns null

            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = content.resourceScope.appName,
                    envStackId = null,
                    resourceGroup = content.resourceScope.resourceGroup
                )
            } returns emptyList()
            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        println(scheduleResult)
        assertEquals(4,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals("9", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_c", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
        assertEquals("8", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_a", scheduleResult.workloadExpectedStates[2].workloadMetadataConstraint.clusterId)
        assertEquals("16", scheduleResult.workloadExpectedStates[2].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[3].workloadMetadataConstraint.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[3].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun testDoSchedule_while_has_cluster_balance_and_cluster_selector() {
        val content = getScheduleRequestContent()
        var apREDeedResult = makeFakeApREDeedResult()
        var runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", makeFakeDeclaration_na620())
            } returns listOf()
        }

        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            this.scheduleStrategyService = ScheduleStrategyService()
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na610")
            } returns makeFakeApREDeclarationPatchDataForClusterSelector()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na620")
            } returns null
            every {
                scheduleStandardService.generateNameSpace(any())
            } returns content.resourceScope.appName
            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = content.resourceScope.appName,
                    envStackId = null,
                    resourceGroup = content.resourceScope.resourceGroup
                )
            } returns emptyList()
            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        println(scheduleResult)
        assertEquals(4,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_a", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals("17", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
        assertEquals("8", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_c", scheduleResult.workloadExpectedStates[2].workloadMetadataConstraint.clusterId)
        assertEquals("8", scheduleResult.workloadExpectedStates[2].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[3].workloadMetadataConstraint.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[3].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun `testDoSchedule -- cluster_balance - has running resources`() {
        val content = getScheduleRequestContent()
        var apREDeedResult = makeFakeApREDeedResult()
        var runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", makeFakeDeclaration_na620())
            } returns listOf()
        }

        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            this.scheduleStrategyService = ScheduleStrategyService()
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na610")
            } returns makeFakeApREDeclarationPatchDataForClusterSelector()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na620")
            } returns null
            every {
                scheduleStandardService.generateNameSpace(any())
            } returns "normandy-test-app4"
            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = content.resourceScope.appName,
                    envStackId = null,
                    resourceGroup = content.resourceScope.resourceGroup
                )
            } returns listOf(
                WorkloadMetadataConstraintAssemble(
                    num = 10,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a"
                    )
                ),
                WorkloadMetadataConstraintAssemble(
                    num = 8,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_b"
                    )
                ),
                WorkloadMetadataConstraintAssemble(
                    num = 2,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_c"
                    )
                )
            )
            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        println(scheduleResult)
        assertEquals(4,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_a", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals("17", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
        assertEquals("8", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_c", scheduleResult.workloadExpectedStates[2].workloadMetadataConstraint.clusterId)
        assertEquals("8", scheduleResult.workloadExpectedStates[2].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[3].workloadMetadataConstraint.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[3].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun `testDoSchedule -- cluster_balance - has running resources,one cluster'resource num is more than expected`() {
        val content = getScheduleRequestContent()
        var apREDeedResult = makeFakeApREDeedResult()
        var runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", makeFakeDeclaration_na620())
            } returns listOf()
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }

        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            this.scheduleStrategyService = ScheduleStrategyService()
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na610")
            } returns makeFakeApREDeclarationPatchDataForClusterSelector()
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = content.resourceScope.appName, resourceGroup = content.resourceScope.resourceGroup, stage = "PRE_PUBLISH", unit = "center", site = "na620")
            } returns null

            every {
                skylineApi.listWorkloadMetadataConstraintThroughServerList(
                    appName = content.resourceScope.appName,
                    envStackId = null,
                    resourceGroup = content.resourceScope.resourceGroup
                )
            } returns listOf(
                WorkloadMetadataConstraintAssemble(
                    num = 50,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a"
                    )
                ),
                WorkloadMetadataConstraintAssemble(
                    num = 1,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_b"
                    )
                ),
                WorkloadMetadataConstraintAssemble(
                    num = 0,
                    workloadMetadataConstraint = WorkloadMetadataConstraint(
                        appName = content.resourceScope.appName,
                        resourceGroup = content.resourceScope.resourceGroup!!,
                        site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_c"
                    )
                )
            )
            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        println(scheduleResult)
        assertEquals(3,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[0].workloadMetadataConstraint.clusterId)
        assertEquals("17", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_c", scheduleResult.workloadExpectedStates[1].workloadMetadataConstraint.clusterId)
        assertEquals("16", scheduleResult.workloadExpectedStates[1].params[ScheduleResultParamConstants.SCALE_NUM])
        assertEquals("clusterId_d", scheduleResult.workloadExpectedStates[2].workloadMetadataConstraint.clusterId)
        assertEquals("67", scheduleResult.workloadExpectedStates[2].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun `testGenerateNameSpace -- native_cloud resource group`() {
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "app-test01",
            resourceGroup = "app-test01-host",
            site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a"
        )
        val runningStateScheduleService = spyk<RunningStateScheduleService>() {
            every {
                doSchedule(any())
            } returns ScheduleResult(workloadExpectedStates = emptyList())
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                scheduleServiceFactory.getScheduleService(ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD))
            } returns runningStateScheduleService
            every {
                skylineApi.getAppGroup(workloadMetadataConstraint.resourceGroup)
            } returns AppGroup(name = workloadMetadataConstraint.resourceGroup, usageType = "usageType", appName = workloadMetadataConstraint.appName, originalName = workloadMetadataConstraint.appName,
            tags = listOf("resource_res_type.native_cloud"))
        }
        val ns = scheduleStandardService.generateNameSpace(workloadMetadataConstraint)
        assertEquals(ns, CloneSetSpecService.DEFAULT_NAME_SPACE)
    }

    @Test
    fun `testGenerateNameSpace -- sts resource group`() {
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "app-test01",
            resourceGroup = "app-test01-host",
            site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a"
        )
        val runningStateScheduleService = spyk<RunningStateScheduleService>() {
            every {
                doSchedule(any())
            } returns ScheduleResult(workloadExpectedStates = emptyList())
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                scheduleServiceFactory.getScheduleService(ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD))
            } returns runningStateScheduleService
            every {
                skylineApi.getAppGroup(workloadMetadataConstraint.resourceGroup)
            } returns AppGroup(name = workloadMetadataConstraint.resourceGroup, usageType = "usageType", appName = workloadMetadataConstraint.appName, originalName = workloadMetadataConstraint.appName,
                tags = listOf("resource_res_type.non_native_cloud"))
        }
        val ns = scheduleStandardService.generateNameSpace(workloadMetadataConstraint)
        assertEquals(ns, "app-test01")
    }

    @Test
    fun `testGenerateNameSpace -- by running state name space`() {
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "app-test01",
            resourceGroup = "app-test01-host",
            site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a", namespace = "app-test01-ns"
        )
        val runningStateScheduleService = spyk<RunningStateScheduleService>() {
            every {
                doSchedule(any())
            } returns ScheduleResult(workloadExpectedStates = listOf(
                WorkloadExpectedState(workloadMetadataConstraint = workloadMetadataConstraint, params = emptyMap())
            ))
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                scheduleServiceFactory.getScheduleService(ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD))
            } returns runningStateScheduleService
        }
        val ns = scheduleStandardService.generateNameSpace(workloadMetadataConstraint)
        assertEquals(ns, "app-test01-ns")
    }

    @Test
    fun `testGenerateNameSpace -- running state namespace is null`() {
        val workloadMetadataConstraint = WorkloadMetadataConstraint(
            appName = "app-test01",
            resourceGroup = "app-test01-host",
            site = "na610", stage = "PRE_PUBLISH", unit = "center", clusterId = "clusterId_a"
        )
        val runningStateScheduleService = spyk<RunningStateScheduleService>() {
            every {
                doSchedule(any())
            } returns ScheduleResult(workloadExpectedStates = listOf(
                WorkloadExpectedState(workloadMetadataConstraint = workloadMetadataConstraint, params = emptyMap())
            ))
        }
        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                scheduleServiceFactory.getScheduleService(ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.LIST_POD))
            } returns runningStateScheduleService
        }
        exceptionRule.expect(IllegalStateException::class.java)
        exceptionRule.expectMessage("namespace不能为空.")
        scheduleStandardService.generateNameSpace(workloadMetadataConstraint)
    }

    @Test
    fun testDoSchedule_while_scale_out_only_one_and_has_two_matchDeclarations() {
        val content = getScheduleRequestContent().run {
            this.copy(
                scheduleRequestParam = this.scheduleRequestParam!!.copy(replicas = 1)
            )
        }
        val apREDeedResult = makeFakeApREDeedResultWithEmptyApRE()
        val runningStateScheduleFilterProcessor = spyk<RunningStateScheduleFilterProcessor>() {
            every {
                skylineApi.listClusterIdByResourceScopeAndDeclaration("d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost", any())
            } returns listOf()
        }

        val scheduleStandardService = spyk<ScheduleStandardService>() {
            every {
                generateNameSpace(any())
            } returns "normandy-test-app4"
        }
        val abstractScaleOutScheduleFacade = spyk<AbstractScaleOutScheduleFacade>() {
            every {
                getApREDeedResultWithResource(content)
            } returns apREDeedResult
            every {
                apREDeclarationPatchService.getClusterBalanceApREDeclarationPatchData(appName = any(), resourceGroup = any(), stage = any(), unit = any(), site = any())
            } returns null

            scheduleFilterServiceFactory = ScheduleFilterServiceFactory()
            scheduleFilterServiceFactory.registryScheduleFilterService(runningStateScheduleFilterProcessor)
            scheduleFilterServiceFactory.registryScheduleFilterService(RandomScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ValidApREScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(ClusterLoadScheduleFilterProcessor())
            scheduleFilterServiceFactory.registryScheduleFilterService(X86ClusterScheduleFilterProcessor())
            this.scheduleStandardService = scheduleStandardService
        }

        val scheduleResult = abstractScaleOutScheduleFacade.doSchedule(content)
        assertEquals(1,scheduleResult.workloadExpectedStates.size)
        assertEquals("clusterId_b", scheduleResult.workloadExpectedStates[0].clusterProfile!!.clusterId)
        assertEquals("1", scheduleResult.workloadExpectedStates[0].params[ScheduleResultParamConstants.SCALE_NUM])
    }

    @Test
    fun doComputeWorkloadReplicasTest() {
        val mockStandardService = mockkClass(ScheduleStandardService::class)
        every { mockStandardService.generateNameSpace(any()) } returns ""
        val scaleOutScheduleFacade = object :AbstractScaleOutScheduleFacade() {
            init {
                scheduleStandardService = mockStandardService
            }

            override fun getApREDeedResultWithResource(content: ScheduleRequestContent): ApREDeedResult = makeFakeApREDeedResult()

            fun testCompute(content: ScheduleRequestContent, declaration: Declaration, resourceList: List<ResourceDO>, replicas: Int) =
                this.doComputeWorkloadReplicas(content, declaration, resourceList, replicas)

        }
        // 1 个扩容额度在3个集群中均分
        val result = scaleOutScheduleFacade.testCompute(
            getScheduleRequestContent(), makeFakeDeclaration_na610(),
            listOf(
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_c","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                )
            ), 1
        )
        assertEquals(listOf("1"), result.map { it.params["SCALE_NUM"] })

        // 7 个扩容额度在2个集群中均分
        val result1 = scaleOutScheduleFacade.testCompute(
            getScheduleRequestContent(), makeFakeDeclaration_na610(),
            listOf(
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                )
            ), 7
        )
        assertEquals(listOf("4", "3"), result1.map { it.params["SCALE_NUM"] })

        // 10 个扩容额度在 3 个集群中均分
        val result2 = scaleOutScheduleFacade.testCompute(
            getScheduleRequestContent(), makeFakeDeclaration_na610(),
            listOf(
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                ),
                ResourceDO(
                    clusterProfileNew = ClusterProfileNew("clusterId_c","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)
                )
            ), 10
        )
        assertEquals(listOf("4", "4", "2"), result2.map { it.params["SCALE_NUM"] })
    }

    private fun getScheduleRequestContent(): ScheduleRequestContent {
        return ScheduleRequestContent(
            resourceScope = ResourceScope("normandy-test-app4","d13f890e-d4f4-48c6-81f4-6e0b8f595244","normandy-test-app4_prehost"),
            declarationData = DeclarationData(null, null),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.SCALE_OUT),
            scheduleRequestParam =ScheduleRequestParam(100)
        )
    }

    private fun makeFakeApREDeclarationPatchDataForClusterLabelSelector(): ApREDeclarationPatchDataDO {
        return ApREDeclarationPatchDataDO(
            balanceType = ApREDeclarationPatchType.BALANCE_CLUSTER.name,
            stage = "PRE_PUBLSIH",
            unit = "center",
            site = "na610",
            declarationPatch = DeclarationPatch(patchItems = listOf(
                PatchItem(clusterSelector = ClusterSelector(labels = listOf(ClusterLabel(name = "feature/type", value = "x86"), ClusterLabel(name = "feature/code", value = "A"))), weight = 1),
                PatchItem(clusterSelector = ClusterSelector(labels = listOf(ClusterLabel(name = "feature/type", value = "M"))), weight = 1)
            )), creator = "10000", modifier = "10000"
        )
    }

    private fun makeFakeApREDeclarationPatchDataForClusterSelector(): ApREDeclarationPatchDataDO {
        return ApREDeclarationPatchDataDO(
            balanceType = ApREDeclarationPatchType.BALANCE_CLUSTER.name,
            stage = "PRE_PUBLSIH",
            unit = "center",
            site = "na610",
            declarationPatch = DeclarationPatch(patchItems = listOf(
                PatchItem(clusterSelector = ClusterSelector(selectType = ClusterSelectorType.CLUSTER, clusterIds = listOf("clusterId_a")), weight = 1),
                PatchItem(clusterSelector = ClusterSelector(selectType = ClusterSelectorType.CLUSTER, clusterIds = listOf("clusterId_b","clusterId_c")), weight = 1)
            )), creator = "10000", modifier = "10000"
        )
    }

    private fun makeFakeApREDeedResult(): ApREDeedResult {
        return ApREDeedResult(
            deedDO = ApREDeedDO(),
            matchDeclarations = listOf(
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(
                        ApREDO(1L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "feature/type", value = "M", type = CONSOLE))
                                ),
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "feature/type", value = "x86", type = CONSOLE),ApRELabelDO(name = "feature/code", value = "A", type = CONSOLE))
                                )
                            )
                        ),
                        ApREDO(2L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_c","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "feature/type", value = "x86", type = CONSOLE),ApRELabelDO(name = "feature/code", value = "A", type = CONSOLE))
                                )
                            )
                        )
                    ),
                    weight = 1,
                    declaration = makeFakeDeclaration_na610()
                ),
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(
                        ApREDO(3L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na620","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(clusterProfileNew = ClusterProfileNew("clusterId_d","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)),
                            )
                        ),
                        ApREDO(4L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na620","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf()
                        )
                    ),
                    weight = 2,
                    declaration = makeFakeDeclaration_na620()
                )
            )
        )
    }

    private fun makeFakeApREDeedResultWithEmptyApRE(): ApREDeedResult {
        return ApREDeedResult(
            deedDO = ApREDeedDO(),
            matchDeclarations = listOf(
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(
                        ApREDO(1L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "feature/type", value = "x86", type = CONSOLE),ApRELabelDO(name = "feature/code", value = "A", type = CONSOLE))
                                )
                            )
                        )
                    ),
                    weight = 1,
                    declaration = makeFakeDeclaration_na610()
                ),
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(),
                    weight = 1,
                    declaration = makeFakeDeclaration_na620()
                )
            )
        )
    }

    private fun makeFakeApREDeedResultWithArm(): ApREDeedResult {
        return ApREDeedResult(
            deedDO = ApREDeedDO(),
            matchDeclarations = listOf(
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(
                        ApREDO(1L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_a","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "customer/machine/type", value = "m", type = CONSOLE))
                                ),
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_b","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "customer/machine/type", value = "x86", type = CONSOLE),ApRELabelDO(name = "feature/code", value = "A", type = CONSOLE))
                                )
                            )
                        ),
                        ApREDO(2L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na610","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(
                                    clusterProfileNew = ClusterProfileNew("clusterId_c","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name),
                                    apRELabels = listOf(ApRELabelDO(name = "customer/machine/type", value = "m", type = CONSOLE),ApRELabelDO(name = "feature/code", value = "A", type = CONSOLE))
                                )
                            )
                        )
                    ),
                    weight = 1,
                    declaration = makeFakeDeclaration_na610()
                ),
                MatchDeclaration(
                    declarationId = UUID.randomUUID().toString(),
                    apres = mutableListOf(
                        ApREDO(3L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na620","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf(
                                ResourceDO(clusterProfileNew = ClusterProfileNew("clusterId_d","","","", siteList = emptyList(), componentDataList = emptyList(),ClusterProfileUseTypeEnum.publish.name)),
                            )
                        ),
                        ApREDO(4L, "",null,"creator","MMMMMMM",
                            "cn-zhangjiakou","na620","PRE_PUBLISH","center",null,null,null,null,null, mutableListOf()
                            , listOf()
                        )
                    ),
                    weight = 2,
                    declaration = makeFakeDeclaration_na620()
                )
            )
        )
    }

    private fun makeFakeDeclaration_na610(): Declaration {
        return Declaration(
            null,
            "cn-zhangjiakou",
            "na610",
            "PRE_PUBLISH",
            "center"
        )
    }

    private fun makeFakeDeclaration_na620(): Declaration {
        return Declaration(
            null,
            "cn-zhangjiakou",
            "na620",
            "PRE_PUBLISH",
            "center"
        )
    }
}