package com.alibaba.koastline.multiclusters.appenv.service.schedule.test

import com.alibaba.koastline.multiclusters.appenv.params.ClusterProfileUseTypeEnum
import com.alibaba.koastline.multiclusters.apre.ApRELabelExt
import com.alibaba.koastline.multiclusters.apre.model.ApREDO
import com.alibaba.koastline.multiclusters.apre.model.ApRELabelDO
import com.alibaba.koastline.multiclusters.apre.model.ClusterProfileNew
import com.alibaba.koastline.multiclusters.apre.model.MatchDeclaration
import com.alibaba.koastline.multiclusters.apre.model.ResourceDO
import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelType
import com.alibaba.koastline.multiclusters.schedule.model.DeclarationData
import com.alibaba.koastline.multiclusters.schedule.model.ResourceScope
import com.alibaba.koastline.multiclusters.schedule.model.SceneEnum
import com.alibaba.koastline.multiclusters.schedule.model.SchedulePatternEnum
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestContent
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleRequestParam
import com.alibaba.koastline.multiclusters.schedule.model.ScheduleType
import com.alibaba.koastline.multiclusters.schedule.service.fiter.X86ClusterScheduleFilterProcessor
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * @author:    <EMAIL>
 * @date:    2024/12/18 2:00 PM
 */
class X86ClusterScheduleFilterProcessorTest {
    @Test
    fun testDoFilter() {
        val x86ClusterScheduleFilterProcessor = X86ClusterScheduleFilterProcessor()
        val matchDeclaration = MatchDeclaration(
            apres = listOf(
                ApREDO(
                    1L,
                    "",
                    null,
                    "creator",
                    "MMMMMMM",
                    "cn-zhangjiakou",
                    "na610",
                    "PRE_PUBLISH",
                    "center",
                    null,
                    null,
                    null,
                    null,
                    null,
                    mutableListOf(),
                    listOf(
                        ResourceDO(
                            clusterProfileNew = ClusterProfileNew(
                                "clusterId_a", "", "", "", siteList = emptyList(), componentDataList = emptyList(),
                                ClusterProfileUseTypeEnum.publish.name
                            ),
                            apRELabels = listOf(
                                ApRELabelDO(
                                    name = ApRELabelExt.APRE_LABEL_NAME_CUSTOMER_MACHINE_TYPE,
                                    value = ApRELabelExt.APRE_LABEL_VALUE_ARM,
                                    type = ApRELabelType.CONSOLE
                                )
                            )
                        ),
                        ResourceDO(
                            clusterProfileNew = ClusterProfileNew(
                                "clusterId_b", "", "", "", siteList = emptyList(), componentDataList = emptyList(),
                                ClusterProfileUseTypeEnum.publish.name
                            ),
                            apRELabels = listOf(
                                ApRELabelDO(name = "feature/type", value = "x86", type = ApRELabelType.CONSOLE)
                            )
                        )
                    )
                ),
                ApREDO(
                    2L,
                    "",
                    null,
                    "creator",
                    "MMMMMMM",
                    "cn-zhangjiakou",
                    "na610",
                    "PRE_PUBLISH",
                    "center",
                    null,
                    null,
                    null,
                    null,
                    null,
                    mutableListOf(),
                    listOf(
                        ResourceDO(
                            clusterProfileNew = ClusterProfileNew(
                                "clusterId_c", "", "", "", siteList = emptyList(), componentDataList = emptyList(),
                                ClusterProfileUseTypeEnum.publish.name
                            ),
                            apRELabels = listOf(
                                ApRELabelDO(
                                    name = ApRELabelExt.APRE_LABEL_NAME_CUSTOMER_MACHINE_TYPE,
                                    value = ApRELabelExt.APRE_LABEL_VALUE_ARM,
                                    type = ApRELabelType.CONSOLE
                                )
                            )
                        )
                    )
                )
            )
        )
        val filterMatchDeclaration = x86ClusterScheduleFilterProcessor.doFilter(
            matchDeclaration = matchDeclaration,
            content = getScheduleRequestContent()
        )
        assertEquals(2, filterMatchDeclaration.apres.size)
        assertEquals(1, filterMatchDeclaration.apres[0].resources.size)
        assertEquals("clusterId_b", filterMatchDeclaration.apres[0].resources[0].clusterProfileNew!!.clusterId)
        assertEquals(0, filterMatchDeclaration.apres[1].resources.size)
    }

    private fun getScheduleRequestContent(): ScheduleRequestContent {
        return ScheduleRequestContent(
            resourceScope = ResourceScope(
                "normandy-test-app4",
                "d13f890e-d4f4-48c6-81f4-6e0b8f595244",
                "normandy-test-app4_prehost"
            ),
            declarationData = DeclarationData(null, null),
            scheduleType = ScheduleType(SchedulePatternEnum.NON_DECLARATIVE, SceneEnum.SCALE_OUT),
            scheduleRequestParam = ScheduleRequestParam(100)
        )
    }
}