package com.alibaba.koastline.multiclusters.common.utils

import com.alibaba.normandy.actor.client.AResult
import org.junit.Test

/**
 * <AUTHOR>
 * @date 2025/4/11 20:58
 */
class RetryUtilsTest {


    @Test(expected = IllegalStateException::class)
    fun retryIfFail_retry_multiple_times_but_fail() {
        RetryUtils.retryIfFail({ mockResultAndException() }) { result, t -> t != null }
    }

    @Test
    fun retryIfFail_retry_multiple_times_and_success() {
        var retry = 0
        RetryUtils.retryIfFail({ mockResultAndException(retry++) }) { result, t -> t != null || result?.isSuccess == false }
    }


    private fun mockResultAndException(retry: Int? = 0): AResult<Boolean> {
        if (retry == 1) {
            return AResult.error()
        }

        if (retry == 2) {
            return AResult.error()
        }

        if (retry == 3) {
            return AResult.success()
        }

        throw IllegalStateException("mock result and exception")
    }


}