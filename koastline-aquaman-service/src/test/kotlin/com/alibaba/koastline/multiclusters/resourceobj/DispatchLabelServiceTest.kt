package com.alibaba.koastline.multiclusters.resourceobj

import com.alibaba.koastline.multiclusters.common.config.CommonProperties
import com.alibaba.koastline.multiclusters.common.exceptions.CallExternalSysException
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValueWithMetadata
import com.alibaba.koastline.multiclusters.resourceobj.base.CheckService
import io.mockk.every
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertEquals

class DispatchLabelServiceTest {

    @Test
    fun calculateMatchedLabel() {
        val labels = listOf(
            ConfigDispatchLabelValueWithMetadata(
                id = 1,
                labelCode = "test",
                labelValue = "1",
                labelType = "test",
                appName = "test",
                idc = "na610",
                creator = "test",
                modifier = "test",
                labelName = "test",
            ),
            ConfigDispatchLabelValueWithMetadata(
                id = 1,
                labelCode = "test",
                labelValue = "2",
                labelType = "test",
                appName = "test",
                groupName = "g1",
                creator = "test",
                modifier = "test",
                labelName = "test",
            )
        )

        assertEquals(
            "2",
            DispatchLabelService.calculateMatchedLabel(
                labels = labels,
                appName = "app",
                groupName = "g1",
                idc = "na610",
                unit = "unit",
                env = "env"
            )!!.labelValue
        )


    }


    @Test
    fun testGetSigmaConfigMapFromString() {
        val jsonString = """
        {
            "key1": {"subKey1": "value1", "subKey2": "value2"},
            "key2": {"subKey1": "value3", "subKey2": "value4"}
        }
    """.trimIndent()

        val expectedMap = mapOf(
            "key1" to mapOf("subKey1" to "value1", "subKey2" to "value2"),
            "key2" to mapOf("subKey1" to "value3", "subKey2" to "value4")
        )

        val result = DispatchLabelService.getSigmaConfigMapFromString(jsonString)
        assertEquals(expectedMap, result)
    }

    @Test
    fun testWhetherFallbackWithoutWildCard() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app1")
            } returns true
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app2")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*")
            } returns false
        }
        assertEquals(false, dispatchLabelService.whetherFallback("app1"))
        assertEquals(true, dispatchLabelService.whetherFallback("app2"))
    }

    @Test
    fun testWhetherFallbackWithWildCard() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app1")
            } returns true
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "app2")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_APP_WHITE_LIST, "*")
            } returns true
        }
        assertEquals(false, dispatchLabelService.whetherFallback("app1"))
        assertEquals(false, dispatchLabelService.whetherFallback("app2"))
    }

    @Test
    fun testGetSigmaConfigMapWithoutCheck() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(any())
            } returns listOf(
                ConfigDispatchLabelValueWithMetadata(
                    id = 1,
                    labelCode = "test",
                    labelValue = "1",
                    labelType = "constraints",
                    appName = "app1",
                    creator = "test",
                    modifier = "test",
                    labelName = "test",
                )
            )
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns true
        }
        val ret = dispatchLabelService.getSigmaConfigMap("app1")
        assertEquals(
            ret,
            "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"1\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        )
        verify(exactly = 0) { dispatchLabelService.atomApi.getSigmaConfigMap(any(), any(), any(), any(), any()) }
    }

    @Test
    fun testGetSigmaConfigMapWithCheckSuccess() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(any())
            } returns listOf(
                ConfigDispatchLabelValueWithMetadata(
                    id = 1,
                    labelCode = "test",
                    labelValue = "1",
                    labelType = "constraints",
                    appName = "app1",
                    creator = "test",
                    modifier = "test",
                    labelName = "test",
                )
            )
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.getSigmaConfigMap(any(), any(), any(), any(), any())
            } returns "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"1\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        }
        val ret = dispatchLabelService.getSigmaConfigMap("app1")
        assertEquals(
            ret,
            "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"1\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        )
        verify(exactly = 1) { dispatchLabelService.atomApi.getSigmaConfigMap(any(), any(), any(), any(), any()) }
    }

    @Test
    fun testGetSigmaConfigMapWithCheckFail() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(any())
            } returns listOf(
                ConfigDispatchLabelValueWithMetadata(
                    id = 1,
                    labelCode = "test",
                    labelValue = "1",
                    labelType = "constraints",
                    appName = "app1",
                    creator = "test",
                    modifier = "test",
                    labelName = "test",
                )
            )
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.getSigmaConfigMap(any(), any(), any(), any(), any())
            } returns "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"2\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        }
        val ret = dispatchLabelService.getSigmaConfigMap("app1")
        assertEquals(
            ret,
            "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"2\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        )
        verify(exactly = 1) { dispatchLabelService.atomApi.getSigmaConfigMap(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `testGetSigmaConfigMap SkipCheckWhenAtomException`() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            checkService = spyk(CheckService()) {
            }
            every {
                whetherFallback("app1")
            } returns false
            every {
                dispatchLabelValueRepo.selectByAppNameWithLabelMetadata(any())
            } returns listOf(
                ConfigDispatchLabelValueWithMetadata(
                    id = 1,
                    labelCode = "test",
                    labelValue = "1",
                    labelType = "constraints",
                    appName = "app1",
                    creator = "test",
                    modifier = "test",
                    labelName = "test",
                )
            )
            every {
                commonProperties.contains(CommonProperties.ATOM_AQUAMAN_CHECK_SWITCH, "false")
            } returns false
            every {
                commonProperties.contains(CommonProperties.ATOM_SWITCH_AQUAMAN_ONLY_APP_LIST, "app1")
            } returns false
            every {
                atomApi.getSigmaConfigMap(any(), any(), any(), any(), any())
            } throws CallExternalSysException("atom getSigmaConfigMap failed!")
        }
        val ret = dispatchLabelService.getSigmaConfigMap("app1")
        assertEquals(
            ret,
            "{\"data\":{\"constraints\":\"{\\\"test\\\":\\\"1\\\"}\",\"spread\":\"{}\",\"allocSpec\":\"{}\",\"hostConfig\":\"{}\",\"extConfig\":\"{}\",\"prohibit\":\"{}\",\"monopolize\":\"{}\"}}"
        )
        verify(exactly = 1) { dispatchLabelService.atomApi.getSigmaConfigMap(any(), any(), any(), any(), any()) }
        verify(exactly = 0) { dispatchLabelService.checkService.checkGetSigmaConfigMap(any(), any(), any(), any(), any(), any(), any()) }
    }
    @Test
    fun testGetLabelValue() {
        val dispatchLabelService = spyk(DispatchLabelService()) {
            every { dispatchLabelValueRepo.selectByAppNameWithLabelMetadata("xlab-ocr") } returns listOf(
                ConfigDispatchLabelValueWithMetadata(
                    id = 1,
                    labelCode = "CpuSetMode",
                    labelValue = "share",
                    labelType = "allocSpec",
                    appName = "xlab-ocr",
                    groupName = "group1",
                    idc = "idc1",
                    unit = "unit1",
                    env = "env1",
                    creator = "creator1",
                    modifier = "modifier1",
                    labelName = "宿主机CpuSet选择模式"
                ),
                ConfigDispatchLabelValueWithMetadata(
                    id = 2,
                    labelCode = "CpuSetMode",
                    labelValue = "set",
                    labelType = "allocSpec",
                    appName = "xlab-ocr",
                    groupName = "group2",
                    idc = "idc2",
                    unit = "unit2",
                    env = "env2",
                    creator = "creator2",
                    modifier = "modifier2",
                    labelName = "宿主机CpuSet选择模式"
                )
            )
        }
        // specify all elements
        val result = dispatchLabelService.getLabelValue("xlab-ocr", "group1", "idc1", "unit1", "env1")
        assertEquals(1, result.size)
        assertEquals("share", result[0].labelValue)
        // specify all elements test 2
        val result2 = dispatchLabelService.getLabelValue("xlab-ocr", "group2", "idc2", "unit2", "env2")
        assertEquals(1, result2.size)
        assertEquals("set", result2[0].labelValue)
        // only appName
        val result3 = dispatchLabelService.getLabelValue("xlab-ocr")
        assertEquals(2, result3.size)
        assertEquals("share", result3[0].labelValue)
        assertEquals("set", result3[1].labelValue)
        // only appName and unit
        val result4 = dispatchLabelService.getLabelValue("xlab-ocr", unit = "unit1")
        assertEquals(1, result4.size)
        assertEquals("share", result4[0].labelValue)
        // only appName and idc
        val result5 = dispatchLabelService.getLabelValue("xlab-ocr", idc = "idc2")
        assertEquals(1, result5.size)
        assertEquals("set", result5[0].labelValue)
        // only appName and env
        val result6 = dispatchLabelService.getLabelValue("xlab-ocr", env = "env1")
        assertEquals(1, result6.size)
        assertEquals("share", result6[0].labelValue)
        // only appName and env, but no result
        val result7 = dispatchLabelService.getLabelValue("xlab-ocr", env = "env3")
        assertEquals(0, result7.size)


    }


}