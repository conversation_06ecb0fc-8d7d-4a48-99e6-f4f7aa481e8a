package testutils

import com.alibaba.koastline.multiclusters.common.logger
import io.mockk.MockKAnnotations
import org.apache.commons.lang.math.RandomUtils
import org.assertj.core.api.JUnitSoftAssertions
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import uk.co.jemos.podam.api.PodamFactory
import uk.co.jemos.podam.api.PodamFactoryImpl
import java.lang.reflect.Type
import java.util.*
import java.util.function.Consumer


/**
 * 包含 random Utils 基础类型和 pojo generator, 测试工具使用 AssertJ
 */
open class BaseTest {

    /**
     * path level
     * 0 -> bu 1 -> bu#int 2 -> bu#int_int
     */
    fun getRandomProductLinePath(pathLevel: Int): String {
        require(pathLevel >= 1) { "path level should be large than or equal to zero" }
        return if (pathLevel == 1) {
            getInt().toString()
        } else {
            val subProductLines = mutableListOf<String>()
            for (index in 1..pathLevel) {
                subProductLines.add(getInt().toString())
            }
            val subPath = subProductLines.joinToString("_")
            "${getInt()}#$subPath"
        }
    }

    fun getInt(): Int = RandomUtils.nextInt()

    fun getIntUnderScope(ceil: Int):Int {
        require(ceil > 0){ "ceil is a positive number, random scope is from 1 to ceil that the inclusive to the exclusive" }
        return RandomUtils.nextInt(ceil) + (ceil-1)
    }

    fun getLong(): Long = RandomUtils.nextLong()

    fun getDouble(): Double = RandomUtils.nextDouble()

    fun getString(): String {
        return manufacturePojo(String::class.java)
    }

    fun getString(maxLength: Int): String {
        val str = getString()
        return if (str.length > maxLength) {
            str.substring(0, maxLength)
        } else str
    }

    fun execute(exceptionConsumer: Consumer<Exception>, action: () -> Unit) {
        try {
            action()
            Assert.fail("never reach here")
        } catch (e: Exception) {
            log.error(e.message, e)
            exceptionConsumer.accept(e)
        }
    }

    fun exceptionTest(exceptionConsumer: (Exception) -> Unit, testAction: () -> Unit) {
        execute(exceptionConsumer, testAction)
    }


    fun <T> manufacturePojo(pojoClass: Class<T>?, vararg genericTypeArgs: Type?): T {
        return podamFactory.manufacturePojo(pojoClass, *genericTypeArgs)
    }

    @Before
    fun setUpMockK() = MockKAnnotations.init(this, relaxUnitFun = true)

    @JvmField
    @Rule
    val softly = JUnitSoftAssertions()

    private val podamFactory: PodamFactory = PodamFactoryImpl()

    val log by logger()
}