package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabel
import org.apache.ibatis.annotations.*


@Mapper
interface DispatchLabelRepo {

  @Insert(
    """
        INSERT INTO 
        config_dispatch_label(
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
            
        )
        VALUES(
            #{code},
            #{cnName},
            #{type},
            #{scope},
            #{specificResourcePool},
            #{isSpecificValue},
            #{specifiedValue},
            #{status},
            #{description},
            #{approveId},
            #{submitSystem},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
  )
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  fun insert(configDispatchLabel: ConfigDispatchLabel): Int


  @Insert(
    """
        INSERT INTO 
        config_dispatch_label(
            id,
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
            
        )
        VALUES(
            #{id},
            #{code},
            #{cnName},
            #{type},
            #{scope},
            #{specificResourcePool},
            #{isSpecificValue},
            #{specifiedValue},
            #{status},
            #{description},
            #{approveId},
            #{submitSystem},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
  )
  fun insertWithId(configDispatchLabel: ConfigDispatchLabel): Int

  @Update(
    """
        UPDATE config_dispatch_label
        SET 
            cn_name = #{cnName},
            type = #{type},
            scope = #{scope},
            specific_resource_pool = #{specificResourcePool},
            is_specific_value = #{isSpecificValue},
            specific_value = #{specifiedValue},
            status = #{status},
            description = #{description},
            submit_system = #{submitSystem},
            modifier = #{modifier},
            gmt_modified = now(),
            is_deleted = 0
        WHERE
            id = #{id}
    """
  )
  fun updateById(configDispatchLabel: ConfigDispatchLabel): Int


  @Select(
    """
        SELECT 
            id,
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label
        WHERE
            code = #{code}
            AND is_deleted = 0
    """
  )
  fun findByCode(@Param("code") code: String): ConfigDispatchLabel?

  @Select(
    """
        SELECT 
            id,
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label
        WHERE
            id = #{id}
    """
  )
  fun findById(@Param("id") id: Long): ConfigDispatchLabel?

  @Select(
    """
        SELECT 
            id,
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label
        WHERE
            code = #{code}
    """
  )
  fun findByCodeWithDeleted(@Param("code") code: String): ConfigDispatchLabel?


  @Update(
    """
        UPDATE config_dispatch_label
        SET
           gmt_modified = now(),
           modifier = #{modifier},
           is_deleted = 1
        WHERE
           code = #{code}
    """
  )
  fun deleteByCode(@Param("code") code: String, @Param("modifier") modifier: String): Int

  @Select(
    """
        SELECT 
            id,
            code,
            cn_name,
            type,
            scope,
            specific_resource_pool,
            is_specific_value,
            specific_value,
            status,
            description,
            approve_id,
            submit_system,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label
        WHERE
            is_deleted = 0
    """
  )
  fun findAllLabelWithoutDeleted(): List<ConfigDispatchLabel>
}