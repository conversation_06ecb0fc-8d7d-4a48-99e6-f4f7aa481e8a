package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeclarationPatchData
import org.apache.ibatis.annotations.*

@Mapper
interface ApREDeclarationPatchDataRepo {

    @Insert(
        """
        INSERT INTO
        apre_declaration_patch_data(
            gmt_create,
            gmt_modified,
            balance_type,
            region,
            stage,
            unit,
            site,
            declaration_patch,
            creator,
            modifier,
            priority,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{balanceType},
            #{region},
            #{stage},
            #{unit},
            #{site},
            #{declarationPatch},
            #{creator},
            #{modifier},
            #{priority},
            #{isDeleted}
        )
        """
    )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apREDeclarationPatchData: ApREDeclarationPatchData): Int

    @Update(
        """
        UPDATE
            apre_declaration_patch_data
        SET
            gmt_modified = now(),
            region = #{region},
            stage = #{stage},
            unit =  #{unit},
            site = #{site},
            declaration_patch = #{declarationPatch},
            modifier = #{modifier},
            priority = #{priority}
        WHERE
            id = #{id} and is_deleted = 'N'
    """
    )
    fun updateById(apREDeclarationPatchData: ApREDeclarationPatchData): Int

    @Update(
        """
        UPDATE
            apre_declaration_patch_data
        SET
            gmt_modified = now(),
            declaration_patch = #{declarationPatch},
            modifier = #{modifier}
        WHERE
            id = #{id} and is_deleted = 'N'
    """
    )
    fun updateDeclarationPatchById(
        @Param("id") id: Long,
        @Param("modifier") modifier: String,
        @Param("declarationPatch") declarationPatch: String
    ): Int

    @Select(
        """
        SELECT
            id,
            balance_type,
            region,
            stage,
            unit,
            site,
            declaration_patch,
            creator,
            modifier,
            priority,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            apre_declaration_patch_data
        WHERE
            id = #{id} and is_deleted = 'N'
    """
    )
    fun findById(@Param("id") id: Long): ApREDeclarationPatchData?

    @Select(
        """
        <script>
            SELECT
                id,
                balance_type,
                region,
                stage,
                unit,
                site,
                declaration_patch,
                creator,
                modifier,
                priority,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                apre_declaration_patch_data
            WHERE
                is_deleted = 'N'
                <if test="keyWords != null">
                    and (unit like "${'$'}{keyWords}%" or stage like "${'$'}{keyWords}%" or region like "${'$'}{keyWords}%")
                </if>
                <if test="region != null">
                    and region = #{region}
                </if>
                <if test="stage != null">
                    and stage = #{stage}
                </if>
                <if test="site != null">
                    and site = #{site}
                </if>
                <if test="unit != null">
                    and unit = #{unit}
                </if>
                <if test="balanceType != null">
                    and balance_type = #{balanceType}
                </if>
                <if test="ids != null">
                    and id IN
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="jsonSelector != null">
                    and ${'$'}{jsonSelector}
                </if>
        </script>
    """
    )
    fun findByCondition(
        @Param("keyWords") keyWords: String? = null,
        @Param("region") region: String? = null,
        @Param("stage") stage: String? = null,
        @Param("unit") unit: String? = null,
        @Param("site") site: String? = null,
        @Param("balanceType") balanceType: String? = null,
        @Param("ids") ids: List<Long>? = null,
        @Param("jsonSelector")jsonSelector: String? = null
    ): List<ApREDeclarationPatchData>

    @Select(
        """
        <script>
            SELECT
            id,
            balance_type,
            region,
            stage,
            unit,
            site,
            declaration_patch,
            creator,
            modifier,
            priority,
            gmt_create,
            gmt_modified,
            is_deleted
            FROM            
                apre_declaration_patch_data
            WHERE
                id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                AND is_deleted = 'N'
        </script>
    """
    )
    fun findByIds(@Param("ids") ids: List<Long>): List<ApREDeclarationPatchData>


    @Update(
        """
        UPDATE
            apre_declaration_patch_data
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier =#{modifier}
        WHERE 
            id = #{id}
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int
}