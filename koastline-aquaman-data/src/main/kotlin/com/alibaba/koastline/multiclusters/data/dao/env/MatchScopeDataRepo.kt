package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.GroupCount
import com.alibaba.koastline.multiclusters.data.vo.env.MatchScopeData
import org.apache.ibatis.annotations.*

@Mapper
interface MatchScopeDataRepo {

    @Insert("""
        INSERT INTO
        match_scope_data(
            gmt_create,
            gmt_modified,
            creator,
            modifier,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{creator},
            #{modifier},
            #{targetId},
            #{targetType},
            #{externalId},
            #{externalType},
            #{exclusions},
            #{restrictions},
            #{isDeleted}
        )
        """)
    fun insert(matchScopeData: MatchScopeData): Int

    @Select("""
        SELECT
            id,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            match_scope_data
        WHERE
            id = #{id} and is_deleted = 'N'
    """)
    fun findById(@Param("id") id: Long): MatchScopeData?


    @Select("""
        <script>
            SELECT
                id,
                target_id,
                target_type,
                external_id,
                external_type,
                exclusions,
                restrictions,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                match_scope_data
            WHERE
                is_deleted = 'N' and target_id IN
                <foreach collection="targetIds" item="id" open="(" separator="," close=")">
                        #{id}
                </foreach>
                AND target_type = #{targetType}
        </script>
    """)
    fun findByTargetIdsAndTargetType(
        @Param("targetIds") targetIds: List<Long>,
        @Param("targetType") targetType: String
    ): List<MatchScopeData>

    @Select("""
        SELECT
            id,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            match_scope_data
        WHERE
            target_id = #{targetId} and target_type = #{targetType} and is_deleted = 'N'
    """)
    fun findByTarget(@Param("targetId") targetId: Long, @Param("targetType") targetType: String): List<MatchScopeData>

    @Select("""
        SELECT
            id,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            match_scope_data
        WHERE
            external_type = #{externalType} and external_id = #{externalId} and target_type = #{targetType} and is_deleted = 'N'
    """)
    fun listByTargetTypeAndExternal(@Param("targetType") targetType: String, @Param("externalType") externalType: String, @Param("externalId") externalId: String): List<MatchScopeData>


    @Select(
        """
        <script>
            SELECT
                id,
                target_id,
                target_type,
                external_id,
                external_type,
                exclusions,
                restrictions,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                match_scope_data
            WHERE
                external_type = #{externalType} 
                and target_type = #{targetType} 
                and is_deleted = 'N'
                and external_id IN
                <foreach collection="externalIds" item="externalId" open="(" separator="," close=")">
                    #{externalId}
                </foreach>
        </script>
    """
    )
    fun listByTargetTypeAndExternalTypeAndExternalIds(
        @Param("targetType") targetType: String,
        @Param("externalType") externalType: String,
        @Param("externalIds") externalIds: List<String>
    ): List<MatchScopeData>

    /**
     * 对确定的 externalId 或非确定的 externalIdPrefix 进行过滤
     */
    @Select("""
        <script>
            SELECT
                COUNT(1) as count,
                external_id as group_name
            FROM            
                match_scope_data
            WHERE
                external_type = #{externalType} and target_type = #{targetType} and is_deleted = 'N'
                <choose>
                  <when test="externalIdPrefix != null and externalId != null">
                    and (external_id like "${'$'}{externalIdPrefix}%" or external_id = #{externalId}) 
                  </when>
                  <when test="externalIdPrefix != null and externalId = null">
                    and external_id like "${'$'}{externalIdPrefix}%" 
                  </when>
                  <when test="externalIdPrefix = null and externalId != null">
                    and external_id = #{externalId} 
                  </when>
                </choose>
            GROUP BY external_id
            ORDER BY external_id
        </script>
    """)
    fun countByTargetTypeAndExternalGroupWithPrefix(
        @Param("targetType") targetType: String,
        @Param("externalType") externalType: String,
        @Param("externalIdPrefix") externalIdPrefix: String?,
        @Param("externalId") externalId: String?,
    ): List<GroupCount>

    /**
     * 计算分组分页的limit后调用参数查询实际分组
     */
    @Select("""
        <script>
            SELECT
                id,
                target_id,
                target_type,
                external_id,
                external_type,
                exclusions,
                restrictions,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                match_scope_data
            WHERE
                external_type = #{externalType} and target_type = #{targetType} 
                and is_deleted = 'N' and external_id in 
                <foreach collection="externalIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
        </script>
    """)
    fun findByTargetTypeAndExternalIds(
        @Param("targetType") targetType: String,
        @Param("externalType") externalType: String,
        @Param("externalIds") externalIds: List<String>,
    ): List<MatchScopeData>

    @Select(
        """
        SELECT
            id,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            match_scope_data
        WHERE
            external_type = #{externalType} and external_id = #{externalId} and target_type = #{targetType} and target_id = #{targetId} and is_deleted = 'N'
    """)
    fun findByExternalAndTarget(@Param("externalType") externalType: String, @Param("externalId") externalId: String, @Param("targetType") targetType: String, @Param("targetId") targetId: Long): MatchScopeData?

    @Select(
        """
        SELECT
            id,
            target_id,
            target_type,
            external_id,
            external_type,
            exclusions,
            restrictions,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            match_scope_data
        WHERE
            external_type = #{externalType} and external_id = #{externalId} and target_type = #{targetType} and target_id = #{targetId} and exclusions = #{exclusions} and restrictions = #{restrictions} and is_deleted = 'N'
    """)
    fun findByExternalAndTargetAndRestrictionsExclusions(
        @Param("externalType") externalType: String,
        @Param("externalId") externalId: String,
        @Param("targetType") targetType: String,
        @Param("targetId") targetId: Long,
        @Param("restrictions") restrictions: String?,
        @Param("exclusions") exclusions: String?,

    ): MatchScopeData?


    @Select(
        """
        SELECT
            m.id,
            m.target_id,
            m.target_type,
            m.external_id,
            m.external_type,
            m.exclusions,
            m.restrictions,
            m.creator,
            m.modifier,
            m.gmt_create,
            m.gmt_modified,
            m.is_deleted
        FROM            
            match_scope_data m,
            resource_object_feature_import fi
        WHERE
            m.external_type = #{externalType} 
            and m.external_id = #{externalId} 
            and m.target_type = 'ResourceObjectFeatureImport' 
            and m.is_deleted = 'N'
            and m.target_id = fi.id 
            and fi.resource_object_feature_key = #{featureKey}
            and fi.is_deleted = 'N'
        FOR UPDATE
    """
    )
    fun findByExternalAndResourceObjectFeatureKey(
        @Param("externalType") externalType: String,
        @Param("externalId") externalId: String,
        @Param("featureKey") featureKey: String
    ): List<MatchScopeData>

    @Select(
        """
        SELECT
            m.id,
            m.target_id,
            m.target_type,
            m.external_id,
            m.external_type,
            m.exclusions,
            m.restrictions,
            m.creator,
            m.modifier,
            m.gmt_create,
            m.gmt_modified,
            m.is_deleted
        FROM            
            match_scope_data m,
            resource_object_feature f
        WHERE
            m.external_type = #{externalType} 
            and m.target_type = 'ResourceObjectFeature' 
            and m.is_deleted = 'N'
            and m.target_id = f.id 
            and f.resource_object_feature_key = #{featureKey}
            and f.is_deleted = 'N'
    """
    )
    fun findFeatureByExternalAndResourceObjectFeatureKey(
        @Param("externalType") externalType: String,
        @Param("featureKey") featureKey: String
    ): List<MatchScopeData>

    @Update("""
        UPDATE
            match_scope_data
        SET
            gmt_modified = now(),
            modifier = #{modifier},
            is_deleted = 'Y'
        WHERE
            id = #{id} and is_deleted = 'N' 
    """)
    fun deleteById(@Param("id") id: Long, @Param("modifier")  modifier: String): Int

    @Update("""
        UPDATE
            match_scope_data
        SET
            target_id = #{targetId},
            target_type = #{targetType},
            external_id = #{externalId},
            external_type = #{externalType},
            exclusions = #{exclusions},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id} and is_deleted = 'N' 
    """)
    fun updateTargetAndExternalById(
        matchScopeData: MatchScopeData
    ): Int

    @Update("""
        UPDATE
            match_scope_data
        SET
            gmt_modified = now(),
            modifier = #{modifier},
            is_deleted = 'Y'
        WHERE
            target_type = #{targetType} and target_id = #{targetId} and is_deleted = 'N' 
    """)
    fun deleteByTarget(@Param("targetId") targetId: Long, @Param("targetType") targetType: String, @Param("modifier")  modifier: String): Int

    @Update("""
        <script>
            UPDATE
                match_scope_data
            SET
                gmt_modified = now(),
                modifier = #{modifier},
                is_deleted = 'Y'
            WHERE
                is_deleted = "N" and 
                id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                        #{id}
                </foreach>
        </script>
    """)
    fun deleteByIds(@Param("idList") idList: List<Long>,@Param("modifier") modifier: String): Int
}