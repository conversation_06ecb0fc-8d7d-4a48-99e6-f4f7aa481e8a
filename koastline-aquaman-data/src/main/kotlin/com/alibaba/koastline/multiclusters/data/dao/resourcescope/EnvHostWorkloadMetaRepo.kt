package com.alibaba.koastline.multiclusters.data.dao.resourcescope

import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import com.alibaba.koastline.multiclusters.data.vo.resourcescope.EnvHostWorkloadMeta
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

@Mapper
interface EnvHostWorkloadMetaRepo {
    @Insert(
        """
        INSERT INTO 
        env_host_workload_meta(
            env_stack_id,
            app_name,
            resource_group,
            site, 
            unit,
            stage,
            cluster_id,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{envStackId},
            #{appName},
            #{resourceGroup},
            #{site},
            #{unit},
            #{stage},
            #{clusterId},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
        """
    )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(envHostWorkloadMeta: EnvHostWorkloadMeta): Int

    @Select(
        """
        SELECT
            id,
            env_stack_id,
            app_name,
            resource_group,
            site, 
            unit,
            stage,
            cluster_id,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_host_workload_meta
        WHERE
            env_stack_id = #{envStackId} and is_deleted = 'N'
    """
    )
    fun listByEnvStackId(@Param("envStackId") envStackId: String): List<EnvHostWorkloadMeta>

    @Update(
        """
        UPDATE 
            env_host_workload_meta
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            env_stack_id = #{envStackId}
    """
    )
    fun deleteByEnvStackId(@Param("envStackId") envStackId: String): Int
}