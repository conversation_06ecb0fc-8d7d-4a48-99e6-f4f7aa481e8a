package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.MetadataConstraint
import org.apache.ibatis.annotations.*

@Mapper
interface MetadataConstraintRepo {
    @Insert("""
        INSERT INTO 
        metadata_constraint(
            gmt_create, 
            gmt_modified, 
            site, 
            unit, 
            stage, 
            is_deleted,
            creator,
            modifier
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{site},
            #{unit},
            #{stage},
            #{isDeleted},
            #{creator},
            #{modifier}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(metadataConstraint: MetadataConstraint): Int

    @Select("""
        SELECT
            id,
            site, 
            unit, 
            stage,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            metadata_constraint
        WHERE
            site = #{site} and unit = #{unit} and stage = #{stage} and is_deleted = 'N'
    """)
    fun findByMetadataConstraint(@Param("site") site: String,@Param("unit") unit: String,@Param("stage") stage: String): MetadataConstraint?

    @Select("""
        SELECT
            id,
            site, 
            unit, 
            stage,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            metadata_constraint
        WHERE
            is_deleted = 'N'
    """)
    fun listAll(): List<MetadataConstraint>


    @Select(
        """
        <script>
            SELECT
                id,
                site, 
                unit, 
                stage,
                gmt_create,
                gmt_modified,
                is_deleted,
                creator,
                modifier
            FROM
                metadata_constraint
            WHERE
                is_deleted = 'N'
                AND unit = #{unit}
                AND stage = #{stage}
        </script>
    """
    )
    fun listByUnitAndStage(
        @Param("unit") unit: String,
        @Param("stage") stage: String,
    ): List<MetadataConstraint>


    @Select("""
        <script>
            SELECT
                id,
                site, 
                unit, 
                stage,
                gmt_create,
                gmt_modified,
                is_deleted,
                creator,
                modifier
            FROM
                metadata_constraint
            WHERE
                is_deleted = 'N'
                <if test="site != null">
                    and site = #{site}
                </if>
                <if test="unit != null">
                    and unit = #{unit}
                </if>
                <if test="stage != null">
                    and stage = #{stage}
                </if>
                <if test="keyWords != null">
                    and ( site like "${'$'}{keyWords}%" or stage like "${'$'}{keyWords}%" or unit like "${'$'}{keyWords}%" ) 
                </if>
        </script>
    """)
    fun listByProperties(
        @Param("site") site: String?,
        @Param("unit") unit: String?,
        @Param("stage") stage: String?,
        @Param("keyWords") keyWords:String?,
    ): List<MetadataConstraint>

    @Update("""
        UPDATE 
            metadata_constraint
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            id = #{id}
    """)
    fun deleteById(id: Long): Int

    @Update(
        """
        UPDATE
            metadata_constraint
        SET
            is_deleted = 'Y',
            modifier = #{modifier}
        WHERE
            site = #{site} and unit = #{unit} and stage = #{stage} and is_deleted = 'N'
    """
    )
    fun deleteByMetadataConstraint(
        @Param("site") site: String,
        @Param("unit") unit: String,
        @Param("stage") stage: String,
        @Param("modifier") modifier: String
    ): Int
}