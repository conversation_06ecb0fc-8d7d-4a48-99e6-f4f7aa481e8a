package com.alibaba.koastline.multiclusters.data.vo.env

import java.util.*

/**
 * <AUTHOR>
 */

/**
 * one unique key composed of @property[clusterId]
 */
data class ClusterProfileData(
        val id: Long?,
        val clusterId: String,
        val clusterName: String,
        val clusterIdExternal: String?=null, // cluster's external id(given by external clusterProvider)
        val clusterProvider: String, // alibaba, aws, google
        val clusterType: String, // alibaba-asi, aliyun-ack, aliyun-ask
        val region: String,
        val site: String, // SLA not guaranteed: "oc27,oc270,ot7,ot70,us44,us68", if multiple az exist, delimiter is ','
        val annotations: String?=null, // in json format
        val useType: String,
        val gmtCreate: Date,
        var gmtModified: Date,
        val isDeleted: String,
        val status: String = ClusterStatus.ONLINE.status, // 兼容原始可空 online / offline
        val creator:String,
        val modifier:String
)

/**
 * contains info about external cluster
 * one unique key is @property[clusterProfileId] which is koastline cluster id
 */
data class ExternalClusterInfoData(
        val id: Long?,
        val clusterProfileId: String, // foreign key: id of ClusterProfile
        val clusterIdExternal: String,
        val gmtCreate: Date,
        val gmtModified: Date
)

enum class ClusterStatus(val status: String) {
        ONLINE("online"),
        WAIT_ONLINE("wait_online"),
        OFFLINE("offline")
}