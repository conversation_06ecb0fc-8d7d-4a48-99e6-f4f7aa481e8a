package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeed
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

@Mapper
interface ApREDeedRepo {

    @Insert("""
        INSERT INTO
        apre_deed(
            gmt_create,
            gmt_modified,
            deed_key,
            env_level,
            app_name,
            env_id,
            env_stack_id,
            node_group,
            content,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{deedKey},
            #{envLevel},
            #{appName},
            #{envId},
            #{envStackId},
            #{nodeGroup},
            #{content},
            #{isDeleted}
        )
        """)
    fun insert(apREDeed: ApREDeed): Int

    @Select("""
        SELECT 
            id,
            deed_key,
            env_level,
            app_name,
            env_id,
            env_stack_id,
            node_group,
            content,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_deed
        WHERE
            deed_key = #{deedKey} and is_deleted = 'N'
    """)
    fun findByKey(@Param("deedKey") key: String): ApREDeed?
}