package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkload
import javax.lang.model.element.NestingKind
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update


@Mapper
interface RuntimeWorkloadRepo {
    @Insert("""
        INSERT INTO 
        runtime_workload(
            gmt_create, 
            gmt_modified,
            runtime_key,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            running_status,
            is_deleted,
            creator,
            modifier
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{runtimeKey},
            #{site},
            #{unit},
            #{stage},
            #{clusterId},
            #{status},
            #{runningStatus},
            #{isDeleted},
            #{creator},
            #{modifier}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(runtimeWorkload: RuntimeWorkload): Int

    @Select("""
        SELECT
            id,
            runtime_key,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            running_status,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            runtime_workload
        WHERE
            runtime_key = #{runtimeKey} and is_deleted = 'N'
    """)
    fun listByRuntimeKey(@Param("runtimeKey") runtimeKey: String): List<RuntimeWorkload>

    @Update(
        """
        UPDATE 
            runtime_workload
        SET
            status = #{status},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun updateStatusById(@Param("id") id: Long, @Param("status") status: String, @Param("modifier") modifier: String): Int


    @Update(
        """
        UPDATE 
            runtime_workload
        SET
            running_status = #{runningStatus},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun updateRunningStatusById(@Param("id") id: Long, @Param("runningStatus") runningStatus: String, @Param("modifier") modifier: String): Int

    @Update(
        """
        UPDATE 
            runtime_workload
        SET
            status = #{status},
            running_status = #{runningStatus},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun updateBothStatusById(@Param("id") id: Long, @Param("status") status: String, @Param("runningStatus") runningStatus: String, @Param("modifier") modifier: String): Int

    @Update(
        """
        UPDATE 
            runtime_workload
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int
}