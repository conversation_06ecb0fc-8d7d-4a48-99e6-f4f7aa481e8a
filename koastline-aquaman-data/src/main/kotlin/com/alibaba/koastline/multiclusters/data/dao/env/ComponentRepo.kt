package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ComponentData
import org.apache.ibatis.annotations.*

@Mapper
interface ComponentRepo {

    @Insert("""
        INSERT INTO
        component_data(
            gmt_create,
            gmt_modified,
            code,
            ref_object_id,
            ref_object_type,
            annotations,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{code},
            #{refObjectId},
            #{refObjectType},
            #{annotations},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(componentData: ComponentData): Int

    @Select("""
        SELECT
            id,
            code,
            ref_object_id,
            ref_object_type,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            component_data
        WHERE
            id = #{id}
        LIMIT 1
    """)
    fun findById(@Param("id") id: Long): ComponentData?

    @Select("""
        SELECT
            id,
            code,
            ref_object_id,
            ref_object_type,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            component_data
        WHERE
            ref_object_type = #{refObjectType} and ref_object_id = #{refObjectId} and is_deleted = 'N'
    """)
    fun findByRef(@Param("refObjectType") refObjectType: String, @Param("refObjectId") refObjectId: String): List<ComponentData>

    @Select("""
        SELECT
            id,
            code,
            ref_object_id,
            ref_object_type,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            component_data
        WHERE
            ref_object_type = #{refObjectType} and ref_object_id = #{refObjectId} and code = #{code} and is_deleted = 'N'
        LIMIT 1
    """)
    fun findByRefAndCode(@Param("refObjectType") refObjectType: String, @Param("refObjectId") refObjectId: String, @Param("code") code: String): ComponentData?

    @Select("""
        <script>
            SELECT
                id,
                code,
                ref_object_id,
                ref_object_type,
                annotations,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                component_data
            WHERE
                ref_object_type = #{refObjectType} and ref_object_id = #{refObjectId} 
                and code in 
                    <foreach collection="codeList" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                and is_deleted = 'N'
        </script>
    """)
    fun findByRefAndCodeList(@Param("refObjectType") refObjectType: String, @Param("refObjectId") refObjectId: String, @Param("codeList") codeList: List<String>): List<ComponentData>

    @Update("""
        UPDATE
            component_data
        SET
            annotations = #{annotations},
            gmt_modified = now()
        WHERE
            id = #{id}
        LIMIT 1
    """)
    fun updateById(@Param("id") id: Long, @Param("annotations") annotations: String): Int

    @Update("""
        UPDATE
            component_data
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            id = #{id}
        LIMIT 1
    """)
    fun deleteById(@Param("id") id: Long): Int
}