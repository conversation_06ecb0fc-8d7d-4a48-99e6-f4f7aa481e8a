package com.alibaba.koastline.multiclusters.data.vo

import java.util.*

/**
 * <AUTHOR>
 */
data class UserRoleData(val id: String?,
                        val gmtCreate: Date,
                        val gmtModified: Date,
                        val userName: String,
                        val role: String)

enum class Role {
    DEVOPS_SYSTEM_RIGHT, // eg. appstack
    CLUSTER_SRE_RIGHT,   // eg. cluster sre
    CLUSTER_AGENT,       // eg. crablet
    ADMIN_ROOT_RIGHT     // eg. koastline admin/sre
}
