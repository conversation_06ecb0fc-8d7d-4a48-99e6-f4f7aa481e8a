package com.alibaba.koastline.multiclusters.data.vo.resourceobj

import java.io.Serializable
import java.time.Instant
import java.util.*

data class UserLabel(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 范围ID
     */
    val externalId: String,
    /**
     * 范围类型
     */
    val externalType: String,
    /**
     * 标签
     */
    val labelName: String,
    /**
     * 标签值
     */
    val labelValue: String,
    /**
     * 提交系统
     */
    val submitter: String,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
