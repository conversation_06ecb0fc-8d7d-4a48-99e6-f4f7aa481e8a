package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ClusterEnvironmentRepo {
    @Insert("""
        INSERT INTO 
        cluster_environment_data(
            gmt_create, 
            gmt_modified, 
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{clusterEnvKey},
            #{clusterEnvName}, 
            #{clusterEnvCreator}, 
            #{managedClusterKey}, 
            #{envNamespace}, 
            #{region},
            #{az},
            #{envLabels},
            #{status}
        )
        """)
    fun insertClusterEnvironment(clusterEnvironmentData: ClusterEnvironmentData): Int

    @Select("""
        SELECT 
            id,
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status,
            gmt_create, 
            gmt_modified
        FROM 
            cluster_environment_data
        WHERE
            cluster_env_key = #{envKey}
    """)
    fun findClusterEnvironmentByEnvKey(envKey: String): ClusterEnvironmentData?

    @Select("""
         SELECT 
            id,
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status,
            gmt_create, 
            gmt_modified
        FROM 
            cluster_environment_data
    """)
    fun listAllClusterEnvironments(): MutableList<ClusterEnvironmentData>

    @Select("""
        SELECT 
            id,
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status,
            gmt_create, 
            gmt_modified
        FROM 
            cluster_environment_data
        WHERE
            managed_cluster_key = #{managedClusterKey}
    """)
    fun findClusterEnvironmentByManagedClusterKey(managedClusterKey: String): List<ClusterEnvironmentData>

    @Select("""
        <script>
            SELECT 
                id,
                cluster_env_key, 
                cluster_env_name, 
                cluster_env_creator, 
                managed_cluster_key, 
                env_namespace, 
                region,
                az,
                env_labels, 
                status,
                gmt_create, 
                gmt_modified
            FROM 
                cluster_environment_data
            WHERE
                cluster_env_key IN
                <foreach collection="envKeyList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
        </script>
    """)
    fun findClusterEnvironmentByEnvKeyList(@Param("envKeyList") envKeyList: List<String>): List<ClusterEnvironmentData?>?

    @Select("""
        SELECT 
            id,
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status,
            gmt_create, 
            gmt_modified
        FROM 
            cluster_environment_data
        WHERE
            region = #{region}
        AND 
            az = #{az}
    """)
    fun getClusterEnvironmentByRegionAndAZ(@Param("region") region: String, @Param("az") az: String): List<ClusterEnvironmentData?>?

    @Select("""
        SELECT 
            id,
            cluster_env_key, 
            cluster_env_name, 
            cluster_env_creator, 
            managed_cluster_key, 
            env_namespace, 
            region,
            az,
            env_labels, 
            status,
            gmt_create, 
            gmt_modified
        FROM 
            cluster_environment_data
        WHERE
            region = #{region}
        AND 
            az = #{az}
        AND 
            env_namespace = #{namespace}
    """)
    fun getClusterEnvironmentByRegionAndAZAndNamespace(@Param("region") region: String, @Param("az") az: String, @Param("namespace") namespace: String): List<ClusterEnvironmentData?>?

    @Update("""
        UPDATE 
            cluster_environment_data
        SET
            env_labels = #{envTags},
            gmt_modified = now()
        WHERE
            cluster_env_key = #{clusterEnvKey}
    """)
    fun updateClusterEnvironmentEnvTagsByClusterEnvKey(@Param("clusterEnvKey") clusterEnvKey: String, @Param("envTags") envTags: String): Int

    @Update("""
        UPDATE
           cluster_environment_data
        SET
           managed_cluster_key = #{managedClusterKey},
           gmt_modified = now()
        WHERE
           cluster_env_key = #{clusterEnvKey}
    """)
    fun updateClusterEnvironmentDependencyByManagedClusterKey(@Param("clusterEnvKey") clusterEnvKey: String, @Param("managedClusterKey") managedClusterKey: String): Int

    @Delete("""
        DELETE FROM 
            cluster_environment_data
        WHERE
            cluster_env_key = #{clusterEnvKey}
    """)
    fun deleteClusterEnvironmentClusterEnvKey(clusterEnvKey: String): Int
}