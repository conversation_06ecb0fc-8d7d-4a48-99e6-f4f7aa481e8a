package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.KvMap
import org.apache.ibatis.annotations.*


@Mapper
interface KvMapRepo {

  @Insert(
    """
        INSERT INTO 
        kv_map(
            type,
            key_name,
            value,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{type},
            #{keyName},
            #{value},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
  )
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  fun insert(kvMap: KvMap): Int

  @Update("""
        DELETE FROM kv_map
        WHERE
            id=#{id}
    """)
  fun deleteById(@Param("id") id: Long): Int

  @Select(
    """
        SELECT
            id,
            type,
            key_name,
            value,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            kv_map
        WHERE
            type = #{type}
            AND key_name = #{keyName}
            AND is_deleted = 'N'
    """
  )
  fun findByTypeAndKey(
    @Param("type") type: String,
    @Param("keyName") keyName: String,
  ): List<KvMap>

  @Select(
    """
        SELECT
            id,
            type,
            key_name,
            value,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            kv_map
        WHERE
            type = #{type}
            AND key_name = #{keyName}
            AND value = #{value}
            AND is_deleted = 'N'
        FOR UPDATE
    """
  )
  fun findByTypeAndKeyAndValue(
    @Param("type") type: String,
    @Param("keyName") keyName: String,
    @Param("value") value: String,
  ): KvMap?
}