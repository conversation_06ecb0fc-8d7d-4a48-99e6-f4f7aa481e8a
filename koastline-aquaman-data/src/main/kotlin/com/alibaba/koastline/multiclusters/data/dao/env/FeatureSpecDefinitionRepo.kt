package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.FeatureSpecDefinition
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

// feature_spec_definition_id uniqueKey
@Mapper
interface FeatureSpecDefinitionRepo {
    @Insert(
        """
        INSERT INTO
        feature_spec_definition(
            feature_spec_definition_id,
            ref_key,
            title,
            scope,
            spec_type,
            spec_code,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        )
        VALUES(
            #{featureSpecDefinitionId},
            #{refKey},
            #{title},
            #{scope},
            #{specType},
            #{specCode},
            #{sourceType},
            #{sourceId},
            #{versionType},
            #{versionId},
            #{annotations},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted},
            #{status},
            #{creator},
            #{modifier}
        )
        """
    )
    fun insert(featureSpecDefinition: FeatureSpecDefinition): Int


    @Select(
        """
        SELECT
            id,
            feature_spec_definition_id,
            ref_key,
            title,
            scope,
            spec_type,
            spec_code,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            feature_spec_definition
        WHERE
            feature_spec_definition_id = #{featureSpecDefinitionId} and is_deleted = "N"
        """
    )
    fun findByFeatureSpecDefinitionId(featureSpecDefinitionId: String): FeatureSpecDefinition?

    @Select(
        """
        SELECT
            id,
            feature_spec_definition_id,
            ref_key,
            title,
            scope,
            spec_type,
            spec_code,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            feature_spec_definition
        WHERE
            spec_code = #{specCode} and is_deleted = "N"
        """
    )
    fun findBySpecCode(specCode: String): FeatureSpecDefinition?

    @Select(
        """
        SELECT
            id,
            feature_spec_definition_id,
            ref_key,
            title,
            scope,
            spec_type,
            spec_code,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            feature_spec_definition
        WHERE
            title = #{title} and is_deleted = "N"
        """
    )
    fun listByTitle(@Param("title") title: String): List<FeatureSpecDefinition>

    @Select(
        """
        SELECT
            id,
            feature_spec_definition_id,
            ref_key,
            title,
            scope,
            spec_type,
            spec_code,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            feature_spec_definition
        WHERE
            ref_key = #{refKey} and is_deleted = "N"
        """
    )
    fun listByRefKey(@Param("refKey") refKey: String): List<FeatureSpecDefinition>

    @Select(
        """
        <script>
            SELECT
                id,
                feature_spec_definition_id,
                ref_key,
                title,
                scope,
                spec_type,
                spec_code,
                source_type,
                source_id,
                version_type,
                version_id,
                annotations,
                gmt_create,
                gmt_modified,
                is_deleted,
                status,
                creator,
                modifier
            FROM
                feature_spec_definition
            WHERE
                is_deleted = "N"
                <if test="title != null">
                    and title like "${'$'}{title}%"
                </if>
                 <if test="specCode != null">
                    and spec_code like "${'$'}{specCode}%"
                </if>
                <if test="scope != null">
                    and scope = #{scope}
                </if>
                <if test="status != null">
                    and status = #{status}
                </if>
                <if test="refKey != null">
                    and ref_key = #{refKey}
                </if>
            ORDER BY
                gmt_modified
            DESC
        </script>
    """
    )
    fun listFeatureSpecDefinitionsByProperties(
        @Param("title") title: String? = null,
        @Param("scope") scope: String? = null,
        @Param("status") status: String? = null,
        @Param("specCode") specCode: String? = null,
        @Param("refKey") refKey: String? = null
    ): List<FeatureSpecDefinition>

    /**
     * 不对SpecCode和创建关联人&时间戳进行修改 specCode唯一键
     *
     * @param featureSpecDefinition
     * @return
     */
    @Update(
        """
        UPDATE 
            feature_spec_definition
        SET
            title = #{title},
            scope = #{scope},
            status = #{status},
            source_type = #{sourceType},
            source_id = #{sourceId},
            version_type = #{versionType},
            version_id = #{versionId},
            annotations = #{annotations},
            gmt_modified = #{gmtModified},
            modifier = #{modifier}
        WHERE
            feature_spec_definition_id = #{featureSpecDefinitionId}
    """
    )
    fun updateByFeatureSpecDefinitionId(featureSpecDefinition: FeatureSpecDefinition): Int

    @Update(
        """
        UPDATE
            feature_spec_definition
        SET 
            is_deleted = 'Y', modifier = #{modifier}
        WHERE
            feature_spec_definition_id = #{featureSpecDefinitionId} and is_deleted= 'N'
    """
    )
    fun deleteByLabelFeatureSpecDefinitionId(
        @Param("featureSpecDefinitionId") featureSpecDefinitionId: String,
        @Param("modifier") modifier: String
    ): Int


    @Update(
        """
        UPDATE
            feature_spec_definition
        SET 
            is_deleted = 'Y', modifier = #{modifier}
        WHERE
            ref_key = #{refKey} and is_deleted= 'N'
    """
    )
    fun deleteByRefKey(
        @Param("refKey") refKey: String,
        @Param("modifier") modifier: String
    ): Int
}