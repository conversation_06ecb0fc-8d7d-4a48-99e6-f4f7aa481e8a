package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValue
import com.alibaba.koastline.multiclusters.data.vo.env.ConfigDispatchLabelValueWithMetadata
import org.apache.ibatis.annotations.*


@Mapper
interface DispatchLabelValueRepo {

  @Insert(
    """
        INSERT INTO 
        config_dispatch_label_value(
            app_name,
            label_code,
            label_value,
            submit_system,
            group_name,
            idc,
            unit,
            env,
            remark,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
            
        )
        VALUES(
            #{appName},
            #{labelCode},
            #{labelValue},
            #{submitSystem},
            #{groupName},
            #{idc},
            #{unit},
            #{env},
            #{remark},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
  )
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  fun insert(configDispatchLabelValue: ConfigDispatchLabelValue): Int

  @Insert(
    """
        INSERT INTO 
        config_dispatch_label_value(
            id,
            app_name,
            label_code,
            label_value,
            submit_system,
            group_name,
            idc,
            unit,
            env,
            remark,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
            
        )
        VALUES(
            #{id},
            #{appName},
            #{labelCode},
            #{labelValue},
            #{submitSystem},
            #{groupName},
            #{idc},
            #{unit},
            #{env},
            #{remark},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
  )
  fun insertWithId(configDispatchLabelValue: ConfigDispatchLabelValue): Int

  @Update(
    """
        UPDATE config_dispatch_label_value
        SET 
            remark = #{remark},
            label_value = #{labelValue},
            modifier = #{modifier},
            gmt_modified = now(),
            is_deleted = 0
        WHERE
            id = #{id}
    """
  )
  fun updateById(configDispatchLabelValue: ConfigDispatchLabelValue): Int

  @Select(
    """
        SELECT
            id,
            app_name,
            label_code,
            label_value,
            submit_system,
            group_name,
            idc,
            unit,
            env,
            remark,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label_value
        WHERE
            id = #{id}
    """
  )
  fun selectById(@Param("id") id: Long): ConfigDispatchLabelValue?

  @Select(
    """
        SELECT
            id,
            app_name,
            label_code,
            label_value,
            submit_system,
            group_name,
            idc,
            unit,
            env,
            remark,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            config_dispatch_label_value
        WHERE
            app_name = #{appName} and
            group_name = #{groupName} and
            idc = #{idc} and
            unit = #{unit} and
            env = #{env} and
            label_code = #{labelCode}
    """
  )
  fun selectBy4ElementsWithDeleted(
    @Param("appName") appName: String,
    @Param("groupName") groupName: String,
    @Param("idc") idc: String,
    @Param("unit") unit: String,
    @Param("env") env: String,
    @Param("labelCode") labelCode: String
  ): ConfigDispatchLabelValue?

  @Select(
    """
        SELECT
            v.id,
            v.app_name,
            v.label_code,
            v.label_value,
            v.submit_system,
            v.group_name,
            v.idc,
            v.unit,
            v.env,
            v.remark,
            v.creator,
            v.modifier,
            v.gmt_create,
            v.gmt_modified,
            v.is_deleted,
            l.type,
            l.cn_name
        FROM
            config_dispatch_label_value v
        JOIN 
            config_dispatch_label l
        ON
            v.app_name = #{appName} and
            v.is_deleted = 0 and
            v.label_code = l.code
            
    """
  )
  fun selectByAppNameWithLabelMetadata(
    @Param("appName") appName: String,
  ): List<ConfigDispatchLabelValueWithMetadata>


  @Update(
    """
        UPDATE config_dispatch_label_value
        SET
           gmt_modified = now(),
           modifier = #{modifier},
           is_deleted = 1
        WHERE
           id = #{id}
    """
  )
  fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int

}