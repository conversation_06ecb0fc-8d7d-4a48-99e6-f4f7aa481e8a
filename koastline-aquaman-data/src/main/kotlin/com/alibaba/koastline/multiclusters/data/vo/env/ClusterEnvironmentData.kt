package com.alibaba.koastline.multiclusters.data.vo.env

import java.util.*

/**
 * <AUTHOR>
 */

/**
 * cluster environment meta data
 *  [ClusterEnvironmentData] to [KManagedClusterData] has many-to-1 relationship
 * unique key is @property[clusterEnvKey]
 * @property[envLabels] serves as meta data in json which extends virtual environment. If the cluster's provider is
 * other than alibaba, the way to locate the cluster is placed into this envMeta.
 */
data class ClusterEnvironmentData(
    val id: Long?,
    val clusterEnvKey: String?, // autogenerated(unique)
    val clusterEnvName: String?,  // env' name
    val clusterEnvCreator: String?, // who created the cluster environment(eg. koastline)
    val managedClusterKey: String, // foreign key: managedClusterIdentity of KManagedCluster
    val envNamespace: String, // the namespace in k8s for the cluster environment
    val region: String, // env region
    val az: String,  // env az
    val envLabels: String, // envLevel/az/unit or others could be put inside this json string, in order to locate the env
    val status: String, // created, deleted
    val gmtCreate: Date,
    val gmtModified: Date
)

/**
 * defines what to be installed in a namespace belongs to k8s cluster
 * unique key is a combination of @property[managedClusterKey] and @property[clusterProfileId]
 */
data class KManagedClusterData(
    val id: Long?,
    val managedClusterKey: String,
    val clusterProfileId: Long?, // foreign key: id of ClusterProfile
    val gatewayId: Long,
    val sysEnvNamespace: String, // cse-system/cse-system-pre/or ...
    val systemComponentsId: Long, // foreign key of SystemComponentsData
    val region: String,
    val managedClusterLabels: String, // put stage related stuff inside this json string field
    val status: String, // created, deleted
    val gmtCreate: Date,
    val gmtModified: Date
) {
    companion object {
        fun createSimplifyKManageCluster(
            managedClusterKey: String,
            region: String,
            status: String,
            gmtCreate: Date,
            gmtModified: Date
        ): KManagedClusterData {
            return KManagedClusterData(
                managedClusterKey = managedClusterKey,
                gatewayId = -1,
                sysEnvNamespace = defaultNamespace,
                systemComponentsId = 0,
                region = region,
                managedClusterLabels = "{}",
                status = status,
                gmtModified = gmtModified,
                gmtCreate = gmtCreate,
                clusterProfileId = -1,
                id = null
            )
        }
        const val defaultNamespace = "cse-system"
    }
}

/**
 * keeps configs for each cluster environment
 * unique key is @property[clusterEnvKey]
 */
data class ClusterEnvironmentConfigData(
    val id: Long?,
    val clusterEnvKey: String,
    val annotations: String, // key-value pairs in json string format
    val gmtCreate: Date,
    val gmtModified: Date
)

/**
 * declares what to be ready inside KManagedCluster
 */
data class SystemComponentsData(
    val id: Long?,
    val managedClusterKey: String,
    val annotations: String, // key-value pairs in json string format
    val gmtCreate: Date,
    val gmtModified: Date
)

/**
 * contains binding related information about the cluster environment
 * unique key is @property[externalId] and @property[externalIdType]
 */
data class ClusterEnvironmentBindingData(
    val id: Long?,
    val clusterEnvKey: String,
    val externalId: String, // could be productLine tree (1-3-4)
    val externalIdType: String, // could be aone-productLine
    val gmtCreate: Date,
    val gmtModified: Date,
    val selector: String?,
    val isDeleted: String = "N"
) {
    constructor(
        id: Long?,
        clusterEnvKey: String,
        externalId: String,
        externalIdType: String,
        gmtCreate: Date,
        gmtModified: Date
    ) : this(
        id, clusterEnvKey, externalId, externalIdType, gmtCreate, gmtModified, null, "N"
    )
}

/**
 * contains az to site mapping relationships
 * unique key is a combination of @property[az] and @property[site]
 */
data class AvailableZoneSiteMappingData(
    val id: Long?,
    val availableZone: String,
    val site: String,
    val region: String,
    val gmtCreate: Date,
    val gmtModified: Date
)