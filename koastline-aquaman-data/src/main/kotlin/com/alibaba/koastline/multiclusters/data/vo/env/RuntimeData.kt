package com.alibaba.koastline.multiclusters.data.vo.env

import java.time.Instant
import java.util.*

data class RuntimeData(
    val id: Long? = null,
    /**
     * Runtime唯一键
     */
    val runtimeKey: String,
    /**
     * 应用名
     */
    val appName: String,
    /**
     * 环境StackId
     */
    val envStackId: String?,
    /**
     * 分组
     */
    val resourceGroupName: String,
    /**
     * 类型
     */
    val type: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val creator: String,
    val modifier: String,
    val isDeleted: String = "N"
)

data class RuntimeWorkload(
    val id: Long? = null,
    val runtimeKey: String,
    /**
     * 站点
     */
    val site: String,
    /**
     * 单元
     */
    val unit: String,
    /**
     * 用途
     */
    val stage: String,
    /**
     * 集群ID
     */
    val clusterId: String,
    /**
     * 状态
     * ONLINE｜OFFLINE
     */
    val status: String,
    /**
     * 运行时状态
     * UNINSTALLED｜INSTALLED
     */
    val runningStatus: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val creator: String,
    val modifier: String,
    val isDeleted: String = "N"
)

data class RuntimeWorkloadMeta(
    val id: Long? = null,
    val runtimeWorkloadId: String,
    val appName: String,
    val resourceGroup: String,
    /**
     * 站点
     */
    val site: String,
    /**
     * 单元
     */
    val unit: String,
    /**
     * 用途
     */
    val stage: String,
    /**
     * 集群ID
     */
    val clusterId: String,
    /**
     * 运行时状态
     * UNINSTALLED｜INSTALLED
     */
    val status: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val creator: String,
    val modifier: String,
    val isDeleted: String = "N",
    val nameSpace: String
) {
    /**
     * 六元组，应用、分组、站点、单元、用途、集群ID，顺序不能乱
     */
    fun toSixMetaTuple(): List<String>{
        return listOf(
            appName,resourceGroup,site,unit,stage,clusterId
        )
    }
}