package com.alibaba.koastline.multiclusters.data.dao.resource

import com.alibaba.koastline.multiclusters.data.vo.resource.Resource
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * @author:    <EMAIL>
 * @date:    2024/5/14 4:34 PM
 */
@Mapper
interface ResourceRepo {
    @Insert("""
        INSERT INTO 
        resource(
            kind,
            name,
            service_provider,
            controller,
            need_recycling,
            baseline_box_id,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{kind},
            #{name},
            #{serviceProvider},
            #{controller},
            #{needRecycling},
            #{baselineBoxId},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resource: Resource): Int

    @Select("""
        SELECT 
            id,
            kind,
            name,
            service_provider,
            controller,
            need_recycling,
            baseline_box_id,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource
        WHERE
            id = #{id}
    """)
    fun findById(id: Long): Resource?

    @Select("""
        SELECT 
            id,
            kind,
            name,
            service_provider,
            controller,
            need_recycling,
            baseline_box_id,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource
        WHERE
            kind = #{kind} and name = #{name}
    """)
    fun findByKindAndName(@Param("kind")kind: String, @Param("name")name: String): Resource?

    @Update("""
        UPDATE resource
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(id: Long): Int

    @Update("""
        UPDATE resource
        SET
           baseline_box_id = #{baseLineBoxId},
           gmt_modified = now()
        WHERE
            id=#{id}
    """)
    fun updateBaselineBoxIdById(@Param("id")id: Long,@Param("baseLineBoxId") baseLineBoxId: Long)
}