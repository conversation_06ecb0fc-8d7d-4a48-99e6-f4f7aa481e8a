package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.EnvLevelStageMappingData
import org.apache.ibatis.annotations.*
import java.util.StringJoiner

/**
 * <AUTHOR>
 * 环境级别与用途标映射关系
 */
@Mapper
interface EnvLevelStageMappingDataRepo {
    @Insert("""
        INSERT INTO
        env_level_stage_mapping(
            gmt_create,
            gmt_modified,
            env_level,
            stage,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{envLevel},
            #{stage},
            #{isDeleted}
        )
        """)
    fun insert(envLevelStageMappingData: EnvLevelStageMappingData): Int

    @Select("""
        SELECT 
            id,
            env_level,
            stage,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_level_stage_mapping
        WHERE
            env_level = #{envLevel} and is_deleted = 'N'
    """)
    fun queryByEnvLevel(@Param("envLevel") envLevel: String): List<EnvLevelStageMappingData>

    @Select("""
         SELECT 
            id,
            env_level,
            stage,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_level_stage_mapping
        WHERE
             is_deleted = 'N'
    """)
    fun listAll(): List<EnvLevelStageMappingData>

    @Update("""
        UPDATE
            env_level_stage_mapping
        SET 
            env_level = #{envLevel},
            stage = #{stage},
            gmt_modified = now()
        WHERE
            id = #{id} and is_deleted = 'N'
    """)
    fun updateById(@Param("id") id: Long, @Param("env_level") envLevel: String,
                                                               @Param("stage") stage: String): Int

    @Update("""
        UPDATE
            env_level_stage_mapping
        SET
            is_deleted = 'Y'
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int

    @Update("""
        UPDATE
            env_level_stage_mapping
        SET
            is_deleted = 'Y'
        WHERE 
            env_level = #{envLevel}
    """)
    fun deleteByEnvLevel(@Param("envLevel") envLevel: String): Int
}