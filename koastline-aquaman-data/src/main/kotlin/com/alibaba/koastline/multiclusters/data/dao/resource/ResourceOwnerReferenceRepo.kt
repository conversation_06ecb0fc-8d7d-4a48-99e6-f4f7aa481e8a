package com.alibaba.koastline.multiclusters.data.dao.resource

import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceOwnerReference
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * @author:    <EMAIL>
 * @date:    2024/5/14 4:34 PM
 */
@Mapper
interface ResourceOwnerReferenceRepo {
    @Insert("""
        INSERT INTO 
        resource_owner_reference(
            sub_resource_id,
            owner_ref_kind,
            owner_ref_name,
            block_owner_deletion,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{subResourceId},
            #{ownerRefKind},
            #{ownerRefName},
            #{blockOwnerDeletion},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourceOwnerReference: ResourceOwnerReference): Int

    @Select("""
        SELECT 
            id,
            sub_resource_id,
            owner_ref_kind,
            owner_ref_name,
            block_owner_deletion,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_owner_reference
        WHERE
            id = #{id}
    """)
    fun findById(id: Long): ResourceOwnerReference?

    @Update("""
        UPDATE resource_owner_reference
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(id: Long): Int

    @Select("""
        SELECT 
            id,
            sub_resource_id,
            owner_ref_kind,
            owner_ref_name,
            block_owner_deletion,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_owner_reference
        WHERE
            owner_ref_kind = #{ownerRefKind} AND owner_ref_name = #{ownerRefName} and is_deleted = 'N'
    """)
    fun querySubResourceByOwnerReference(@Param("ownerRefKind") ownerRefKind: String,@Param("ownerRefName") ownerRefName: String): List<ResourceOwnerReference>

    @Select("""
        SELECT 
            id,
            sub_resource_id,
            owner_ref_kind,
            owner_ref_name,
            block_owner_deletion,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_owner_reference
        WHERE
            sub_resource_id = #{subResourceId} and is_deleted = 'N'
    """)
    fun queryOwnerReferenceBySubResourceId(@Param("subResourceId") subResourceId: Long): List<ResourceOwnerReference>
}