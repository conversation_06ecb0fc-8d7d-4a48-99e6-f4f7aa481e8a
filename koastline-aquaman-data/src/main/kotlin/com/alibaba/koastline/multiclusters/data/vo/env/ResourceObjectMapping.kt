package com.alibaba.koastline.multiclusters.data.vo.env

import java.time.Instant
import java.util.*

/**
 * TODO
 *
 * @property id
 * @property resourceObjectFeatureKey
 * @property resourceObjectFeatureTargetId
 * @property creator
 * @property modifier
 * @property gmtCreate
 * @property gmtModified
 * @property isDeleted
 */
data class ResourceObjectMapping(
    val id: Long?,
    val resourceObjectFeatureKey: String,
    val resourceObjectFeatureTargetId: Long,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)