package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.KoastlineGatewayData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface KoastlineGatewayRepo {

    @Insert("""
        INSERT INTO 
        koastline_gateway(
            gmt_create,
            gmt_modified,
            managed_cluster_key,
            namespace,
            host,
            port,
            protocol
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{managedClusterKey},
            #{namespace},
            #{host},
            #{port},
            #{protocol}
        )
    """)
    fun insertGateway(koastlineGateway: KoastlineGatewayData): Int

    @Select("""
        SELECT 
            id,
            managed_cluster_key,
            namespace,
            host,
            port,
            protocol,            
            gmt_create,
            gmt_modified
        FROM
            koastline_gateway 
        WHERE
            id = #{id}
    """)
    fun findGatewayByGatewayId(id: Long): KoastlineGatewayData?

    @Select("""
        SELECT 
            id,
            managed_cluster_key,
            namespace,
            host,
            port,
            protocol,            
            gmt_create,
            gmt_modified
        FROM
            koastline_gateway 
        WHERE
            managed_cluster_key = #{key}
    """)
    fun findGatewayByKManagedClusterKey(key: String): KoastlineGatewayData?

    @Select("""
        <script>
            SELECT 
                id,
                managed_cluster_key,
                namespace,
                host,
                port,
                protocol,            
                gmt_create,
                gmt_modified
            FROM
                koastline_gateway 
            WHERE
                id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
        </script>
        """)
    fun findGatewaysByGatewayIds(@Param("ids") ids: List<Long>): List<KoastlineGatewayData?>?

    @Update("""
        UPDATE koastline_gateway
        SET
            namespace = #{namespace},
            protocol = #{protocol},
            gmt_modified = now()
        WHERE
            id= #{gatewayId}
    """)
    fun updateGatewayByGatewayId(@Param("gatewayId") gatewayId: Long, @Param("namespace") namespace: String, @Param("protocol") protocol: String): Int?

    @Select("""
        SELECT 
            id,
            managed_cluster_key,
            namespace,
            host,
            port,
            protocol,            
            gmt_create,
            gmt_modified
        FROM
            koastline_gateway 
        WHERE
            namespace = #{namespace}
        AND 
            host = #{host}
        AND
            port = #{port}
    """)
    fun findGatewayByHostPortNamespace(namespace: String, host: String, port: String): KoastlineGatewayData?

    @Delete("""
        DELETE FROM
            koastline_gateway 
        WHERE
            id = #{id}
    """)
    fun deleteGatewayByGatewayId(id: String): Int
}