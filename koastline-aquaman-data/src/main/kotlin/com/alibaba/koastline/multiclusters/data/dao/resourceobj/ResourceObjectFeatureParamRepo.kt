package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureParam
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ResourceObjectFeatureParamRepo {

    @Insert("""
        INSERT INTO 
        resource_object_feature_param(
            resource_object_feature_key,
            title,
            code,
            type,
            required,
            optional_value,
            value_constraint
        )
        VALUES(
            #{resourceObjectFeatureKey},
            #{title},
            #{code},
            #{type},
            #{required}
            #{optionalValue},
            #{valueConstraint}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourceObjectFeatureParam: ResourceObjectFeatureParam): Int

    @Select("""
        SELECT 
            id,
            resource_object_feature_key,
            title,
            code,
            type,
            required,
            optional_value,
            value_constraint
        FROM
            resource_object_feature_param
        WHERE
            id = #{id}
    """)
    fun findById(@Param("id") id: Long): ResourceObjectFeatureParam?

    @Select("""
        SELECT 
            id,
            resource_object_feature_key,
            title,
            code,
            type,
            required,
            optional_value,
            value_constraint
        FROM
            resource_object_feature_param
        WHERE
            resource_object_feature_key = #{resourceObjectFeatureKey}
    """)
    fun listByResourceObjectFeatureKey(resourceObjectFeatureId: Long): List<ResourceObjectFeatureParam>

    @Update("""
        UPDATE resource_object_feature_param
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(@Param("id") id: Long): Int
}