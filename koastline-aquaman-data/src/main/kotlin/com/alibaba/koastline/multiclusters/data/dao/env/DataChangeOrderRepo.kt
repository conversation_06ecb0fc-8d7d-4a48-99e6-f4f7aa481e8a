package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.DataChangeOrder
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import java.util.*

@Mapper
interface DataChangeOrderRepo {
    @Insert(
        """
        INSERT INTO 
        data_change_order(
            change_event,
            change_data_input,
            start_time,
            finish_time,
            operator,
            success,
            result,
            change_error_msg,
            trace_id
        )
        VALUES(
            #{changeEvent}, 
            #{changeDataInput},
            #{startTime},
            #{finishTime}, 
            #{operator}, 
            #{success}, 
            #{result},
            #{changeErrorMsg},
            #{traceId}
        )
        """
    )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(dataChangeOrder: DataChangeOrder): Int

    @Select(
        """
        SELECT 
            id,
            change_event,
            change_data_input,
            start_time,
            finish_time,
            operator,
            success,
            result,
            change_error_msg,
            trace_id
        FROM
            data_change_order
        WHERE
           id = #{id}
        """
    )
    fun find(@Param("id") id: Long): DataChangeOrder

    @Select(
        """
        SELECT 
            id,
            change_event,
            change_data_input,
            start_time,
            finish_time,
            operator,
            success,
            result,
            change_error_msg,
            trace_id
        FROM
            data_change_order
        WHERE
           change_event = #{changeEvent} and start_time > #{changeEvent} and finish_time < #{finishTime}
        Limit
            #{limit}
        """
    )
    fun listByChangeEvent(
        @Param("changeEvent") changeEvent: String,
        @Param("startTime") startTime: Date,
        @Param("finishTime") finishTime: Date,
        @Param("limit") limit: Int = 10
    ): List<DataChangeOrder>


    @Select(
        """
        SELECT 
            id,
            change_event,
            change_data_input,
            start_time,
            finish_time,
            operator,
            success,
            result,
            change_error_msg,
            trace_id
        FROM
            data_change_order
        WHERE
            operator = #{operator}
        Limit
            #{limit}
        """
    )
    fun listByOperator(@Param("operator") operator: String, @Param("limit") limit: Int = 10): List<DataChangeOrder>

    @Select("""
        <script>
            SELECT
                id,
                change_event,
                change_data_input,
                start_time,
                finish_time,
                operator,
                success,
                result,
                change_error_msg,
                trace_id
            FROM
                data_change_order
            <trim prefix="WHERE" prefixOverrides="and|or">
                <if test="changeEvent != null">
                    and change_event = #{changeEvent}
                </if>
                <if test="startTime != null">
                    and start_time > #{startTime}
                </if>
                <if test="operator != null">
                    and operator = #{operator}
                </if>
                <if test="finishTime != null">
                    and #{finishTime} > finish_time
                </if>
                <if test="success != null">
                    and success = #{success}
                </if>
                <if test="traceId != null">
                    and trace_id = #{traceId}
                </if>
            </trim>
        </script>
    """)
    fun listByProperties(
        @Param("changeEvent") changeEvent: String? = null,
        @Param("startTime") startTime: Date? = null,
        @Param("finishTime") finishTime: Date? = null,
        @Param("operator") operator: String? = null,
        @Param("success") success: String? = null,
        @Param("traceId") traceId: String? = null,
    ): List<DataChangeOrder>
}