package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentConfigData
import org.apache.ibatis.annotations.*
import java.util.ListResourceBundle

/**
 * <AUTHOR>
 */
@Mapper
interface ClusterEnvironmentConfigRepo {

    @Insert("""
        INSERT INTO
        cluster_environment_config_data(
            gmt_create,
            gmt_modified,
            cluster_env_key,
            annotations
        )
        VALUES(
            now(),
            now(),
            #{clusterEnvKey},
            #{annotations}
        )
    """)
    fun createClusterEnvironmentCong(@Param("clusterEnvKey") clusterEnvKey: String, @Param("annotations") annotations: String): Int?

    @Update("""
        UPDATE 
            cluster_environment_config_data
        SET
            annotations = #{annotations},
            gmt_modified = now()
        WHERE
            cluster_env_key = #{clusterEnvKey}
    """)
    fun updateClusterEnvironmentConfig(@Param("clusterEnvKey") clusterEnvKey: String, @Param("annotations") annotations: String): Int?

    @Select("""
        SELECT
            id,
            cluster_env_key,
            annotations,
            gmt_create,
            gmt_modified
        FROM
            cluster_environment_config_data
        WHERE
            cluster_env_key = #{clusterEnvKey}
    """)
    fun queryClusterEnvironmentConfig(clusterEnvKey: String): ClusterEnvironmentConfigData?

    @Select("""
        <script>
            SELECT
                id,
                cluster_env_key,
                annotations,
                gmt_create,
                gmt_modified
            FROM
                cluster_environment_config_data
            WHERE
                cluster_env_key IN
                <foreach collection="clusterEnvKeyList" item="clusterEnvKey" open="(" separator="," close=")">
                   #{clusterEnvKey}
                </foreach>
        </script>
    """)
    fun queryClusterEnvironmentConfigByClusterEnvKeyList(@Param("clusterEnvKeyList") clusterEnvKeyList: List<String>): List<ClusterEnvironmentConfigData>

    @Delete("""
        DELETE
        FROM
            cluster_environment_config_data
        WHERE
            cluster_env_key = #{clusterEnvKey}
    """)
    fun deleteClusterEnvironmentConfigByclusterEnvKey(clusterEnvKey: String): Int?
}