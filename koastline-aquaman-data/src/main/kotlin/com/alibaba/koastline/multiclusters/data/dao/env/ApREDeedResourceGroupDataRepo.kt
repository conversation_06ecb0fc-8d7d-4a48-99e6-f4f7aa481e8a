package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREDeedResourceGroupBindingData
import com.alibaba.koastline.multiclusters.data.vo.env.EnvLevelStageMappingData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 * 运行时环境声明与分组映射关系
 */
@Mapper
interface ApREDeedResourceGroupDataRepo {
    @Insert("""
        INSERT INTO
        apre_deed_resource_group_binding_data(
            gmt_create,
            gmt_modified,
            apre_deed_key,
            resource_group,
            app_name
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{apREDeedKey},
            #{resourceGroup},
            #{appName}
        )
        """)
    fun insert(apREDeedResourceGroupBindingData: ApREDeedResourceGroupBindingData): Int

    @Select("""
        SELECT 
            id,
            apre_deed_key,
            resource_group,
            app_name,
            gmt_create,
            gmt_modified
        FROM
            apre_deed_resource_group_binding_data
        WHERE
            resource_group = #{resourceGroup}
    """)
    fun queryByResourceGroup(@Param("resourceGroup") resourceGroup: String): List<ApREDeedResourceGroupBindingData>

    @Delete("""
        DELETE FROM
            apre_deed_resource_group_binding_data
        WHERE
            resource_group = #{resourceGroup} 
    """)
    fun deleteByResourceGroup(@Param("resourceGroup") resourceGroup: String): Int

    @Delete("""
        DELETE FROM
            apre_deed_resource_group_binding_data
        WHERE
            id = #{id} 
    """)
    fun deleteById(@Param("id") id: Long): Int
}