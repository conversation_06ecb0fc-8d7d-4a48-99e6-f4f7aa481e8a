package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.AvailableZoneSiteMappingData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface AvailableZoneSiteMappingRepo {
    @Insert("""
        INSERT INTO
        available_zone_site_mapping_data(
            gmt_create,
            gmt_modified,
            available_zone,
            site,
            region
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{availableZone},
            #{site},
            #{region}
        )
        """)
    fun insertAvailableZoneSiteMapping(azSiteMapping: AvailableZoneSiteMappingData): Int?

    @Select("""
        SELECT 
            id,
            available_zone,
            site,
            region,
            gmt_create,
            gmt_modified
        FROM
            available_zone_site_mapping_data
        WHERE
            available_zone = #{availableZone}
        AND 
            region = #{region}
    """)
    fun queryAvailableZoneSiteMappingByAvailableZoneAndRegion(@Param("availableZone") availableZone: String,
                                      @Param("region") region: String): AvailableZoneSiteMappingData?

    @Select("""
         SELECT 
            id,
            available_zone,
            site,
            region,
            gmt_create,
            gmt_modified
        FROM
            available_zone_site_mapping_data
    """)
    fun listAvailableZoneSiteMapping(): List<AvailableZoneSiteMappingData>

    @Update("""
        UPDATE
            available_zone_site_mapping_data
        SET 
            site = #{site},
            gmt_modified = now()
        WHERE
            available_zone = #{availableZone}
        AND 
            region = #{region}
    """)
    fun updateAvailableZoneSiteMappingByAvailableZoneAndRegion(@Param("availableZone") availableZone: String,
                                                      @Param("site") site: String,
                                                      @Param("region") region: String): Int?
    @Update("""
        UPDATE
            available_zone_site_mapping_data
        SET 
            availableZone = #{availableZone},
            site = #{site},
            region = #{region},
            gmt_modified = now()
        WHERE
            id = #{id}      
    """)
    fun updateAvailableZoneSiteMappingById(@Param("id") id: String,
                                           @Param("availableZone") availableZone: String,
                                           @Param("region") region: String,
                                           @Param("site") site: String): Int?

    @Delete("""
        DELETE
        FROM
            available_zone_site_mapping_data
        WHERE 
            available_zone = #{availableZone}
        AND 
            region = #{region}
            
    """)
    fun deleteAvailableZoneSiteMappingByAvailableZoneAndRegion(@Param("availableZone") availableZone: String,
                                                               @Param("region") region: String): Int
    @Delete("""
        DELETE 
        FROM
            available_zone_site_mapping_data
        WHERE 
            id = #{id}
    """)
    fun deleteAvailableZoneSiteMappingById(@Param("id") id: String):Int
}