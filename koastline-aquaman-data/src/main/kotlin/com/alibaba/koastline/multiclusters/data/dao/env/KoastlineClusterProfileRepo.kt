package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ClusterProfileData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface KoastlineClusterProfileRepo {

    @Insert(
        """
        INSERT INTO
        cluster_profile(
            gmt_create,
            gmt_modified,
            cluster_id,
            cluster_name,
            cluster_id_external,
            cluster_provider,
            cluster_type,
            region,
            site,
            annotations,
            use_type,
            is_deleted,
            status,
            creator,
            modifier
        )   
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{clusterId},
            #{clusterName},
            #{clusterIdExternal},
            #{clusterProvider},
            #{clusterType},
            #{region},
            #{site},
            #{annotations},
            #{useType},
            #{isDeleted},
            #{status},
            #{creator},
            #{modifier}
        )
    """
    )
    fun insertClusterProfile(clusterProfileData: ClusterProfileData): Int

    @Update(
        """
        UPDATE cluster_profile
        SET
            gmt_modified = #{gmtModified},
            cluster_name = #{clusterName},
            cluster_id_external = #{clusterIdExternal},
            cluster_provider = #{clusterProvider},
            cluster_type = #{clusterType},
            region = #{region},
            site = #{site},
            annotations = #{annotations},
            use_type = #{useType},
            is_deleted = #{isDeleted},
            status = #{status},
            modifier = #{modifier}
        WHERE
            cluster_id = #{clusterId} and is_deleted= 'N'
    """
    )
    fun updateClusterProfile(clusterProfileData: ClusterProfileData): Int

    @Select(
        """
        SELECT
            id,
            cluster_id,
            cluster_name,
            cluster_id_external,
            cluster_provider,
            cluster_type,
            region,
            site,
            annotations,
            use_type,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator, 
            modifier
        FROM
            cluster_profile
        WHERE
            cluster_id = #{clusterId} and is_deleted= 'N'
    """
    )
    fun findClusterProfileByClusterId(clusterId: String): ClusterProfileData?

    @Select(
        """
        <script>
            SELECT
                id,
                cluster_id,
                cluster_name,
                cluster_id_external,
                cluster_provider,
                cluster_type,
                region,
                site,
                annotations,
                use_type,
                gmt_create,
                gmt_modified,
                is_deleted,
                status,
                creator, 
                modifier
            FROM
                cluster_profile
            WHERE
                cluster_id in 
                <foreach collection="clusterIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                AND is_deleted = 'N'
        </script>
    """
    )
    fun listClusterProfileByClusterIds(@Param("clusterIdList") clusterIdList: List<String>): List<ClusterProfileData>

    @Select(
        """
        SELECT
            id,
            cluster_id,
            cluster_name,
            cluster_id_external,
            cluster_provider,
            cluster_type,
            region,
            site,
            annotations,
            use_type,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            cluster_profile
        WHERE
            cluster_name = #{clusterName} and is_deleted= 'N'        
    """
    )
    fun findClusterProfileByClusterName(clusterName: String): ClusterProfileData?

    @Select(
        """
        <script>
            SELECT
                id,
                cluster_id,
                cluster_name,
                cluster_id_external,
                cluster_provider,
                cluster_type,
                region,
                site,
                annotations,
                use_type,
                gmt_create,
                gmt_modified,
                is_deleted,
                status,
                creator,
                modifier
            FROM            
                cluster_profile
            WHERE
                is_deleted = 'N'
                <choose>
                    <when test="clusterId != null and clusterName != null"  >
                        and ( cluster_id like "%${'$'}{clusterId}%" or cluster_name like "%${'$'}{clusterName}%" )
                    </when>
                    <when test="clusterId != null">
                        and cluster_id like "%${'$'}{clusterId}%"
                    </when>
                    <when test="clusterName != null">
                        and cluster_name like "%${'$'}{clusterName}%"
                    </when>
	            </choose>
                <if test="clusterProvider != null">
                    and cluster_provider = #{clusterProvider}
                </if>
                <if test="clusterType != null">
                    and cluster_type = #{clusterType}
                </if>
                <if test="region != null">
                    and region = #{region}
                </if>
                <if test="status != null">
                    and status = #{status}
                </if>
        </script>     
    """
    )
    fun findClusterProfilesByProperties(
        @Param("clusterId") clusterId: String?, @Param("clusterName") clusterName: String?,
        @Param("clusterProvider") clusterProvider: String?, @Param("clusterType") clusterType: String?,
        @Param("region") region: String?, @Param("status") status: String?
    ): List<ClusterProfileData>

    @Select(
        """
        <script>
            SELECT
                id,
                cluster_id,
                cluster_name,
                cluster_id_external,
                cluster_provider,
                cluster_type,
                region,
                site,
                annotations,
                use_type,
                gmt_create,
                gmt_modified,
                is_deleted,
                status,
                creator,
                modifier
            FROM            
                cluster_profile
            WHERE
                is_deleted = 'N' AND cluster_name IN
                <foreach collection="clusterNameList" item="clusterName" open="(" separator="," close=")">
                    #{clusterName}
                </foreach>
        </script>     
    """
    )
    fun listByClusterNameList(
        @Param("clusterNameList") clusterNameList: List<String>,
    ): List<ClusterProfileData>

    @Select(
        """
        <script>
            SELECT
                id,
                cluster_id,
                cluster_name,
                cluster_id_external,
                cluster_provider,
                cluster_type,
                region,
                site,
                annotations,
                use_type,
                gmt_create,
                gmt_modified,
                is_deleted,
                status,
                creator,
                modifier
            FROM            
                cluster_profile
            WHERE
                is_deleted = 'N' AND
                cluster_id IN
                <foreach collection="clusterIdList" item="clusterId" open="(" separator="," close=")">
                    #{clusterId}
                </foreach>
        </script>     
    """
    )
    fun listByClusterIdList(
        @Param("clusterIdList") clusterIdList: List<String>,
    ): List<ClusterProfileData>


    @Select("""
        SELECT
            id,
            cluster_id,
            cluster_name,
            cluster_id_external,
            cluster_provider,
            cluster_type,
            region,
            site,
            annotations,
            use_type,
            gmt_create,
            gmt_modified,
            is_deleted,
            status,
            creator,
            modifier
        FROM
            cluster_profile
        WHERE
            id = #{id}
    """
    )
    fun findClusterProfileById(id: Long): ClusterProfileData?

    @Update(
        """
        UPDATE
            cluster_profile
        SET 
            is_deleted = 'Y'
        WHERE
            cluster_id = #{clusterId} and is_deleted= 'N'
    """
    )
    fun deleteClusterProfileByClusterId(id: String): Int

    @Update(
        """
        UPDATE
            cluster_profile
        SET 
            is_deleted = 'Y'
        WHERE 
            id = #{id}
    """
    )
    fun deleteClusterProfileById(id: String): Int
}