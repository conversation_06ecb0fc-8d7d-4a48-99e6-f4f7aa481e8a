package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.KManagedClusterData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface KManagedClusterRepo {

    @Insert("""
        INSERT INTO
        kmanaged_cluster_data(
            gmt_create,
            gmt_modified,
            managed_cluster_key,
            cluster_profile_id,
            gateway_id,
            sys_env_namespace,
            system_components_id,
            region,
            managed_cluster_labels,
            status
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{managedClusterKey},
            #{clusterProfileId},
            #{gatewayId},
            #{sysEnvNamespace},
            #{systemComponentsId},
            #{region},
            #{managedClusterLabels},
            #{status}                                   
        )
    """)
    fun createKManagedCluster(kManagedClusterData: KManagedClusterData): Int

    @Select("""
        SELECT
            id,
            managed_cluster_key,
            cluster_profile_id,
            gateway_id,
            sys_env_namespace,
            system_components_id,
            region,
            managed_cluster_labels,
            status,
            gmt_create,
            gmt_modified            
        FROM
            kmanaged_cluster_data
        WHERE
            cluster_profile_id = #{clusterProfileId}
        AND
            sys_env_namespace = #{namespace}
        order by id asc 
        limit 1
    """)
    @Deprecated("this api will be offline in near future")
    fun findKManagedClusterByClusterProfileIdAndNamespace(@Param("clusterProfileId") clusterProfileId: Long, @Param("namespace") namespace: String): KManagedClusterData?

    @Select("""
        SELECT
            id,
            managed_cluster_key,
            cluster_profile_id,
            gateway_id,
            sys_env_namespace,
            system_components_id,
            region,
            managed_cluster_labels,
            status,
            gmt_create,
            gmt_modified            
        FROM
            kmanaged_cluster_data
        WHERE
            managed_cluster_key = #{managedClusterKey}
    """)
    fun findKManagedClusterByManagedClusterKey(@Param("managedClusterKey") managedClusterKey: String): KManagedClusterData?

    @Select("""
        SELECT
            id,
            managed_cluster_key,
            cluster_profile_id,
            gateway_id,
            sys_env_namespace,
            system_components_id,
            region,
            managed_cluster_labels,
            status,
            gmt_create,
            gmt_modified            
        FROM
            kmanaged_cluster_data
        WHERE
            cluster_profile_id = #{clusterProfileId}
    """)
    @Deprecated(
        message = "this function is deprecated!",
        level = DeprecationLevel.HIDDEN
    )
    fun findKManagedClusterByClusterProfileId(@Param("clusterProfileId") clusterProfileId: Long): List<KManagedClusterData?>?

    @Delete("""
        DELETE FROM 
            kmanaged_cluster_data 
        WHERE
            id = #{id}
    """)
    fun deleteKManagedCluster(id: Long): Int?

    @Delete("""
        DELETE FROM 
            kmanaged_cluster_data 
        WHERE
            managed_cluster_key = #{clusterManagerKey}
    """)
    fun deleteKManagedClusterByKey(clusterManagerKey: String): Int?
}