package com.alibaba.koastline.multiclusters.data.vo.env

import java.time.Instant
import java.util.*

/**
 * <AUTHOR>
 */

/**
 * app runtime environment data
 *  [AppRuntimeEnvironmentData] to [KManagedClusterData] has many-to-1 relationship
 * unique key is @property[runtimeEnvKey]
 */
data class AppRuntimeEnvironmentData(
    val id: Long?,
    val runtimeEnvKey: String, // autogenerated(unique)
    val name: String?,  // runtime env' name
    val creator: String, // who created the runtime env(eg. koastline),
    val managedClusterKey: String, // foreign key: managedClusterIdentity of KManagedCluster
    val region: String, // env region
    val az: String,  // env az,这里应该用site
    val stage: String,  // env stage
    val unit: String,  // env unti
    val status: String, // online, offline, wait_online
    val metaData: String?, // metadata could be put inside this json string.
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val modifier:String = "SYSTEM_ADMIN", // who update title and status for AppRuntimeEnvironmentData
)

/**
 * contains env_level to stage mapping relationships
 */
data class EnvLevelStageMappingData(
    val id: Long?,
    val envLevel: String,
    val stage: String,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * app runtime environment label relationships
 * ps:数据库中因 dms 无法 renaming 字段名称 targetKey 对应于 runtime_env_key
 */
data class ApRELabel(
    val id: Long? = null,
    val targetKey: String,
    val name: String,
    val value: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val apRELabelKey: String,
    val targetType: String,
    val type: ApRELabelType
)

/**
 * while label name is alibaba/apre/feature,it contains some specs
 */
data class ApREFeatureSpec(
    val id: Long? = null,
    val apRELabelKey: String,
    val title: String,
    val specType: String,
    val specCode: String?,
    val scope: String,
    val status: String,
    val sourceType: String?,
    val sourceId: String?,
    val versionType: String?,
    val versionId: String?,
    val annotations: String?,
    val labels: String?,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

/**
 * apre deed relationships
 */
data class ApREDeed(
    val id: Long?,
    val deedKey: String,
    val envLevel: String,
    val appName: String,
    val envId: String?,
    val envStackId: String?,
    val nodeGroup: String?,
    val content: String,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * default feature relationships
 */
data class ApREDefaultFeature (
    val id: Long?,
    val featureKey: String,
    val code: String,
    val featureUsage: String, // 使用方式，默认导入/指定导入
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * default feature spec relationships
 */
data class ApREDefaultFeatureSpec (
    val id: Long?,
    val featureKey: String,
    val title: String,
    val specType: String,
    val specCode: String,
    val scope: String,
    val status: String,
    val annotations: String?,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * apre deed env bingding data
 */
data class ApREDeedResourceGroupBindingData(
    val id: Long?,
    val apREDeedKey: String,
    val resourceGroup: String,
    val appName: String,
    val gmtCreate: Date,
    val gmtModified: Date
)

/**
 * apre declaration patch data
 * 业务SRE等角色基于契约声明附加补充条款
 */
data class ApREDeclarationPatchData(
    val id: Long? = null,
    val balanceType: String,
    val region: String? = null,
    val unit: String,
    val stage: String,
    val site: String? = null,
    val declarationPatch: String, //附加条款
    val creator: String, //工号
    val modifier: String, //工号
    val priority: Int = 1,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
) {
    /**
     * 标识同类限定身份
     */
    fun identity() = "$balanceType#$unit#$stage#$site"
}

/**
 * match scope data
 */
data class MatchScopeData(
    val id: Long?,
    val targetId: Long,
    val targetType: String,
    val externalId: String,
    val externalType: String,
    val exclusions: String?,
    val restrictions: String?,
    val creator: String, //工号
    val modifier: String, //工号
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

/**
 * match scope data condition
 */
data class MatchScopeDataCondition(
    val targetType: String,
    val externalId: String,
    val externalType: String
)

/**
 * component data
 */
data class ComponentData(
    val id: Long?,
    val code: String,
    val refObjectId: String,
    val refObjectType: String,
    val annotations: String,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * resource pool data
 */
data class ResourcePoolData(
    val id: Long? = null,
    val resourcePoolKey: String,
    val clusterId: String,
    val managedClusterKey: String,
    val creator: String, //工号
    val modifier: String, //工号
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

/**
 * apre resource group binding data
 */
data class ApREResourceGroupBindingData(
    val id: Long?,
    val appName: String,
    val resourceGroup: String,
    val runtimeEnvKey: String,
    val selector: String,
    val gmtCreate: Date,
    val gmtModified: Date,
    val isDeleted: String
)

/**
 * apre resource group binding data
 */
data class StackServerlessBaseAppBindingData(
    val id: Long? = null,
    val envStackId: String,
    val serverlessBaseAppName: String,
    val extraParams: String?,
    val creator: String, //工号
    val modifier: String, //工号
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

/**
 * ApRE 默认标签模板
 * title 唯一
 * name&value 唯一
 */
data class ApRELabelDefinition(
    val id:Long?,
    val apRELabelDefinitionId: String?,
    val title:String = "",
    val name:String, // key
    val value:String, // value
    val scope: String? = null, // ApRE / RESOURCE_POOL
    val gmtCreate: Date? = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date? = Date(Instant.now().toEpochMilli()),
    val isDeleted: String? = "N",
    val modifier: String? = null,
    val creator: String? = null,
)

data class FeatureSpecDefinition(
    val id: Long? = null,
    val featureSpecDefinitionId: String? = null,
    val refKey: String,
    val title: String,
    val scope: String,
    val specType: String = "",
    val specCode: String,
    val sourceType: String = "",
    val sourceId: String = "",
    val versionType: String = "",
    val versionId: String = "",
    val annotations: String = "{}",
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val status: String, // status
    val creator: String,
    val modifier: String,
)

/**
 * ApREBindingData 补充授权信息表 实现对范围性的授权记录
 */
data class ExtraApREBindingData(
    val id: Long? = null,
    val targetId: String,
    val properties: String,
    val type: ExtraApREBindingDataType,
    val description: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val creator: String,
    val modifier: String,
    val isDeleted: String = "N"
)

enum class ExtraApREBindingDataType {
    SERVERLESS_BASE,
}

/**
 * ApRELabel 类型
 */
enum class ApRELabelType {
    COMMON,
    DEFAULT,
    SERVERLESS,
    CONSOLE,
}