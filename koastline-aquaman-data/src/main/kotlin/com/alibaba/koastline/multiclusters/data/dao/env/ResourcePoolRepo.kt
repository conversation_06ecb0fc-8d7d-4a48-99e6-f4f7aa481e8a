package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ResourcePoolData
import org.apache.ibatis.annotations.*
import org.apache.ibatis.type.JdbcType

@Mapper
interface ResourcePoolRepo {

    @Insert("""
        INSERT INTO
        resource_pool_data(
            resource_pool_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            cluster_id,
            managed_cluster_key,
            is_deleted
        )
        VALUES(
            #{resourcePoolKey},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{clusterId},
            #{managedClusterKey},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourcePoolData: ResourcePoolData): Int

    @Select("""
        SELECT
            id,
            resource_pool_key,
            cluster_id,
            managed_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            resource_pool_data
        WHERE
            id = #{id}
    """)
    @Results(id="ResourcePoolData", value = [
        Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
        Result(column="resource_pool_key", property="resourcePoolKey", jdbcType= JdbcType.VARCHAR),
        Result(column="cluster_id", property="clusterId", jdbcType= JdbcType.VARCHAR),
        Result(column="managed_cluster_key", property="managedClusterKey", jdbcType= JdbcType.VARCHAR),
        Result(column="creator", property="creator", jdbcType= JdbcType.VARCHAR),
        Result(column="modifier", property="modifier", jdbcType= JdbcType.VARCHAR),
        Result(column="gmt_create", property="gmtCreate", jdbcType= JdbcType.TIMESTAMP),
        Result(column="gmt_modified", property="gmtModified", jdbcType= JdbcType.TIMESTAMP),
        Result(column="is_deleted", property="isDeleted", jdbcType= JdbcType.VARCHAR)
    ])
    fun findById(@Param("id") id: Long): ResourcePoolData

    @Select("""
        SELECT
            id,
            resource_pool_key,
            cluster_id,
            managed_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            resource_pool_data
        WHERE
            resource_pool_key = #{key}
    """)
    @ResultMap(value = ["ResourcePoolData"])
    fun findByResourcePoolKey(@Param("key") key: String): ResourcePoolData?

    @Select("""
        SELECT
            distinct(cluster_id)
        FROM            
            resource_pool_data
        WHERE
            is_deleted = 'N'
    """)
    fun listRelativeClusters(): List<String>

    @Select("""
        SELECT
            id,
            resource_pool_key,
            cluster_id,
            managed_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            resource_pool_data
        WHERE
            managed_cluster_key = #{managedClusterKey} and is_deleted = 'N'
    """)
    @ResultMap(value = ["ResourcePoolData"])
    fun findByManagedClusterKey(@Param("managedClusterKey") managedClusterKey: String): List<ResourcePoolData>

    @Select("""
        SELECT
            id,
            resource_pool_key,
            cluster_id,
            managed_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            resource_pool_data
        WHERE
            cluster_id = #{clusterId} and is_deleted = 'N'
    """)
    @ResultMap(value = ["ResourcePoolData"])
    fun findByClusterId(@Param("clusterId") clusterId: String): List<ResourcePoolData>

    @Select("""
        SELECT
            id,
            resource_pool_key,
            cluster_id,
            managed_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            resource_pool_data
        WHERE
            managed_cluster_key = #{managedClusterKey} and cluster_id = #{clusterId} and is_deleted = 'N'
        LIMIT 1
    """)
    @ResultMap(value = ["ResourcePoolData"])
    fun findByManagedClusterKeyAndClusterId(@Param("managedClusterKey") managedClusterKey: String, @Param("clusterId") clusterId: String): ResourcePoolData?

    @Update("""
        UPDATE
            resource_pool_data
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            id = #{id}
        LIMIT 1
    """)
    fun deleteById(@Param("id") id: Long): Int

    @Update("""
        UPDATE
            resource_pool_data
        SET
            is_deleted = 'Y',
            modifier = #{modifier},
            gmt_modified = now()
        WHERE
            managed_cluster_key = #{managedClusterKey} and cluster_id = #{clusterId} and is_deleted = 'N'
    """)
    fun deleteByManagedClusterKeyAndClusterId(@Param("managedClusterKey") managedClusterKey: String, @Param("clusterId") clusterId: String, @Param("modifier") modifier: String): Int

    @Update(
        """
        UPDATE
            resource_pool_data
        SET
            is_deleted = 'Y',
            modifier = #{modifier},
            gmt_modified = now()
        WHERE
            managed_cluster_key = #{managedClusterKey} and is_deleted = 'N'
    """
    )
    fun deleteByManagedClusterKey(
        @Param("managedClusterKey") managedClusterKey: String,
        @Param("modifier") modifier: String
    ): Int

    @Update("""
        UPDATE
            resource_pool_data
        SET
            is_deleted = 'Y',
            modifier = #{modifier},
            gmt_modified = now()
        WHERE
            resource_pool_key = #{resourcePoolKey} and is_deleted = 'N'
    """)
    fun deleteByResourcePoolKey(@Param("resourcePoolKey") resourcePoolKey: String, @Param("modifier") modifier: String): Int
}