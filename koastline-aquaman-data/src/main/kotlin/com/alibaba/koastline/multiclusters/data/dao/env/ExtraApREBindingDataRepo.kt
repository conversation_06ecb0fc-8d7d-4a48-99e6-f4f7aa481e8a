package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.GroupCount
import com.alibaba.koastline.multiclusters.data.vo.env.ExtraApREBindingData
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

@Mapper
interface ExtraApREBindingDataRepo {

    @Insert("""
        INSERT INTO
        extra_apre_binding_data(
            target_id,
            properties,
            type,
            description,
            gmt_create,
            gmt_modified,
            creator,
            modifier,
            is_deleted
        )
        VALUES(
            #{targetId},
            #{properties},
            #{type},
            #{description},
            #{gmtCreate},
            #{gmtModified},
            #{creator},
            #{modifier},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(extraApREBindingData: ExtraApREBindingData): Int


    @Select(
        """
        <script>
            SELECT 
                id,
                target_id,
                properties,
                type,
                description,
                gmt_create,
                gmt_modified,
                creator,
                modifier,
                is_deleted
            FROM
                extra_apre_binding_data
            WHERE
                is_deleted = 'N'
                <if test="keyWords != null">
                    and target_id like "${'$'}{keyWords}%"
                </if>
        </script>
    """
    )
    fun findByKeyWordsWithTargetId(@Param("keyWords") keyWords: String?): List<ExtraApREBindingData>

    @Select(
        """
        <script>
            SELECT
                COUNT(1) as count,
                target_id as group_name
            FROM
                extra_apre_binding_data
            WHERE
                is_deleted = 'N'
                <if test="keyWords != null">
                    and target_id like "${'$'}{keyWords}%"
                </if>
            GROUP BY target_id
            ORDER BY target_id
        </script>
    """
    )
    fun findScopeGroupCountByType(@Param("keyWords") keyWords: String?, @Param("type") type: String): List<GroupCount>

    @Select("""
        <script>
            SELECT
                id,
                target_id,
                properties,
                type,
                description,
                gmt_create,
                gmt_modified,
                creator,
                modifier,
                is_deleted
            FROM
                extra_apre_binding_data
            WHERE
                is_deleted = 'N' and target_id IN
                <foreach collection="targetIds" item="id" open="(" separator="," close=")">
                        #{id}
                </foreach>
        </script>
    """
    )
    fun findByTargetIds(@Param("targetIds") targetIds: List<String>): List<ExtraApREBindingData>

    @Select("""
        SELECT 
            id,
            target_id,
            properties,
            type,
            description,
            gmt_create,
            gmt_modified,
            creator,
            modifier,
            is_deleted
        FROM
            extra_apre_binding_data
        WHERE
            id = #{id} and is_deleted = 'N'
    """)
    fun findById(@Param("id") id: Long): ExtraApREBindingData

    @Select("""
        SELECT 
            id,
            target_id,
            properties,
            type,
            description,
            gmt_create,
            gmt_modified,
            creator,
            modifier,
            is_deleted
        FROM
            extra_apre_binding_data
        WHERE
            target_id = #{targetId} and is_deleted = 'N'
    """)
    fun findByTargetId(@Param("targetId") targetId: String): List<ExtraApREBindingData>

    @Select("""
        <script>
            SELECT 
                id,
                target_id,
                properties,
                type,
                description,
                gmt_create,
                gmt_modified,
                creator,
                modifier,
                is_deleted
            FROM
                extra_apre_binding_data
            WHERE
                id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                AND is_deleted = 'N'
        </script>
    """)
    fun findByIds(@Param("ids") ids:List<Long>): List<ExtraApREBindingData>


    @Update("""
        UPDATE
            extra_apre_binding_data
        SET
            gmt_modified = #{gmtModified},
            modifier = #{modifier},
            description = #{description},
            properties = #{properties}
        WHERE 
            id = #{id}
    """)
    fun updateExtraApREBindingData(extraApREBindingData:ExtraApREBindingData):Int

    @Update("""
        UPDATE
            extra_apre_binding_data
        SET
            is_deleted = 'Y'
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int
}