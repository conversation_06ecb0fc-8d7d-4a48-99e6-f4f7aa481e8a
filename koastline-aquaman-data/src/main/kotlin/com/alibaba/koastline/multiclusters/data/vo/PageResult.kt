package com.alibaba.koastline.multiclusters.data.vo

import com.github.pagehelper.Page

data class PageData<T>(
    var pageNumber: Int,
    var pageSize: Int,
    var totalCount: Long,
    var data: List<T>? = null,
    val nextToken: String? = null,
) {
    fun <R> map(transform: ((T) -> R)): PageData<R> {
        return PageData(
            pageNumber,
            pageSize,
            totalCount,
            data = data?.map(transform) ?: emptyList()
        )
    }

    companion object {
        fun <T> of(pageSize: Int, pageNumber: Int, totalCount: Long, data: List<T>?): PageData<T> {
            return PageData(pageNumber, pageSize, totalCount, data)
        }

        fun <T> zeroPage(pageSize: Int, pageNumber: Int): PageData<T> {
            return of(pageSize = pageSize, pageNumber = pageNumber, totalCount = 0, emptyList())
        }

        fun <T> transformFrom(page: Page<T>, nextToken: String? = null): PageData<T> {
            return PageData(
                pageNumber = page.pageNum,
                pageSize = page.pageSize,
                totalCount = page.total,
                data = page,
                nextToken = nextToken,
            )
        }
    }
}
