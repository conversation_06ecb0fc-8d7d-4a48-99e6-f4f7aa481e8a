package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabelDefinition
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

@Mapper
interface LabelDefinitionRepo {

    @Insert(
        """
        INSERT INTO
        label_definition(
            apre_label_definition_id,
            title,
            name,
            value,
            scope,
            gmt_create,
            gmt_modified,
            is_deleted,
            modifier,
            creator
        )
        VALUES(
            #{apRELabelDefinitionId},
            #{title},
            #{name},
            #{value},
            #{scope},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted},
            #{modifier},
            #{creator}
        )
        """
    )
    fun insert(apRELabelDefinition: ApRELabelDefinition): Int


    @Select(
        """
        SELECT
            id,
            apre_label_definition_id,
            title,
            name,
            value,
            scope,
            gmt_create,
            gmt_modified,
            is_deleted,
            modifier,
            creator
        FROM
            label_definition
        WHERE
            apre_label_definition_id = #{apRELabelDefinitionId} and is_deleted = "N"
        """
    )
    fun findByLabelDefinitionId(apRELabelDefinitionId: String): ApRELabelDefinition?

    @Select(
        """
        SELECT
            id,
            apre_label_definition_id,
            title,
            name,
            value,
            scope,
            gmt_create,
            gmt_modified,
            is_deleted,
            modifier,
            creator
        FROM
            label_definition
        WHERE
            name = #{name} and value = #{value} and is_deleted = "N"
        """
    )
    fun findByNameAndValue(@Param("name") name: String, @Param("value") value: String): ApRELabelDefinition?

    @Select(
        """
        SELECT
            id,
            apre_label_definition_id,
            title,
            name,
            value,
            scope,
            gmt_create,
            gmt_modified,
            is_deleted,
            modifier,
            creator
        FROM
            label_definition
        WHERE
            title = #{title} and is_deleted = "N"
        """
    )
    fun findByTitle(@Param("title") title: String): ApRELabelDefinition?

    @Select(
        """
        <script>
            SELECT
                id,
                apre_label_definition_id,
                title,
                name,
                value,
                scope,
                gmt_create,
                gmt_modified,
                is_deleted,
                modifier,
                creator
            FROM
                label_definition
            WHERE
                is_deleted = "N"
                <if test="title != null">
                    and title like "${'$'}{title}%"
                </if>
                <if test="name != null">
                    and name = #{name}
                </if>
                <if test="value != null">
                    and value = #{value}
                </if>
            ORDER BY
                gmt_modified
            DESC
        </script>
    """
    )
    fun listLabelDefinitionsByProperties(
        @Param("title") title: String?,
        @Param("name") name: String?,
        @Param("value") value: String?
    ): List<ApRELabelDefinition>

    @Update(
        """
        UPDATE 
            label_definition
        SET
            title = #{title},
            name = #{name},
            value = #{value},
            scope = #{scope},
            gmt_modified = #{gmtModified},
            modifier = #{modifier}
        WHERE
            apre_label_definition_id = #{apRELabelDefinitionId}
    """
    )
    fun updateByLabelDefinitionId(apRELabelDefinition: ApRELabelDefinition): Int

    @Update(
        """
        UPDATE
            label_definition
        SET 
            is_deleted = 'Y', modifier = #{modifier}
        WHERE
            apre_label_definition_id = #{apRELabelDefinitionId} and is_deleted= 'N'
    """
    )
    fun deleteByLabelDefinitionId(
        @Param("apRELabelDefinitionId") apRELabelDefinitionId: String,
        @Param("modifier") modifier: String
    ): Int

    @Select(
        """
        SELECT
            id,
            apre_label_definition_id,
            title,
            name,
            value,
            scope,
            gmt_create,
            gmt_modified,
            is_deleted,
            modifier,
            creator
        FROM
            label_definition
        WHERE
            is_deleted = "N"
        """
    )
    fun listAll(): List<ApRELabelDefinition>
}