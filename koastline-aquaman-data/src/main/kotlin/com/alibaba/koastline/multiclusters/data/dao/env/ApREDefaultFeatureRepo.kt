package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREDefaultFeature
import org.apache.ibatis.annotations.*

@Mapper
interface ApREDefaultFeatureRepo {
    @Insert("""
        INSERT INTO
        apre_default_feature(
            gmt_create,
            gmt_modified,
            feature_key,
            code,
            feature_usage,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{featureKey},
            #{code},
            #{featureUsage},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apREDefaultFeature: ApREDefaultFeature): Int

    @Select("""
        SELECT 
            id,
            feature_key,
            code,
            feature_usage,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_default_feature
        WHERE
            code = #{code} and is_deleted = 'N'
        LIMIT 1
    """)
    fun queryByCode(@Param("code") code: String): ApREDefaultFeature?

    @Select("""
        SELECT 
            id,
            feature_key,
            code,
            feature_usage,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_default_feature
        WHERE
            is_deleted = 'N'
    """)
    fun queryAll(): List<ApREDefaultFeature>

    @Select("""
        SELECT 
            id,
            feature_key,
            code,
            feature_usage,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_default_feature
        WHERE
            feature_usage = #{featureUsage} and is_deleted = 'N'
    """)
    fun queryByFeatureUsage(featureUsage: String): List<ApREDefaultFeature>

    @Update("""
        UPDATE
            apre_default_feature
        SET
            gmt_modified = now(),
            feature_usage = #{featureUsage},
            is_deleted = #{isDeleted}
        WHERE 
            id = #{id}
    """)
    fun updateById(apREDefaultFeature: ApREDefaultFeature): Int

    @Update("""
        UPDATE
            apre_default_feature
        SET
            isDeleted = 'Y',
            gmt_modified = now()
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int
}