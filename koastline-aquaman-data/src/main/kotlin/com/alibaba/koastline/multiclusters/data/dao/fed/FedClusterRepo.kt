package com.alibaba.koastline.multiclusters.data.dao.fed

import com.alibaba.koastline.multiclusters.data.vo.fed.FedCluster
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

@Mapper
interface FedClusterRepo {
    @Insert(
        """
        INSERT INTO 
        fed_cluster(
            fed_env_name,
            region,
            site,
            fed_cluster_key,
            status,
            tenant_manage_cluster_key,
            member_manage_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{fedEnvName},
            #{region},
            #{site},
            #{fedClusterKey},
            #{status},
            #{tenantManageClusterKey},
            #{memberManageClusterKey},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """
    )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(fedCluster: FedCluster): Int

    @Select(
        """
        SELECT
            id,
            fed_env_name,
            region,
            site,
            fed_cluster_key,
            status,
            tenant_manage_cluster_key,
            member_manage_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            fed_cluster
        WHERE
            fed_cluster_key = #{fedClusterKey} and is_deleted = 'N'
    """
    )
    fun findByFedClusterKey(@Param("fedClusterKey") fedClusterKey: String): FedCluster?

    @Select(
        """
        SELECT
            f.id,
            f.fed_env_name,
            f.region,
            f.site,
            f.fed_cluster_key,
            f.status,
            f.tenant_manage_cluster_key,
            f.member_manage_cluster_key,
            f.creator,
            f.modifier,
            f.gmt_create,
            f.gmt_modified,
            f.is_deleted
        FROM `fed_cluster` f
        JOIN `resource_pool_data` r
        ON f.`tenant_manage_cluster_key` = r.`managed_cluster_key`
        AND f.`is_deleted` = 'N' AND r.`is_deleted` = 'N'
        JOIN `cluster_profile` c
        ON r.`cluster_id` = c.`cluster_id` AND c.`is_deleted` = 'N'
        WHERE c.`cluster_name` = #{tenantClusterName};
    """
    )
    fun findByTenantClusterName(@Param("tenantClusterName") tenantClusterName: String): FedCluster?


    @Select(
        """
        <script>
            SELECT
                id,
                fed_env_name,
                region,
                site,
                fed_cluster_key,
                status,
                tenant_manage_cluster_key,
                member_manage_cluster_key,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                fed_cluster
            WHERE
                is_deleted = 'N' and tenant_manage_cluster_key IN
                <foreach collection="tenantManageClusterKeys" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </script>
    """
    )
    fun listByTenantClusterKeys(@Param("tenantManageClusterKeys") tenantManageClusterKeys: List<String>): List<FedCluster>

    @Select(
        """
        <script>
            SELECT
                id,
                fed_env_name,
                region,
                site,
                fed_cluster_key,
                status,
                tenant_manage_cluster_key,
                member_manage_cluster_key,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                fed_cluster
            WHERE
                is_deleted = 'N' and member_manage_cluster_key IN
                <foreach collection="memberManageClusterKey" item="item" open="(" separator="," close=")">
                        #{item}
                </foreach>
        </script>
    """
    )
    fun listByMemberClusterKeys(@Param("memberManageClusterKey") memberManageClusterKey: List<String>): List<FedCluster>


    @Select(
        """
    <script>
        SELECT
            id,
            fed_env_name,
            region,
            site,
            fed_cluster_key,
            status,
            tenant_manage_cluster_key,
            member_manage_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            fed_cluster
        WHERE
            is_deleted = 'N'
            <if test="keyWords != null">
                and fed_env_name like "%${'$'}{keyWords}%"
            </if>
            <if test="region != null">
                and region = #{region}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="ids != null">
                and id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </script>
    """
    )
    fun listByProperties(
        @Param("keyWords") keyWords: String? = null,
        @Param("region") region: String? = null,
        @Param("status") status: String? = null,
        @Param("ids") ids: List<Long>? = null,
    ): List<FedCluster>

    @Select(
        """
        SELECT
            id,
            fed_env_name,
            region,
            site,
            fed_cluster_key,
            status,
            tenant_manage_cluster_key,
            member_manage_cluster_key,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM            
            fed_cluster
        WHERE
            id = #{id} and is_deleted = 'N'
    """
    )
    fun findById(@Param("id") id: Long): FedCluster?

    @Select(
        """
        <script>
            SELECT
                id,
                fed_env_name,
                region,
                site,
                fed_cluster_key,
                status,
                tenant_manage_cluster_key,
                member_manage_cluster_key,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM            
                fed_cluster
            WHERE
                is_deleted = 'N' and id IN
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                        #{item}
                </foreach>
        </script>
    """
    )
    fun findByIds(@Param("ids") ids: List<Long>): List<FedCluster>

    /**
     * 只开放修改部分属性:
     *
     * @param id
     * @param modifier
     * @return
     */
    @Update(
        """
        <script>
            UPDATE
                fed_cluster
            SET
                <if test="fedEnvName != null">
                    fed_env_name = #{fedEnvName},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                gmt_modified = now(),
                modifier = #{modifier}
            WHERE
                is_deleted = "N" and id = #{id}
        </script>
    """
    )
    fun update(
        @Param("id") id: Long,
        @Param("modifier") modifier: String,
        @Param("fedEnvName") fedEnvName: String? = null,
        @Param("status") status: String? = null,
    ): Int

    @Update(
        """
        <script>
            UPDATE
                fed_cluster
            SET
                gmt_modified = now(),
                modifier = #{modifier},
                is_deleted = 'Y'
            WHERE
                is_deleted = "N" and id = #{id}
        </script>
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int
}