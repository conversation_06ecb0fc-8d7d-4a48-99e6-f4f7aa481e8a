package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.StackServerlessBaseAppBindingData
import org.apache.ibatis.annotations.*

@Mapper
interface StackServerlessBaseAppBindingRepo {

    @Insert("""
        INSERT INTO 
        stack_serverless_base_app_binding_data(
            env_stack_id,
            serverless_base_app_name,
            extra_params,
            creator,
            modifier,
            gmt_create, 
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{envStackId},
            #{serverlessBaseAppName}, 
            #{extraParams},
            #{creator},
            #{modifier},
            #{gmtCreate}, 
            #{gmtModified},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(stackServerlessBaseAppBindingData: StackServerlessBaseAppBindingData): Int

    @Select("""
        SELECT 
            id,
            env_stack_id,
            serverless_base_app_name,
            extra_params,
            creator,
            modifier,
            gmt_create, 
            gmt_modified,
            is_deleted
        FROM
            stack_serverless_base_app_binding_data
        WHERE
            id = #{id}
    """)
    fun findById(@Param("id") id: Long): StackServerlessBaseAppBindingData

    @Select("""
        SELECT 
            id,
            env_stack_id,
            serverless_base_app_name,
            extra_params,
            creator,
            modifier,
            gmt_create, 
            gmt_modified,
            is_deleted
        FROM
            stack_serverless_base_app_binding_data
        WHERE
            env_stack_id = #{envStackId} and is_deleted = 'N'
    """)
    fun findByEnvStackId(@Param("envStackId") envStackId: String): StackServerlessBaseAppBindingData?

    @Update("""
        UPDATE 
            stack_serverless_base_app_binding_data
        SET
            is_deleted = 'Y',
            modifier = #{modifier},
            gmt_modified = now()
        WHERE
            env_stack_id = #{envStackId}
    """)
    fun delByEnvStackId(@Param("envStackId")  envStackId: String, @Param("modifier") modifier: String): Int

    @Update("""
        UPDATE 
            stack_serverless_base_app_binding_data
        SET
            is_deleted = 'Y',
            modifier = #{modifier},
            gmt_modified = now()
        WHERE
            id = #{id}
    """)
    fun delById(@Param("id") id: Long,@Param("modifier") modifier: String): Int
}