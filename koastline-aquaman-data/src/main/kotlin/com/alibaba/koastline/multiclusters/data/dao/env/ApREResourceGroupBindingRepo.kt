package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREResourceGroupBindingData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ApREResourceGroupBindingRepo {
    @Insert("""
        INSERT INTO 
        apre_resource_group_binding_data(
            app_name,
            resource_group,
            runtime_env_key,
            selector,
            gmt_create, 
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{appName}, 
            #{resourceGroup}, 
            #{runtimeEnvKey},
            #{selector},
            #{gmtCreate}, 
            #{gmtModified},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apREResourceGroupBindingData: ApREResourceGroupBindingData): Int

    @Select("""
        SELECT 
            id,
            app_name,
            resource_group,
            runtime_env_key,
            selector,
            gmt_create, 
            gmt_modified,
            is_deleted
        FROM
            apre_resource_group_binding_data
        WHERE
            id = #{id}
    """)
    fun findById(@Param("id") id: Long): ApREResourceGroupBindingData

    @Select("""
        SELECT 
            id,
            app_name,
            resource_group,
            runtime_env_key,
            selector,
            gmt_create, 
            gmt_modified,
            is_deleted
        FROM
            apre_resource_group_binding_data
        WHERE
            app_name = #{appName} and resource_group = #{resourceGroup} and runtime_env_key = #{runtimeEnvKey} and is_deleted = 'N'
    """)
    fun findByCondition(@Param("appName") appName: String,@Param("resourceGroup") resourceGroup: String,@Param("runtimeEnvKey") runtimeEnvKey: String): ApREResourceGroupBindingData?

    @Update("""
        UPDATE
            apre_resource_group_binding_data
        SET 
            selector = #{selector},
            gmt_modified = now()
        WHERE
            id = #{id}      
    """)
    fun updateById(@Param("id") id: Long,
                   @Param("selector") selector: String): Int?

    @Update("""
        UPDATE 
            apre_resource_group_binding_data
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            id = #{id}
    """)
    fun delById(@Param("id") id: Long): Int
}