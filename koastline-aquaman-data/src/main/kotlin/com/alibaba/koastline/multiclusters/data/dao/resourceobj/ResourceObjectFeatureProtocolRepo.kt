package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureProtocol
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ResourceObjectFeatureProtocolRepo {

    @Insert("""
        INSERT INTO 
        resource_object_feature_protocol(
            resource_object_feature_key,
            protocol,
            version,
            patch,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            strategy
        )
        VALUES(
            #{resourceObjectFeatureKey},
            #{protocol},
            #{version},
            #{patch},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted},
            #{strategy}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourceObjectFeatureProtocol: ResourceObjectFeatureProtocol): Int

    @Select("""
        SELECT 
            id,
            resource_object_feature_key,
            protocol,
            version,
            patch,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            strategy
        FROM
            resource_object_feature_protocol
        WHERE
            id = #{id}
    """)
    fun findById(id: Long): ResourceObjectFeatureProtocol?

    @Select("""
        SELECT 
            id,
            resource_object_feature_key,
            protocol,
            version,
            patch,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            strategy
        FROM
            resource_object_feature_protocol
        WHERE
            resource_object_feature_key = #{resourceObjectFeatureKey} and is_deleted = 'N'
    """)
    fun findByResourceObjectFeatureKey(resourceObjectFeatureKey: String): List<ResourceObjectFeatureProtocol>

    @Select(
        """
        SELECT 
            id,
            resource_object_feature_key,
            protocol,
            version,
            patch,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            strategy
        FROM
            resource_object_feature_protocol
        WHERE
            resource_object_feature_key = #{resourceObjectFeatureKey} and is_deleted = 'N'
            and version = #{version}
    """
    )
    fun findByResourceObjectFeatureKeyAndVersion(
        @Param("resourceObjectFeatureKey") resourceObjectFeatureKey: String,
        @Param("version") version: String,
    ): List<ResourceObjectFeatureProtocol>

    @Update(
        """
        UPDATE resource_object_feature_protocol
        SET
            patch = #{patch},
            modifier = #{modifier},
            gmt_modified = now(),
            strategy = #{strategy}
        WHERE
            id = #{id}
    """
    )
    fun updateById(
        @Param("id") id: Long,
        @Param("patch") patch: String,
        @Param("modifier") modifier: String,
        @Param("strategy") strategy: String?,
    ): Int

    @Update("""
        UPDATE resource_object_feature_protocol
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(id: Long): Int
}