package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeData
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update


@Mapper
interface RuntimeRepo {
    @Insert("""
        INSERT INTO 
        runtime(
            gmt_create, 
            gmt_modified,
            runtime_key,
            app_name, 
            env_stack_id,
            resource_group_name,
            type,
            is_deleted,
            creator,
            modifier
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{runtimeKey},
            #{appName},
            #{envStackId},
            #{resourceGroupName},
            #{type},
            #{isDeleted},
            #{creator},
            #{modifier}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(runtime: RuntimeData): Int

    @Select("""
        SELECT
            id,
            runtime_key,
            app_name, 
            env_stack_id,
            resource_group_name,
            type,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            runtime
        WHERE
            env_stack_id = #{envStackId} and is_deleted = 'N'
    """)
    fun findByEnvStackId(@Param("envStackId") envStackId: String): RuntimeData?

    @Select("""
        SELECT
            id,
            runtime_key,
            app_name, 
            env_stack_id,
            resource_group_name,
            type,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            runtime
        WHERE
            resource_group_name = #{resourceGroupName} and is_deleted = 'N'
    """)
    fun findByResourceGroupName(@Param("resourceGroupName") resourceGroupName: String): RuntimeData?

    @Select("""
        SELECT
            id,
            runtime_key,
            app_name,
            env_stack_id,
            resource_group_name,
            type,
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            runtime
        WHERE
            runtime_key = #{runtimeKey} and is_deleted = 'N'
    """)
    fun findByRuntimeKey(@Param("runtimeKey") runtimeKey: String): RuntimeData?

    @Update(
        """
        UPDATE 
            runtime
        SET
            env_stack_id = #{envStackId},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun updateEnvStackIdById(@Param("id") id: Long, @Param("modifier") modifier: String, @Param("envStackId") envStackId: String): Int

    @Update(
        """
        UPDATE 
            runtime
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int
}