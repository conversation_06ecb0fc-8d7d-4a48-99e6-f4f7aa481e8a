package com.alibaba.koastline.multiclusters.data.vo.resource

import java.time.Instant
import java.util.*

data class Resource(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 类型
     */
    val kind: String,
    /**
     * 唯一KEY
     */
    val name: String,
    /**
     * 服务提供方
     */
    val serviceProvider: String,
    /**
     * 代表是否受控于父级资源文件，即是否可以随上层资源删除而级联删除
     */
    val controller: Boolean = true,
    /**
     * 资源是否需要回收，部分资源的使用是可以不用回收资源的【依据业务场景】
     */
    val needRecycling: Boolean = true,
    /**
     * 基线版本ID
     */
    val baselineBoxId: Long = -1,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
)

data class ResourceBox(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 资源ID
     */
    val resourceId: Long,
    /**
     * 资源协议
     */
    val spec: String,
    /**
     * 版本
     */
    val version: Int,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
)

data class ResourceOwnerReference(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 子级资源ID
     */
    val subResourceId: Long,
    /**
     * 关联父级类型
     */
    val ownerRefKind: String,
    /**
     * 关联父级资源唯一Name
     */
    val ownerRefName: String,
    /**
     * true: 需要当前资源删除，才能开始删除OwnerRef资源;false: 不需要等待当前资源删除，OwnerRef资源即可开始删除.
     */
    val blockOwnerDeletion: Boolean = true,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
)

data class ResourceDesc(
    /**
     * 类型
     */
    val kind: String,
    /**
     * 唯一KEY
     */
    val name: String,
    /**
     * 服务提供方
     */
    val serviceProvider: String,
    /**
     * 代表是否受控于父级资源文件，即是否可以随上层资源删除而级联删除
     */
    val controller: Boolean = true,
    /**
     * 资源是否需要回收，部分资源的使用是可以不用回收资源的【依据业务场景】
     */
    val needRecycling: Boolean = true,
    /**
     * 是否子资源
     */
    val sub: Boolean,
    /**
     * 资源描述Spec
     */
    val spec: String,
    /**
     * Owner关联
     */
    val ownerReferences: List<OwnerReference> = emptyList(),
)

data class OwnerReference(
    val kind: String,
    val name: String,
    /**
     * true: 需要当前资源删除，才能开始删除OwnerRef资源
     * false: 不需要等待当前资源删除，OwnerRef资源即可开始删除
     **/
    val blockOwnerDeletion: Boolean? = true,
)
