package com.alibaba.koastline.multiclusters.data.vo.env

import java.time.Instant
import java.util.*

const val DEFAULT_VERSION = "0.0.1"


/**
 * 资源对象特性
 */
data class ResourceObjectFeature(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 资源对象特性Key
     */
    val resourceObjectFeatureKey: String,
    /**
     * 标题
     */
    val title: String,
    /**
     * 使用范围
     * @see com.alibaba.koastline.multiclusters.resourceobj.model.ResourceObjectFeatureUseScope
     */
    val useScope: String,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val feasibleProtocols: String,
    val type: String,
    val effectiveStage: String,
    val jsonSchema: String? = null,
    val displayTheme: String,
    val version: String = DEFAULT_VERSION,
    val submitters: String = "SYSTEM",
)

/**
 * 资源对象特性协议
 */
data class ResourceObjectFeatureProtocol(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 资源对象特性Key
     */
    val resourceObjectFeatureKey: String,
    /**
     * 协议
     */
    val protocol: String,
    /**
     * 版本
     */
    val version: String = DEFAULT_VERSION,
    /**
     * 补丁内容
     */
    val patch: String,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val strategy: String? = null
)

/**
 * 资源对象特性参数
 */
data class ResourceObjectFeatureParam(
    /**
     * ID
     */
    val id: Long?,
    /**
     * 资源对象特性Key
     */
    val resourceObjectFeatureKey: String,
    /**
     * 标题
     */
    val title: String,
    /**
     * 代码
     */
    val code: String,
    /**
     * 类型：INPUT/SELECT
     */
    val type: String,
    /**
     * 是否必填
     */
    val required: Boolean,
    /**
     * 可选值定义
     */
    val optionalValue: String?,
    /**
     * 数值约束
     */
    val valueConstraint: String?
)

/**
 * 资源对象特性注入
 */
data class ResourceObjectFeatureImport(
    /**
     * ID
     */
    val id: Long? = null,
    /**
     * 资源对象特性Key
     */
    val resourceObjectFeatureKey: String,
    /**
     * 状态,Enabled|Disabled
     */
    val status: String,
    /**
     * 预置参数映射
     */
    val paramMap: String?,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
    val version: String = DEFAULT_VERSION,
)

/**
 * 资源对象特性注入并添加范围
 */
data class ResourceObjectFeatureImportWithMatchScope(
  /**
   * ID
   */
  val id: Long? = null,
  /**
   * 资源对象特性Key
   */
  val resourceObjectFeatureKey: String,
  /**
   * 状态,Enabled|Disabled
   */
  val status: String,
  /**
   * 预置参数映射
   */
  val paramMap: String?,
  /**
   * 通用属性
   */
  val creator: String,
  val modifier: String,
  val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
  val gmtModified: Date = Date(Instant.now().toEpochMilli()),
  val isDeleted: String = "N",
  val version: String = DEFAULT_VERSION,
  val externalId: String,
  val externalType: String,
)

/**
 * 资源对象注入 与 matchScope关联的精简版宽表数据
 */
data class ResourceObjectFeatureImportWithMatchScopeCombine(

    /**
     * 资源特性注入id
     */
    val importId: Long,

    /**
     * 关联表id
     * 用于进行分页
     */
    val matchScopeId: Long,

    /**
     * 资源特性key
     */
    val resourceObjectFeatureKey: String,

    /**
     * 预置参数映射
     */
    val paramMap: String?,

    /**
     * 业务Id
     */
    val externalId: String,

    /**
     * 业务类型
     */
    val externalType: String,

    /**
     * 限制
     */
    val restrictions: String?
)

/*
调度标签定义
 */
data class ConfigDispatchLabel(
  /**
   * ID
   */
  val id: Long? = null,

  /*
  * 标签Code
   */
  val code: String? = null,

  /*
  * 标签的中文名
   */
  val cnName: String?,

  /*
  * 标签类型
   */
  val type: String? = "public",

  /*
  * 作用域
   */
  val scope: String? = "ALL",

  /*
  * 指定资源池
   */
  val specificResourcePool: String?,

  /*
  * 标签的取值是否可以为任意值
   */
  val isSpecificValue: Boolean = false,

  /*
  * 指定值内容，json结构
   */
  val specifiedValue: String?,

  /*
  * 状态
   */
  val status: String = "ONLINE",

  /*
  * 标签的描述
   */
  val description: String?,

  /*
  * bpms审批单号
   */
  val approveId: String? = null,

  /*
  * 提交方
   */
  val submitSystem: String = "hcrm-grop",
  /**
   * 通用属性
   */
  val creator: String? = null,
  val modifier: String,
  val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
  val gmtModified: Date = Date(Instant.now().toEpochMilli()),
  val isDeleted: Int = 0
)

/*
调度标签值
 */
data class ConfigDispatchLabelValue(
  /**
   * ID
   */
  val id: Long? = null,

  val appName: String,

  /*
  * 标签Code
   */
  val labelCode: String,

  /*
  * 标签值
   */
  val labelValue: String,

  /*
  * 调用系统
   */
  val submitSystem: String = "0",

  /*
  * 分组名
   */
  val groupName: String?,

  /*
  * 机房
   */
  val idc: String?,

  /*
  * 单元
   */
  val unit: String?,

  /*
  * 用途
   */
  val env: String?,

  /*
  * 备注
   */
  val remark: String?,


  /**
   * 通用属性
   */
  val creator: String,
  val modifier: String,
  val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
  val gmtModified: Date = Date(Instant.now().toEpochMilli()),
  val isDeleted: Int = 0
)

data class ConfigDispatchLabelValueWithMetadata(
  /**
   * ID
   */
  val id: Long? = null,

  val appName: String,

  /*
  * 标签Code
   */
  val labelCode: String,

  /*
  * 标签值
   */
  val labelValue: String,

  /*
  * 调用系统
   */
  val submitSystem: String = "0",

  /*
  * 分组名
   */
  val groupName: String? = null,

  /*
  * 机房
   */
  val idc: String? = null,

  /*
  * 单元
   */
  val unit: String? = null,

  /*
  * 用途
   */
  val env: String? = null,

  /*
  * 备注
   */
  val remark: String? = null,


  /**
   * 通用属性
   */
  val creator: String,
  val modifier: String,
  val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
  val gmtModified: Date = Date(Instant.now().toEpochMilli()),
  val isDeleted: Int = 0,
  val labelType: String,
  val labelName: String
)

data class KvMap(

    val id: Long? = null,

    val type: String,

    val keyName: String,

    val value: String,

    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
)
