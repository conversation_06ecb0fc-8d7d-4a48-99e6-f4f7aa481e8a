package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.RuntimeWorkloadMeta
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

@Mapper
interface RuntimeWorkloadMetaRepo {
    @Insert(
        """
        INSERT INTO 
        runtime_workload_meta(
            gmt_create, 
            gmt_modified,
            app_name,
            resource_group,
            runtime_workload_id,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            is_deleted,
            creator,
            modifier,
            name_space
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{appName},
            #{resourceGroup},
            #{runtimeWorkloadId},
            #{site},
            #{unit},
            #{stage},
            #{clusterId},
            #{status},
            #{isDeleted},
            #{creator},
            #{modifier},
            #{nameSpace}
        )
        """
    )
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(runtimeWorkloadMeta: RuntimeWorkloadMeta): Int

    @Select(
        """
        SELECT
            id,
            runtime_workload_id,
            app_name,
            resource_group,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            gmt_create, 
            gmt_modified,
            creator,
            modifier,
            is_deleted,
            name_space
        FROM
            runtime_workload_meta
        WHERE
            runtime_workload_id = #{runtimeWorkloadId} and is_deleted = 'N'
    """
    )
    fun findByRuntimeWorkloadId(@Param("runtimeWorkloadId") runtimeWorkloadId: String): RuntimeWorkloadMeta?

    @Select(
        """
        SELECT
            id,
            runtime_workload_id,
            app_name,
            resource_group,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            gmt_create, 
            gmt_modified,
            creator,
            modifier,
            is_deleted,
            name_space
        FROM
            runtime_workload_meta
        WHERE
            runtime_workload_id = #{runtimeWorkloadId} and is_deleted = 'N'
    """
    )
    fun findById(@Param("id") id: Long): RuntimeWorkloadMeta?


    @Select(
        """
        <script>
            SELECT
                id,
                runtime_workload_id,
                app_name,
                resource_group,
                site, 
                unit,
                stage,
                cluster_id,
                status,
                gmt_create, 
                gmt_modified,
                creator,
                modifier,
                is_deleted,
                name_space
            FROM
                runtime_workload_meta
            WHERE
                app_name = #{appName} and resource_group in 
                <foreach collection="resourceGroupList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                and is_deleted = 'N' 
        </script>
    """
    )
    fun findByAppNameAndAppGroupList(
        @Param("appName") appName: String,
        @Param("resourceGroupList") resourceGroupList: List<String>
    ): List<RuntimeWorkloadMeta>

    @Select(
        """
        SELECT
            id,
            runtime_workload_id,
            app_name,
            resource_group,
            site, 
            unit,
            stage,
            cluster_id,
            status,
            gmt_create, 
            gmt_modified,
            creator,
            modifier,
            is_deleted,
            name_space
        FROM
            runtime_workload_meta
        WHERE
            app_name = #{appName} and is_deleted = 'N' 
    """
    )
    fun listByAppName(
        @Param("appName") appName: String
    ): List<RuntimeWorkloadMeta>

    @Update(
        """
        UPDATE 
            runtime_workload_meta
        SET
            status = #{status},
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun updateStatusById(
        @Param("id") id: Long,
        @Param("status") status: String,
        @Param("modifier") modifier: String
    ): Int


    @Update(
        """
        UPDATE 
            runtime_workload_meta
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int
}