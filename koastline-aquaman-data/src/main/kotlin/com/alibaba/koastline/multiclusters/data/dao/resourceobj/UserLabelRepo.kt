package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.resourceobj.UserLabel
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface UserLabelRepo {

    @Insert("""
        INSERT INTO 
        user_label(
            external_id,
            external_type,
            label_name,
            label_value,
            submitter,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{externalId},
            #{externalType},
            #{labelName},
            #{labelValue},
            #{submitter},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(userLabel: UserLabel): Int

    @Select("""
        SELECT 
            id,
            external_id,
            external_type,
            label_name,
            label_value,
            submitter,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            user_label
        WHERE
            id = #{id}
    """)
    fun findById(id: Long): UserLabel?

    @Select("""
        SELECT 
            id,
            external_id,
            external_type,
            label_name,
            label_value,
            submitter,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            user_label
        WHERE
            external_id = #{externalId} and external_type = #{externalType} and is_deleted = 'N'
    """)
    fun findByExternal(@Param("externalId") externalId: String, @Param("externalType")  externalType: String): List<UserLabel>

    @Select("""
         <script>
            SELECT 
                id,
                external_id,
                external_type,
                label_name,
                label_value,
                submitter,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted
            FROM
                user_label
            WHERE
                label_name = #{labelName} 
                AND external_type = #{externalType} 
                AND is_deleted = 'N'
                <if test="idToken != null">
                    AND id &lt; #{idToken}
                </if>
                <if test="externalId != null">
                    AND external_id LIKE "%${'$'}{externalId}%"
                </if>
                <if test="labelValue != null">
                    and label_value = #{labelValue}
                </if>
        </script>
    """)
    fun findByExternalTypeAndName(
        @Param("externalType") externalType: String,
        @Param("labelName") labelName: String,
        @Param("idToken") idToken: Long? = null,
        @Param("externalId") externalId: String? = null,
        @Param("labelValue") labelValue: String? = null,
    ): List<UserLabel>

    @Select("""
         <script>
            SELECT 
                DISTINCT(label_value)
            FROM
                user_label
            WHERE
                label_name = #{labelName} 
                AND external_type = #{externalType} 
                AND is_deleted = 'N'
        </script>
    """)
    fun findDistinctValueByExternalTypeAndName(
        @Param("externalType") externalType: String,
        @Param("labelName") labelName: String,
    ): List<String>

    @Select("""
        SELECT 
            id,
            external_id,
            external_type,
            label_name,
            label_value,
            submitter,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            user_label
        WHERE
            external_id = #{externalId} and external_type = #{externalType}
            and label_name = #{labelName} and is_deleted = 'N'
        order by id desc limit 1
    """)
    fun findByExternalAndLabel(@Param("externalId") externalId: String, @Param("externalType")  externalType: String, @Param("labelName")  labelName: String): UserLabel?

    @Select("""
        <script>
        SELECT 
            id,
            external_id,
            external_type,
            label_name,
            label_value,
            submitter,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            user_label
        WHERE  
            external_id IN 
            <foreach collection="externalIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND external_type = #{externalType}
            AND label_name = #{labelName} 
            AND is_deleted = 'N'
            <if test="labelValue != null">
                AND label_value = #{labelValue}
            </if>
        </script>
    """)
    fun batchQueryByMultiExternalAndLabel(
        @Param("externalIdList") externalIdList: List<String>,
        @Param("externalType") externalType: String,
        @Param("labelName") labelName: String,
        @Param("labelValue") labelValue: String?
    ): List<UserLabel>

    @Update("""
        UPDATE user_label
        SET
           gmt_modified = now(),
           modifier = #{modifier},
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(@Param("id") id: Long,@Param("modifier") modifier: String): Int
}