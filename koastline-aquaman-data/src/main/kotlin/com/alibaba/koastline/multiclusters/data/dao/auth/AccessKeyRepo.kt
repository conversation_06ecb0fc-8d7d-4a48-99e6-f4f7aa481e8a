package com.alibaba.koastline.multiclusters.data.dao.auth

import com.alibaba.koastline.multiclusters.data.vo.auth.AccessKey
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * <AUTHOR>
 */
@Mapper
interface AccessKeyRepo {

    @Insert("""
        INSERT INTO 
        access_key(gmt_create, gmt_modified, owner, access_key, access_secret, open_api_acl)
        VALUES
        (#{gmtCreate}, #{gmtModified}, #{owner}, #{accessKey}, #{accessSecret}, #{openApiAcl})
    """)
    fun insertAccessKey(accessKey: AccessKey): Int

    @Select("""
        SELECT 
        id, gmt_create, gmt_modified, owner, access_key, access_secret, open_api_acl
        FROM
        access_key
        WHERE
        owner = #{owner}
    """)
    fun findAccessKeyByOwner(owner: String): AccessKey?

    @Update("""
        UPDATE 
            access_key
        SET
            open_api_acl = #{openApiAcl}, gmt_modified = now()
        WHERE
            owner = #{owner}
        LIMIT 1
    """)
    fun updateOpenApiAcl(@Param("owner") owner: String,@Param("openApiAcl") openApiAcl: String?)
}