package com.alibaba.koastline.multiclusters.data.utils

object DataValidateUtils {
    /**
     * 保证查询时候存在过滤条件 不允许全量过滤
     */
    fun hasNotNullProperty(properties: List<String?>): Boolean {
        if (properties.isEmpty()) {
            return false
        }
        properties.forEach {
            it?.let {
                return true
            }
        }
        return false
    }

    /**
     * 保证所有元素都不是blank元素且不能为空list
     */
    fun notEmptyAndAllNotBlank(properties: List<String>): Boolean {
        if (properties.isEmpty()) {
            return false
        }
        properties.forEach {
            if (it.isBlank()) {
                return false
            }
        }
        return true
    }
}