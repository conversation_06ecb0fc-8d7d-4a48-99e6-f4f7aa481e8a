package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.AppRuntimeEnvironmentData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface AppRuntimeEnvironmentDataRepo {
    @Insert("""
        INSERT INTO 
        app_runtime_environment_data(
            gmt_create, 
            gmt_modified, 
            runtime_env_key, 
            name, 
            creator, 
            managed_cluster_key, 
            region,
            az,
            stage,
            unit,
            status,
            meta_data, 
            is_deleted,
            modifier
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{runtimeEnvKey},
            #{name}, 
            #{creator}, 
            #{managedClusterKey}, 
            #{region},
            #{az},
            #{stage},
            #{unit},
            #{status},
            #{metaData},
            #{isDeleted},
            #{modifier}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(runtimeEnvironmentData: AppRuntimeEnvironmentData): Int

    @Select("""
        SELECT 
            id,
            runtime_env_key, 
            name, 
            creator, 
            managed_cluster_key, 
            region,
            az,
            stage,
            unit,
            status,
            meta_data,
            gmt_create, 
            gmt_modified,
            is_deleted,
            modifier
        FROM 
            app_runtime_environment_data
        WHERE
            runtime_env_key = #{runtimeEnvKey} AND is_deleted = 'N'
    """)
    fun findByRuntimeEnvKey(runtimeEnvKey: String): AppRuntimeEnvironmentData?

    @Select("""
        <script>
            SELECT 
                id,
                runtime_env_key, 
                name, 
                creator, 
                managed_cluster_key, 
                region,
                az,
                stage,
                unit,
                status,
                meta_data,
                gmt_create, 
                gmt_modified,
                is_deleted,
                modifier
            FROM 
                app_runtime_environment_data
            WHERE
                 is_deleted = 'N'
                 <if test="keyWords != null">
                    and ( runtime_env_key like "${'$'}{keyWords}%" or name like "${'$'}{keyWords}%" )
                 </if>
                 <if test="unit != null">
                    and unit = #{unit}
                </if>
                <if test="site != null">
                    and az = #{site}
                </if>
                <if test="stage != null">
                    and stage = #{stage}
                </if>
                <if test="status != null">
                    and status = #{status}
                </if>
        </script>
    """)
    fun listByConditions(
        @Param("keyWords") keyWords: String? = null, @Param("unit") unit: String? = null,
        @Param("site") site: String? = null, @Param("stage") stage: String? = null,
        @Param("status") status: String? = null
    ): List<AppRuntimeEnvironmentData>

    @Select(
        """
        <script>
            SELECT 
                id,
                runtime_env_key, 
                name, 
                creator, 
                managed_cluster_key, 
                region,
                az,
                stage,
                unit,
                status,
                meta_data,
                gmt_create, 
                gmt_modified,
                is_deleted,
                modifier
            FROM 
                app_runtime_environment_data
            WHERE
                 is_deleted = 'N'
                 and unit = #{unit}
                 and az = #{site}
                 and stage = #{stage}
        </script>
    """
    )
    fun listBySiteAndStageAndUnit(
        @Param("unit") unit: String,
        @Param("site") site: String,
        @Param("stage") stage: String,
    ): List<AppRuntimeEnvironmentData>

    @Select("""
        SELECT 
            id,
            runtime_env_key, 
            name, 
            creator, 
            managed_cluster_key, 
            region,
            az,
            stage,
            unit,
            status,
            meta_data,
            gmt_create, 
            gmt_modified,
            is_deleted,
            modifier
        FROM 
            app_runtime_environment_data
        WHERE
            region = #{region} AND az = #{az} AND stage = #{stage} AND unit = #{unit} AND is_deleted = 'N'
    """)
    fun findByRegionAndAzAndStageAndUnit(@Param("region") region: String,@Param("az")  az: String,
                                         @Param("stage") stage: String,@Param("unit")  unit: String): List<AppRuntimeEnvironmentData>

    @Select("""
        <script>
            SELECT 
                id,
                runtime_env_key, 
                name, 
                creator, 
                managed_cluster_key, 
                region,
                az,
                stage,
                unit,
                status,
                meta_data,
                gmt_create, 
                gmt_modified,
                is_deleted,
                modifier
            FROM 
                app_runtime_environment_data
            WHERE
                is_deleted = 'N' and runtime_env_key in
                <foreach collection="runtimeEnvKeyList" item="runtimeEnvKey" open="(" separator="," close=")">
                    #{runtimeEnvKey}
                </foreach>
        </script>
    """)
    fun findByRuntimeEnvKeyList(@Param("runtimeEnvKeyList") runtimeEnvKeyList: List<String>): List<AppRuntimeEnvironmentData>

    @Select("""
        <script>
            SELECT 
                id,
                runtime_env_key, 
                name, 
                creator, 
                managed_cluster_key, 
                region,
                az,
                stage,
                unit,
                status,
                meta_data,
                gmt_create, 
                gmt_modified,
                is_deleted,
                modifier
            FROM 
                app_runtime_environment_data
            WHERE
                    runtime_env_key IN
                    <foreach collection="runtimeEnvKeyList" item="runtimeEnvKey" open="(" separator="," close=")">
                        #{runtimeEnvKey}
                    </foreach>
                AND
                    stage IN
                    <foreach collection="stageList" item="stage" open="(" separator="," close=")">
                        #{stage}
                    </foreach>
                AND is_deleted = 'N'
        </script>
    """)
    fun findByKeyAndStage(@Param("runtimeEnvKeyList") runtimeEnvKeyList: List<String>, @Param("stageList") stageList: List<String>): List<AppRuntimeEnvironmentData>

    @Update("""
        UPDATE 
            app_runtime_environment_data
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE
            runtime_env_key = #{runtimeEnvKey}  AND is_deleted = 'N'
    """)
    fun delByRuntimeEnvKey(runtimeEnvKey: String): Int

    @Update(
        """
        <script>
            UPDATE 
                app_runtime_environment_data
            SET
                status = #{status},
                name = #{name},
                gmt_modified = now()
            WHERE
                runtime_env_key = #{runtimeEnvKey}  AND is_deleted = 'N'
        </script>
    """
    )
    fun updateNameAndStatusByRuntimeEnvKey(
        @Param("runtimeEnvKey") runtimeEnvKey: String,
        @Param("status") status: String,
        @Param("name") name: String?,
        @Param("modifier")modifier:String?
    ): Int

    @Delete("""
        DELETE
        FROM
            app_runtime_environment_data
        WHERE
            runtime_env_key = #{runtimeEnvKey}
        LIMIT 1
    """)
    fun physicalDelByRuntimeEnvKey(runtimeEnvKey: String): Int
}