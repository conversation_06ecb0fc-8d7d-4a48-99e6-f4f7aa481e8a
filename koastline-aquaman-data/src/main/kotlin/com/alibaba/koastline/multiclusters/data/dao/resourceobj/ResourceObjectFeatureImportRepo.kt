package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImport
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImportWithMatchScope
import com.alibaba.koastline.multiclusters.data.vo.env.ResourceObjectFeatureImportWithMatchScopeCombine
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ResourceObjectFeatureImportRepo {
    @Insert("""
        INSERT INTO 
        resource_object_feature_import(
            resource_object_feature_key,
            status,
            param_map,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            version
        )
        VALUES(
            #{resourceObjectFeatureKey},
            #{status},
            #{paramMap},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted},
            #{version}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourceObjectFeatureImport: ResourceObjectFeatureImport): Int

    @Select("""
        SELECT 
            id,
            resource_object_feature_key,
            status,
            param_map,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted,
            version
        FROM
            resource_object_feature_import
        WHERE
            id = #{id}
    """)
    fun findById(@Param("id") id: Long): ResourceObjectFeatureImport?

    @Select(
        """
        <script>
            SELECT 
                id,
                resource_object_feature_key,
                status,
                param_map,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted,
                version
            FROM
                resource_object_feature_import
            WHERE
                id in 
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                AND is_deleted = 'N'
        </script>
    """
    )
    fun findByIdList(@Param("idList") idList: List<Long>): List<ResourceObjectFeatureImport>

    @Select(
        """
        <script>
            SELECT 
                id,
                resource_object_feature_key,
                status,
                param_map,
                creator,
                modifier,
                gmt_create,
                gmt_modified,
                is_deleted,
                version
            FROM
                resource_object_feature_import
            WHERE
                is_deleted = 'N'
                AND id in 
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="paramKeyWords != null">
                    AND param_map like "%${'$'}{paramKeyWords}%"
                </if>
        </script>
    """
    )
    fun findByConditions(
        @Param("idList") idList: List<Long>,
        @Param("paramKeyWords") paramKeyWords: String? = null,
        @Param("status") status: String? = null
    ): List<ResourceObjectFeatureImport>

    @Select("""
        SELECT 
            a.id,
            a.resource_object_feature_key,
            a.status,
            a.param_map,
            a.creator,
            a.modifier,
            a.gmt_create,
            a.gmt_modified,
            a.is_deleted,
            a.version
        FROM
            resource_object_feature_import a 
        JOIN
            match_scope_data b
        ON
            b.external_type = #{matchScopeExternalType} and b.external_id = #{matchScopeExternalId} and b.target_type = 'ResourceObjectFeatureImport' and b.target_id = a.id and a.resource_object_feature_key = #{resourceObjectFeatureKey}
            AND a.is_deleted = 'N' AND b.is_deleted = 'N'
        LIMIT 1
    """)
    fun findByMatchScopeAndFeatureKey(@Param("matchScopeExternalType") matchScopeExternalType: String, @Param("matchScopeExternalId") matchScopeExternalId: String, @Param("resourceObjectFeatureKey") resourceObjectFeatureKey: String): ResourceObjectFeatureImport?

    /**
     * 带有分页能力进行特性+match宽表数据合并查询
     * 从性能上考虑，nextToken指定为targetId
     * 由于tarGetId非唯一，因此对于nextToken需要通过>=进行约束。
     * asi需要在查询后针对已经处理过的feature进行过滤
     *
     * @param keyList 特性key列表
     * @param status 特性注入状态
     * @param matchScopeIdToken 匹配范围id
     * @return
     */
    @Select(
        """
        <script>
            SELECT 
                featureImport.id,
                scope.id,
                featureImport.resource_object_feature_key,
                featureImport.param_map,
                scope.external_id,
                scope.external_type,
                scope.restrictions
            FROM
                resource_object_feature_import featureImport
            JOIN 
                match_scope_data scope
            ON
                scope.target_type = 'ResourceObjectFeatureImport'
                AND scope.target_id = featureImport.id
                AND scope.is_deleted = 'N'
            WHERE
                featureImport.resource_object_feature_key IN 
                <foreach collection="resourceObjectFeatureKeyList" item="key" open="(" separator="," close=")">
                    #{key}
                </foreach>
                AND featureImport.status = #{importStatus}
                AND featureImport.is_deleted = "N"   
                <if test="matchScopeIdToken != null">
                    AND scope.target_id <![CDATA[ >= ]]> #{matchScopeIdToken}
                </if>
                AND scope.external_type IN 
                <foreach collection="exTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            LIMIT #{pageSize}
        </script>
        """
    )
    fun findCombineByKeys(
        @Param("importStatus") status: String,
        @Param("resourceObjectFeatureKeyList") keyList: List<String>,
        @Param("exTypeList") exTypeList: List<String>,
        @Param("matchScopeIdToken") matchScopeIdToken: String? = null,
        @Param("pageSize") pageSize: Int,
    ): List<ResourceObjectFeatureImportWithMatchScopeCombine>

  @Select(
    """
    <script>
        SELECT 
            a.id,
            a.resource_object_feature_key,
            a.status,
            a.param_map,
            a.creator,
            a.modifier,
            a.gmt_create,
            a.gmt_modified,
            a.is_deleted,
            a.version,
            b.external_id,
            b.external_type
        FROM
            resource_object_feature_import a 
        JOIN
            match_scope_data b
        ON
            b.external_type = #{matchScopeExternalType} and 
            b.external_id in 
                <foreach collection="matchScopeExternalIds" item="matchScopeExternalId" open="(" separator="," close=")">
                    #{matchScopeExternalId}
                </foreach> and 
            b.target_type = 'ResourceObjectFeatureImport' and 
            b.target_id = a.id and 
            a.resource_object_feature_key = #{resourceObjectFeatureKey} AND 
            a.is_deleted = 'N' AND 
            b.is_deleted = 'N'
    </script>
    """
  )
  fun findByMatchScopeAndFeatureKeyBatch(
    @Param("matchScopeExternalType") matchScopeExternalType: String,
    @Param("matchScopeExternalIds") matchScopeExternalIds: List<String>,
    @Param("resourceObjectFeatureKey") resourceObjectFeatureKey: String
  ): List<ResourceObjectFeatureImportWithMatchScope>

    @Select("""
        SELECT 
            a.id,
            a.resource_object_feature_key,
            a.status,
            a.param_map,
            a.creator,
            a.modifier,
            a.gmt_create,
            a.gmt_modified,
            a.is_deleted,
            a.version
        FROM
            resource_object_feature_import a 
        JOIN
            match_scope_data b
        ON
            b.external_type = #{matchScopeExternalType} and b.external_id = #{matchScopeExternalId} and b.target_type = 'ResourceObjectFeatureImport' and b.target_id = a.id and a.resource_object_feature_key = #{resourceObjectFeatureKey}
            AND a.is_deleted = 'N' AND b.is_deleted = 'N'
    """)
    fun listByMatchScopeAndFeatureKey(@Param("matchScopeExternalType") matchScopeExternalType: String, @Param("matchScopeExternalId") matchScopeExternalId: String, @Param("resourceObjectFeatureKey") resourceObjectFeatureKey: String): List<ResourceObjectFeatureImport>

    @Update("""
        UPDATE resource_object_feature_import
        SET
            param_map = #{paramMap},
            status = #{status},
            modifier = #{modifier},
            gmt_modified = now(),
            version = #{version}
        WHERE
            id = #{id}
    """)
    fun updateById(
        @Param("id") id: Long,
        @Param("modifier") modifier: String,
        @Param("paramMap") paramMap: String,
        @Param("status") status: String,
        @Param("version") version: String
    ): Int

    @Update("""
        UPDATE resource_object_feature_import
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(@Param("id") id: Long): Int
}