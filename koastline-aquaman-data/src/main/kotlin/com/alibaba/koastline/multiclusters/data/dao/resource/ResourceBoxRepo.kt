package com.alibaba.koastline.multiclusters.data.dao.resource

import com.alibaba.koastline.multiclusters.data.vo.resource.Resource
import com.alibaba.koastline.multiclusters.data.vo.resource.ResourceBox
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * @author:    <EMAIL>
 * @date:    2024/5/14 4:34 PM
 */
@Mapper
interface ResourceBoxRepo {
    @Insert("""
        INSERT INTO 
        resource_box(
            resource_id,
            spec,
            version,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{resourceId},
            #{spec},
            #{version},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(resourceBox: ResourceBox): Int

    @Select("""
        SELECT 
            id,
            resource_id,
            spec,
            version,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_box
        WHERE
            id = #{id}
    """)
    fun findById(id: Long): ResourceBox?

    @Select("""
        SELECT 
            id,
            resource_id,
            spec,
            version,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_box
        WHERE
            resource_id = #{resourceId} AND version = #{version}
    """)
    fun findByResourceIdAndVersion(@Param("resourceId")resourceId: Long,@Param("version") version: Int): ResourceBox?


    @Select("""
        SELECT 
            id,
            resource_id,
            spec,
            version,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            resource_box
        WHERE
            resource_id = #{resourceId}
        ORDER BY ID DESC 
        LIMIT 1
    """)
    fun findLatestByResourceId(@Param("resourceId")resourceId: Long): ResourceBox?

    @Update("""
        UPDATE resource_box
        SET
           gmt_modified = now(),
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
    fun deleteById(id: Long): Int

    @Update("""
        UPDATE resource_box
        SET 
            gmt_modified = now(),
            spec = #{spec}
        WHERE
            id=#{id}
    """)
    fun updateSpecById(@Param("id")id: Long, @Param("spec")spec: String)
}