package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.SystemComponentsData
import org.apache.ibatis.annotations.*
import java.util.*

/**
 * <AUTHOR>
 */
@Mapper
interface SystemComponentsRepo {

    @Insert("""
        INSERT INTO 
        system_components_data(
            gmt_create,
            gmt_modified,
            managed_cluster_key,
            annotations            
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{managedClusterKey},
            #{annotations}            
        )
    """)
    fun insertSystemComponents(systemComponentsData: SystemComponentsData): Int

    @Select("""
        SELECT 
            id,
            managed_cluster_key,
            annotations,
            gmt_create,
            gmt_modified
        FROM
            system_components_data
        WHERE
            managed_cluster_key = #{clusterKey}
    """)
    fun querySystemComponentsByClusterKey(clusterKey: String): SystemComponentsData?

    @Select("""
        SELECT 
            id,
            managed_cluster_key,
            annotations,
            gmt_create,
            gmt_modified
        FROM
            system_components_data
        WHERE
            id = #{systemComponentsDataId}
    """)
    fun querySystemComponentsById(systemComponentsDataId: Long): SystemComponentsData?

    @Update("""
        UPDATE system_components_data
        SET
            gmt_modified = #{gmtModified},
            annotations = #{annotations}
        WHERE
            managed_cluster_key = #{clusterKey}
    """)
    fun updateSystemComponentsByClusterKey(@Param("clusterKey") clusterKey: String, @Param("annotations") annotations: String, @Param("gmtModified") gmtModified: Date): Int

    @Delete("""
        DELETE FROM
            system_components_data
        WHERE
            id=#{id}
    """)
    fun deleteSystemComponentsById(id: Long): Int
}