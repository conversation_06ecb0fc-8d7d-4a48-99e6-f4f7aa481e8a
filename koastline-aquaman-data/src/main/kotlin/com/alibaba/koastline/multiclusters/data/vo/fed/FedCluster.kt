package com.alibaba.koastline.multiclusters.data.vo.fed

import java.time.Instant
import java.util.*

/**
 * fed集群cluster
 *
 * @property id
 * @property fedEnvName
 * @property region
 * @property site
 * @property fedClusterKey
 * @property status
 * @property tenantManageClusterKey
 * @property memberManageClusterKey
 * @property creator
 * @property modifier
 * @property gmtCreate
 * @property gmtModified
 * @property isDeleted
 */
data class FedCluster(
    val id: Long? = null,
    val fedEnvName: String,
    val region: String,
    val site: String? = null,
    val fedClusterKey: String, // record unique key
    val status: String,
    val tenantManageClusterKey: String, // to record tenantManageClusterKey
    val memberManageClusterKey: String, // to record memberMangeClusterKey
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String,
)