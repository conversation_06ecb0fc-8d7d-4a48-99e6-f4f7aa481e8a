package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREDefaultFeatureSpec
import org.apache.ibatis.annotations.*

@Mapper
interface ApREDefaultFeatureSpecRepo {
    @Insert("""
        INSERT INTO
        apre_default_feature_spec(
            gmt_create,
            gmt_modified,
            feature_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            annotations,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{featureKey},
            #{title},
            #{specType},
            #{specCode},
            #{scope},
            #{status},
            #{annotations},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apREDefaultFeatureSpec: ApREDefaultFeatureSpec): Int

    @Select("""
        SELECT 
            id,
            feature_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_default_feature_spec
        WHERE
            feature_key = #{featureKey} and is_deleted = 'N'
    """)
    fun queryByFeatureKey(@Param("featureKey") featureKey: String): List<ApREDefaultFeatureSpec>

    @Select("""
        SELECT 
            id,
            feature_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            annotations,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_default_feature_spec
        WHERE
            feature_key = #{featureKey} and status =#{status} and is_deleted = 'N'
    """)
    fun queryByFeatureKeyAndStatus(@Param("featureKey") featureKey: String, @Param("status") status: String): List<ApREDefaultFeatureSpec>

    @Update("""
        UPDATE
            apre_default_feature_spec
        SET
            gmt_modified = now(),
            title = #{title},
            spec_type = #{specType},
            spec_code = #{specCode},
            scope = #{scope},
            status = #{status},
            annotations = #{annotations},
            is_deleted = #{isDeleted}
        WHERE 
            id = #{id}
    """)
    fun updateById(apREDefaultFeatureSpec: ApREDefaultFeatureSpec): Int

    @Update("""
        UPDATE
            apre_default_feature_spec
        SET
            gmt_modified = now(),
            status = #{status}
        WHERE 
            id = #{id}
    """)
    fun updateStatusById(@Param("id") id: Long, @Param("status") status: String): Int

    @Update("""
        UPDATE
            apre_default_feature_spec
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int
}