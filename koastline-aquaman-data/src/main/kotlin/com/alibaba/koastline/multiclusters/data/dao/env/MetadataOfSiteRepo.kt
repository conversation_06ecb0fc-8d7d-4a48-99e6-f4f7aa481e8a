package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.MetadataOfSite
import org.apache.ibatis.annotations.*
import java.util.StringJoiner

@Mapper
interface MetadataOfSiteRepo {
    @Insert("""
        INSERT INTO 
        metadata_of_site(
            gmt_create, 
            gmt_modified, 
            site, 
            region, 
            is_deleted,
            creator,
            modifier
        )
        VALUES(
            #{gmtCreate}, 
            #{gmtModified},
            #{site},
            #{region},
            #{isDeleted},
            #{creator},
            #{modifier}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(metadataOfSite: MetadataOfSite): Int

    @Select("""
        SELECT
            id,
            site, 
            region, 
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            metadata_of_site
        WHERE
            site = #{site} and is_deleted = 'N'
    """)
    fun findBySite(@Param("site") site: String): MetadataOfSite?

    @Select("""
        SELECT
            id,
            site, 
            region, 
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            metadata_of_site
        WHERE
            site = #{site} and region = #{region} and is_deleted = 'N'
    """)
    fun findBySiteAndRegion(@Param("site") site: String, @Param("region") region: String): MetadataOfSite?

    @Select("""
        SELECT
            id,
            site, 
            region, 
            gmt_create,
            gmt_modified,
            is_deleted,
            creator,
            modifier
        FROM
            metadata_of_site
        WHERE
            is_deleted = 'N'
    """)
    fun listAll(): List<MetadataOfSite>

    @Select("""
        <script>
            SELECT
                id,
                site, 
                region, 
                gmt_create,
                gmt_modified,
                is_deleted,
                creator,
                modifier
            FROM
                metadata_of_site
            WHERE
                is_deleted = 'N'
                <if test="region != null">
                    and region = #{region}
                </if>
                <if test="site != null">
                    and site like "${'$'}{site}%"
                </if>
        </script>
    """
    )
    fun listByProperties(
        @Param("region") region: String?,
        @Param("site") site: String?
    ): List<MetadataOfSite>

    @Update(
        """
        UPDATE 
            metadata_of_site
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            id = #{id}
    """
    )
    fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int

    @Update(
        """
        UPDATE
            metadata_of_site
        SET
            is_deleted = 'Y',
            gmt_modified = now(),
            modifier = #{modifier}
        WHERE
            site = #{site} and is_deleted = 'N'
    """
    )
    fun deleteBySite(@Param("site") site: String, @Param("modifier") modifier: String): Int
}