package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApREFeatureSpec
import org.apache.ibatis.annotations.*

@Mapper
interface ApREFeatureSpecRepo {
    @Insert("""
        INSERT INTO
        apre_feature_spec(
            gmt_create,
            gmt_modified,
            apre_label_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            labels,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{apRELabelKey},
            #{title},
            #{specType},
            #{specCode},
            #{scope},
            #{status},
            #{sourceType},
            #{sourceId},
            #{versionType},
            #{versionId},
            #{annotations},
            #{labels},
            #{isDeleted}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apREFeatureSpec: ApREFeatureSpec): Int

    @Select("""
        SELECT 
            id,
            apre_label_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            labels,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_feature_spec
        WHERE
            apre_label_key = #{apRELabelKey} and is_deleted = 'N'
    """)
    fun queryByApRELabelKey(@Param("apRELabelKey") apRELabelKey: String): List<ApREFeatureSpec>

    @Select("""
        <script>
        SELECT 
            id,
            apre_label_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            labels,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_feature_spec
        WHERE apre_label_key IN 
            <foreach collection="apRELabelKeyList" item="apRELabelKey" open="(" separator="," close=")">
                #{apRELabelKey}
            </foreach> and status = #{status} and is_deleted = 'N'
        </script>
    """)
    fun batchQueryByApRELabelKeyAndStatus(@Param("apRELabelKeyList") apRELabelKeyList: List<String>, @Param("status") status: String): List<ApREFeatureSpec>

    @Select("""
        SELECT 
            id,
            apre_label_key,
            title,
            spec_type,
            spec_code,
            scope,
            status,
            source_type,
            source_id,
            version_type,
            version_id,
            annotations,
            labels,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            apre_feature_spec
        WHERE
            apre_label_key = #{apRELabelKey} and status = #{status} and is_deleted = 'N'
    """)
    fun queryByApRELabelKeyAndStatus(@Param("apRELabelKey") apRELabelKey: String, @Param("status") status: String): List<ApREFeatureSpec>

    @Update("""
        UPDATE
            apre_feature_spec
        SET
            gmt_modified = now(),
            title = #{title},
            spec_type = #{specType},
            spec_code = #{specCode},
            scope = #{scope},
            status = #{status},
            source_type = #{sourceType},
            source_id = #{sourceId},
            version_type = #{versionType},
            version_id = #{versionId},
            annotations = #{annotations},
            labels = #{labels},
            is_deleted = #{isDeleted}
        WHERE 
            id = #{id}
    """)
    fun updateById(apREFeatureSpec: ApREFeatureSpec): Int

    @Update("""
        UPDATE
            apre_feature_spec
        SET
            gmt_modified = now(),
            status = #{status}
        WHERE 
            id = #{id}
    """)
    fun updateStatusById(@Param("id") id: Long,@Param("status") status: String): Int

    @Update("""
        UPDATE
            apre_feature_spec
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int

    @Update("""
        UPDATE
            apre_feature_spec
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE 
            apre_label_key = #{labelKey} and spec_code = #{specCode} and is_deleted = 'N'
        LIMIT 1
    """)
    fun deleteByLabelKeyAndSpecCode(@Param("labelKey") labelKey: String, @Param("specCode") specCode: String): Int
}