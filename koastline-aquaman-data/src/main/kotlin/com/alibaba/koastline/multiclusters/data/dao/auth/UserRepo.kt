package com.alibaba.koastline.multiclusters.data.dao.auth

import com.alibaba.koastline.multiclusters.data.vo.auth.UserData
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 */
@Mapper
interface UserRepo {
    @Insert("""
        INSERT INTO 
        user_data(gmt_create, gmt_modified, user_name, employee_user_name, employee_id) 
        VALUES 
        (#{gmtCreate},#{gmtModified},#{userName},#{employeeUserName}, #{employeeId})
        """)
    fun insertUser(user: UserData): Int
    @Select("""
        SELECT 
        id, gmt_create, gmt_modified, user_name, employee_user_name, employee_id
        FROM 
        user_data
        WHERE
        user_name = #{username}
    """)
    fun findUserByUserName(username: String): UserData?

}