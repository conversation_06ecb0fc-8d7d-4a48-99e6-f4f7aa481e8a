package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ClusterEnvironmentBindingData
import org.apache.ibatis.annotations.*

/**
 * <AUTHOR>
 */
@Mapper
interface ClusterBindingRepo {

    @Insert("""
        INSERT INTO 
        cluster_environment_binding_data(
            gmt_create,
            gmt_modified,
            cluster_env_key,
            external_id,
            external_id_type,
            selector,
            is_deleted
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{clusterEnvKey},
            #{externalId},
            #{externalIdType},
            #{selector},
            #{isDeleted}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun createClusterBindingData(clusterEnvironmentBindingData: ClusterEnvironmentBindingData): Int

    @Select("""
        SELECT
            id,
            cluster_env_key,
            external_id,
            external_id_type,
            gmt_create,
            gmt_modified,
            selector,
            is_deleted
        FROM 
            cluster_environment_binding_data
        WHERE
            cluster_env_key = #{clusterEnvKey} and is_deleted = "N"
    """)
    fun listClusterBindingData(clusterEnvKey: String): List<ClusterEnvironmentBindingData>

    @Select("""
        SELECT
            id,
            cluster_env_key,
            external_id,
            external_id_type,
            gmt_create,
            gmt_modified,
            selector,
            is_deleted
        FROM 
            cluster_environment_binding_data
        WHERE
            id = #{id} and is_deleted = "N"
    """)
    fun findById(id: Long): ClusterEnvironmentBindingData?

    @Select("""
        <script>
            SELECT
                id,
                cluster_env_key,
                external_id,
                external_id_type,
                gmt_create,
                gmt_modified,
                selector,
                is_deleted
            FROM 
                cluster_environment_binding_data
            WHERE
                is_deleted = "N" and 
                id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
        </script>
    """)
    fun listByIdList(@Param("idList") idList: List<Long>): List<ClusterEnvironmentBindingData>

    @Select(
        """
        <script>
            SELECT
              cluster_environment_binding_data.id,
              cluster_environment_binding_data.cluster_env_key,
              cluster_environment_binding_data.external_id,
              cluster_environment_binding_data.external_id_type,
              cluster_environment_binding_data.gmt_create,
              cluster_environment_binding_data.gmt_modified,
              cluster_environment_binding_data.selector,
              cluster_environment_binding_data.is_deleted
            FROM
              cluster_environment_binding_data
              JOIN app_runtime_environment_data 
              ON cluster_environment_binding_data.cluster_env_key = app_runtime_environment_data.runtime_env_key
            WHERE
              cluster_environment_binding_data.id IN 
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
              AND cluster_environment_binding_data.is_deleted = "N"
              AND app_runtime_environment_data.is_deleted = "N"
              <if test="site != null">
                AND app_runtime_environment_data.az = #{site}
              </if>
              <if test="unit != null">
                AND app_runtime_environment_data.unit = #{unit}
              </if>
              <if test="stage != null">
                AND app_runtime_environment_data.stage = #{stage}
              </if> 
        </script>
    """
    )
    fun listLegalBindingDataByIdList(
        @Param("idList") idList: List<Long>,
        @Param("site") site: String? = null,
        @Param("unit") unit: String? = null,
        @Param("stage") stage: String? = null,
    ): List<ClusterEnvironmentBindingData>

    @Select("""
        SELECT
            id,
            cluster_env_key,
            external_id,
            external_id_type,
            gmt_create,
            gmt_modified,
            selector,
            is_deleted
        FROM
            cluster_environment_binding_data
        WHERE
            external_id_type = #{externalType}
            and external_id = #{externalId}
            and is_deleted = "N"
    """)
    fun listClusterBindingDataByExternalId(@Param("externalType") externalType: String, @Param("externalId") externalId: String): List<ClusterEnvironmentBindingData?>?

    @Select("""
        SELECT
            id,
            cluster_env_key,
            external_id,
            external_id_type,
            gmt_create,
            gmt_modified,
            selector,
            is_deleted
        FROM
            cluster_environment_binding_data
        WHERE
            external_id_type = #{externalType} 
            and is_deleted = "N"
    """)
    fun findClusterBindingDataByExternalType(@Param("externalType") externalType: String) : List<ClusterEnvironmentBindingData?>?

    @Select("""
        SELECT
            id,
            cluster_env_key,
            external_id,
            external_id_type,
            gmt_create,
            gmt_modified,
            selector,
            is_deleted
        FROM
            cluster_environment_binding_data
        WHERE
            cluster_env_key = #{clusterEnvKey} 
            and is_deleted = "N"
    """)
    fun listClusterBindingDataByClusterEnvKey(@Param("clusterEnvKey") clusterEnvKey: String) : List<ClusterEnvironmentBindingData>

    @Update("""
        UPDATE
            cluster_environment_binding_data
        SET
            external_id = #{externalId},
            gmt_modified = now()
        WHERE
            cluster_env_key = #{clusterEnvKey}
        AND
            external_id_type = #{externalType}
        AND
            external_id = #{externalIdBase}
    """)
    fun updateClusterBindingDataByClusterEnvironmentKey(@Param("clusterEnvKey") clusterEnvKey: String,
                                                        @Param("externalType") externalType: String,
                                                        @Param("externalId") externalId: String,
                                                        @Param("externalIdBase") externalIdBase: String): Int

    @Update("""
        UPDATE
            cluster_environment_binding_data
        SET
            selector = #{selector},
            gmt_modified = now()
        WHERE
            id = #{id}
    """)
    fun updateClusterBindingSelectorDataById(@Param("id") id: Long, @Param("selector") selector: String?): Int

    @Update("""
        UPDATE 
            cluster_environment_binding_data
        SET
            is_deleted = "Y",
            gmt_modified = now()
        WHERE 
            cluster_env_key = #{clusterEnvKey}
    """)
    fun deleteClusterBindingDataByClusterEnvKey(clusterEnvKey: String): Int

    @Update(
        """
        UPDATE 
            cluster_environment_binding_data
        SET
            is_deleted = "Y",
            gmt_modified = now()
        WHERE 
            id = #{id}
    """)
    fun deleteClusterBindingDataById(id: Long): Int
}