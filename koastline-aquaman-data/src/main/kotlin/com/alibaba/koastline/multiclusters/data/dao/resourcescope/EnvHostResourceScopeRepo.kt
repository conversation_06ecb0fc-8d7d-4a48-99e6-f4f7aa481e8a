package com.alibaba.koastline.multiclusters.data.dao.resourcescope

import com.alibaba.koastline.multiclusters.data.vo.resourcescope.EnvHostResourceScope
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Options
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * @author:    <EMAIL>
 * @description:  环境主机模式资源范围定义
 * @date:    2025/3/7 3:35 PM
 */
@Mapper
interface EnvHostResourceScopeRepo {
        @Insert("""
        INSERT INTO 
        env_host_resource_scope(
            app_name,
            current_env_stack_id,
            base_env_stack_id,
            resource_scope,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        )
        VALUES(
            #{appName},
            #{currentEnvStackId},
            #{baseEnvStackId},
            #{resourceScope},
            #{creator},
            #{modifier},
            #{gmtCreate},
            #{gmtModified},
            #{isDeleted}
        )
    """)
        @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
        fun insert(envHostResourceScope: EnvHostResourceScope): Int

        @Select("""
        SELECT 
            id,
            app_name,
            current_env_stack_id,
            base_env_stack_id,
            resource_scope,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_host_resource_scope
        WHERE
            id = #{id}
    """)
        fun findById(id: Long): EnvHostResourceScope?

        @Select("""
        SELECT 
            id,
            app_name,
            current_env_stack_id,
            base_env_stack_id,
            resource_scope,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_host_resource_scope
        WHERE
            current_env_stack_id = #{currentEnvStackId} and is_deleted = 'N'
        LIMIT 1
    """)
        fun findByCurrentEnvStackId(currentEnvStackId: String): EnvHostResourceScope?

        @Select("""
        SELECT 
            id,
            app_name,
            current_env_stack_id,
            base_env_stack_id,
            resource_scope,
            creator,
            modifier,
            gmt_create,
            gmt_modified,
            is_deleted
        FROM
            env_host_resource_scope
        WHERE
            app_name = #{appName} and is_deleted = 'N'
    """)
        fun listByAppName(appName: String): List<EnvHostResourceScope>

        @Update("""
        UPDATE env_host_resource_scope
        SET
           gmt_modified = now(),
           modifier = #{modifier},
           is_deleted = 'Y'
        WHERE
            id=#{id}
    """)
        fun deleteById(@Param("id") id: Long, @Param("modifier") modifier: String): Int

        @Update("""
        UPDATE env_host_resource_scope
        SET
           gmt_modified = now(),
           modifier = #{modifier},
           is_deleted = 'Y'
        WHERE
            current_env_stack_id=#{currentEnvStackId}
    """)
        fun deleteByCurrentEnvStackId(@Param("currentEnvStackId") currentEnvStackId: String, @Param("modifier") modifier: String): Int
}