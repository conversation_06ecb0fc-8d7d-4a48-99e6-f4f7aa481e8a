package com.alibaba.koastline.multiclusters.data.dao.env

import com.alibaba.koastline.multiclusters.data.vo.env.ApRELabel
import org.apache.ibatis.annotations.*

@Mapper
interface ApRELabelRepo {
    @Insert("""
        INSERT INTO
        apre_label(
            gmt_create,
            gmt_modified,
            runtime_env_key,
            name,
            value,
            is_deleted,
            apre_label_key,
            target_type,
            type
        )
        VALUES(
            #{gmtCreate},
            #{gmtModified},
            #{targetKey},
            #{name},
            #{value},
            #{isDeleted},
            #{apRELabelKey},
            #{targetType},
            #{type}
        )
        """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(apRELabel: ApRELabel): Int

    @Select("""
        SELECT 
            id,
            runtime_env_key,
            name,
            value,
            gmt_create,
            gmt_modified,
            is_deleted,
            apre_label_key,
            target_type,
            type
        FROM
            apre_label
        WHERE
            runtime_env_key = #{targetKey} and target_type = #{targetType} and is_deleted = 'N'
    """)
    fun findByTarget(@Param("targetKey") targetKey: String, @Param("targetType") targetType: String): List<ApRELabel>

    @Select("""
        SELECT 
            id,
            runtime_env_key,
            name,
            value,
            gmt_create,
            gmt_modified,
            is_deleted,
            apre_label_key,
            target_type,
            type
        FROM
            apre_label
        WHERE
            name = #{name} and value like "${'$'}{valuePrefix}%" and is_deleted = 'N'
    """
    )
    fun findByNameAndValuePrefix(@Param("name") name: String, @Param("valuePrefix") valuePrefix: String):List<ApRELabel>


    @Select(
        """
        SELECT 
            DISTINCT(value)
        FROM
            apre_label
        WHERE
            name = #{name} and value like "${'$'}{valuePrefix}%" and is_deleted = 'N' and type = #{type}
    """
    )
    fun findValuesByNameAndValuePrefixAndType(
        @Param("name") name: String,
        @Param("valuePrefix") valuePrefix: String,
        @Param("type") type: String
    ): List<String>


    @Select("""
        SELECT 
            id,
            runtime_env_key,
            name,
            value,
            gmt_create,
            gmt_modified,
            is_deleted,
            apre_label_key,
            target_type,
            type
        FROM
            apre_label
        WHERE
            name = #{name} and value = #{value} and is_deleted = 'N'
    """)
    fun findByNameAndValue(@Param("name") name: String, @Param("value") value: String): List<ApRELabel>

    @Select("""
        SELECT 
            id,
            runtime_env_key,
            name,
            value,
            gmt_create,
            gmt_modified,
            is_deleted,
            apre_label_key,
            target_type,
            type
        FROM
            apre_label
        WHERE
            apre_label_key = #{apRELabelKey} and is_deleted = 'N'
    """)
    fun findByApRELabelKey(@Param("apRELabelKey") apRELabelKey: String): ApRELabel?

    @Update(
        """
        UPDATE
            apre_label
        SET
            runtime_env_key = #{targetKey},
            name = #{name},
            value = #{value},
            gmt_modified = #{gmtModified},
            target_type = #{targetType} 
        WHERE
            apre_label_key = #{apRELabelKey} and is_deleted = 'N'
    """
    )
    fun updateByApRELabelKey(
        apRELabel: ApRELabel
    ): ApRELabel

    @Update("""
        UPDATE
            apre_label
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE 
            id = #{id}
    """)
    fun deleteById(@Param("id") id: Long): Int

    @Update("""
        UPDATE
            apre_label
        SET
            is_deleted = 'Y',
            gmt_modified = now()
        WHERE 
            apre_label_key = #{apRELabelKey}
    """)
    fun deleteByApRELabelKey(@Param("apRELabelKey") apRELabelKey: String): Int

    /**
     * 匹配不同的类型的name,实现对不同类型的ApRELabel进行删除管控
     * */
    @Update(
        """
        <script>
            UPDATE
                apre_label
            SET
                is_deleted = 'Y',
                gmt_modified = now()
            WHERE 
                runtime_env_key = #{targetKey} and target_type = #{targetType} and is_deleted = 'N' and type = #{type}
        </script>
        """
    )
    fun deleteByTargetAndType(
        @Param("targetKey") targetKey: String, @Param("targetType") targetType: String,
        @Param("type") type: String
    ): Int


    @Update(
        """
        <script>
            UPDATE
                apre_label
            SET
                is_deleted = 'Y',
                gmt_modified = now()
            WHERE 
                runtime_env_key = #{targetKey} and target_type = #{targetType} and is_deleted = 'N'
        </script>
        """
    )
    fun deleteByTarget(
        @Param("targetKey") targetKey: String, @Param("targetType") targetType: String
    ): Int
}