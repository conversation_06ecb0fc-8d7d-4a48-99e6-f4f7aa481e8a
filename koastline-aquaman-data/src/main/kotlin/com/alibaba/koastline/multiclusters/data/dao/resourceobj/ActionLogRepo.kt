package com.alibaba.koastline.multiclusters.data.dao.resourceobj

import com.alibaba.koastline.multiclusters.data.vo.env.ActionLog
import org.apache.ibatis.annotations.*

@Mapper
interface ActionLogRepo {
    @Insert("""
        INSERT INTO 
        action_log(
            source_type,
            source_id,
            action_type,
            target_type,
            target_name,
            params,
            operator,
            gmt_create,
            submitters
        )
        VALUES(
            #{sourceType},
            #{sourceId},
            #{actionType},
            #{targetType},
            #{targetName},
            #{params},
            #{operator},
            #{gmtCreate},
            #{submitters}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(actionLog: ActionLog): Int

    @Select("""
    <script>
        SELECT 
            a.id,
            a.source_type,
            a.source_id,
            a.action_type,
            a.target_type,
            a.target_name,
            a.params,
            a.operator,
            a.gmt_create,
            a.submitters
        FROM
            action_log a 
        WHERE 
            a.target_type = 'ResourceObjectFeatureImport' and a.source_type = #{matchScopeExternalType} and a.source_id = #{matchScopeExternalId} 
            <if test="submitters != null and submitters != 'ALL'">
                AND a.submitters = #{submitters}
            </if>
        ORDER BY
            a.gmt_create
        DESC
    </script>
    """)
    fun listByMatchScopeWithSubmitters(
        @Param("matchScopeExternalType") matchScopeExternalType: String,
        @Param("matchScopeExternalId") matchScopeExternalId: String,
        @Param("submitters") submitters: String,
    ): List<ActionLog>

}