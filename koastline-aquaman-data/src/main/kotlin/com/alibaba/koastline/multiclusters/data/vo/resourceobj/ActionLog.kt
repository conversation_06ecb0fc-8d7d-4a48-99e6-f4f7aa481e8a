package com.alibaba.koastline.multiclusters.data.vo.env

import java.time.Instant
import java.util.*

data class ActionLog(

    val id: Long? = null,

    /*
    来源类型，例如环境/分组/应用
     */
    val sourceType: String,

    /*
    来源，例如环境名，分组名，应用名
     */
    val sourceId: String,

    /*
    事件类型，例如创建/更新/删除
     */
    val actionType: String,

    /*
    操作对象类型，例如环境特性
     */
    val targetType: String,

    /*
    操作对象名称，例如环境特性key
     */
    val targetName: String,

    /*
    额外参数
     */
    val params: String,

    val operator: String,

    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),

    /*
    租户名称，例如LLM表示大模型类型特性族
     */
    val submitters: String,
)
