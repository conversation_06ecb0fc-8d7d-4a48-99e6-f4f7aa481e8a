package com.alibaba.koastline.multiclusters.data.dao.auth

import com.alibaba.koastline.multiclusters.data.vo.UserRoleData
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 */
@Mapper
interface UserRoleRepo {
    @Insert("""
        INSERT INTO
        user_role_data(gmt_create, gmt_modified, user_name,role)
        VALUES
        (#{gmtCreate}, #{gmtModified}, #{userName}, #{role})
    """)
    fun insertUserRole(userRoleData: UserRoleData): Int
    @Select("""
        SELECT 
        id, gmt_create, gmt_modified, user_name, role
        FROM 
        user_role_data
        WHERE
        user_name = #{username}
    """)
    fun findUserRoleByUserName(username:String): UserRoleData?
}