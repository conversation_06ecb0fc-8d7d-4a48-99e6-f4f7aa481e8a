package com.alibaba.koastline.multiclusters.data.vo.resourcescope

import java.time.Instant
import java.util.Date

/**
 * @author:    <EMAIL>
 * @description:  环境主机模式资源范围配置
 * @date:    2025/3/7 3:31 PM
 */
data class EnvHostResourceScope(
    val id: Long? = null,
    val appName: String,
    /**
     * 当前环境StackId
     */
    val currentEnvStackId: String,
    /**
     * 基准环境StackId
     */
    val baseEnvStackId: String,
    val resourceScope: String,
    /**
     * 通用属性
     */
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N"
)

data class EnvHostWorkloadMeta(
    val id: Long? = null,
    val envStackId: String,
    val appName: String,
    val resourceGroup: String,
    /**
     * 站点
     */
    val site: String,
    /**
     * 单元
     */
    val unit: String,
    /**
     * 用途
     */
    val stage: String,
    /**
     * 集群ID
     */
    val clusterId: String,
    val creator: String,
    val modifier: String,
    val gmtCreate: Date = Date(Instant.now().toEpochMilli()),
    val gmtModified: Date = Date(Instant.now().toEpochMilli()),
    val isDeleted: String = "N",
)