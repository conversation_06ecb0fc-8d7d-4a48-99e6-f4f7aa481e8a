/******************************************/
/*   DatabaseName = koastline_aquaman 资源对象实体表   */
/******************************************/
CREATE TABLE `resource_object_feature` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `title` varchar(64) NOT NULL COMMENT '标题',
   `resource_object_feature_key` varchar(64) NOT NULL COMMENT '资源对象特性key',
   `use_scope` varchar(64) NOT NULL COMMENT '使用范围',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_resource_object_feature_key` (`resource_object_feature_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源对象特性';

CREATE TABLE `resource_object_feature_protocol` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `resource_object_feature_key` varchar(64) NOT NULL COMMENT '资源对象特性key',
   `protocol` varchar(64) NOT NULL COMMENT '协议',
   `version` varchar(64) NULL COMMENT '版本',
   `patch` text DEFAULT NULL COMMENT '片段',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   KEY `idx_resource_object_feature_key` (`resource_object_feature_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源对象特性协议';

CREATE TABLE `resource_object_feature_param` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `resource_object_feature_key` varchar(64) NOT NULL COMMENT '资源对象特性key',
   `title` varchar(64) NOT NULL COMMENT '标题',
   `code` varchar(64) NULL COMMENT '代码',
   `type` varchar(64) NOT NULL COMMENT '类型',
   `required` varchar(64) NOT NULL COMMENT '是否必填',
   `optional_value` text DEFAULT NULL COMMENT '可选字段',
   `value_constraint` text DEFAULT NULL COMMENT '约束',
   PRIMARY KEY (`id`),
   KEY `idx_resource_object_feature_key` (`resource_object_feature_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源对象特性参数';

CREATE TABLE `resource_object_feature_import` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
     `resource_object_feature_key` varchar(64) NOT NULL COMMENT '资源对象特性key',
     `status` varchar(32) NOT NULL COMMENT '状态',
     `param_map` text DEFAULT NULL COMMENT '预制参数',
     `creator` varchar(64) NOT NULL COMMENT '创建人工号',
     `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
     `gmt_create` datetime NOT NULL COMMENT '创建时间',
     `gmt_modified` datetime NOT NULL COMMENT '修改时间',
     `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
     PRIMARY KEY (`id`),
     KEY `idx_resource_object_feature_key` (`resource_object_feature_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源对象特性导入';

INSERT INTO `resource_object_feature` (`resource_object_feature_key`,`title`,`use_scope`,`creator`,`modifier`,`gmt_create`,`gmt_modified`,`is_deleted`) VALUES ('RESOURCE_SPEC','资源规格','SCALE_OUT','admin','admin','2022-11-04 00:00:00','2022-11-04 00:00:00','N');
INSERT INTO `resource_object_feature` (`resource_object_feature_key`,`title`,`use_scope`,`creator`,`modifier`,`gmt_create`,`gmt_modified`,`is_deleted`) VALUES ('RESOURCE_SPEC','资源规格','DEPLOY,SCALE_OUT','admin','admin','2022-11-04 00:00:00','2022-11-04 00:00:00','N');
INSERT INTO `resource_object_feature_protocol` (`resource_object_feature_key`,`protocol`,`version`,`patch`,`creator`,`modifier`,`gmt_create`,`gmt_modified`,`is_deleted`) VALUES ('RESOURCE_SPEC','StatefulSet',null,'spec:
  template:
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${user.resources.requests.cpu}"
              memory: "${user.resources.requests.memory}"
              ephemeral-storage: "${user.resources.requests.disk}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${user.resources.requests.gpu}"
              </#if>
            limits:
              cpu: "${user.resources.limits.cpu}"
              memory: "${user.resources.limits.memory}"
              ephemeral-storage: "${user.resources.limits.disk}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${user.resources.limits.gpu}"
              </#if>','admin','admin','2022-11-04 00:00:00','2022-11-04 00:00:00','N');

INSERT INTO `resource_object_feature_protocol` (`resource_object_feature_key`,`protocol`,`version`,`patch`,`creator`,`modifier`,`gmt_create`,`gmt_modified`,`is_deleted`) VALUES ('RESOURCE_SPEC','CloneSet',null,'spec:
  template:
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "${user.resources.requests.cpu}"
              memory: "${user.resources.requests.memory}"
              ephemeral-storage: "${user.resources.requests.disk}"
              <#if user.resources.requests.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${user.resources.requests.gpu}"
              </#if>
            limits:
              cpu: "${user.resources.limits.cpu}"
              memory: "${user.resources.limits.memory}"
              ephemeral-storage: "${user.resources.limits.disk}"
              <#if user.resources.limits.gpu?? && user.resources.requests.gpu?trim?length gt 0>
              nvidia.com/gpu: "${user.resources.limits.gpu}"
              </#if>','admin','admin','2023-03-07 00:00:00','2023-03-07 00:00:00','N');


CREATE TABLE `user_label` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `external_id` varchar(256) NOT NULL COMMENT '范围ID',
   `external_type` varchar(64) NOT NULL COMMENT '范围类型',
   `label_name` varchar(64) NOT NULL COMMENT '标签',
   `label_value` text NOT NULL COMMENT '标签值',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `submitter` varchar(255) NULL COMMENT '提交系统',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   KEY `idx_isdeleted_external_labelname` (`is_deleted`,`external_id`,`external_type`,`label_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户标签';

ALTER TABLE `resource_object_feature_protocol`add strategy TEXT DEFAULT NULL COMMENT '协议对应的注入策略'

ALTER TABLE `resource_object_feature`
    ADD COLUMN `type` varchar(128) NOT NULL DEFAULT 'INPUT' COMMENT '特性类型（例如输入、拦截等）'

alter table resource_object_feature add effective_stage varchar(255) NOT NULL DEFAULT 'AFTER_VERSIONOUT' COMMENT '特性的生效阶段，取值为 AFTER_VERSIONOUT（出版本后） 和 DURING_VERSIONOUT（出版本时）'

alter table resource_object_feature add submitters varchar(255) NOT NULL DEFAULT 'SYSTEM' COMMENT '特性的提交来源，取值为 SYSTEM（平台） 和 USER-1，USER-2（三方用户）'

alter table resource_object_feature add json_schema text COMMENT '协议参数的 Json Schema 定义'

/******************************************/
/*   DatabaseName = koastline_aquaman   */
/*   TableName = action_log   */
/******************************************/
CREATE TABLE `action_log` (
                              `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `action_type` varchar(256) NOT NULL COMMENT '操作类型（新增/修改/删除等）',
                              `action_target` varchar(256) NOT NULL COMMENT '操作对象',
                              `description` text NOT NULL COMMENT '操作描述',
                              `params` text NOT NULL COMMENT '参数 JSON 保存',
                              `operator` varchar(64) NOT NULL COMMENT '操作人',
                              `gmt_create` datetime NOT NULL COMMENT '操作时间',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志记录'
;

alter table action_log add source_type varchar(256) COMMENT '日志来源类型，例如应用/分组/环境等';
alter table action_log add source_id varchar(256) COMMENT '日志来源ID，例如应用名/分组名/环境stackid等';
alter table action_log CHANGE COLUMN description target_name varchar(512) COMMENT '操作目标名称，例如特性Key';
alter table action_log add target_name varchar(512) NOT NULL DEFAULT '' COMMENT '操作目标名称，例如特性Key';
alter table action_log CHANGE COLUMN action_target target_type varchar(256) NOT NULL COMMENT '操作对象';
alter table action_log add target_type varchar(256) NOT NULL DEFAULT '' COMMENT '操作对象';

ALTER TABLE `resource_object_feature`
    ADD COLUMN `display_theme` varchar(255) NOT NULL DEFAULT 'TREE' COMMENT '特性的展示样式，有表格式的 TABLE 和树形的 TREE 两种';


/******************************************/
/*   DatabaseName = hcrm_grop   */
/*   TableName = config_dispatch_label_value   */
/******************************************/
CREATE TABLE `config_dispatch_label_value` (
                                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                               `gmt_modified` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                               `creator` varchar(32) NOT NULL COMMENT '提交方（调用系统）',
                                               `modifier` varchar(32) NOT NULL COMMENT '修改方（调用系统）',
                                               `is_deleted` tinyint(4) NOT NULL COMMENT '是否删除',
                                               `app_name` varchar(128) NOT NULL COMMENT '应用名称',
                                               `label_code` varchar(64) NOT NULL COMMENT '标签名',
                                               `label_value` varchar(2048) NOT NULL COMMENT '标签值',
                                               `submit_system` varchar(64) DEFAULT '0' COMMENT '调用系统',
                                               `group_name` varchar(256) DEFAULT NULL COMMENT '分组名',
                                               `idc` varchar(256) DEFAULT NULL COMMENT '机房',
                                               `unit` varchar(256) DEFAULT NULL COMMENT '单元',
                                               `env` varchar(256) DEFAULT NULL COMMENT '环境',
                                               `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `uk_app_info` (`app_name`(32),`idc`(16),`unit`(32),`group_name`(64),`env`(16),`label_code`(32))
) ENGINE=InnoDB AUTO_INCREMENT=1672759 DEFAULT CHARSET=utf8mb4 COMMENT='基于应用四元组的调度标签值'
;


/******************************************/
/*   DatabaseName = hcrm_grop   */
/*   TableName = config_dispatch_label   */
/******************************************/
CREATE TABLE `config_dispatch_label` (
                                         `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                         `gmt_modified` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                         `creator` varchar(256) DEFAULT NULL COMMENT '创建人',
                                         `modifier` varchar(256) DEFAULT NULL COMMENT '修改人',
                                         `is_deleted` tinyint(4) NOT NULL COMMENT '是否删除',
                                         `code` varchar(64) NOT NULL COMMENT '代号（英文名字）',
                                         `cn_name` varchar(64) DEFAULT NULL COMMENT '标签的中文名',
                                         `type` varchar(32) NOT NULL DEFAULT 'public' COMMENT '类型',
                                         `scope` varchar(16) NOT NULL DEFAULT 'ALL' COMMENT '作用域，对应枚举值：container、nc、all',
                                         `specific_resource_pool` varchar(32) DEFAULT NULL COMMENT '标签适用于指定资源池，字段为空表示适用于所有资源池',
                                         `is_specific_value` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '标签的value是否可以为任意值，默认为任意值(boolean值为0)',
                                         `specific_value` text COMMENT '指定值内容，json结构',
                                         `status` varchar(16) NOT NULL DEFAULT 'ONLINE' COMMENT '状态，对应枚举值：wait_to_approve, available, rejected',
                                         `description` varchar(512) DEFAULT NULL COMMENT '标签的描述',
                                         `approve_id` varchar(128) DEFAULT NULL COMMENT 'bpms审批单号',
                                         `submit_system` varchar(64) NOT NULL DEFAULT 'hcrm-grop' COMMENT '提交方（系统名称）',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COMMENT='全局资源管理配置中心调度标签定义'
;

ALTER TABLE `resource_object_feature`
    ADD COLUMN `version` varchar(256) NOT NULL DEFAULT '0.0.1' COMMENT '版本号';

ALTER TABLE `resource_object_feature_protocol`
    MODIFY COLUMN `version` varchar(256) NOT NULL DEFAULT '0.0.1' COMMENT '版本号';

ALTER TABLE `resource_object_feature_import`
    ADD COLUMN `version` varchar(256) NOT NULL DEFAULT '0.0.1' COMMENT '版本号';

ALTER TABLE `resource_object_feature`
DROP KEY `uk_resource_object_feature_key`,
	ADD UNIQUE KEY `uk_resource_object_feature_key_version` (`resource_object_feature_key`,`version`);


ALTER TABLE `action_log`
    ADD COLUMN `submitters` varchar(256) NOT NULL DEFAULT 'SYSTEM' COMMENT '租户/提交方';


CREATE TABLE `kv_map` (
                          `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                          `type` varchar(64) NOT NULL COMMENT '映射类型',
                          `key_name` varchar(128) NOT NULL COMMENT 'Key',
                          `value` varchar(512) NOT NULL COMMENT 'Value',
                          `creator` varchar(64) NOT NULL COMMENT '创建人工号',
                          `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
                          `gmt_create` datetime NOT NULL COMMENT '创建时间',
                          `gmt_modified` datetime NOT NULL COMMENT '修改时间',
                          `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
                          PRIMARY KEY (`id`),
                          KEY `idx_type_keyname_value_isdeleted` (`type`,`key_name`,`value`,`is_deleted`),
                          KEY `idx_type_keyname_isdeleted` (`type`,`key_name`,`is_deleted`)
) DEFAULT CHARACTER SET=utf8mb4 AUTO_INCREMENT=1 COMMENT='通用Key-Value映射关系表';


