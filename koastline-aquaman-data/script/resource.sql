CREATE TABLE `resource` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `kind` varchar(255) NOT NULL COMMENT '资源对象特性key',
    `name` varchar(255) NOT NULL COMMENT '协议唯一标识',
    `service_provider` varchar(255) NOT NULL COMMENT '服务提供方',
    `controller` tinyint(1) DEFAULT 1 NOT NULL COMMENT '是否受控于父级资源文件',
    `need_recycling` tinyint(1) DEFAULT 1 NOT NULL COMMENT '是否需要回收',
    `baseline_box_id` bigint(20) NOT NULL COMMENT '资源基线版本ID',
    `creator` varchar(64) NOT NULL COMMENT '创建人工号',
    `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_kind_name` (`kind`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源';

CREATE TABLE `resource_box` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `resource_id` bigint(20) NOT NULL COMMENT '资源ID',
    `spec` text DEFAULT NULL COMMENT '协议',
    `version` int NOT NULL COMMENT '服务提供方',
    `creator` varchar(64) NOT NULL COMMENT '创建人工号',
    `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_resource_id_version` (`resource_id`,`version`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源版本';

CREATE TABLE `resource_owner_reference` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `sub_resource_id` bigint(20) NOT NULL COMMENT '子资源ID',
    `owner_ref_kind` varchar(255) NOT NULL COMMENT '关联父资源类型',
    `owner_ref_name` varchar(255) NOT NULL COMMENT '关联父资源唯一标识',
    `block_owner_deletion` tinyint(1) DEFAULT 1 NOT NULL COMMENT '是否阻塞OwnerRef资源删除',
    `creator` varchar(64) NOT NULL COMMENT '创建人工号',
    `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源关联关系';