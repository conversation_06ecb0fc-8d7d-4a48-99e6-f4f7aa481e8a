CREATE TABLE `env_host_resource_scope` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_name` varchar(255) NOT NULL COMMENT '应用名',
    `current_env_stack_id` varchar(255) NOT NULL COMMENT '当前环境StackId',
    `base_env_stack_id` varchar(255) NOT NULL COMMENT '基准环境StackId',
    `resource_scope` text DEFAULT NULL COMMENT '资源范围',
    `creator` varchar(64) NOT NULL COMMENT '创建人工号',
    `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_current_env_stack_id` (`current_env_stack_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='环境主机资源范围配置';

CREATE TABLE `env_host_workload_meta` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `app_name` varchar(255) NOT NULL COMMENT '应用名',
   `env_stack_id` varchar(255) NOT NULL COMMENT '环境StackId',
   `resource_group` varchar(255) NOT NULL COMMENT '分组',
   `site` varchar(255) NOT NULL COMMENT '机房',
   `unit` varchar(255) NOT NULL COMMENT '单元',
   `stage` varchar(255) NOT NULL COMMENT '用途',
   `cluster_id` varchar(255) NOT NULL COMMENT '集群',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   KEY `idx_env_stack_id_is_deleted` (`env_stack_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='环境主机Workload元数据';