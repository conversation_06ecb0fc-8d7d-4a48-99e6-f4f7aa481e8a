/******************************************/
/*   DatabaseName = koastline_aquaman   */
/******************************************/
CREATE TABLE `env_level_stage_mapping` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `env_level` varchar(255) NOT NULL COMMENT '环境级别',
   `stage` varchar(64) NOT NULL COMMENT '用途标',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='环境级别&资源用途映射表';
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','production','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','gray','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','beta','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','spe','SMALLFLOW','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','staging','PRE_PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','daily','DAILY','N');

-- 基座Runtime
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','production-runtime','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','production-runtime','SMALLFLOW','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','production-runtime','PRE_PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','production-runtime','DAILY','N');

INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','testing-runtime','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','testing-runtime','SMALLFLOW','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','testing-runtime','PRE_PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','testing-runtime','DAILY','N');

INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','staging-runtime','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','staging-runtime','SMALLFLOW','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','staging-runtime','PRE_PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','staging-runtime','DAILY','N');

INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','SMALLFLOW-runtime','PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','SMALLFLOW-runtime','SMALLFLOW','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','SMALLFLOW-runtime','PRE_PUBLISH','N');
INSERT INTO `env_level_stage_mapping` (`gmt_create`,`gmt_modified`,`env_level`,`stage`,`is_deleted`) VALUES ('2022-07-18 00:00:00','2022-07-18 00:00:00','SMALLFLOW-runtime','DAILY','N');

CREATE TABLE `app_runtime_environment_data` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `runtime_env_key` varchar(64) NOT NULL COMMENT 'key',
    `name` varchar(64) DEFAULT NULL COMMENT '名称',
    `creator` varchar(64) NOT NULL COMMENT '创建者',
    `managed_cluster_key` varchar(64) NOT NULL COMMENT '管控集群索引key',
    `region` varchar(64) NOT NULL COMMENT '四元组之region',
    `az` varchar(64) NOT NULL COMMENT '四元组之az',
    `stage` varchar(64) NOT NULL COMMENT '四元组之用途标',
    `unit` varchar(64) NOT NULL COMMENT '四元组之单元',
    `status` varchar(32) NOT NULL DEFAULT 'online' COMMENT 'online/wait_online/offline',
    `meta_data` text DEFAULT NULL COMMENT 'apre元数据',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_runtime_env_key` (`runtime_env_key`),
    KEY `idx_region_az_stage_unit_is_deleted` (`region`,`az`,`stage`,`unit`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境数据';

CREATE TABLE `apre_label` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `runtime_env_key` varchar(64) NOT NULL COMMENT 'key',
    `name` varchar(255) DEFAULT NULL COMMENT '名称',
    `value` varchar(255) NOT NULL COMMENT '值',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    `apre_label_key` varchar(64) NOT NULL COMMENT 'key',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_apre_label_key` (`apre_label_key`),
    KEY `idx_runtime_env_key` (`runtime_env_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境标签';

CREATE TABLE `apre_feature_spec` (
      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
      `gmt_create` datetime NOT NULL COMMENT '创建时间',
      `gmt_modified` datetime NOT NULL COMMENT '修改时间',
      `apre_label_key` varchar(64) NOT NULL COMMENT '标签key',
      `title` varchar(255) DEFAULT NULL COMMENT '标题',
      `spec_type` varchar(64) DEFAULT NOT NULL COMMENT '规格类型',
      `spec_code` varchar(255) DEFAULT NULL COMMENT '规格代码',
      `scope` varchar(64) NOT NULL COMMENT '范围',
      `status` varchar(64) NOT NULL COMMENT '状态',
      `source_type` varchar(255) DEFAULT NULL COMMENT '来源类型',
      `source_id` varchar(255) DEFAULT NULL COMMENT '来源ID',
      `version_type` varchar(255) DEFAULT NULL COMMENT '来源版本类型',
      `version_id` varchar(255) DEFAULT NULL COMMENT '来源版本ID',
      `annotations` text DEFAULT NULL COMMENT '注释',
      `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
      PRIMARY KEY (`id`),
      KEY `idx_apre_label_key` (`apre_label_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境特性规格';

CREATE TABLE `apre_deed` (
      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
      `gmt_create` datetime NOT NULL COMMENT '创建时间',
      `gmt_modified` datetime NOT NULL COMMENT '修改时间',
      `deed_key` varchar(64) NOT NULL COMMENT 'key',
      `env_level` varchar(64) NOT NULL COMMENT '环境级别',
      `app_name` varchar(64) NOT NULL COMMENT '应用名',
      `env_id` varchar(64) NULL COMMENT '环境ID',
      `env_stack_id` varchar(64) NULL COMMENT '环境StackID',
      `node_group` varchar(64) NULL COMMENT '分组名',
      `content` text NOT NULL COMMENT '内容',
      `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_deed_key` (`deed_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='契约';

CREATE TABLE `apre_default_feature` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `feature_key` varchar(64) NOT NULL COMMENT 'key',
    `code` varchar(64) NOT NULL COMMENT '代码',
    `feature_usage` varchar(32) NOT NULL COMMENT '使用方式：默认导入/指定导入',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='缺省特性';

CREATE TABLE `apre_default_feature_spec` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `feature_key` varchar(64) NOT NULL COMMENT '特性key',
    `title` varchar(255) DEFAULT NULL COMMENT '标题',
    `spec_type` varchar(64) DEFAULT NOT NULL COMMENT '规格类型',
    `spec_code` varchar(255) DEFAULT NULL COMMENT '规格代码',
    `scope` varchar(64) NOT NULL COMMENT '范围',
    `status` varchar(64) NOT NULL COMMENT '状态',
    `annotations` text DEFAULT NULL COMMENT '注释',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_feature_key` (`feature_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='缺省特性规格';

CREATE TABLE `metadata_constraint` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
     `gmt_create` datetime NOT NULL COMMENT '创建时间',
     `gmt_modified` datetime NOT NULL COMMENT '修改时间',
     `site` varchar(64) NOT NULL COMMENT '站点',
     `unit` varchar(64) NOT NULL COMMENT '单元',
     `stage` varchar(64) NOT NULL COMMENT '用途',
     `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据约束';

CREATE TABLE `metadata_of_site` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `site` varchar(64) NOT NULL COMMENT '站点',
   `region` varchar(64) NOT NULL COMMENT '区域',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_site_region` (`site`,`region`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据之站点';

CREATE TABLE `apre_deed_resource_group_binding_data` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `apre_deed_key` varchar(64) NOT NULL COMMENT '声明KEY',
   `resource_group` varchar(256) NOT NULL COMMENT '分组名',
   `app_name` varchar(256) NOT NULL COMMENT '应用名',
   PRIMARY KEY (`id`),
   KEY `idx_resource_group` (`resource_group`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境声明与分组绑定关系';

CREATE TABLE `apre_declaration_patch_data` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
     `gmt_create` datetime NOT NULL COMMENT '创建时间',
     `gmt_modified` datetime NOT NULL COMMENT '修改时间',
     `balance_type` varchar(64) NOT NULL COMMENT '均衡类型',
     `region` varchar(64) NULL COMMENT '四元组之region',
     `stage` varchar(64) NOT NULL COMMENT '四元组之用途标',
     `unit` varchar(64) NOT NULL COMMENT '四元组之单元',
     `site` varchar(64) NULL COMMENT '站点',
     `declaration_patch` text NULL COMMENT '补充声明',
     `creator` varchar(64) NOT NULL COMMENT '创建人工号',
     `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
     `priority` int NOT NULL COMMENT '优先级',
     `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境声明补偿数据';

CREATE TABLE `match_scope_data` (
       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
       `gmt_create` datetime NOT NULL COMMENT '创建时间',
       `gmt_modified` datetime NOT NULL COMMENT '修改时间',
       `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人工号',
       `modifier` varchar(64) NOT NULL DEFAULT '' COMMENT '修改人工号',
       `target_id` bigint(20) NOT NULL COMMENT '目标ID',
       `target_type` varchar(64) NOT NULL COMMENT '目标类型',
       `external_id` varchar(256) NOT NULL COMMENT '范围ID',
       `external_type` varchar(64) NOT NULL COMMENT '范围类型',
       `exclusions` text NULL COMMENT '排除的子范围',
       `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除'
       PRIMARY KEY (`id`),
       UNIQUE KEY `uk_external_type_external_id_target_type_target_id` (`external_type`,`external_id`,`target_type`,`target_id`),
       KEY `idx_target_type_target_id` (`target_type`,`target_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='匹配范围';

CREATE TABLE `component_data` (
       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
       `gmt_create` datetime NOT NULL COMMENT '创建时间',
       `gmt_modified` datetime NOT NULL COMMENT '修改时间',
       `code` varchar(64) NOT NULL COMMENT '组件代码',
       `ref_object_id` varchar(64) NOT NULL COMMENT '关联对象ID',
       `ref_object_type` varchar(64) NOT NULL COMMENT '关联对象类型',
       `annotations` text NULL COMMENT '注释',
       `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
       PRIMARY KEY (`id`),
       KEY `idx_ref_object_type_ref_object_id` (`ref_object_type`,`ref_object_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='组件数据';

CREATE TABLE `resource_pool_data` (
      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
      `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人工号',
      `modifier` varchar(64) NOT NULL DEFAULT '' COMMENT '修改人工号',
      `gmt_create` datetime NOT NULL COMMENT '创建时间',
      `gmt_modified` datetime NOT NULL COMMENT '修改时间',
      `cluster_id` varchar(64) NOT NULL COMMENT '集群ID',
      `managed_cluster_key` varchar(64) NOT NULL COMMENT '管控面KEY',
      `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
      PRIMARY KEY (`id`),
      KEY `idx_managed_cluster_key` (`managed_cluster_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='虚拟资源池';

CREATE TABLE `apre_resource_group_binding_data` (
      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
      `gmt_create` datetime NOT NULL COMMENT '创建时间',
      `gmt_modified` datetime NOT NULL COMMENT '修改时间',
      `app_name` varchar(64) NOT NULL COMMENT '应用名',
      `resource_group` varchar(256) NOT NULL COMMENT '分组名',
      `runtime_env_key` varchar(64) NOT NULL COMMENT '运行时环境key',
      `selector` text COMMENT '运行时环境选择器',
      `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运行时环境分组授权';

ALTER TABLE `cluster_profile` ADD COLUMN `use_type` varchar(64) NOT NULL DEFAULT 'publish' COMMENT '用途';
ALTER TABLE `cluster_profile` ADD COLUMN `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除';
ALTER TABLE `apre_label` ADD COLUMN `target_type` varchar(32) NOT NULL DEFAULT 'APRE' COMMENT '打标目标类型';
ALTER TABLE `resource_pool_data` ADD COLUMN `resource_pool_key` varchar(64) NOT NULL COMMENT '资源池KEY';
ALTER TABLE `match_scope_data` ADD COLUMN `restrictions` text NULL COMMENT '限定条件';

CREATE TABLE `stack_serverless_base_app_binding_data` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人工号',
    `modifier` varchar(64) NOT NULL DEFAULT '' COMMENT '修改人工号',
    `gmt_create` datetime NOT NULL COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL COMMENT '修改时间',
    `env_stack_id` varchar(64) NOT NULL COMMENT '环境stackId',
    `serverless_base_app_name` varchar(256) NOT NULL COMMENT 'Serverless基座应用',
    `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_env_stack_id_is_deleted` (`env_stack_id`,`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='环境基座应用绑定数据';

ALTER TABLE `stack_serverless_base_app_binding_data` ADD COLUMN `extra_params` text NULL COMMENT '额外条件';

ALTER TABLE `apre_feature_spec` ADD COLUMN `labels` text NULL COMMENT '标签集合';



