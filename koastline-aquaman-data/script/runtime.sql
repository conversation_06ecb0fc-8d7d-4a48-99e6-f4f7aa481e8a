CREATE TABLE `runtime` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `app_mame` varchar(255) NOT NULL COMMENT '应用名',
   `env_stack_id` varchar(255) NULL COMMENT '环境StackId',
   `runtime_key` varchar(255) NOT NULL COMMENT 'RuntimeKey',
   `resource_group_name` varchar(255) NOT NULL COMMENT '分组',
   `type` varchar(64) NOT NULL COMMENT '类型',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_runtimeKey` (`runtimeKey`),
   KEY `idx_env_stack_id` (`env_stack_id`),
   KEY `idx_resource_group_name` (`resource_group_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='基座Runtime';

CREATE TABLE `runtime_workload` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `runtime_key` varchar(255) NOT NULL COMMENT 'RuntimeKey',
   `site` varchar(255) NOT NULL COMMENT '机房',
   `unit` varchar(255) NOT NULL COMMENT '单元',
   `stage` varchar(255) NOT NULL COMMENT '用途',
   `cluster_id` varchar(255) NOT NULL COMMENT '集群',
   `status` varchar(64) NOT NULL COMMENT '状态',
   `running_status` varchar(64) NOT NULL COMMENT '运行态',
   `creator` varchar(64) NOT NULL COMMENT '创建人工号',
   `modifier` varchar(64) NOT NULL COMMENT '修改人工号',
   `gmt_create` datetime NOT NULL COMMENT '创建时间',
   `gmt_modified` datetime NOT NULL COMMENT '修改时间',
   `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除',
   PRIMARY KEY (`id`),
   KEY `idx_runtime_key` (`runtime_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='RuntimeWorkload';
