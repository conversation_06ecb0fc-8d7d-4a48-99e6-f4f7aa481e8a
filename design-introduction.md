## 背景
* 集团现状是使用四元组进行集群映射，集团中间件依赖四元组进行流量以及安全策略的管控等等。
* koastline gateway 在k8s集群（比如集团asi）安装后会在集群产生SLB的访问入口，屏蔽了底层k8s的路由信息，从而产生了研发域实际与koastline
gateway交互的诉求，弱化了对k8s集群的底层的关注。 
  
基于上述的背景，需要提供一套路由服务，支持符合集团用户心智的四元组来定位koastline gateway，由koastline gateway及其operators代理负责应用发布与运维的全生命周期管理。

## Terminology
* 集群环境
环境是提供给业务应用以及系统组件使用的隔离实体，进一步可以划分为业务环境和系统环境。用户环境需要依赖系统环境的创建。可以类比os中的system space和user space的逻辑划分。
* 系统组件(管控集群)
系统环境对所有koastline相关的组件进行封装，可以对上层的用户环境提供系统级别的支持。
* 业务环境
用户环境对所有用户的可执行应用进行封装，依赖系统环境的存在，若系统环境不存在则无法进行用户环境的创建。
  
## 设计
![data model](data%20model.png)
aquaman数据模型自下往上，依次为k8s集群模型，koastline管控的逻辑集群模型，以及集群环境。各个数据模型的具体含义见下部分。

### 数据模型
koastline aquaman提供了三层数据模型设计，第一层为k8s集群信息，第二层为koastline gateway
入口的逻辑隔离集群（koastline管控集群），第三层为集群环境，集群环境可以被上层的业务环境（比如
应用环境）消费，同时进行业务附属信息的扩展。详细设计文档见 [link](https://yuque.antfin.com/xtask/bxi4v8/zotv18#fb3ea612579ec7fe8b2b093e114d5f0b).
