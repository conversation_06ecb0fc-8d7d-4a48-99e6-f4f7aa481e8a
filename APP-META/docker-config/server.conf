#sever
    server {
        listen          80 sndbuf=640k default_server;

        # include tmd4_loc.conf;

        location @page_not_found {
            rewrite ^/(.*)$ /error404.html;
        }

        location = /status.taobao {
                tmd off;
        }

        ####################################
        ###all the location must add here###
        ####################################

        location / {
                proxy_pass   http://backends;
        }

        location =/nginx_status {
            allow   127.0.0.0/24;
            allow   33.0.0.0/8;
            deny    all;
            stub_status     on;
            expires         off;
        }
    }