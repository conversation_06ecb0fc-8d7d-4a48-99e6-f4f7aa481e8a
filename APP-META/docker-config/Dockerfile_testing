FROM reg.docker.alibaba-inc.com/aone-base/ae-boot-web-inc-7u2:latest

RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# nginx should NOT redirect for HTTP status code 400-599
COPY tengine-http.conf /home/<USER>/cai/conf/
# All requests go to backend, including static files
COPY server.conf /home/<USER>/cai/conf/

COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# ilogtail
RUN mkdir -p /usr/local/ilogtail/
COPY environment/ilogtail/ilogtail_config.json /usr/local/ilogtail/
RUN mkdir -p /etc/ilogtail/users/
RUN echo "udi-koastline-aquaman-daily" > /etc/ilogtail/user_defined_id
RUN rm -rf /etc/ilogtail/users/* && \
    touch /etc/ilogtail/users/1434335963645324

ENV spring_profiles_active=test
ENV STATUSROOT_HOME=/home/<USER>/koastline-aquaman/target/htdocs
RUN source /home/<USER>/${APP_NAME}/bin/setenv.sh