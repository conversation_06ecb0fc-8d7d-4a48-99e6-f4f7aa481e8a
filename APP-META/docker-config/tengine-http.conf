http {

  include                 mime.types;
  default_type            text/plain;

  root                    /home/<USER>/koastline-aquaman/target/htdocs;


  log_format          proxyformat "$remote_addr $request_time_usec $http_x_readtime [$time_local] \"$request_method $scheme://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$http_user_agent\" $remote_port \"$cookie_ali_beacon_id\" \"a=$ali_apache_track; b=$ali_apache_tracktmp; c=$ali_resin_trace\" \"$hostname\" $http_ORIG_CLIENT_IP\" \"$eagleeye_traceid\"";

  access_log          "pipe:/opt/taobao/cronolog/sbin/cronolog /home/<USER>/cai/logs/cronolog/%Y/%m/%Y-%m-%d-access_log" proxyformat;

  log_not_found       off;

  beacon on;
  beacon_config aplus-beacon.cfg;

  alicookie_enable    on;
  alicookie_forevercookiedomain  .aliexpress.com;

  alibeacon_enable  off;
  alibeacon_config  alibeacon.cfg;
  underscores_in_headers on;
  proxy_pass_header Server;


  sendfile                on;
  tcp_nopush              on;

  keepalive_requests      30;
  keepalive_timeout       25s;

  client_header_timeout   50s;
  send_timeout            50s;
  client_max_body_size    20m;

  server_tokens           off;

  # gzip
  gzip                on;
  gzip_http_version   1.0;
  gzip_comp_level     6;
  gzip_min_length     1024;
  gzip_proxied        any;
  gzip_vary           on;
  gzip_disable        msie6;
  gzip_buffers        96 8k;
  gzip_types          text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml;

  eagleeye_traceid_var    $eagleeye_traceid;

  # proxy
  proxy_set_header        Host $host;
  proxy_set_header        X-Real-IP $remote_addr;
  proxy_set_header        Web-Server-Type nginx;
  proxy_set_header        WL-Proxy-Client-IP $remote_addr;
  proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header        EagleEye-TraceId $eagleeye_traceid;
  proxy_redirect          off;
  proxy_buffers           128 8k;
  proxy_temp_path         data/proxy;
  proxy_intercept_errors  on;

  index               index.htm index.html;

  # include tmd4_http.conf;
  # init_by_lua_file "/opt/taobao/tengine/conf/init_by_lua_file.lua";

  upstream backends {
    server              127.0.0.1:7001;
  }

  include server.conf;
}