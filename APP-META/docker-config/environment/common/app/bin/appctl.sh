#!/bin/bash
##
## Usage  :    sh bin/jbossctl pubstart
## Author :    <EMAIL>
## Docs   :    http://gitlab.alibaba-inc.com/spring-boot-incubator/boot-scripts-rpm.git
## =========================================================================================

#######  error code specification  #########
# Please update this documentation if new error code is added.
# 1   => reserved for script error
# 2   => bad usage
# 3   => bad user
# 4   => core env is not exist
# 5   => preload.sh check failed
# 6   => hsf online failed
# 7   => nginx start failed
# 8   => status.taobao check failed
# 9   => hsf offline failed
# 128 => exit with error message

##------------- constants -----------------##
PROG_NAME=$0
ACTION=$1
JAVA_PORT="7001"
HEALTH_URL="http://localhost:7002/health"
LAUNCHER_HOME="/home/<USER>/ali-springboot-launcher"
##------------- constants -----------------##


##------------- functions define ----------------##
usage() {
    echo "Usage: $PROG_NAME {start|stop|online|offline|pubstart|restart|deploy}" && exit 2
}

error(){
    if [ ! -f ${LAUNCHER_HOME}/.error ]; then
        touch ${LAUNCHER_HOME}/.error
    fi
}

success(){
    echo "[   OK] -- SUCCESS"
    echo "-----------------------------------------------------------------------------"
}

offline_http() {
    echo "[ 1/10] -- offline http from load balance server"
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        rm -f $STATUSROOT_HOME/status.taobao
        rm -f $STATUSROOT_HOME/status.html
        sleep 30 ## sleep some time to make sure all visits are finished
    fi
}

offline_hsf() {
    echo "[ 2/10] -- offline hsf from hsf registry server"
    OFFLINE_HSF_URL="http://localhost:7002/hsf/offline"
    if curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} | grep -q hsf ; then
        hsf_offlined=false
        times=10
        for e in $(seq 10); do
            curl -s --connect-timeout 3 --max-time 5 ${OFFLINE_HSF_URL} -o /dev/null
            sleep 1
            HEALTH_CHECK_CODE=`curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} -o /dev/null -w %{http_code}`
            if [ "$HEALTH_CHECK_CODE" == "503" ] && curl -s ${HEALTH_URL} | grep -q OUT_OF_SERVICE ; then
                hsf_offlined=true
                sleep 5 ## sleep some time to make sure all visits are finished
                break
            else
                COSTTIME=$(($times - $e ))
                echo -n -e  "\offline hsf `expr $COSTTIME` seconds."
            fi
        done
        if [ "$hsf_offlined" = false ] ; then
            echo "hsf offline failed. HSF is still online!"
        fi
    fi
}

stop_http() {
    echo "[ 3/10] -- stop http server"
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        if [ ! -f "$NGINXCTL" ]; then
            echo "        -- $NGINXCTL not found." && error && exit 1
        fi
        "$NGINXCTL" stop | sed 's/^/        -- /g'
    fi
}

fetchjavapid(){
    if [ -f $PIDFILE ]; then
       pid=`cat $PIDFILE`
       echo $pid
    else
       pid=`ps -ef|grep java|grep $APP_NAME|grep -v appctl.sh|grep -v jbossctl| grep -v restart.sh |grep -v grep`
       echo $pid
    fi
}

stopjava() {
    echo "[ 4/10] -- stop java process"
    if [ -f "$PIDFILE" ]; then
        rm -rf $PIDFILE
    fi
    if [ -f "$PIDFILEBAK" ]; then
        rm -rf $PIDFILEBAK
    fi
    times=60
    for e in $(seq 60)
    do
        sleep 1
        COSTTIME=$(($times - $e ))
        checkjavapida=`ps -ef|grep java|grep $APP_NAME|grep -v appctl.sh|grep -v jbossctl| grep -v restart.sh |grep -v grep`
        if [[ $checkjavapida ]];then
                checkjavapid=`ps -ef|grep java|grep $APP_NAME|grep -v appctl.sh|grep -v jbossctl | grep -v restart.sh |grep -v grep|awk '{print $2}'`
                kill -0 $checkjavapid > /dev/null 2>&1
                if [ $? -eq 0 ]; then
                   kill -15 $checkjavapid
                   sleep 5
                   checkjavapid=`ps -ef|grep java|grep $APP_NAME|grep -v appctl.sh|grep -v jbossctl | grep -v restart.sh |grep -v grep|awk '{print $2}'`
                   if [[ "X$checkjavapid" != "X" ]];then
                        kill -9 $checkjavapid
                   fi
                   echo -n -e  "\r        -- stopping java lasts `expr $COSTTIME` seconds."
                fi
        else
                break;
        fi
    done
    echo ""
}

extract_tgz() {
    echo "[ 5/10] -- extract tgz package"

    APP_TGZ=`ls ${APP_HOME}/target/*${APP_NAME}*.tgz`
    if tar tvf ${APP_TGZ} | grep -q "${APP_NAME}.jar" ; then
        echo "        -- package to launch: ${APP_NAME}.jar"
        PACKAGING="jar"
        RUNNABLE_PACKAGE=${APP_NAME}.jar
    elif tar tvf ${APP_TGZ} | grep -q "${APP_NAME}.war" ; then
        echo "        -- package to launch: ${APP_NAME}.war"
        PACKAGING="war"
        RUNNABLE_PACKAGE=${APP_NAME}.war
        EXPLODED_SKIP=0
    else
        echo "        -- no package found"
        error && exit 2
    fi

    cd "${APP_HOME}/target" || (error;exit 1)
    rm -rf ${APP_HOME}/target/${RUNNABLE_PACKAGE}
    tar xzf ${APP_TGZ} -C "${APP_HOME}/target/"
    [[ ${APP_HOME}/target/${RUNNABLE_PACKAGE} ]] || ( echo "unzip failed";error;exit 1 )

    ## special logic for war package, its exploded dir must be same as war file name
    if [[ "${PACKAGING}" == "war" ]]; then
        mv ${APP_HOME}/target/${RUNNABLE_PACKAGE} ${APP_HOME}/target/${RUNNABLE_PACKAGE}.original
        EXPLODED_TARGET="${APP_HOME}/target/${RUNNABLE_PACKAGE}"
        RUNNABLE_PACKAGE="${RUNNABLE_PACKAGE}.original"
    fi

    if [ $EXPLODED_SKIP -eq 0 ]; then
        cd "${APP_HOME}/target" || exit 1
        if [ -d ${EXPLODED_TARGET} ] ; then
            rm -fr ${EXPLODED_TARGET}
        fi
        mkdir -p ${EXPLODED_TARGET}
        unzip -q -d ${EXPLODED_TARGET} ${RUNNABLE_PACKAGE}
        echo "        -- package exploded to: ${EXPLODED_TARGET}"
    fi
}


prepare_xflush_config(){
    echo "        -- prepare xflush config"
    ## prepare log directory, create symbolic link for legacy xflush monitor config
    mkdir -p ${APP_HOME}/logs/
    touch ${APP_HOME}/logs/application.log
    LEGACY_LOG="/home/<USER>/out/logs/sys/webx.log"
    mkdir -p `dirname $LEGACY_LOG`
    [ -f $LEGACY_LOG ] && rm -f $LEGACY_LOG
    ln -s ${APP_HOME}/logs/application.log $LEGACY_LOG
    ## prepare log directory done
}


launch_java(){
    echo "        -- java stdout log: ${JAVA_OUT}"
    SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --startup.at=$(($(date +%s%N)/1000000))"
    ## need explode the jar
    if [ $EXPLODED_SKIP -eq 0 ]; then
        cd ${EXPLODED_TARGET} || (error;exit 1)
        if [[ "${PACKAGING}" == "jar" ]]; then
            nohup $JAVA_HOME/bin/java $JAVA_OPTS org.springframework.boot.loader.JarLauncher $SPRINGBOOT_OPTS &>$JAVA_OUT &
        elif [[ "${PACKAGING}" == "war" ]]; then
            nohup $JAVA_HOME/bin/java $JAVA_OPTS org.springframework.boot.loader.WarLauncher $SPRINGBOOT_OPTS &>$JAVA_OUT &
        else
            echo "        -- unable to launch java in ${EXPLODED_TARGET} " && error && exit 1
        fi
    else
        JAVA_OPTS="$JAVA_OPTS -jar"
        nohup $JAVA_HOME/bin/java $JAVA_OPTS ${APP_HOME}/target/${RUNNABLE_PACKAGE} ${SPRINGBOOT_OPTS} &>$JAVA_OUT &
    fi
}

## support to fast failed for starting from 1.4 bom
write_java_pidfile(){
    for e in $(seq 10); do
        sleep 1
        if [ -f "$PIDFILE" ]; then
            pid=`cat $PIDFILE`
            if [[ "X$pid" != "X" ]]; then
                echo "        -- backup APP_PID=$pid"
                if [ ! -f "$PIDFILEBAK" ]; then
                   touch $PIDFILEBAK
                fi
                echo $pid > $PIDFILEBAK
                break
            fi
        fi
    done
}

startjava() {
    extract_tgz
    echo "[ 6/10] -- start java process"
    prepare_xflush_config
    launch_java
    ## write java pid into application.pid
    write_java_pidfile
    ## check health for starters
    check_java
}

## check process java & starter
do_check(){
  if [ -f "$PIDFILE" ]; then
      APP_PID=`cat $PIDFILE`
      checkjavapid=`ps aux | grep -w $APP_PID | grep -v grep | awk '{print $2}'`
      if [ "X$checkjavapid" == "X" ]; then
          echo $HEALTH_CODE_FAILED
      fi
  fi

  HEALTH_CHECK_CODE=`curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} -o /dev/null -w %{http_code}`
  if [ "$HEALTH_CHECK_CODE" == "200" ]; then
    echo $HEALTH_CODE_SUCCESS
    elif [ "$HEALTH_CHECK_CODE" == "503" ] && curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} | grep -q '"status":"DOWN"' ; then
        echo -n -e  "\r        -- check health lasts `expr $COSTTIME` seconds."
  elif [ "$HEALTH_CHECK_CODE" == "503" ] && curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} | grep -q OUT_OF_SERVICE ; then
    echo $HEALTH_CODE_SUCCESS
  else
    echo -n -e  "\r        -- check health lasts `expr $COSTTIME` seconds."
  fi
}

check_java(){
    echo "[ 7/10] -- check health for java process & starters"
    echo "        -- java health check url: ${HEALTH_URL}"

    times=${HEALTH_CHECK_TIMES}
    status=0
    for e in $(seq $times); do
        sleep 1
        COSTTIME=$(($times - $e ))
        CHECK_JAVA_RESULT=$(do_check)
        if [ "$CHECK_JAVA_RESULT" == "$HEALTH_CODE_SUCCESS" ]; then
            status=1
            break
        elif [ "$CHECK_JAVA_RESULT" == "$HEALTH_CODE_FAILED" ]; then
            break
        else
            echo $CHECK_JAVA_RESULT
        fi
    done

    ## have error
    if [ $status -eq 0 ]; then
        echo "        -- health check failed." && error
        HEALTH_CHECK_CODE=`curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} -o /dev/null -w %{http_code}`
        if [ $? -eq 7 ]; then
            echo "[ERROR] -- could not connect to ${HEALTH_URL}"
        else
            echo "[ERROR] -- server[${HEALTH_URL}] responded http code ${HEALTH_CHECK_CODE}"
        fi
        exit 10001
    fi
}

start_http() {
    echo "[ 8/10] -- start http server"
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        if [ ! -f "$NGINXCTL" ]; then
            echo "        -- $NGINXCTL not found." && error && exit 1
        fi
        "$NGINXCTL" start | sed 's/^/        -- /g'
        if [ "$?" == "0" ]; then
            :
        else
            echo "[ERROR] -- HTTP Start Failed."
            exit 7 # nginx start failed
        fi
    fi
}

online_http() {
    echo "[ 9/10] -- online http from load balance server"
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        mkdir -p $STATUSTAOBAO_HOME
        touch -m $STATUSTAOBAO_HOME/status.taobao
        touch -m $STATUSTAOBAO_HOME/status.html
    fi
}

online_hsf() {
    echo "[10/10] -- online hsf from hsf registry server"
    ONLINE_HSF_URL="http://localhost:7002/hsf/online"
    if curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} | grep -q hsf ; then
        hsf_onlined=false
        times=10
        for e in $(seq 10); do
            curl -s --connect-timeout 3 --max-time 5 ${ONLINE_HSF_URL} -o /dev/null
            sleep 1
            HEALTH_CHECK_CODE=`curl -s --connect-timeout 3 --max-time 5 ${HEALTH_URL} -o /dev/null -w %{http_code}`
            if [ "$HEALTH_CHECK_CODE" == "200" ]; then
                hsf_onlined=true
                break
            else
                COSTTIME=$(($times - $e ))
                echo -n -e  "        -- online hsf `expr $COSTTIME` seconds."
            fi
        done
        if [ "$hsf_onlined" = false ] ; then
            echo "[ERROR] -- hsf online failed. HSF is NOT online!"
            exit 6
        fi
    fi
}


online() {
    online_http
    online_hsf
}


offline() {
    offline_http
    offline_hsf
}

before_start() {
    if [ -f /home/<USER>/${APP_NAME}/bin/before_start.sh ]; then
        source /home/<USER>/${APP_NAME}/bin/before_start.sh;
    fi
}

after_start() {
    if [ -f /home/<USER>/${APP_NAME}/bin/after_start.sh ]; then
        source /home/<USER>/${APP_NAME}/bin/after_start.sh;
    fi
}

before_stop() {
    if [ -f /home/<USER>/${APP_NAME}/bin/before_stop.sh ]; then
        source /home/<USER>/${APP_NAME}/bin/before_stop.sh;
    fi
}

after_stop() {
    if [ -f /home/<USER>/${APP_NAME}/bin/after_stop.sh ]; then
        source /home/<USER>/${APP_NAME}/bin/after_stop.sh;
    fi
}


##------------- functions define ----------------##


echo "-----------------------------------------------------------------------------"
echo "[  CMD] -- '/bin/bash ${LAUNCHER_HOME}/appctl.sh $@'"
echo "-----------------------------------------------------------------------------"

if [ -f ${LAUNCHER_HOME}/.error ]; then
    rm -rf ${LAUNCHER_HOME}/.error
fi

if [ -f ${LAUNCHER_HOME}/dockerenv.sh ]; then
    source ${LAUNCHER_HOME}/dockerenv.sh
else
    echo "[ERROR] -- ${LAUNCHER_HOME}/dockerenv.sh is not exist "
    error && exit 4
fi

if [ -z "$APP_NAME" ]; then
    echo "[ERROR] -- can not find env APP_NAME"
    error && exit 4
fi

APP_HOME="/home/<USER>/${APP_NAME}"

if [ "$UID" -eq 0 ]; then
    echo "[ERROR] -- can't run as root, please use: sudo -u admin $0 $@"
    error && exit 3
fi

if [ $# -lt 1 ]; then
    usage
    error && exit 2
fi

source "$LAUNCHER_HOME/setenv.sh"

PACKAGING="unknown"
RUNNABLE_PACKAGE="unknown"
EXPLODED_TARGET="${APP_HOME}/target/exploded"
PIDFILE="${APP_HOME}/logs/application.pid"
PIDFILEBAK="${LAUNCHER_HOME}/.application.pid"


case "$ACTION" in
    start)
        before_start
        startjava
        start_http
        after_start
        online
        success
    ;;
    stop)
        offline
        before_stop
        stop_http
        stopjava
        after_stop
        success
    ;;
    pubstart)
        offline
        before_stop
        stop_http
        stopjava
        after_stop
        before_start
        startjava
        start_http
        after_start
        online
        success
    ;;
    online)
        online
        success
    ;;
    offline)
        offline
        success
    ;;
    restart)
        offline
        before_stop
        stop_http
        stopjava
        after_stop
        before_start
        startjava
        start_http
        after_start
        online
        success
    ;;
    *)
        usage
    ;;
esac